# PR 制片系统

## 环境

- 开发环境：pr-dev.changdu.ltd
- 测试环境：pr-test.changdu.ltd
- 预发环境：pr-stage.changdu.vip
- 生产环境：pr.changdu.vip



export enum RoleType {
  Director = 1, // 导演
  Screenwriter = 2, // 编剧
  Producer = 3, // 制片人
  Photographer = 4, // 摄影
  Lighting = 5, // 灯光
  Costume = 6, // 服装
  Props = 7, // 道具
  Makeup = 8, // 化妆
  Actor = 9, // 演员
  ExecutiveProducer = 10, // 总监制
  ResponsibleProducer = 11, // 负责监制
  LineProducer = 12, // 执行制片
  ActorCoordinator = 13, // 演员统筹
  AssistantDirectorActor = 14, // 演员副导
  Coordinator = 15, // 统筹
  ExecutiveDirector = 16, // 执行导演
  DIT = 17, // DIT
  FieldProducer = 18, // 现场制片
  Driver = 19, // 司机
  ArtDirector = 20, // 美术
  CostumeAndMakeupHead = 21, // 服化负责人
  ProductionManager = 22, // 制片主任
  ExternalProducer = 23, // 外联制片
  LifeProducer = 24, // 生活制片
  SetAssistant = 25, // 场务
  ScriptSupervisor = 26, // 场记
  PhotographyAssistant = 27, // 摄影助理
  StillPhotographer = 28, // 剧照
  SoundRecordist = 29, // 收音师
  SoundAssistant = 30, // 收音助理
  LightingTechnician = 31, // 灯光师
  LightingAssistant = 32, // 灯光师助理
  Stylist = 33, // 造型师
  OnSetSupervisor = 34, // 现场主盯
  OnSetMakeup = 35, // 现场跟妆
  MakeupArtist = 36, // 改妆师
  HairAndMakeup = 37, // 梳化
  SetConstruction = 38, // 制景
  CameraEquipment = 39, // 摄影器材
  LightingEquipment = 40, // 灯光器材
  SetSupplies = 41, // 场务用品
  MartialArtsDirector = 42, // 武术指导
  Wuxing = 43, // 武行
  ArtAssistant = 44, // 美术助理
  StylistAssistant = 45, // 造型师助理
  CostumeAssistant = 46, // 服装助理
  DirectorAssistant = 47, // 导演助理
  ProducerAssistant = 48, // 制片助理
  MakeupAssistant = 49, // 化妆助理
  ActorAssistant = 50, // 演员助理
  Cleaner = 51 // 保洁人员
}