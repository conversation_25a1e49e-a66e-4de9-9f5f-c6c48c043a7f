<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="renderer" content="webkit" />
    <meta name="force-rendering" content="webkit" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>畅读制片管理系统</title>
    <style>
      .loader {
        position: fixed;
        left: 0;
        top: 0;
        z-index: 99999;
        width: 100%;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #f7f8f8;
        background-size: 400% 400%;
        opacity: 1;
        transition: opacity 1s ease-in-out 0.3s;
        animation: GradientAnimation 10s ease infinite;
      }
      .loader.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .loader div {
        width: 50px;
        height: 50px;
        position: absolute;
        border-radius: 50%;
        background: radial-gradient(circle, #ff9900, #f60);
        opacity: 0.6;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        animation: bounce 2s infinite ease-in-out;
      }
      .loader div:nth-child(2) {
        animation-delay: -1s;
      }
      @keyframes bounce {
        0%,
        100% {
          transform: translateY(0);
        }
        50% {
          transform: translateY(-20px);
          opacity: 1;
        }
      }
      @keyframes GradientAnimation {
        0% {
          background-position: 0 50%;
        }
        50% {
          background-position: 100% 50%;
        }
        100% {
          background-position: 0 50%;
        }
      }
    </style>
  </head>

  <body>
    <div id="root"></div>
    <div class="loader" id="loader">
      <div></div>
      <div></div>
    </div>
  </body>
</html>
