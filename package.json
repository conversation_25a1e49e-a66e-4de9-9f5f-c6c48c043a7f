{"name": "pr", "version": "1.0.0", "description": "制片", "main": "index.js", "repository": {"type": "git", "url": "https://gitlab.changdu.ltd/fe/pc/compass.git"}, "author": "lsh", "license": "ISC", "scripts": {"dev": "cross-env NODE_ENV=development webpack-dev-server -c build/webpack.dev.js", "dev:mock": "cross-env NODE_ENV=development NODE_MOCK=mock webpack-dev-server -c build/webpack.dev.js", "build": "webpack -c build/webpack.prod.js", "stylelint": "stylelint 'src/**/*.(scss|css|less)' --fix", "prepare": "husky install", "tag:test": "node auto-tag.js test", "tag:release": "node auto-tag.js release"}, "lint-staged": {"src/**/*.{js,jsx,tsx,ts,json}": ["prettier --write .", "eslint --fix", "git add"], "src/**/*.scss": ["prettier --write .", "stylelint --fix", "git add"], "*.md": ["prettier --write"]}, "browserslist": ["chrome 90"], "eslintConfig": {"extends": ["@fe/eslint-config-react"]}, "stylelint": {"extends": "@fe/stylelint-config-scss"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "postcss": {"plugins": ["autoprefixer"]}, "eslintIgnore": ["build/*.js"], "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/preset-env": "^7.25.4", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@fe/eslint-config-react": "0.2.1-rc.3", "@fe/stylelint-config-scss": "0.3.1-rc.2", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.15", "@province-city-china/level": "^8.5.7", "@province-city-china/province": "^8.5.7", "@types/file-saver": "^2.0.7", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.20", "buffer": "^6.0.3", "commitlint": "^17.8.1", "copy-webpack-plugin": "^11.0.0", "core-js": "^3.38.1", "cross-env": "^7.0.3", "css-loader": "^6.11.0", "esbuild-loader": "^4.2.2", "eslint": "^8.57.0", "glob-all": "^3.3.1", "html-loader": "^5.1.0", "html-webpack-plugin": "^5.6.0", "husky": "^8.0.3", "lint-staged": "^13.3.0", "mini-css-extract-plugin": "^2.9.1", "mockjs": "^1.1.0", "postcss": "^8.4.47", "postcss-loader": "^8.1.1", "prettier": "^2.8.8", "react-refresh": "^0.14.2", "sass": "^1.79.5", "sass-loader": "^16.0.2", "stream": "^0.0.2", "style-loader": "^3.3.4", "stylelint": "^15.11.0", "typescript": "^5.5.4", "util": "0.10.0", "webpack": "^5.99.9", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.2", "webpack-merge": "^5.10.0", "webpack-subresource-integrity": "5.2.0-rc.1", "workbox-webpack-plugin": "^7.1.0"}, "dependencies": {"@ahooksjs/use-url-state": "^3.5.1", "@ant-design/pro-components": "2.6.3", "@fe/apm": "^0.2.5", "@fe/rockrose": "^3.0.2", "@js-preview/docx": "1.3.0", "antd": "^5.20.3", "axios": "^1.9.0", "cos-js-sdk-v5": "^1.8.4", "dayjs": "^1.11.13", "decimal.js": "^10.4.3", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "handlebars": "^4.7.8", "i18next": "^23.15.1", "nanoid": "^5.0.7", "nprogress": "^0.2.0", "p-limit": "6.1.0", "pinyin-pro": "^3.26.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-i18next": "^15.0.2", "react-infinite-scroll-component": "^6.1.0", "react-router-dom": "^6.30.0", "typed.js": "^2.1.0", "zustand": "^5.0.5"}, "jscpd": {"threshold": 50, "reporters": ["html", "console"], "ignore": ["**/svg/**"], "absolute": true, "gitignore": true}, "packageManager": "pnpm@10.6.3+sha512.bb45e34d50a9a76e858a95837301bfb6bd6d35aea2c5d52094fa497a467c43f5c440103ce2511e9e0a2f89c3d6071baac3358fc68ac6fb75e2ceb3d2736065e6"}