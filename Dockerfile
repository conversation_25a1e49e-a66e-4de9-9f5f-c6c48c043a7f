FROM harbor.changdu.ltd/public/node:v6 as builder

WORKDIR /app
COPY . .

ARG LANG
ARG DOMAIN
ARG ENV
ARG TYPE

RUN  pnpm install \
  && echo $LANG \
  && echo $DOMAIN \
  && echo $ENV \
  && echo $TYPE \
  && npm_config_lang=$LANG npm_config_domain=$DOMAIN npm_config_env=$ENV npm_config_type=$TYPE  npm run build

FROM harbor.changdu.ltd/cos/coscmd:v7

WORKDIR /app/dist

ARG WEB_BUCKET
ARG WEB_REGION
ARG G_BUCKET
ARG G_REGION
ARG DOMAIN

COPY --from=builder /app/dist .

RUN if [ -d "/app/dist/source-map" ]; then cp -r /app/dist/source-map/* /mnt/source-map/; fi

RUN bash /opt/ms-cos-client.sh $WEB_BUCKET $WEB_REGION $G_BUCKET $G_REGION $DOMAIN
CMD ["/bin/bash"]

