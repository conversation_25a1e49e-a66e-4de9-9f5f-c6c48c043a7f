const path = require('path')
const webpack = require('webpack')
const { merge } = require('webpack-merge')
const CopyPlugin = require('copy-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const { EsbuildPlugin } = require('esbuild-loader')
const { GenerateSW } = require('workbox-webpack-plugin')
const baseConfig = require('./webpack.base.js')

const domain = process.env.npm_config_domain

module.exports = merge(baseConfig, {
  mode: 'production',
  devtool: false,
  optimization: {
    splitChunks: {
      // 分隔代码
      cacheGroups: {
        vendors: {
          test: /node_modules/,
          name: 'vendors',
          minChunks: 1,
          chunks: 'initial',
          minSize: 0,
          priority: 1,
        },
        commons: {
          name: 'commons',
          minChunks: 2,
          chunks: 'initial',
          minSize: 0,
        },
      },
    },
    minimizer: [
      new EsbuildPlugin({
        target: 'es2015',
        css: true,
      }),
    ],
  },
  plugins: [
    new MiniCssExtractPlugin({ filename: '[contenthash:8].css' }),
    new CopyPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, '../public'),
          to: path.resolve(__dirname, '../dist'),
          filter: source => !source.includes('index.html'),
        },
      ],
    }),
    new GenerateSW({
      clientsClaim: true,
      skipWaiting: true,
      swDest: `${domain}/sw.js`,
      include: [/\.js$/, /\.css/, /\.ico/, /\.json/],
      exclude: [/\.DS_Store/, /\.map$/],
      maximumFileSizeToCacheInBytes: 30 * 1024 * 1024,
      dontCacheBustURLsMatching: /.*\.(?:js|css|ico)/,
      runtimeCaching: [
        {
          urlPattern: /https:\/\/(g.changdu.vip|g.cdreader.com)/,
          handler: 'CacheFirst', // 缓存优先
          options: {
            cacheName: 'asset',
            cacheableResponse: {
              statuses: [0, 200],
            },
            expiration: {
              maxEntries: 50, // 最多缓存50个文件，避免缓存溢出
              maxAgeSeconds: 30 * 24 * 60 * 60, // 30天
            },
          },
        },
        {
          urlPattern: new RegExp(`https://${domain}`),
          handler: 'NetworkFirst', // 网络优先
          options: {
            cacheName: 'html',
            cacheableResponse: {
              statuses: [0, 200],
            },
            networkTimeoutSeconds: 3,
          },
        },
      ],
    }),
    new webpack.SourceMapDevToolPlugin({
      filename: 'source-map/[file].map',
      append: false,
      exclude: [/.css/, /sensorsdata/, /track/, /vendor/, /react/],
    }),
  ],
})
