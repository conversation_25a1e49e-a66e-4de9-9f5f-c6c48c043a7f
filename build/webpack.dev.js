/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path')
const { merge } = require('webpack-merge')
const ReactRefreshWebpackPlugin = require('@pmmmwh/react-refresh-webpack-plugin')
const baseConfig = require('./webpack.base.js')

module.exports = merge(baseConfig, {
  mode: 'development',
  devtool: 'eval-cheap-module-source-map',
  devServer: {
    port: 3999,
    compress: false,
    hot: true,
    historyApiFallback: true,
    static: { directory: path.join(__dirname, '../public') },
    open: true,
  },
  plugins: [new ReactRefreshWebpackPlugin()],
})
