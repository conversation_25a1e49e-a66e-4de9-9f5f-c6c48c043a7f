const path = require('path')
const webpack = require('webpack')
const HtmlWebpackPlugin = require('html-webpack-plugin')
const MiniCssExtractPlugin = require('mini-css-extract-plugin')
const pkg = require('../package.json')

const plugins = []
const isDev = process.env.NODE_ENV === 'development'
const lang = process.env.npm_config_lang
const env = process.env.npm_config_env
const domain = process.env.npm_config_domain
const webType = process.env.npm_config_type

/**
 * 本地: /
 * 线上 & PC: g.cdreader.com
 * 其他: g.changdu.vip
 *
 */
const publicPath = isDev
  ? '/'
  : env === 'prod' && webType === 'PC'
    ? 'https://g.cdreader.com/'
    : 'https://g.changdu.vip/'
const htmlPluginOptions = {
  filename: 'index.html',
  template: path.resolve(__dirname, '../public/index.html'),
  favicon: path.resolve(__dirname, `../public/${pkg.name}.ico`),
  inject: true,
}

if (isDev) {
  plugins.push(new HtmlWebpackPlugin(htmlPluginOptions))
} else {
  if (!domain || !webType || !lang || !env) {
    throw Error('构建参数不全，请检查[lang]、[env]、[domain]、[type]是否缺失！')
  }

  lang?.split(',').forEach(item => {
    plugins.push(
      new HtmlWebpackPlugin({
        ...htmlPluginOptions,
        filename: `${domain}/index.html`,
      })
    )
  })
}

module.exports = {
  entry: path.join(__dirname, '../src/index.tsx'),
  output: {
    filename: '[name].[chunkhash:8].js',
    path: path.join(__dirname, '../dist'),
    clean: true,
    publicPath,
  },
  module: {
    rules: [
      {
        test: /\.[jt]sx?$/,
        use: [
          {
            loader: 'esbuild-loader',
            options: { target: 'es2015' },
          },
        ],
        include: [path.resolve(__dirname, '../src')],
      },
      {
        test: /.(s?css)$/,
        use: [
          isDev ? 'style-loader' : MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              modules: {
                mode: 'local',
                localIdentName: isDev ? '[path]_[name]_[local]' : '[local]-[hash:base64:8]',
              },
            },
          },
          'postcss-loader',
          'sass-loader',
        ],
        include: [path.resolve(__dirname, '../src')],
      },
      {
        test: /.css$/,
        use: [isDev ? 'style-loader' : MiniCssExtractPlugin.loader, 'css-loader'],
        include: [path.resolve(__dirname, '../node_modules')],
      },
      {
        test: /.html$/,
        use: 'html-loader',
      },
      {
        test: /.(png|jpg|jpeg|gif|svg|woff2?|eot|ttf|otf|mp4|webm|ogg|mp3|wav|flac|aac)$/,
        type: 'asset',
        parser: { dataUrlCondition: { maxSize: 10 * 1024 } },
        generator: { filename: '[contenthash:8][ext]' },
      },
    ],
  },
  // 构建缓存
  cache: { type: 'filesystem' },
  resolve: {
    alias: {
      '@': path.join(__dirname, '../src'),
      '~': path.join(__dirname, '../src'),
    },
    extensions: ['.tsx', '.ts', '.js', '.scss', '.css'],
    modules: [path.resolve(__dirname, '../node_modules')],
  },
  plugins: [...plugins, new webpack.EnvironmentPlugin({ NODE_MOCK: process.env?.NODE_MOCK || '' })],
}
