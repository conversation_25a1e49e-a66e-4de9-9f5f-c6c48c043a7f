{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"], "~/*": ["src/*"]}, "target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "experimentalDecorators": true}, "include": ["./src/*", "./src/types/*"]}