const { exec } = require('child_process')

// 获取命令行参数
const args = process.argv.slice(2) // 去掉前两个默认参数（node 和脚本路径）
const env = args[0] || 'test' // 默认 env 为 test
const today = new Date().toISOString().slice(0, 10).replace(/-/g, '') // 获取当前日期，格式为 YYYYMMDD

// 拉取远程仓库的更新
function fetchRemoteTags() {
  return new Promise((resolve, reject) => {
    exec('git fetch --tags -f', (error, stdout, stderr) => {
      if (error) {
        reject(`Error: ${error.message}`)
        return
      }
      if (stderr) {
        console.warn(`Stderr: ${stderr}`) // git fetch 的 stderr 通常是警告信息，不一定是错误
      }
      console.log('Remote tags fetched successfully.')
      resolve()
    })
  })
}

// 获取最后一个以 env 开头的 Tag
function getLastTags(env) {
  return new Promise((resolve, reject) => {
    exec(
      'git for-each-ref --sort=-creatordate --format "%(refname:short)" refs/tags',
      (error, stdout, stderr) => {
        if (error) {
          reject(`Error: ${error.message}`)
          return
        }
        if (stderr) {
          reject(`Stderr: ${stderr}`)
          return
        }

        // 获取所有 Tag，并按行分割为数组
        const tags = stdout
          .trim()
          .split('\n')
          .filter(tag => tag.startsWith(`${env}_${today}`))

        // 找到版本号最大的 Tag 不能使用最后一个 Tag,可能存在最后一个不是最大版本
        let lastTag = null
        let maxVersion = -1

        tags.forEach(tag => {
          const parsedTag = parseTag(tag)
          if (parsedTag) {
            const version = parseInt(parsedTag.version, 10)
            if (version > maxVersion) {
              maxVersion = version
              lastTag = tag
            }
          }
        })

        resolve(lastTag || null)
      }
    )
  })
}

// 解析 Tag 名称
function parseTag(tag) {
  if (!tag) return null

  const parts = tag.split('_')
  if (parts.length !== 3) return null

  const [prefix, date, version] = parts
  return { prefix, date, version }
}

// 生成新的 Tag
function generateNewTag(env, lastTag) {
  if (!lastTag) {
    // 如果没有最后一个 Tag，生成当天的 Tag，版本号从 01 开始
    return `${env}_${today}_01`
  }

  const parsedTag = parseTag(lastTag)

  if (!parsedTag) {
    // 如果解析失败，生成当天的 Tag，版本号从 01 开始
    return `${env}_${today}_01`
  }

  const { date, version } = parsedTag
  if (date === today) {
    // 如果最后一个 Tag 的日期是今天，版本号自动累加
    const newVersion = String(parseInt(version, 10) + 1).padStart(2, '0')

    return `${env}_${today}_${newVersion}`
  } else {
    // 如果最后一个 Tag 的日期不是今天，生成当天的 Tag，版本号从 01 开始
    return `${env}_${today}_01`
  }
}

// 自动打 Tag 并推送
function createAndPushTag(tagName) {
  return new Promise((resolve, reject) => {
    exec(`git tag ${tagName}`, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error: ${error.message}`)
        return
      }
      if (stderr) {
        console.error(`Stderr: ${stderr}`)
        return
      }
      console.log(`Tag ${tagName} created successfully.`)

      // 推送 Tag 到远程仓库
      exec(`git push origin ${tagName}`, (error, stdout, stderr) => {
        if (error) {
          console.error(`Error: ${error.message}`)
          return
        }
        if (stderr) {
          console.error(`Stderr: ${stderr}`)
          resolve(tagName)
          return
        }
        console.log(`Tag ${tagName} pushed to remote successfully.`)
      })
    })
  })
}

// 使用示例
fetchRemoteTags()
  .then(() => {
    getLastTags(env)
      .then(lastTag => {
        console.log(`Last tag: ${lastTag}`)
        const newTag = generateNewTag(env, lastTag)
        console.log(`Generated new tag: ${newTag}`)
        createAndPushTag(newTag).then(() => {
          console.log(`TAG提交成功: ${newTag}`)
        })
      })
      .catch(error => {
        console.error(error)
      })
  })
  .catch(e => {
    console.error(e)
  })
