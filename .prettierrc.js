/**
 * prettier 配置文件
 * 详情：https://www.prettier.cn/docs/options.html
 */
module.exports = {
  requireConfig: true, // 必须有.prettierrc.js配置文件
  semi: false, // 不加分号
  singleQuote: true, // 单引号
  quoteProps: 'as-needed', // 对象属性名尽量不用引号
  trailingComma: 'es5', // 对象、数组最后添加逗号
  bracketSpacing: true, // 括号之间添加空格
  bracketSameLine: true, // 尖括号 > 不需要单独一行
  arrowParens: 'avoid', // 箭头函数只有1个参数时不需要小括号
  htmlWhitespaceSensitivity: 'css', // html空格不敏感
  jsxSingleQuote: false, // jsx中使用双引号
  /** ------以下4个配置项和 .editorconfig 重复，以 .editorconfig 为准--------- */
  // useTabs: false, // 不适用tab
  // tabWidth: 2, // 缩进2空格
  // printWidth: 120, // 每行最大字符
  // endOfLine: 'lf', // 以 lf 结尾
}
