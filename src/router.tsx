import Layout from '@/layouts'
import Error from '@/pages/error'
import Home from '@/pages/home'
import Login from '@/pages/login/index'
import Logout from '@/pages/login/logout'
import ActorList from '@/pages/production/actor/list'
import ContractList from '@/pages/production/contract/list'
import Room<PERSON><PERSON>ckin from '@/pages/room/checkin'
import RoomGroup from '@/pages/room/group/index'
import RoomHistory from '@/pages/room/history/index'
import Room from '@/pages/room/list'

import PersonList from '@/pages/production/person/list'
import PartnerList from '@/pages/production/partner/list'
import ProjectDetailPage from '@/pages/production/project/detail'
import ProjectList from '@/pages/production/project/list'
import VenueList from '@/pages/production/venue/list'
import Button from '@/pages/system/button'
import Community from '@/pages/system/community'
import Company from '@/pages/system/company'
import Log from '@/pages/system/log'
import Menu from '@/pages/system/menu'
import Role from '@/pages/system/role'
import User from '@/pages/system/user'
import WxUser from '@/pages/system/wx-user'
import { Navigate, type RouteObject } from 'react-router-dom'

export const routes = [
  {
    path: '/login',
    element: <Login />,
  },
  {
    path: '/logout',
    element: <Logout />,
  },
  {
    name: '404',
    path: '*',
    element: <Error />,
  },
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        name: '首页',
        path: '/home',
        element: <Home />,
      },
      {
        index: true,
        element: <Navigate to="/home" />,
      },
    ],
  },
  {
    path: '/production',
    element: <Layout />,
    name: '演员列表',
    children: [
      {
        name: '演员信息库',
        path: '/production/actor',
        element: <ActorList />,
      },
      {
        name: '项目列表',
        path: '/production/project',
        element: <ProjectList />,
      },
      {
        name: '项目详情',
        path: '/production/project/:id',
        element: <ProjectDetailPage />,
      },
      {
        name: '场地',
        path: '/production/venue',
        element: <VenueList />,
      },
      {
        name: '人员管理',
        path: '/production/person',
        element: <PersonList />,
      },
      {
        name: '合同签约',
        path: '/production/contract',
        element: <ContractList />,
      },
       {
        name: '合作商',
        path: '/production/partner',
        element: <PartnerList />,
      },
    ],
  },
  {
    path: '/system',
    element: <Layout />,
    name: '系统',
    children: [
      {
        name: '用户管理',
        path: '/system/user',
        element: <User />,
      },
      {
        name: '菜单管理',
        path: '/system/menu',
        element: <Menu />,
      },
      {
        name: '角色管理',
        path: '/system/role',
        element: <Role />,
      },
      {
        name: '按钮管理',
        path: '/system/button',
        element: <Button />,
      },
      {
        name: '操作日志',
        path: '/system/log',
        element: <Log />,
      },
    ],
  },
  {
    path: '/property',
    element: <Layout />,
    name: '资产',
    children: [
      {
        name: '公寓列表',
        path: '/property/room',
        element: <Room />,
      },
      {
        name: '入住管理',
        path: '/property/roomCheckin',
        element: <RoomCheckin />,
      },
      {
        name: '公寓管理',
        path: '/property/roomGroup',
        element: <RoomGroup />,
      }, {
        name: '入住记录',
        path: '/property/roomHistory',
        element: <RoomHistory />,
      },
      {
        name: '微信用户',
        path: '/property/wx-user',
        element: <WxUser />,
      },
      {
        name: '公司字典',
        path: '/property/company',
        element: <Company />,
      },
      {
        name: '小区字典',
        path: '/property/community',
        element: <Community />,
      },
    ],
  },
] as RouteObject[]
