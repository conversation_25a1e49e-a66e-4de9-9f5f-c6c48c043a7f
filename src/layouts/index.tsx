import { DATE_FORMAT_SHORT } from '@/consts/date'
import { MENU_GROUP_ICON } from '@/consts/menu'
import { routes } from '@/router'
import useIndexStore from '@/store'
import { findMenu } from '@/utils/findMenu'
import * as Icon from '@ant-design/icons'
import { CacheManager, CuteAvatar } from '@fe/rockrose'
import { Button, FloatButton, Layout, Menu, Popover, Space, Typography, Watermark, type MenuProps } from 'antd'
import type { ItemType } from 'antd/es/menu/interface'
import dayjs from 'dayjs'
import React, { useEffect, useMemo, useState } from 'react'
import { Link, Outlet, useLocation, useNavigate } from 'react-router-dom'

import styles from './index.scss'

function generateMenuType(initMenus: Array<any> = []) {
  initMenus.sort((item1, item2) => item2.sort - item1.sort)

  return initMenus.map(item => ({
    label: item.appName,
    key: item.appId,
    icon: MENU_GROUP_ICON[item.appName] ? React.createElement(Icon[MENU_GROUP_ICON[item.appName]]) : null,
  }))
}

const LayoutAdmin: React.FC = () => {
  CacheManager.useCacheManager()
  const { handleLogout, authority, userInfo, fetchPermissions, menusPermission, fetchAuthority } = useIndexStore()

  const defaultType = '/production/actor'
  const [collapsed, setCollapsed] = useState<boolean>(false)
  const navigate = useNavigate()
  const { pathname } = useLocation()

  const current = pathname.split('/')[1] === 'home' ? defaultType : pathname
  const [menus, setMenus] = useState<MenuProps['items']>([])
  const [siderMenu, setSiderMenu] = useState<any>([])
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])
  const memoMenus = useMemo(() => menus?.filter(item => item.label !== '设置'), [menus])
  const hasSetting = useMemo(() => menus?.find(item => item.label === '设置'), [menus])
  const init = async () => {
    fetchPermissions()
    // await fetchAccountInfo()
    document.getElementById('loader')?.classList.add('hidden')
  }
  console.log('current=>', current)

  const generateSubMenu = (initMenus: Array<any> = []): MenuProps['items'] =>
    initMenus.map(item => {
      if (current) {
        if (current === item.url) {
          setSelectedKeys([item.appId])
        } else if (current.includes('/production/project/') && '/production/project' === item.url) {
          setSelectedKeys([item.appId])
        }
      }

      return {
        label:
          item.menuList && item.menuList.length ? (
            <div>{item.menuName}</div>
          ) : (
            <Link to={item.url}>{item.menuName}</Link>
          ),
        key: item.url,
        icon: MENU_GROUP_ICON[item.menuName] ? React.createElement(Icon[MENU_GROUP_ICON[item.menuName]]) : null,
        type: item.menuList && item.menuList.length ? 'group' : null,
        children: item.menuList && item.menuList.length ? generateSubMenu(item.menuList) : null,
      } as ItemType
    })

  const generateMenu = (initMenus: Array<any> = []): Record<string, MenuProps['items']> => {
    initMenus.sort((item1, item2) => item1.sort - item2.sort)

    const menus: Record<string, MenuProps['items']> = {}

    initMenus.forEach(item => {
      menus[item.appId] = generateSubMenu(item.menuList || [])
    })

    return menus
  }

  const handleMenuSelect = (item: any) => {
    setSelectedKeys([item.key])
    if (siderMenu[item.key][0]?.key) {
      navigate(siderMenu[item.key][0].key)
    } else if (siderMenu[item.key][0]?.type === 'group' && siderMenu[item.key][0]?.children[0]?.key) {
      navigate(siderMenu[item.key][0]?.children[0]?.key)
    }
  }

  useEffect(() => {
    setMenus(generateMenuType(menusPermission?.userPowerList || []))
    setSiderMenu(generateMenu(menusPermission?.userPowerList || []))
  }, [menusPermission, pathname])

  useEffect(() => {
    if (authority && authority?.userPowerList) {
      let pathStr = pathname
      if (pathStr.includes('/production/project/')) {
        pathStr = '/production/project'
      }
      const menuId = findMenu(pathStr, authority.userPowerList)?.menuId
      fetchAuthority({ controllerName: '', menuId })
    }

    /*
     * if (userInfo?.userName) {
     *   fetchUserInfo(userInfo?.userName)
     * }
     * fetchAuthority()
     */
  }, [authority, pathname])

  useEffect(() => {
    if (!localStorage.token) {
      navigate('/login')
    }

    init()
  }, [])

  return (
    <Layout className="full-v">
      <Layout.Header className={`${styles.header} ${collapsed ? styles.headerDark : ''}`}>
        <Space size={6} className={styles.logo}>
          <img src={require('@/assets/images/logo.png')} alt="logo" />
          <span>制片管理系统</span>
        </Space>
        <Button
          type="text"
          className={styles.collapsed}
          icon={<Icon.MenuOutlined />}
          onClick={() => setCollapsed(!collapsed)}
        />
        <Space size={64} className={styles.menuGroup}>
          <Menu
            mode="horizontal"
            theme={collapsed ? 'dark' : void 0}
            items={memoMenus}
            selectedKeys={selectedKeys}
            onSelect={handleMenuSelect}
          />
          <Popover
            placement="bottomRight"
            trigger="hover"
            content={
              <Space direction="vertical">
                <Button type="text" onClick={handleLogout}>
                  退出登录
                </Button>
                {hasSetting ? (
                  <Button
                    type="text"
                    onClick={() => {
                      handleMenuSelect(hasSetting)
                    }}>
                    设置
                  </Button>
                ) : null}
              </Space>
            }>
            <Space>
              <CuteAvatar />
              <Typography.Text className={styles.name}>{userInfo?.nickName || ''}</Typography.Text>
            </Space>
          </Popover>
        </Space>
      </Layout.Header>
      <Layout hasSider className={styles.body}>
        <Layout.Sider width={220} collapsed={collapsed} collapsedWidth={0} className={styles.sider}>
          <Menu
            mode="inline"
            theme="light"
            items={siderMenu[selectedKeys[0]]}
            selectedKeys={pathname.split('/')[1] === 'home' ? [] : [current]}
          />
        </Layout.Sider>

        <Layout.Content className="full-v">
          <Watermark
            className="full-v"
            font={{ fontSize: 14, color: 'rgba(170, 170, 170,0.4)', fontWeight: 400 }}
            content={[userInfo?.account || userInfo?.nickName || '', dayjs(new Date()).format(DATE_FORMAT_SHORT)]}
            gap={[200, 200]}>
            <Layout className="full-v">
              <Layout.Content className={styles.content} id="content">
                <CacheManager.RoutesKeepAlive routes={routes} pathsToExclude={['/production/project/:id','/property/roomGroup','/property/roomCheckin']}>
                  <Outlet />
                </CacheManager.RoutesKeepAlive>
                <FloatButton.BackTop target={() => document.getElementById('content') || document.body} />{' '}
              </Layout.Content>
            </Layout>
          </Watermark>
        </Layout.Content>
      </Layout>
    </Layout>
  )
}

export default LayoutAdmin
