@use 'sass:color';
@use '/src/assets/styles/variables' as *;

.sider {
  overflow-y: auto;
  background-color: $sider-bg-color !important;

  :global {
    .ant-menu-root {
      padding: 0 $space-inline;
      height: calc(100vh - $nav-height);
      background-color: $sider-bg-color;
      border-inline-end: 0 !important;
    }

    .ant-menu-item {
      width: 100%;
      border-radius: $border-radius;
      margin-inline: 0;
    }

    .ant-menu-item:hover,
    .ant-menu-item-selected {
      font-weight: 500;
      color: $color-primary !important;
      background-color: $sider-menu-selected-bg;
    }

    .ant-menu-item-icon {
      font-size: $sider-icon-font-size !important;
    }
  }
}

.body {
  overflow-y: hidden;
  height: calc(100vh - $nav-height);
}

.content {
  overflow-y: auto;
  padding: $content-padding;
  padding-bottom: 24px;
  height: 100%;
  flex: 1;
  background: #fff;
}

.logo {
  display: flex;
  justify-content: center;
  align-items: center;
  width: $sider-width;
  height: 100%;
  font-size: 16px;
  font-weight: bold;
  background-color: $sider-bg-color;
  line-height: 1.2;
  gap: 2px;

  img {
    height: 40px;
  }
}

.collapsed {
  margin-left: 2px;
  width: 64px !important;
  height: $nav-height;
  font-size: $sider-icon-font-size;

  &:hover {
    color: $color-primary !important;
    background-color: transparent !important;
  }
}

.menuGroup {
  flex: 1;
  justify-content: flex-end;
  border-bottom: 0;
}

.header {
  z-index: 10;
  display: flex;
  align-items: center;
  padding: 0;
  height: $nav-height;
  border-bottom: 1px solid #f9f9f9;
  padding-inline-end: $nav-padding-inline;
  line-height: $nav-height;

  :global {
    .ant-menu-horizontal {
      border-bottom: none !important;
    }
  }
}

.headerDark {
  border-bottom-color: $nav-dark-bg-color;
  background-color: $nav-dark-bg-color;

  .logo {
    background-image: linear-gradient(300deg, $color-processing 25%, #ff85c0 40%, $color-primary 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .collapsed,
  .name {
    color: #fff !important;
  }

  :global {
    .ant-menu-dark {
      background-color: $nav-dark-bg-color;
    }

    .ant-menu-item-selected {
      background-color: color.adjust($nav-dark-bg-color, $lightness: 8%) !important;

      &::after {
        border-bottom-width: 2px !important;
        border-bottom-color: $color-primary !important;
      }
    }
  }
}
