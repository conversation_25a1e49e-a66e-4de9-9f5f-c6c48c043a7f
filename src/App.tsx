import React from 'react'
import { App, ConfigProvider } from 'antd'
import { RouterProvider, createBrowserRouter } from 'react-router-dom'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import antdConfig from '@/consts/antd'
import { CacheManager } from '@fe/rockrose'
import { routes } from './router'
import '@/assets/styles/index.scss'

const router = createBrowserRouter(routes)
const MyApp = () => (
  <DndProvider backend={HTML5Backend}>
    <ConfigProvider {...antdConfig}>
      <CacheManager.AliveScope>
        <App>
          <RouterProvider router={router} />
        </App>
      </CacheManager.AliveScope>
    </ConfigProvider>
  </DndProvider>
)

export default MyApp
