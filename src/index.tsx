import App from '@/App'
import apm from '@fe/apm'
import { CacheManager } from '@fe/rockrose'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import utc from 'dayjs/plugin/utc'
import { createRoot } from 'react-dom/client'

try {
  const config: any = {
    appId: '1930873999953367040',
    appName: 'film',
    appType: 'MS',
  }

  if (localStorage.getItem('userInfo')) {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') as string)

    config.userId = `${userInfo.nickName}-${userInfo.account}`
  }

  apm.init(config)
} catch (e) {
  console.error(e)
}

dayjs.extend(relativeTime)
dayjs.extend(utc)

const root = document.getElementById('root')

root &&
  createRoot(root).render(
    <CacheManager.AliveScope>
      <App />
    </CacheManager.AliveScope>
  )

// 注册 Service Worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker
      .register('/sw.js')
      .then(reg => {
        console.log('service-worker registered:', reg)
      })
      .catch(() => {
        console.log('service-worker register error')
      })
  })
}

setTimeout(() => {
  document.getElementById('loader')?.classList.add('hidden')
}, 800)
