import { LOGOUT_URL } from '@/consts/auth'
import { removeTokenCookie } from '@/utils/cookie'
import useRequest, { envAuthUrl, post, get as requestGet } from '@/utils/request'
import apm from '@fe/apm'
import { create } from 'zustand'

export interface IPagination {
  total: number
  current: number
  pageSize?: number
}

export enum Role {
  NULL = 0,
  STAFF = 1,
  ORGANIZATION = 2,
  NORMAL = 3,
  ADMIN = 9,
  GROUPER = 4,
}

interface IAuthCodeParams {
  Code: string
  SessionState: string
  redirectUri: string
}

export interface ICommonState {
  userInfo: IUserInfo | null
  isLogining: boolean

  menusPermission: any
  authority: { userPowerList?: IAuthority[] }

  role: any
  isInstAdmin: any
  institutionName: string
  institutionID: number

  authorBtn: string[]

  handleLogin: () => Promise<void>
  handleLogout: () => Promise<void>
  fetchAuthUrl: () => void
  fetchAuthUrlKeycloak: () => Promise<void>
  checkAuthCode: (params: IAuthCodeParams) => Promise<boolean | void>
  checkAuthDingCode: (params: any) => Promise<boolean | void>
  checkLoginStatus: () => Promise<boolean>

  fetchPermissions: (userName?: string) => Promise<any>
  fetchAccountInfo: () => Promise<any>
  fetchAuthority: (params: any) => Promise<void>
}

export default create<ICommonState>((set, get) => ({
  userInfo: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo') || '') : {},
  isLogining: false,

  menusPermission: [],
  authority: [],

  role: null,
  institutionName: '',
  isInstAdmin: false,

  institutionID: 0,

  authorBtn: [],

  // 执行登录
  handleLogin: async () => {
    const { isLogining, fetchAuthUrl, fetchAuthUrlKeycloak } = get()

    if (isLogining) {
      return
    } // 避免多个接口同时请求，会执行多次登录跳转

    set(() => ({ isLogining: true }))
    const authType = localStorage.getItem('AUTH_TYPE')

    if (authType === 'KEY_CLOAK_KEY') {
      await fetchAuthUrlKeycloak()
    } else {
      fetchAuthUrl()
    }

    set(() => ({ isLogining: false }))
  },

  // 执行登出
  handleLogout: async () => {
    const { pathname, search, origin } = window.location
    const callbackUrl = `${pathname}${search}`.includes('/logout') ? '/home' : `${pathname}${search}`
    const authType = localStorage.getItem('AUTH_TYPE')

    localStorage.clear()
    localStorage.setItem('session-url', callbackUrl)

    removeTokenCookie()

    set(() => ({ userInfo: null }))

    if (authType === 'KEY_CLOAK_KEY') {
      localStorage.removeItem('AUTH_TYPE')
      const redirectUrl = `${origin}/login`

      window.location.replace(
        `https://keycloak.changdu.vip/auth/realms/master/protocol/openid-connect/logout?redirect_uri=${redirectUrl}`
      )
    } else {
      window.location.replace(LOGOUT_URL)
    }
  },

  // 跳去统一登录地址
  fetchAuthUrl: () => {
    const { pathname, search, origin } = window.location

    const callbackUrl = `${pathname}${search}`.includes('/logout') ? '/' : `${pathname}${search}`

    localStorage.setItem('session-url', callbackUrl)

    window.location.replace(LOGOUT_URL)
  },

  // 跳去统一登录地址（Keycloak）
  fetchAuthUrlKeycloak: async () => {
    const { pathname, search, origin } = window.location

    const res = await requestGet('/keycloak-signin', {
      params: { url: `${origin}/login` },
      responseType: 'text',
      baseURL: envAuthUrl,
    })
    const callbackUrl =
      `${pathname}${search}`.includes('/logout') || `${pathname}${search}`.includes('/login')
        ? '/'
        : `${pathname}${search}`

    if (res.status) {
      localStorage.setItem('session-url', callbackUrl)
      if (typeof res.data === 'string') {
        // eslint-disable-next-line require-atomic-updates
        window.location.replace(res.data)
      }
    }
  },

  // 检测是否登录
  async checkLoginStatus() {
    const { handleLogin, userInfo } = get()

    if (!userInfo?.userId) {
      handleLogin()

      return false
    }

    const { data, status } = await post<any, any>(
      '/Home/checkuserauth',
      { userId: userInfo?.userId },
      {
        headers: {
          'Content-Type': 'application/json;charset=UTF-8',
          Authorization: `Bearer ${localStorage.getItem('token')}`,
        },
        baseURL: envAuthUrl,
      }
    )

    if (!status || data?.statusCode != 1) {
      handleLogin()

      return false
    }

    return true
  },

  // 检测登录状态
  async checkAuthCode(params) {
    const { data, status } = await post<any, any>('/Home/SsoLogin', params, { baseURL: envAuthUrl })

    if (status) {
      const { statusCode, statusMsg, accessToken, userId, ...rest } = data

      if (statusCode == 1 && statusMsg) {
        // 登录成功
        set(() => ({ userInfo: rest }))
        localStorage.setItem('ACCESS_TOKEN', accessToken)
        localStorage.setItem('token', accessToken)
        localStorage.setItem('userId', userId)
        localStorage.setItem('userInfo', JSON.stringify(rest))

        apm.setConfig({ userId: rest?.account })

        return true
      }
    }

    const { handleLogin } = get()

    handleLogin()

    return false
  },

  async checkAuthDingCode(params) {
    const { data, status } = await post<any, any>('/Home/DingLogin', params, { baseURL: envAuthUrl })

    if (status) {
      const { statusCode, statusMsg, accessToken, userId, ...rest } = data

      if (statusCode == 1 && statusMsg) {
        // 登录成功
        set(() => ({ userInfo: rest }))
        localStorage.setItem('ACCESS_TOKEN', accessToken)
        localStorage.setItem('token', accessToken)
        localStorage.setItem('userId', userId)
        localStorage.setItem('userInfo', JSON.stringify(rest))

        apm.setConfig({ userId: rest?.account })

        return true
      }
    }

    const { handleLogin } = get()

    handleLogin()

    return false
  },

  // 获取权限
  fetchPermissions: async () => {
    const { data, status } = await useRequest<any, any>({
      url: '/UserInfo/GetUserMenu',
      method: 'POST',
      params: {
        appId: '',
        state: 2,
      },
      baseURL: envAuthUrl,
    })

    if (data && status) {
      const list: any = data?.userPowerList?.filter(item => item.appName === '首页')

      if (list?.length !== 0) {
        const newMenu = data?.userPowerList?.filter(item => item.appName !== '首页')

        set(() => ({
          menusPermission: { ...data, userPowerList: newMenu },
          authority: data || {},
        }))
      } else {
        set(() => ({
          menusPermission: data,
          authority: data || {},
        }))
      }
    }
  },

  /**
   * 0 无角色
   * 1 员工
   * 2 机构
   * 3 普通用户
   * 9 超管
   */
  async fetchAccountInfo() {
    const { data } = await requestGet('/api/Home/GetAccountInfo', { baseURL: envAuthUrl })

    const {
      role = null,
      isInstAdmin = false,
      isNeedUpPwd = false,
      institutionName = '',
      institutionID,
    } = data?.data || {}

    set({ role, isInstAdmin, institutionName, institutionID })
  },

  fetchAuthority: async (params: any) => {
    if (params) {
      const { data, status } = await useRequest<any, any>({
        url: '/Permission/GetAuthority',
        method: 'GET',
        params,
        baseURL: envAuthUrl,
      })

      status && set(() => ({ authorBtn: [...data?.top, ...data?.right] || [] }))
    }
  },
}))
