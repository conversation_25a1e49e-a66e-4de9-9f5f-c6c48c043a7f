import useUrlState from '@ahooksjs/use-url-state'
import { useLocalStorageState } from 'ahooks'
import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'

/**
 * 页面使用的 search 参数(object) + url 参数(string) + storage 缓存参数(string) 同步
 * 依赖 ahooks/use-local-storage-state, 文档地址: https://ahooks.gitee.io/zh-CN/hooks/use-local-storage-state
 * 依赖 ahooks/use-url-state, 文档地址: https://ahooks.gitee.io/zh-CN/hooks/use-url-state
 */
export default <T>(): [T, any] => {
  const [storageSearch, setStorageSearch] = useLocalStorageState<any>('url-search', { defaultValue: {} })
  const { pathname, search } = useLocation()

  /**
   * 首次加载
   * 若 url - search 有值，则使用 url - search；
   * 若 url - search 没有值，则使用 storage 中的 search
   */
  const [urlState, setUrlState] = useUrlState<Partial<{ [key in keyof T]: any }>>(
    search ? null : storageSearch[pathname],
    {
      navigateMode: 'replace',
      parseOptions: {
        arrayFormat: 'comma', // 识别逗号转为 array 类型, parse('foo=1,2,3') => {foo: ['1', '2', '3']}
        parseNumbers: false, // 识别数字转为 number 类型, parse('foo=1') => { foo: 1 }
        parseBooleans: true, // 识别布尔值转为 boolean 类型, parse('foo=true') => { foo: true }
      },
      stringifyOptions: {
        arrayFormat: 'comma', // 识别 array 类型转为逗号, stringify({ foo: [1, 2, 3] }) => 'foo=1,2,3'
        skipNull: true, // 识别 null/undefined 删除, stringify({ a: 1, b: undefined, c: null, d: 4 }) => 'a=1&d=4'
        skipEmptyString: true, // 识别 '' 删除, stringify({ a: 1, b: '', c: '', d: 4 }) => 'a=1&d=4'
      },
    }
  )

  /**
   * urlState 改变时，更新 storage 中的 search
   */
  useEffect(() => {
    const result: any = {}
    const params = new URLSearchParams(search)

    for (const [key, value] of params.entries()) {
      result[key] = value
    }
    setStorageSearch({ ...storageSearch, [pathname]: result })
  }, [search])

  return [urlState as any, setUrlState]
}

/**
 * 解析数字参数，支持默认值
 * @param value 原始值
 * @param defaultValue 默认值
 * @returns 数字或默认值
 */
export const parseNumber = (value: any, defaultValue = 0): number => {
  if (value === null || value === void 0 || value === '') {
    return defaultValue
  }
  const num = Number(value)

  return isNaN(num) ? defaultValue : num
}

/**
 * 解析数组参数，确保返回数组格式
 * @param value 原始值（可能是字符串或数组）
 * @returns 数组
 */
export const parseArray = <T = string>(value: any): T[] => {
  if (!value) {
    return []
  }

  if (Array.isArray(value)) {
    return value
  }

  return [value]
}

/**
 * 解析数字数组参数
 * @param value 原始值（可能是字符串、数字或数组）
 * @returns 数字数组
 */
export const parseNumberArray = (value: any): number[] => {
  const arr = parseArray(value)

  return arr
    .map(item => {
      const num = Number(item)

      return isNaN(num) ? 0 : num
    })
    .filter(num => num !== 0) // 过滤掉无效的 0 值
}

/**
 * 解析分页参数
 * @param urlState URL 状态对象
 * @param defaultPagination 默认分页配置
 * @returns 解析后的分页参数
 */
export const parsePagination = (
  urlState: any,
  defaultPagination: { current: number; pageSize: number } = { current: 1, pageSize: 20 }
) => ({
  current: parseNumber(urlState.pageIndex || urlState.current, defaultPagination.current),
  pageSize: parseNumber(urlState.pageSize, defaultPagination.pageSize),
})

/**
 * 初始化表单数据的通用方法
 * @param urlState URL 状态对象
 * @param form Antd 表单实例
 * @param config 配置对象
 * @param config.numberFields 需要转换为数字的字段名数组
 * @param config.arrayFields 需要转换为数组的字段名数组（字符串数组）
 * @param config.numberArrayFields 需要转换为数字数组的字段名数组
 * @param config.excludeFields 需要排除的字段名数组（如分页参数）
 * @returns 处理后的表单数据
 */
export const initFormFromUrlState = (
  urlState: any,
  form: any,
  config: {
    numberFields?: string[]
    arrayFields?: string[]
    numberArrayFields?: string[]
    excludeFields?: string[]
  } = {}
) => {
  const {
    numberFields = [],
    arrayFields = [],
    numberArrayFields = [],
    excludeFields = ['pageIndex', 'pageSize', 'current'],
  } = config

  if (!urlState) {
    return {}
  }

  const formData: any = { ...urlState }

  // 排除不需要的字段
  excludeFields.forEach(field => {
    delete formData[field]
  })

  // 转换数字字段
  numberFields.forEach(field => {
    if (formData[field] !== void 0) {
      formData[field] = parseNumber(formData[field])
    }
  })

  // 转换普通数组字段
  arrayFields.forEach(field => {
    if (formData[field] !== void 0) {
      formData[field] = parseArray(formData[field])
    }
  })

  // 转换数字数组字段
  numberArrayFields.forEach(field => {
    if (formData[field] !== void 0) {
      formData[field] = parseNumberArray(formData[field])
    }
  })

  // 设置表单值
  if (form && form.setFieldsValue) {
    form.setFieldsValue(formData)
  }

  return formData
}

/**
 * 格式化要设置到 URL 的状态数据
 * @param data 要设置的数据
 * @param config 配置对象
 * @param config.arrayFields 数组字段名数组，会确保这些字段有默认的空数组值
 * @returns 格式化后的数据
 */
export const formatUrlState = (
  data: any,
  config: {
    arrayFields?: string[]
  } = {}
) => {
  const { arrayFields = [] } = config
  const result = { ...data }

  // 确保数组字段有默认值
  arrayFields.forEach(field => {
    if (!result[field]) {
      result[field] = []
    }
  })

  return result
}

/**
 * 使用示例：
 *
 * // 1. 基本导入
 * import useSyncParams, {
 *   parseNumber, parseNumberArray, parsePagination, initFormFromUrlState
 * } from '@/hooks/useSyncParams'
 *
 * // 2. 简化的 initParams 实现
 * const initParams = () => {
 *   // 自动处理表单字段格式化
 *   initFormFromUrlState(urlState, form, {
 *     numberFields: ['id', 'type'],           // 需要转为数字的字段
 *     arrayFields: ['cityName', 'tags'],      // 需要转为字符串数组的字段
 *     numberArrayFields: ['status', 'ids'],   // 需要转为数字数组的字段
 *     excludeFields: ['pageIndex', 'pageSize'] // 排除的字段（默认已包含分页字段）
 *   })
 *
 *   // 处理分页参数
 *   const { current, pageSize } = parsePagination(urlState, {
 *     current: 1,
 *     pageSize: 20
 *   })
 *
 *   handleSearch(current, pageSize)
 * }
 *
 * // 3. 单独使用工具函数
 * const status = parseNumberArray(urlState.status)  // ['1', '2'] -> [1, 2]
 * const pageIndex = parseNumber(urlState.pageIndex, 1)  // '2' -> 2, undefined -> 1
 * const cityList = parseArray(urlState.cityName)  // 'beijing' -> ['beijing'], undefined -> []
 *
 * // 4. 设置 URL 状态时格式化
 * setUrlState(formatUrlState({
 *   productionName: values.productionName,
 *   status: values.status,
 *   cityName: values.cityName,
 *   pageSize: result.pageSize,
 *   pageIndex: result.pageIndex,
 * }, {
 *   arrayFields: ['cityName'] // 确保 cityName 有默认空数组值
 * }))
 */
