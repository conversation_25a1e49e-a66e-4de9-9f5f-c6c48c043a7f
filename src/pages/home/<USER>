import { SAYINGS } from '@/consts'
import useIndexStore from '@/store'
import { getRandomItem } from '@/utils'
import { Card, Descriptions } from 'antd'
import React, { useEffect } from 'react'
import Typed from 'typed.js'
import styles from './index.scss'

const App: React.FC = () => {
  const { userInfo } = useIndexStore()
  const el = React.useRef(null)

  useEffect(() => {
    if (el.current) {
      const typed = new Typed(el.current, {
        strings: [getRandomItem(SAYINGS)],
        typeSpeed: 75,
      })

      return () => {
        typed.destroy()
      }
    }
  }, [])

  return (
    <Card className={styles.container}>
      <Descriptions title="个人信息" column={1} colon={false}>
        <Descriptions.Item label="账号">{userInfo?.account}</Descriptions.Item>
        <Descriptions.Item label="昵称">{userInfo?.nickName}</Descriptions.Item>
      </Descriptions>

      <div className={styles.typed}>
        <span ref={el}></span>
      </div>
    </Card>
  )
}

export default App
