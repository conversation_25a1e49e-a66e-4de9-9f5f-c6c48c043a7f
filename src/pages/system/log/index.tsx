import { DATE_FORMAT_YEAR } from '@/consts/date'
import useSyncParams from '@/hooks/useSyncParams'
import { ListHeader } from '@fe/rockrose'
import { Form, Space } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import List from './components/List'
import Search from './components/Search'
import useStore from './store'

const Page: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [urlState, setUrlState] = useSyncParams<ISearchParams>()
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 })
  const { fetchList } = useStore()

  const initParams = () => {
    handleSearch()
  }

  // 提交搜索
  const handleSearch = async (current = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true)
    const params = form.getFieldsValue()

    if (params.Year) {
      params.Year = dayjs(params.Year).format(DATE_FORMAT_YEAR)
    }

    const total = await fetchList({
      ...params,
      pageIndex: current,
      pageSize,
    }).finally(() => setLoading(false))

    setUrlState({
      ...params,
      Year: undefined,
      pageSize,
    })

    setPagination({ ...pagination, total, current, pageSize })
  }

  useEffect(() => {
    if (urlState) {
      const transformedState = {
        ...urlState,
        userType: urlState.userType ? parseInt(urlState.userType, 10) : undefined, // 将字符串转换为数字类型
      }

      form?.setFieldsValue(transformedState)
    }
    initParams()
  }, [])

  return (
    <Space direction="vertical" className="full-h" size="large">
      <Search form={form} loading={loading} onSearch={handleSearch} />

      <div>
        <ListHeader title="日志列表" total={pagination?.total} />
        <List pagination={pagination} loading={loading} onSearch={handleSearch} />
      </div>
    </Space>
  )
}

export default Page
