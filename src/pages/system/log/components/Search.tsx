import ActionView from '@/components/ActionView'
import { DATE_FORMAT_YEAR } from '@/consts/date'
import { useDebounceFn } from 'ahooks'
import { Col, DatePicker, Form, Input, Row } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect } from 'react'

const Search: React.FC<ISearchProps> = ({ form, loading, onSearch }) => {
  const { run: onSearchDebounce } = useDebounceFn(
    () => {
      onSearch && onSearch(1)
    },
    { wait: 500 }
  )

  const disabledDate = (current: any) => {
    const startYear = 2024
    const endYear = dayjs().year()

    return current.year() < startYear || current.year() > endYear
  }

  useEffect(() => {
    form.setFieldValue('Year', dayjs(dayjs(new Date()).format(DATE_FORMAT_YEAR), DATE_FORMAT_YEAR))
  }, [])

  return (
    <Form layout="vertical" form={form} className="search-form">
      <Row gutter={24}>
        <Col span={4}>
          <Form.Item label="表" name="TableName">
            <Input allowClear placeholder="输入表名" onChange={() => onSearchDebounce()} />
          </Form.Item>
        </Col>
        <Col span={4}>
          <Form.Item label="模块" name="ModuleName">
            <Input allowClear placeholder="输入模块名称" onChange={() => onSearchDebounce()} />
          </Form.Item>
        </Col>
        <Col span={4}>
          <Form.Item label="Action" name="actionName">
            <Input allowClear placeholder="例如：Update" onChange={() => onSearchDebounce()} />
          </Form.Item>
        </Col>
        <Col span={4} xl={7} xxl={4}>
          <Form.Item label="年份" name="Year">
            <DatePicker
              className="full-h"
              format={DATE_FORMAT_YEAR}
              picker="year"
              allowClear={false}
              disabledDate={disabledDate}
            />
          </Form.Item>
        </Col>
        <Col span={4}>
          <Form.Item label=" ">
            <ActionView
              loading={loading}
              form={form}
              onSearch={onSearch}
              onReset={() => {
                form.setFieldValue('Year', dayjs(dayjs(new Date()).format(DATE_FORMAT_YEAR), DATE_FORMAT_YEAR))
              }}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  )
}

export default Search
