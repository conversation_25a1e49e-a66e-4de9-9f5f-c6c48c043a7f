import React from 'react'
import { Collapse, List, Descriptions, Flex, Typography, Tooltip, Space } from 'antd'
import { MinusSquareOutlined, PlusSquareOutlined } from '@ant-design/icons'
import dayjs from 'dayjs'
import { DATE_FORMAT_BASE } from '@/consts/date'
import { PAGINATION } from '@/consts'
import { IPagination } from '@/store'
import useStore, { ILogItem } from '../store'
import styles from './List.scss'

interface IListProps {
  loading: boolean
  pagination?: IPagination
  onSearch: (pageIndex: any, pageSize?: number) => void
}

const LogList: React.FC<IListProps> = ({ loading, pagination, onSearch }) => {
  const { list } = useStore()

  return (
    <>
      <List
        dataSource={list}
        className={styles.list}
        loading={loading}
        renderItem={(item: ILogItem) => (
          <List.Item key={item.id}>
            <Collapse
              className="full-h"
              bordered={false}
              ghost
              accordion
              key={item.id}
              expandIcon={({ isActive }) =>
                isActive ? (
                  <MinusSquareOutlined className="icon-normal" />
                ) : (
                  <PlusSquareOutlined className="icon-normal" />
                )
              }
              items={[
                {
                  key: item.id,
                  label: (
                    <Flex align="center" justify="space-between">
                      <Flex flex={1} gap={24}>
                        <Typography.Text>{dayjs(item?.createTime).format(DATE_FORMAT_BASE)}</Typography.Text>
                        <Typography.Text strong>
                          {item.moduleName}/{item.tableName}
                        </Typography.Text>
                        <Tooltip title="操作人">
                          <Typography.Text>{item.creator}</Typography.Text>
                        </Tooltip>
                      </Flex>
                    </Flex>
                  ),
                  children: (
                    <Descriptions
                      column={1}
                      bordered
                      size="small"
                      items={[
                        {
                          key: 1,
                          label: '模块/表',
                          span: 2,
                          labelStyle: { width: 100 },
                          children: (
                            <Space>
                              <Typography.Text strong>{item.moduleName}</Typography.Text>
                              <Tooltip title="表名">
                                <Typography.Text type="secondary"> {item.tableName}</Typography.Text>
                              </Tooltip>
                            </Space>
                          ),
                        },
                        {
                          key: 2,
                          label: '修改前',
                          labelStyle: { width: 100 },
                          span: 2,
                          children: item.beforeText ? (
                            <Typography.Paragraph ellipsis={{ rows: 8 }} copyable className="no-margin">
                              {item.beforeText}
                            </Typography.Paragraph>
                          ) : (
                            '-'
                          ),
                        },
                        {
                          key: 3,
                          label: '修改后',
                          span: 2,
                          labelStyle: { width: 100 },
                          children: item.afterText ? (
                            <Typography.Paragraph copyable ellipsis={{ rows: 8 }} className="no-margin">
                              {item.afterText}
                            </Typography.Paragraph>
                          ) : (
                            '-'
                          ),
                        },
                      ]}
                    />
                  ),
                },
              ]}
            />
          </List.Item>
        )}
        pagination={{
          ...PAGINATION,
          ...pagination,
          onChange: (page: number, pageSize: number) => onSearch(page, pageSize),
        }}
      />
    </>
  )
}

export default LogList
