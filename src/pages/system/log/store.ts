import request, { envAuthUrl } from '@/utils/request'
import { create } from 'zustand'

// 角色类型列表
export const ROLE_TYPE_LIST = [
  { value: 'middleman', label: '机构' },
  { value: 'agent', label: '代理商' },
  { value: 'copyright', label: '版权方' },
  { value: 'admin', label: '管理员' },
]

export interface ISearchParams {
  pageIndex?: number
  pageSize?: number
  userId?: string
  title?: string
  ip?: string
  time?: number[]
}

export interface ILogItem {
  id: number
  moduleId: number
  actionName: string
  moduleName: string
  tableName: string
  creator: string
  createTime: string
  afterText: string
  beforeText: string
}

export interface IState {
  list: ILogItem[]
  fetchList: (params: any) => Promise<number>
}

export default create<IState>(set => ({
  list: [],
  // 获取日志列表
  async fetchList(params: any) {
    const { data } = await request({
      url: '/api/AuditLog/GetList',
      method: 'GET',
      params,
      baseURL: envAuthUrl,
    })

    if (data?.code === 200) {
      set(() => ({ list: data?.data?.list || [] }))

      return data?.data?.totalCount || 0
    }

    return 0
  },
}))
