import { DATE_FORMAT_DAY } from '@/consts/date'
import paramUtils from '@/utils/paramUtils'
import { ListHeader } from '@fe/rockrose'
import { Flex, Form, message } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useRef, useState } from 'react'
import Add from './components/Add'
import AssignRolesModal from './components/AssignRolesModal'
import List from './components/List'
import LogModal from './components/LogModal'
import Search from './components/Search'
import useStore, { IListItem, IOperateType } from './store'

const Page: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [btnLoading, setBtnLoading] = useState(false)
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20, total: 0 })
  const { fetchList, fetchDelete, fetchAdd, fetchUpdate, fetchReset } = useStore()
  const addModalRef = useRef()
  const assignRolesModalRef = useRef()
  const logModalRef = useRef()

  // 提交搜索
  const handleSearch = async (current = pagination.current, pageSize = pagination.pageSize) => {
    const _params = form.getFieldsValue()

    if (_params.dateRange) {
      const [start, end] = _params.dateRange as any

      _params.createTime = dayjs(start).format(DATE_FORMAT_DAY)
      _params.createTimeEnd = dayjs(end).format(DATE_FORMAT_DAY)

      delete _params.dateRange
    }

    setLoading(true)
    const params = paramUtils.filterEmptyProps(_params)

    const total = await fetchList({
      ...params,
      pageIndex: current,
      pageSize,
    }).finally(() => setLoading(false))

    setPagination({ ...pagination, total, current, pageSize })
    setLoading(false)
  }

  // 操作
  const onOperate = (type: IOperateType, data = {}) => {
    switch (type) {
      case 'add':
        addModalRef && (addModalRef.current as any).show({})
        break
      case 'edit':
        addModalRef && (addModalRef.current as any).show(data)
        break
      case 'delete':
        handleDelete(data as any)
        break
      case 'distribution':
        assignRolesModalRef && (assignRolesModalRef.current as any).show(data)
        break
      case 'log':
        logModalRef && (logModalRef.current as any).show(data)
        break

      default:
        break
    }
  }

  // 删除
  const handleDelete = async (data: IListItem) => {
    const res = await fetchDelete({ userId: data.userId })

    res && message.success('删除成功')

    handleSearch()
  }

  // 添加/编辑
  const handleSubmit = async (params: IListItem) => {
    const fun = params.userId ? fetchUpdate : fetchAdd

    setBtnLoading(true)
    const res = await fun(params)

    setBtnLoading(false)

    res && message.success(params.userId ? '编辑成功' : '添加成功')
    addModalRef && (addModalRef.current as any).close()
    handleSearch()
  }

  useEffect(() => {
    handleSearch()
  }, [])

  return (
    <Flex vertical gap={24}>
      <Search form={form} loading={loading} onSearch={handleSearch} />
      <div>
        <ListHeader
          title="用户列表"
          total={pagination?.total}
          unitText="个"

          /*
           * actions={[
           *   {
           *     label: '添加用户',
           *     icon: <PlusOutlined />,
           *     onClick: () => onOperate('add'),
           *   },
           * ]}
           */
        />
        <List pagination={pagination} loading={loading} onSearch={handleSearch} onOperate={onOperate} />
      </div>
      <Add ref={addModalRef} loading={btnLoading} onSubmit={handleSubmit} />
      <AssignRolesModal ref={assignRolesModalRef} />
      <LogModal ref={logModalRef} />
    </Flex>
  )
}

export default Page
