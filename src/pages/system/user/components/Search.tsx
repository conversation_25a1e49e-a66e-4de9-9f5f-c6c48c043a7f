import React from 'react'
import { Form, Input, Row, Col, DatePicker, Select } from 'antd'
import { useDebounceFn } from 'ahooks'
import { USER_TYPE_MAP } from '@/consts/user'
import { object2Arr } from '@/utils/index'
import { DATE_FORMAT_DAY, DATE_PRESETS_BASE } from '@/consts/date'
import ActionView from '@/components/ActionView'

const Search: React.FC<ISearchProps> = ({ form, loading, onSearch }) => {
  const { run: onSearchDebounce } = useDebounceFn(
    () => {
      onSearch && onSearch(1)
    },
    { wait: 500 }
  )

  return (
    <Form layout="vertical" form={form} className="search-form">
      <Row gutter={24}>
        <Col span={4}>
          <Form.Item label="用户" name="nickName">
            <Input allowClear placeholder="输入账号或昵称" onChange={onSearchDebounce} />
          </Form.Item>
        </Col>
        <Col span={4}>
          <Form.Item label="类型" name="userType">
            <Select
              options={object2Arr(USER_TYPE_MAP, true)}
              showSearch={false}
              allowClear
              placeholder="默认全选"
              onChange={onSearchDebounce}
            />
          </Form.Item>
        </Col>
        <Col span={4}>
          <Form.Item label="创建日期" name="dateRange">
            <DatePicker.RangePicker
              format={DATE_FORMAT_DAY}
              presets={DATE_PRESETS_BASE}
              className="full-h"
              onChange={() => onSearch(1)}
            />
          </Form.Item>
        </Col>
        <Col span={4}>
          <Form.Item label=" ">
            <ActionView loading={loading} form={form} onSearch={onSearch} />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  )
}

export default Search
