import React, { useEffect, useState, useImperative<PERSON><PERSON>le } from 'react'
import { Modal, Transfer } from 'antd'
import useStore from '../store'

interface RecordType {
  key: string
  title: string
}

const AssignRoles = React.forwardRef<any, any>((props, ref) => {
  const [mockData, setMockData] = useState<RecordType[]>([])
  const [targetKeys, setTargetKeys] = useState<string[]>([])
  const [data, setData] = useState({})
  const [open, setOpen] = useState(false)
  const { fetchRoleList, fetchUserRoleList, fetchAddUserRole, fetchDeleteUserRole } = useStore()

  useImperativeHandle(ref, () => ({
    show(params: any) {
      setData(params)
      setOpen(true)
    },
    close() {
      hide()
    },
  }))

  const hide = () => {
    setOpen(false)
  }

  const fetchAll = async () => {
    const list = await fetchRoleList({})

    setMockData(
      list.map((item: { roleId: any; roleName: any }) => ({
        ...item,
        key: item.roleId,
        title: item.roleName,
      }))
    )
  }
  const fetchRole = async () => {
    const list = await fetchUserRoleList({ userId: data?.userId, pageIndex: 1, pageSize: 999 })

    setTargetKeys(list.map(({ roleId }: { roleId: string }) => roleId))
  }

  const add = async (roleId: string) => {
    await fetchAddUserRole({ roleId, userId: data?.userId })
    init()
  }
  const remove = async (roleId: string) => {
    await fetchDeleteUserRole({ roleId, userId: data?.userId })
    init()
  }

  const handleChange = (newTargetKeys: string[], direction: string, moveKeys: string[]) => {
    moveKeys.forEach(key => {
      direction === 'right' ? add(key) : remove(key)
    })

    // setTargetKeys(newTargetKeys)
  }

  const init = () => {
    fetchAll()
    fetchRole()
  }

  useEffect(() => {
    if (open) {
      init()
    }
  }, [open])

  return (
    <Modal
      title="分配角色"
      open={open}
      width={700}
      maskClosable={false}
      okText="保存"
      destroyOnClose
      footer={null}
      onCancel={hide}>
      <Transfer
        dataSource={mockData}
        showSearch
        listStyle={{
          width: 300,
          height: '70vh',
        }}
        titles={['角色列表', '已有角色']}
        operations={['添加', '移除']}
        targetKeys={targetKeys}
        render={item => item.title}
        onChange={handleChange}
      />
    </Modal>
  )
})

AssignRoles.displayName = 'AssignRoles'

export default AssignRoles
