import { PAGINATION } from '@/consts'
import { DATE_FORMAT_BASE } from '@/consts/date'
import { IPagination } from '@/store'
import { AimOutlined, EditOutlined, UserSwitchOutlined } from '@ant-design/icons'
import { List, Space, Tag, Tooltip, Typography } from 'antd'
import React from 'react'
// import { USER_COLOR_MAP } from '@/consts/user'
import { ActionCard, Dict } from '@fe/rockrose'
import dayjs from 'dayjs'
import useStore, { IListItem, IOperateType } from '../store'

const { Text } = Typography

interface IListProps {
  loading: boolean
  pagination?: IPagination
  onSearch: (params: any, pageSize: number) => void
  onOperate: (type: IOperateType, data: IListItem) => void
}

const UserList: React.FC<IListProps> = ({ loading, pagination, onSearch, onOperate }) => {
  const { list } = useStore()

  return (
    <List
      dataSource={list}
      loading={loading}
      className="no-border"
      bordered={false}
      grid={{ gutter: 20, column: 5 }}
      renderItem={item => (
        <List.Item key={item.userId}>
          <ActionCard
            className="hover-move"
            defaultHideActions={false}
            title={
              <>
                {item.userType === 2 && !!item?.systemType && (
                  <Tag bordered={false} color="processing" className="no-margin fw-normal">
                    业务用户
                  </Tag>
                )}
                {item.userType === 1 && (
                  <Tag bordered={false} color="orange" className="no-margin fw-normal">
                    超级管理员
                  </Tag>
                )}
                <Tooltip
                  title={
                    <Text className="text-pure" copyable={{ tooltips: false }}>
                      {item?.userId}
                    </Text>
                  }>
                  <Text>{item?.nickName}</Text>
                </Tooltip>
              </>
            }
            subTitle={
              <>
                <Tooltip
                  title={
                    <Text className="text-pure" copyable={{ tooltips: false }}>
                      {item?.account}
                    </Text>
                  }>
                  <Text type="secondary">{item?.account}</Text>
                </Tooltip>
              </>
            }
            actions={[
              {
                label: '日志',
                key: 'log',
                icon: <AimOutlined className="icon" />,
              },
              {
                key: 'distribution',
                label: '分配角色',
                icon: <UserSwitchOutlined className="icon" />,
              },
              {
                label: '编辑',
                key: 'edit',
                icon: <EditOutlined className="icon" />,
                disabled: !!item?.systemType,
              },
            ].filter((item: any) => !item.disabled)}
            onClick={(key, info) => onOperate(key as any, item)}>
            <Space>
              {item.isDeparted && <Tag className="no-margin text-danger">已离职</Tag>}
              {item.lastLoginTime ? (
                <Dict title="最近登录于" value={dayjs(item.lastLoginTime).format(DATE_FORMAT_BASE)} type="secondary" />
              ) : (
                <Text type="secondary">未登录过</Text>
              )}
            </Space>
          </ActionCard>
        </List.Item>
      )}
      pagination={{
        ...PAGINATION,
        ...pagination,
        onChange: (page: number, pageSize: number) => onSearch(page, pageSize),
      }}
    />
  )
}

export default UserList
