import React, { useState, useImperativeHandle, useEffect } from 'react'
import { Drawer, Table } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import useStore, { ILogItem } from '../store'

const LogModal = React.forwardRef<any, any>((props, ref) => {
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const [dataList, setDataList] = useState([])
  const [info, setInfo] = useState({} as any)
  const [pagination, setPagination] = useState({ current: 1, pageIndex: 1, pageSize: 10, total: 0 })
  const { fetchLogList } = useStore()

  useImperativeHandle(ref, () => ({
    show(data: any) {
      setInfo(data)
      setOpen(true)
    },
    close() {
      setOpen(false)
    },
  }))

  useEffect(() => {
    if (open) {
      getList()
    }
  }, [open])

  // 关闭弹框
  const close = () => {
    setOpen(false)
  }

  // 获取列表数据
  const getList = async (current = pagination.pageIndex, pageSize = pagination.pageSize) => {
    setLoading(true)
    const { totalCount = 0, list = [] } = await fetchLogList({
      pageIndex: current,
      pageSize,
      userId: info.userId,
    })

    setLoading(false)

    setPagination({ ...pagination, current, pageSize, total: totalCount })
    setDataList(list)
  }

  const columns: ColumnsType<ILogItem> = [
    {
      title: '角色 / 主菜单',
      dataIndex: 'appName',
      key: 'appName',
      width: 150,
      align: 'center',
    },
    {
      title: '子菜单',
      dataIndex: 'menuName',
      key: 'menuName',
      align: 'center',
      width: 150,
    },
    {
      title: '操作项',
      dataIndex: 'operationStr',
      key: 'operationStr',
      align: 'center',
      width: 100,
    },
    {
      title: '操作人',
      dataIndex: 'creator',
      key: 'creator',
      align: 'center',
      width: 90,
    },
    {
      title: '操作时间',
      dataIndex: 'createTimeStr',
      key: 'createTimeStr',
      align: 'center',
      width: 100,
    },
  ]

  return (
    <Drawer title={`${info.nickName}的操作日志`} open={open} width={1200} onClose={close} destroyOnClose>
      <Table
        rowKey="id"
        loading={loading}
        dataSource={dataList}
        columns={columns}
        pagination={pagination}
        bordered={false}
        onChange={({ current, pageSize }) => getList(current ?? 1, pageSize ?? 10)}
        sticky={{ offsetHeader: -24 }}
      />
    </Drawer>
  )
})

LogModal.displayName = 'LogModal'

export default LogModal
