import React, { useState, useImperativeHandle } from 'react'
import { Modal, Space, Button } from 'antd'
import { ProForm, ProFormSelect, ProFormText } from '@ant-design/pro-components'
import { useThrottleFn } from 'ahooks'
import { required } from '@/consts/rules'
import { USER_TYPE_MAP } from '@/consts/user'
import { object2Arr } from '@/utils/index'

const Add = React.forwardRef<any, any>(({ loading = false, onSubmit }, ref) => {
  const [form] = ProForm.useForm()
  const [open, setOpen] = useState(false)
  const [info, setInfo] = useState({} as any)

  useImperativeHandle(ref, () => ({
    show(data: any) {
      setInfo(data)
      setOpen(true)

      if (data && data.userId) {
        form.setFieldsValue(data)
      }
    },
    close() {
      hide()
    },
  }))

  const hide = () => {
    setOpen(false)
  }

  // 提交
  const { run: handleSubmit } = useThrottleFn(
    async () => {
      const values = await form.validateFields().catch(() => false)

      if (!values) {
        return
      }

      onSubmit({ ...info, ...values })
    },
    { wait: 3000, trailing: false }
  )

  // 重置表单数据
  const handleReset = () => form.resetFields()

  const title = info?.userId ? '编辑用户' : '添加用户'

  return (
    <Modal
      title={title}
      open={open}
      width={700}
      cancelText="取消"
      onCancel={hide}
      maskClosable={false}
      afterClose={handleReset}
      footer={
        <Space size={'small'}>
          <Button type="default" onClick={hide}>
            取消
          </Button>
          <Button type="primary" loading={loading} onClick={handleSubmit}>
            保存
          </Button>
        </Space>
      }>
      <ProForm
        form={form}
        submitter={false}
        layout="horizontal"
        labelCol={{ span: 4, offset: 2 }}
        wrapperCol={{ span: 14 }}
        className="form-padding-20"
        autoComplete="off">
        <ProFormText
          name="account"
          label="账号"
          placeholder="输入登录账号"
          readonly={!!info?.userId}
          rules={[required]}
          fieldProps={{ maxLength: 20 }}
        />
        <ProFormText
          name="nickName"
          label="昵称"
          placeholder="输入用户昵称"
          rules={[required]}
          fieldProps={{ maxLength: 20 }}
        />
        <ProFormSelect
          name="userType"
          label="类型"
          rules={[required]}
          options={object2Arr(USER_TYPE_MAP, true)}
          fieldProps={{ allowClear: true }}
          placeholder="选择用户类型"
        />
        {!info?.userId && (
          <ProFormText.Password
            name="pwd"
            label="密码"
            placeholder="输入密码"
            fieldProps={{ minLength: 6, maxLength: 20 }}
          />
        )}
        {!info?.userId && (
          <ProFormText.Password
            name="conpwd"
            label="确认密码"
            placeholder="再次输入密码"
            fieldProps={{ minLength: 6, maxLength: 20 }}
            rules={[
              {
                validator: async (_, value) => {
                  if (value && value !== form.getFieldValue('pwd')) {
                    return Promise.reject('两次输入的密码必须一致！')
                  }

                  return Promise.resolve()
                },
              },
            ]}
          />
        )}
        <ProFormText
          name="phone"
          label="手机号"
          rules={[
            {
              pattern: new RegExp('^1(3|4|5|6|7|8|9)\\d{9}$'),
              message: '输入正确的手机号码',
            },
          ]}
          placeholder="输入11位手机号"
        />
        <ProFormText name="email" label="邮箱" placeholder="输入邮箱" />
        <ProFormText name="realUserId" label="真实ID" placeholder="输入真实ID" />
      </ProForm>
    </Modal>
  )
})

Add.displayName = 'Add'

export default Add
