import request, { envAuthUrl } from '@/utils/request'
import { create } from 'zustand'

export interface ISearchParams {
  current?: number
  pageIndex?: number
  pageSize?: number
  account?: string
  nickName?: string
  phone?: string
}
export interface ILogItem {
  appName: string
  creator: string
  id: number
  menuName: string
  operationStr: string
  createTimeStr: string
}

export type IOperateType = 'delete' | 'reset' | 'edit' | 'add' | 'distribution' | 'log'

export interface IListItem {
  account: string
  nickName: string
  phone: string
  userId: string
  userType: number
  systemType?: number
  createTime: string
  updateTime: string
  lastLoginTime: string
  email?: string
  realUserId?: string
  roleId?: string
  isDeparted?: boolean
}

export interface IState {
  list: IListItem[]
  fetchList: (params: any) => Promise<number>
  fetchAdd: (params: IListItem) => Promise<boolean>
  fetchUpdate: (params: IListItem) => Promise<boolean>
  fetchDelete: (params: any) => Promise<boolean>
  fetchReset: (params: any) => Promise<boolean>
  fetchResetPwd: (params: any) => Promise<boolean>
  fetchRoleList: (params: any) => Promise<any>
  fetchUserRoleList: (params: any) => Promise<any>
  fetchAddUserRole: (params: any) => Promise<any>
  fetchDeleteUserRole: (params: any) => Promise<any>
  fetchLogList: (params: any) => Promise<any>
}

export default create<IState>(set => ({
  list: [],

  // 获取用户列表
  async fetchList(params: any) {
    const { data } = await request({
      url: '/UserInfo/GetUserList',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    set(() => ({ list: data?.userlist || [] }))

    return data?.totalCount || 0
  },
  // 添加用户
  async fetchAdd(params: any) {
    const { data } = await request({
      url: '/UserInfo/AddUser',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 编辑用户
  async fetchUpdate(params: any) {
    const { data } = await request({
      url: '/UserInfo/UpdateUser?opttype=modify',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 删除用户
  async fetchDelete(params: any) {
    const { data } = await request({
      url: '/UserInfo/UpdateUser?opttype=del',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 修改密码
  async fetchReset(params: any) {
    const { data } = await request({
      url: '/UserInfo/UpdateUser?opttype=reset',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },

  // 修改密码
  async fetchResetPwd(params: any) {
    const { data } = await request({
      url: '/UserInfo/ChangePwd',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },

  // 获取角色列表
  async fetchRoleList(params: any) {
    const { data } = await request({
      url: '/Role/GetAllList',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.list || []
  },
  // 获取用户角色列表
  async fetchUserRoleList(params: any) {
    const { data } = await request({
      url: '/UserRole/GetList',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.list || []
  },
  // 给用户添加角色
  async fetchAddUserRole(params: any) {
    const { data } = await request({
      url: '/UserRole/Add',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 给用户删除角色
  async fetchDeleteUserRole(params: any) {
    const { data } = await request({
      url: '/UserRole/Delete',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 获取用户日志列表
  async fetchLogList(params: any) {
    const { data } = await request({
      url: '/LogOperation/GetList',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data
  },
}))
