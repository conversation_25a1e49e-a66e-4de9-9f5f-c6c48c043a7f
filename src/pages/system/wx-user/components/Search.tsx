import { useDebounceFn } from 'ahooks'
import { Col, DatePicker, Form, Input, Row } from 'antd'
import React from 'react'
import ActionView from '../../../../components/ActionView'
import { DATE_FORMAT_DAY, DATE_PRESETS_BASE } from '../../../../consts/date'

interface IWxUserSearchProps {
  form: any
  loading?: boolean
  onSearch: (pageIndex: number) => void
}

const Search: React.FC<IWxUserSearchProps> = ({ form, loading, onSearch }) => {
  const { run: onSearchDebounce } = useDebounceFn(
    () => {
      onSearch && onSearch(1)
    },
    { wait: 500 }
  )

  return (
    <Form layout="vertical" form={form} className="search-form">
      <Row gutter={24}>
        <Col span={4}>
          <Form.Item label="昵称" name="nickName">
            <Input allowClear placeholder="输入微信昵称" onChange={onSearchDebounce} />
          </Form.Item>
        </Col>

        <Col span={4}>
          <Form.Item label="创建日期" name="dateRange">
            <DatePicker.RangePicker
              format={DATE_FORMAT_DAY}
              presets={DATE_PRESETS_BASE}
              className="full-h"
              onChange={() => onSearch(1)}
            />
          </Form.Item>
        </Col>
        <Col span={4}>
          <Form.Item label=" ">
            <ActionView loading={loading} form={form} onSearch={onSearch} />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  )
}

export default Search
