import { AimOutlined } from '@ant-design/icons'
import { ActionCard, Dict } from '@fe/rockrose'
import { List, Tooltip, Typography } from 'antd'
import dayjs from 'dayjs'
import React from 'react'
import { PAGINATION } from '../../../../consts'
import { DATE_FORMAT_BASE } from '../../../../consts/date'
import useStore, { IOperateType, IWxUserListItem } from '../store'

const { Text } = Typography

interface IPagination {
  current: number
  pageSize: number
  total: number
}

interface IListProps {
  loading: boolean
  pagination?: IPagination
  onSearch: (params: any, pageSize: number) => void
  onOperate: (type: IOperateType, data: IWxUserListItem) => void
}

const WxUserList: React.FC<IListProps> = ({ loading, pagination, onSearch, onOperate }) => {
  const { list } = useStore()

  return (
    <List
      dataSource={list}
      loading={loading}
      className="no-border"
      bordered={false}
      grid={{ gutter: 20, column: 4 }}
      renderItem={item => (
        <List.Item key={item.userId}>
          <ActionCard
            className="hover-move"
            style={{ height: '240px' }}
            defaultHideActions={false}
            title={
              <>
                <Tooltip
                  title={
                    <Text className="text-pure" copyable={{ tooltips: false }}>
                      {item?.userId}
                    </Text>
                  }>
                  <Text>{item?.nickName}</Text>
                </Tooltip>
              </>
            }
            subTitle={
              <>
                <Tooltip
                  title={
                    <Text className="text-pure" copyable={{ tooltips: false }}>
                      {item?.account}
                    </Text>
                  }>
                  <Text type="secondary">{item?.account}</Text>
                </Tooltip>
              </>
            }
            actions={[
              {
                label: '查看详情',
                key: 'view',
                icon: <AimOutlined className="icon" />,
              },
            ]}
            onClick={(key, info) => onOperate(key as any, item)}>
            <>
              {item.lastLoginTime ? (
                <Dict title="最近登录于" value={dayjs(item.lastLoginTime).format(DATE_FORMAT_BASE)} type="secondary" />
              ) : (
                <Text type="secondary">未登录过</Text>
              )}
            </>
          </ActionCard>
        </List.Item>
      )}
      pagination={{
        ...PAGINATION,
        ...pagination,
        onChange: (page: number, pageSize: number) => onSearch(page, pageSize),
      }}
    />
  )
}

export default WxUserList
