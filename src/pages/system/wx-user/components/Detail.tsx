import { Descriptions, Drawer } from 'antd'
import React, { useImperativeHandle, useState } from 'react'
import { IWxUserListItem } from '../store'

const DetailModal = React.forwardRef<any>((props, ref) => {
  const [open, setOpen] = useState(false)
  const [info, setInfo] = useState({} as IWxUserListItem)

  useImperativeHandle(ref, () => ({
    show(data: IWxUserListItem) {
      setInfo(data)
      setOpen(true)
    },
    close() {
      setOpen(false)
    },
  }))

  // 关闭弹框
  const close = () => {
    setOpen(false)
  }

  // 格式化时间
  const formatTime = (timeStr?: string) => {
    if (!timeStr) {
      return '-'
    }

    return new Date(timeStr).toLocaleString()
  }

  return (
    <Drawer title={`用户详情 - ${info.nickName || '未知'}`} open={open} width={600} onClose={close}>
      <Descriptions column={1} bordered>
        <Descriptions.Item label="账号">{info.account || '-'}</Descriptions.Item>
        <Descriptions.Item label="昵称">{info.nickName || '-'}</Descriptions.Item>
        <Descriptions.Item label="手机号">{info.phone || '-'}</Descriptions.Item>
        <Descriptions.Item label="邮箱">{info.email || '-'}</Descriptions.Item>
        <Descriptions.Item label="用户ID">{info.userId || '-'}</Descriptions.Item>
        {/* <Descriptions.Item label="用户类型">{formatUserType(info.userType)}</Descriptions.Item> */}
        {/* <Descriptions.Item label="系统类型">{info.systemType || '-'}</Descriptions.Item> */}
        {/* <Descriptions.Item label="角色ID">{info.roleId || '-'}</Descriptions.Item> */}
        {/* <Descriptions.Item label="钉钉ID">{info.dingId || '-'}</Descriptions.Item> */}
        <Descriptions.Item label="创建时间">{formatTime(info.createTime)}</Descriptions.Item>
        <Descriptions.Item label="更新时间">{formatTime(info.updateTime)}</Descriptions.Item>
        <Descriptions.Item label="最后登录时间">{formatTime(info.lastLoginTime)}</Descriptions.Item>
      </Descriptions>
    </Drawer>
  )
})

DetailModal.displayName = 'DetailModal'

export default DetailModal
