import { create } from 'zustand'
import request, { envAuthUrl } from '../../../utils/request'

export interface ISearchParams {
  current?: number
  pageIndex?: number
  pageSize?: number
  account?: string
  nickName?: string
  phone?: string
  createTime?: string
  createTimeEnd?: string
}

export type IOperateType = 'view'

export interface IWxUserListItem {
  account: string
  nickName: string
  phone?: string
  userId: string
  userType: number
  systemType?: string
  createTime: string
  updateTime: string
  lastLoginTime?: string
  email?: string
  realUserId?: string
  roleId?: string
  delFlag: number
  lastUpPwdTime?: string
  dingId?: string
  pwd?: string
}

export interface IWxUserState {
  list: IWxUserListItem[]
  fetchList: (params: any) => Promise<number>
}

export default create<IWxUserState>(set => ({
  list: [],

  // 获取微信用户列表
  async fetchList(params: any) {
    const { data } = await request({
      url: '/UserInfo/GetWxUserList',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    set(() => ({ list: data?.userlist || [] }))

    return data?.totalCount || 0
  },
}))
