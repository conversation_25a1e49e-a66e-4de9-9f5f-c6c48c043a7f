import { ListHeader } from '@fe/rockrose'
import { Flex, Form } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useRef, useState } from 'react'
import { DATE_FORMAT_DAY } from '../../../consts/date'
import useSyncParams, { initFormFromUrlState, parsePagination } from '../../../hooks/useSyncParams'
import paramUtils from '../../../utils/paramUtils'
import Detail from './components/Detail'
import List from './components/List'
import Search from './components/Search'
import useStore, { IOperateType, ISearchParams } from './store'

const WxUserPage: React.FC = () => {
  const [urlState, setUrlState] = useSyncParams<ISearchParams>()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20, total: 0 })
  const { fetchList } = useStore()
  const detailModalRef = useRef()

  // 初始化URL数据
  const initParams = () => {
    // 使用工具函数简化表单初始化
    initFormFromUrlState(urlState, form, { excludeFields: ['pageIndex', 'pageSize', 'current'] })

    // 使用工具函数处理分页
    const { current, pageSize } = parsePagination(urlState, {
      current: pagination.current,
      pageSize: pagination.pageSize,
    })

    handleSearch(current, pageSize)
  }

  // 提交搜索
  const handleSearch = async (current = pagination.current, pageSize = pagination.pageSize) => {
    const formParams = form.getFieldsValue()

    if (formParams.dateRange) {
      const [start, end] = formParams.dateRange as any

      formParams.createTime = dayjs(start).format(DATE_FORMAT_DAY)
      formParams.createTimeEnd = dayjs(end).format(DATE_FORMAT_DAY)

      delete formParams.dateRange
    }

    setLoading(true)
    const params = paramUtils.filterEmptyProps(formParams)

    const total = await fetchList({
      ...params,
      pageIndex: current,
      pageSize,
    }).finally(() => setLoading(false))

    // 同步URL参数
    setUrlState({
      account: (params as any).account || '',
      nickName: (params as any).nickName || '',
      phone: (params as any).phone || '',
      createTime: (params as any).createTime || '',
      createTimeEnd: (params as any).createTimeEnd || '',
      pageIndex: current,
      pageSize,
    })

    setPagination({ ...pagination, total, current, pageSize })
    setLoading(false)
  }

  // 操作
  const onOperate = (type: IOperateType, data = {}) => {
    switch (type) {
      case 'view':
        detailModalRef && (detailModalRef.current as any).show(data)
        break
      default:
        break
    }
  }

  useEffect(() => {
    initParams()
  }, [])

  return (
    <Flex vertical gap={24}>
      <Search form={form} loading={loading} onSearch={handleSearch} />
      <div>
        <ListHeader title="用户列表" total={pagination?.total} unitText="个" />
        <List pagination={pagination} loading={loading} onSearch={handleSearch} onOperate={onOperate} />
      </div>
      <Detail ref={detailModalRef} />
    </Flex>
  )
}

export default WxUserPage
