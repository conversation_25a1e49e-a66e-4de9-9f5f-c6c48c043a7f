import React, { useEffect, useState } from 'react'
import { Modal } from 'antd'
import { ProForm, ProFormText, ProFormTextArea } from '@ant-design/pro-components'
import { useThrottleFn } from 'ahooks'
import { required } from '@/consts/rules'
import { IListItem } from '../store'

interface IDetailProps {
  title?: string
  show?: boolean
  data?: IListItem
  onSubmit: (data: any) => Promise<any>
  onCancel: () => void
}
const Add: React.FC<IDetailProps> = ({ title = '添加', show = false, data = {}, onSubmit, onCancel }) => {
  const [loading, setLoading] = useState(false)
  const [form] = ProForm.useForm()

  // modal提交
  const { run: handleSubmit } = useThrottleFn(
    async () => {
      const values = await form.validateFields().catch(() => false)

      if (!values) {
        return
      }
      setLoading(true)
      await onSubmit({ ...data, ...values })
      setLoading(false)
    },
    { wait: 3000, trailing: false }
  )

  // 重置表单数据
  const handleReset = () => form.resetFields()

  const init = () => {
    if (data) {
      form.setFieldsValue(data)
    }
  }

  useEffect(() => {
    if (show) {
      init()
    }
  }, [show])

  return (
    <Modal
      title={title}
      open={show}
      width={700}
      okText="保存"
      cancelText="取消"
      maskClosable={false}
      confirmLoading={loading}
      onOk={handleSubmit}
      onCancel={onCancel}
      afterClose={handleReset}>
      <ProForm
        form={form}
        submitter={false}
        layout="horizontal"
        labelCol={{ span: 3, offset: 2 }}
        wrapperCol={{ span: 16 }}
        style={{ padding: 20 }}>
        <ProFormText
          name="roleName"
          label="角色"
          placeholder="输入角色名称"
          rules={[required]}
          fieldProps={{ maxLength: 32, min: 2 }}
        />
        <ProFormTextArea name="roleBemark" label="描述" placeholder="输入角色描述" />
      </ProForm>
    </Modal>
  )
}

export default Add
