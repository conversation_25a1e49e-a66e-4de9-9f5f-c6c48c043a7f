import React, { useState, useEffect, useImperativeHandle } from 'react'
import { Col, Row, Tree, Button, message, Spin, Flex, Space, Drawer, Result } from 'antd'
import { SmileOutlined } from '@ant-design/icons'
import { IMenu } from '../../menu/store'
import useStore from '../store'
import styles from './ChildrenModal.scss'

const ChildrenModal = React.forwardRef<any, any>(({ openParents }, ref) => {
  const { fetchRoleTree, fetchMenuTreeByRoleId, fetchSaveRoleTree } = useStore()
  const [totalMenus, setTotalMenus] = useState<IMenu[]>([]) // 全量菜单树
  const [checkedMenus, setCheckedMenus] = useState<number[]>([]) // 已选中菜单id
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<any>({})
  const [open, setOpen] = useState(false)

  useImperativeHandle(ref, () => ({
    show(params: any) {
      setData(params)
      setOpen(true)
    },
    close() {
      hide()
    },
  }))

  const hide = () => {
    setOpen(false)
  }

  useEffect(() => {
    if (open && data?.roleId) {
      init()
    }
  }, [open, data])

  const init = async () => {
    setLoading(true)
    const menuTree = await fetchRoleTree({ roleId: data.roleId })
    const checkedMenusId = await fetchMenuTreeByRoleId({ roleId: data.roleId })

    setTotalMenus(menuTree)
    setCheckedMenus(checkedMenusId)

    setLoading(false)
  }

  const loopSelectMenu = (node?: IMenu): Array<any> => {
    const { parent, id } = node || {}

    if (!parent) {
      return [id]
    }

    return [id].concat(loopSelectMenu(parent))
  }

  const handleCheck = (action: boolean, node: any, checkedKeys: any[]) => {
    const { id } = node

    if (action) {
      // 选中
      const parentIds = loopSelectMenu(node)

      setCheckedMenus([...new Set([...checkedKeys])])
    } else {
      // 取消选中
      setCheckedMenus(checkedKeys)
    }
  }

  const handle2Bind = () => {
    hide()
    openParents(data)
  }

  // 提交
  const handleSubmit = async () => {
    if (data.roleId) {
      setLoading(true)
      const result = await fetchSaveRoleTree({ roleId: data.roleId, menuId: checkedMenus })

      setLoading(false)
      if (result) {
        message.success('保存成功')
        hide()
      }
    }
  }

  return (
    <>
      <Drawer
        width={900}
        title={`${data.roleName} - 菜单权限`}
        placement="right"
        onClose={hide}
        destroyOnClose
        maskClosable={false}
        open={open}>
        <Spin spinning={loading}>
          {totalMenus.length ? (
            <>
              <Tree
                treeData={totalMenus}
                checkedKeys={checkedMenus}
                defaultExpandAll
                checkable
                checkStrictly
                showLine
                selectable={false}
                multiple
                fieldNames={{ title: 'name', key: 'id' }}
                blockNode
                className={styles.tree}
                onCheck={({ checked: checkedKeys }, { node, checked: action }) =>
                  handleCheck(action, node, checkedKeys)
                }
                titleRender={({ name, id }) => (
                  <Row key={id} wrap={false} onClick={e => e.stopPropagation()}>
                    <Col>{name}</Col>
                  </Row>
                )}
              />
              <Flex justify="end">
                <Space size={'small'}>
                  <Button key="cancel" type="default" onClick={hide}>
                    取消
                  </Button>
                  <Button
                    key="submit"
                    type="primary"
                    loading={loading}
                    onClick={() => {
                      handleSubmit()
                    }}>
                    保存
                  </Button>
                </Space>
              </Flex>
            </>
          ) : (
            <Result
              icon={<SmileOutlined />}
              title="该角色暂未绑定任何菜单哦~"
              extra={
                <Button type="primary" onClick={handle2Bind}>
                  去绑定
                </Button>
              }
            />
          )}
        </Spin>
      </Drawer>
    </>
  )
})

ChildrenModal.displayName = 'ChildrenModal'

export default ChildrenModal
