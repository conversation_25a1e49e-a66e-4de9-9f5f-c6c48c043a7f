import { DATE_FORMAT_BASE } from '@/consts/date'
import { Divider, Popconfirm, Table, Typography } from 'antd'
import type { ColumnsType, TablePaginationConfig } from 'antd/es/table'
import dayjs from 'dayjs'
import React from 'react'
import { IListItem } from '../store'

interface IListProps {
  data: IListItem[]
  loading: boolean
  pagination: TablePaginationConfig
  onChange: (page: number, pageSize: number) => void
  onOperate: (event: string, data?: IListItem) => void
}

const List: React.FC<IListProps> = ({ data, loading, pagination, onChange, onOperate }) => {
  const columns: ColumnsType<IListItem> = [
    {
      title: '角色',
      dataIndex: 'roleName',
      key: 'roleName',
      align: 'center',
      width: 160,
      render: roleName => <Typography.Text strong>{roleName}</Typography.Text>,
    },
    {
      title: '描述',
      dataIndex: 'roleBemark',
      key: 'roleBemark',
      width: 250,
      align: 'center',
      render: text => <div className="text-left">{text}</div>,
    },
    {
      title: '更新时间',
      dataIndex: 'modifyTime',
      key: 'modifyTime',
      width: 120,
      align: 'center',
      render: (text: number) => (text ? dayjs(text).format(DATE_FORMAT_BASE) : '-'),
    },
    {
      key: 'operate',
      title: '操作',
      width: 250,
      align: 'center',
      fixed: 'right',
      render: (text, row: IListItem) => (
        <>
          {row.code === 'admin' ? null : (
            <>
              <Popconfirm
                key="remove"
                title="注意"
                description={
                  <>
                    确定删除 <Typography.Text strong>{row.roleName}</Typography.Text> 吗？
                  </>
                }
                okButtonProps={{ shape: 'round' }}
                cancelButtonProps={{ type: 'link' }}
                onConfirm={() => onOperate('remove', row)}
                okText="确定删除"
                okType="danger"
                cancelText="取消">
                <Typography.Link>删除</Typography.Link>
              </Popconfirm>
              <Divider type="vertical" />
            </>
          )}
          <Typography.Link onClick={() => onOperate('edit', row)}>编辑</Typography.Link>
          <Divider type="vertical" />
          <Typography.Link onClick={() => onOperate('user', row)}>授权用户</Typography.Link>
          <Divider type="vertical" />
          <Typography.Link onClick={() => onOperate('parent', row)}>配置主菜单</Typography.Link>
          <Divider type="vertical" />
          <Typography.Link onClick={() => onOperate('child', row)}>配置子菜单</Typography.Link>
          <Divider type="vertical" />
          <Typography.Link onClick={() => onOperate('button', row)}>配置按钮</Typography.Link>
        </>
      ),
    },
  ]

  return (
    <Table
      rowKey="roleId"
      loading={loading}
      dataSource={data}
      columns={columns}
      pagination={pagination}
      onChange={({ current, pageSize }) => onChange(current ?? 1, pageSize ?? 10)}
    />
  )
}

export default List
