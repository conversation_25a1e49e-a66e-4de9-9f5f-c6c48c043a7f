import React, { useRef, useState, useImperative<PERSON>andle } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Flex, Table, Typography } from 'antd'
import { ProForm } from '@ant-design/pro-components'
import type { ColumnsType } from 'antd/es/table'
import useStore from '../store'
import { IMenu } from '../../menu/store'
import ButtonsModal from './ButtonsModal'

// eslint-disable-next-line react/display-name
const MenuModal = React.forwardRef<any, any>((props, ref) => {
  const [form] = ProForm.useForm()
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const [list, setList] = useState([])
  const [info, setInfo] = useState({} as any)
  const { fetchRoleMenu } = useStore()
  const buttonModalRef = useRef()

  useImperativeHandle(ref, () => ({
    show(data: any) {
      setInfo(data)
      init(data)
      setOpen(true)
    },
    close() {
      hide()
    },
  }))

  // 关闭弹框
  const hide = () => {
    setOpen(false)
    handleReset()
  }

  const init = async (data: any) => {
    setLoading(true)
    const res = await fetchRoleMenu({ roleId: data.roleId })

    setList(res)

    setLoading(false)
  }

  // 操作
  const onOperate = (type: string, data: any) => {
    switch (type) {
      case 'edit':
        buttonModalRef && (buttonModalRef.current as any).show(data)
        break

      default:
        break
    }
  }

  // 重置表单数据
  const handleReset = () => form.resetFields()

  const columns: ColumnsType<IMenu> = [
    {
      title: '菜单',
      dataIndex: 'menuName',
      key: 'menuName',
      width: 300,
      align: 'center',
    },
    {
      key: 'operate',
      title: '操作',
      width: 200,
      align: 'center',
      render: (text, row: IMenu) =>
        row?._isTop || row?.children?.length ? (
          ''
        ) : (
          <Typography.Link onClick={() => onOperate('edit', { ...row, roleId: info.roleId })}>绑定按钮</Typography.Link>
        ),
    },
  ]

  return (
    <>
      <Drawer
        title={`${info?.roleName} - 按钮权限`}
        open={open}
        width={700}
        onClose={hide}
        destroyOnClose
        maskClosable={false}
        loading={loading}
        footer={
          <Flex justify="end">
            <Button type="primary" ghost onClick={hide}>
              关闭
            </Button>
          </Flex>
        }>
        <Table
          rowKey="menuId"
          loading={loading}
          dataSource={list}
          columns={columns}
          sticky={{ offsetHeader: -24 }}
          pagination={false}
          expandable={{ defaultExpandAllRows: false }}
        />
      </Drawer>
      <ButtonsModal ref={buttonModalRef} />
    </>
  )
})

export default MenuModal
