import React, { useEffect, useState, useImperative<PERSON>andle } from 'react'
import { Modal, Transfer } from 'antd'
import useStore from '../store'

interface RecordType {
  key: string
  title: string
}

const UserAuthModal = React.forwardRef<any, any>((props, ref) => {
  const [mockData, setMockData] = useState<RecordType[]>([])
  const [targetKeys, setTargetKeys] = useState<string[]>([])
  const [data, setData] = useState({})
  const [open, setOpen] = useState(false)
  const { fetchUserList, fetchUserAuthList, fetchRoleBind, fetchRoleUnbind } = useStore()

  useImperativeHandle(ref, () => ({
    show(params: any) {
      setData(params)
      setOpen(true)
    },
    close() {
      hide()
    },
  }))

  const hide = () => {
    setOpen(false)
  }

  const fetchAll = async () => {
    const list = await fetchUserList({})

    setMockData(
      list.map((item: { userId: any; nickName: any }) => ({
        ...item,
        key: item.userId,
        title: item.nickName,
      }))
    )
  }
  const fetchRole = async () => {
    const list = await fetchUserAuthList({ roleId: data?.roleId })

    setTargetKeys(list.map(({ userId }: { userId: string }) => userId))
  }

  const add = async (userId: string[]) => {
    await fetchRoleBind({ roleId: data?.roleId, userId })
    init()
  }
  const remove = async (userId: string[]) => {
    await fetchRoleUnbind({ roleId: data?.roleId, userId })
    init()
  }

  const handleChange = (newTargetKeys: string[], direction: string, moveKeys: string[]) => {
    direction === 'right' ? add(moveKeys) : remove(moveKeys)

    // setTargetKeys(newTargetKeys)
  }

  const init = () => {
    fetchAll()
    fetchRole()
  }

  useEffect(() => {
    if (open) {
      init()
    }
  }, [open])

  return (
    <Modal
      title={`${data?.roleName} - 授权用户`}
      open={open}
      width={700}
      okText="保存"
      destroyOnClose
      maskClosable={false}
      footer={null}
      onCancel={hide}>
      <Transfer
        dataSource={mockData}
        showSearch
        listStyle={{
          width: 300,
          height: '580px',
        }}
        titles={['用户列表', '已授权用户']}
        operations={['添加', '移除']}
        targetKeys={targetKeys}
        onChange={handleChange}
        render={item => item.title}
      />
    </Modal>
  )
})

UserAuthModal.displayName = 'UserAuthModal'

export default UserAuthModal
