import React, { useEffect, useState, useImperativeHandle } from 'react'
import { Modal, Transfer } from 'antd'
import useStore from '../store'

interface RecordType {
  key: string
  title: string
}

const ParentsModal = React.forwardRef<any, any>((props, ref) => {
  const [mockData, setMockData] = useState<RecordType[]>([])
  const [targetKeys, setTargetKeys] = useState<string[]>([])
  const [data, setData] = useState({} as any)
  const [open, setOpen] = useState(false)
  const { fetchParents, fetchOwnerParents, fetchParentsAdd, fetchParentsDelete } = useStore()

  useImperativeHandle(ref, () => ({
    show(params: any) {
      setData(params)
      setOpen(true)
    },
    close() {
      hide()
    },
  }))

  const hide = () => {
    setOpen(false)
  }

  const fetchAll = async () => {
    const list = await fetchParents()

    setMockData(
      list.map((item: { appId: any; appName: any }) => ({
        ...item,
        key: String(item.appId),
        title: item.appName,
      }))
    )
  }
  const fetchOwn = async () => {
    const list = await fetchOwnerParents({
      pageIndex: 1,
      pageSize: 999,
      roleId: data.roleId,
    })
    const initList = list.filter((item: any) => mockData.some((mockItem: any) => mockItem.appId === item.appId))

    setTargetKeys(initList.map(({ appId }: { appId: number }) => appId))
  }

  const add = async (appId: string[]) => {
    const { roleId } = data

    await fetchParentsAdd({ roleId, appId })
    init()
  }
  const remove = async (appId: string[]) => {
    const { roleId } = data

    await fetchParentsDelete({ roleId, appId })
    init()
  }

  const handleChange = (newTargetKeys: string[], direction: string, moveKeys: string[]) => {
    direction === 'right' ? add(moveKeys) : remove(moveKeys)

    // setTargetKeys(newTargetKeys)
  }

  const init = () => {
    fetchAll()
  }

  useEffect(() => {
    if (open) {
      init()
    }
  }, [open])
  useEffect(() => {
    if (mockData.length === 0) {
      return
    }
    fetchOwn()
  }, [mockData])

  return (
    <Modal
      title={`${data?.roleName} - 主菜单`}
      open={open}
      width={660}
      okText="保存"
      destroyOnClose
      footer={null}
      maskClosable={false}
      onCancel={hide}>
      <Transfer
        style={{ width: '100%' }}
        dataSource={mockData}
        showSearch
        listStyle={{
          width: 250,
          height: '580px',
        }}
        titles={['菜单列表', '已绑定菜单']}
        operations={['添加', '移除']}
        targetKeys={targetKeys}
        onChange={handleChange}
        render={item => item.title}
      />
    </Modal>
  )
})

ParentsModal.displayName = 'ParentsModal'

export default ParentsModal
