import { Button, Col, Form, Input, Row, Select } from 'antd'
import React from 'react'

const Search: React.FC<ISearchProps> = ({ form, loading, onSearch }) => (
  <Form layout="vertical" form={form}>
    <Row gutter={24}>
      <Col span={4}>
        <Form.Item label="角色" name="name">
          <Input allowClear placeholder="输入角色名" />
        </Form.Item>
      </Col>
      <Col span={4}>
        <Form.Item label="编码" name="code">
          <Input allowClear placeholder="例如：admin" />
        </Form.Item>
      </Col>
      <Col span={4}>
        <Form.Item label="状态" name="status">
          <Select
            options={[
              { label: '启用', value: 1 },
              { label: '禁用', value: 0 },
            ]}
            fieldNames={{ label: 'label', value: 'value' }}
            allowClear
            showSearch
            optionFilterProp="name"
            placeholder="默认全选"
            onChange={() => onSearch(1)}
          />
        </Form.Item>
      </Col>
      <Col span={4}>
        <Form.Item label=" ">
          <Button type="primary" loading={loading} onClick={() => onSearch(1)}>
            查询
          </Button>
        </Form.Item>
      </Col>
    </Row>
  </Form>
)

export default Search
