import paramUtils from '@/utils/paramUtils'
import request, { envAuthUrl } from '@/utils/request'
import { create } from 'zustand'
import { IMenu } from '../menu/store'

const filterMenuName = menuList => {
  menuList.menuName = `${menuList.menuName}（菜单）`
  if (menuList.menuList?.length > 0) {
    menuList.menuList.map(i => {
      i = filterMenuName(i)
    })
  }

  return menuList
}

const forReturnData = list => {
  const data = []

  if (list === undefined) {
    return
  }
  for (let i = 0; i < list.length; i++) {
    if (list[i].menuList === undefined) {
      return
    }

    const { menuId, appName, menuName, appId, id } = list[i]

    data.push({
      id: menuId ? menuId : appId || id,
      key: menuId ? menuId : appId || id,
      label: appName ? appName : menuName,
      title: appName ? appName : menuName,
      isMenu: menuName !== undefined,
      controllerName: list[i].controllerName || '',
      children: forReturnData(list[i].menuList),
    })
  }

  return data
}

// 构建按钮
const forReturnMenuBtn = (list, menuBtnConfig) => {
  list.map(i => {
    const childrenList = []

    if (i.children?.length > 0) {
      i.children = forReturnMenuBtn(i.children, menuBtnConfig)
    } else {
      menuBtnConfig.map(btnItem => {
        childrenList.push({
          children: [],
          id: `${i.id}-${btnItem.id}`,
          key: `${i.id}-${btnItem.id}`,
          btnId: btnItem.id,
          menuId: i.id,
          controllerName: i.controllerName || '',
          isMenu: false,
          isBtn: true,
          title: btnItem.btnName,
          label: btnItem.btnName,
        })

        i.children = childrenList
      })
    }
  })

  return list
}

export interface ISearchParams {
  current?: number
  pageSize?: number
  code?: string
  name?: string
  status?: number
}
export interface IUser {
  userId?: string
  nickName: string
}
export interface IPageState {
  list: []
  current: number
  pageSize: number
  total: number
  loading: boolean
  applets: Array<any>
  buttonList: []
  menuTreeList: []

  fetchList: (params: ISearchParams) => void
  fetchUserList: (params: any) => void
  fetchUserAuthList: (params: any) => void
  fetchAdd: (params: ICreate) => Promise<any>
  fetchUpdate: (params: IEdit) => Promise<any>
  fetchDelete: (params: any) => Promise<any>
  fetchRoleBind: (params: any) => Promise<any>
  fetchRoleUnbind: (params: any) => Promise<any>
  fetchBtnAdd: (params: any) => Promise<any>
  fetchBtnDelete: (params: any) => Promise<any>
  fetchRoleMenu: (params: any) => Promise<any>
  fetchMenuBtn: () => Promise<any>
  fetchOwnerBtn: (params: any) => Promise<any>
  fetchParents: () => Promise<any>
  fetchOwnerParents: (params: any) => Promise<any>
  fetchParentsAdd: (params: any) => Promise<any>
  fetchParentsDelete: (params: any) => Promise<any>
  fetchRoleTree: (params: any) => Promise<any>
  fetchSaveRoleTree: (params: any) => Promise<any>
  fetchMenuTreeByRoleId: (params: any) => Promise<any>
}

export interface ICreate {
  code: string
  name: string
  description: string
  status: number
  [key: string]: any
}
export interface IEdit {
  id: number
  code: string
  name: string
  description: string
  status: number
  [key: string]: any
}
export interface IListItem {
  id: number
  code: string
  name: string
  description: string
  status: number
  [key: string]: any
}

export default create<IPageState>(set => ({
  list: [],
  current: 1,
  pageSize: 10,
  total: 0,
  loading: false,
  applets: [],
  appletMap: {},
  buttonList: [],
  menuTreeList: [],

  async fetchList(params = {}) {
    set(() => ({ loading: true }))
    const { data } = await request({
      url: '/Role/GetList',
      method: 'POST',
      data: paramUtils.filterEmptyProps(params),
      baseURL: envAuthUrl,
    })

    if (data?.list && data?.list.length) {
      set(() => ({
        loading: false,
        list: data.list,
        total: data?.totalCount || 0,
        current: params?.pageIndex || 1,
        pageSize: params?.pageSize || 10,
      }))
    } else {
      set(() => ({ list: [], total: 0, loading: false }))
    }
  },
  // 获取所有用户列表
  async fetchUserList(params) {
    const { data } = await request({
      url: '/UserInfo/GetUserAuthorityList',
      method: 'POST',
      data: { ...params, pageIndex: 1, pageSize: 9999999 },
      baseURL: envAuthUrl,
    })

    if (data?.userlist && data?.userlist?.length) {
      const { userlist } = data

      return userlist
    }

    return []
  },
  // 获取已授权用户列表
  async fetchUserAuthList(params) {
    const { data } = await request({
      url: '/UserInfo/GetUserAuthorityList',
      method: 'POST',
      data: { ...params, pageIndex: 1, pageSize: 9999999 },
      baseURL: envAuthUrl,
    })

    if (data?.userlist && data?.userlist?.length) {
      return data.userlist
    }

    return []
  },
  // 授权用户
  async fetchRoleBind(params) {
    const { data } = await request({
      url: '/UserRole/BatchAdd',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 解绑用户
  async fetchRoleUnbind(params) {
    const { data } = await request({
      url: '/UserRole/BatchDelete',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 添加
  async fetchAdd(params) {
    const { data } = await request({
      url: '/Role/Add',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 修改
  async fetchUpdate(params) {
    const { data } = await request({
      url: '/Role/Update?opttype=modify',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 删除
  async fetchDelete(params) {
    const { data } = await request({
      url: 'Role/Update?opttype=del',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 获取某角色下的菜单列表
  async fetchRoleMenu(params) {
    const { data } = await request({
      url: '/RoleApp/GetRoleAppMenuList',
      method: 'POST',
      data: { ...params, pageIndex: 1, pageSize: 999 },
      baseURL: envAuthUrl,
    })

    if (data?.list && data?.list.length) {
      let list = data.list

      list = list.map((item: any) => ({
        ...item,
        _isTop: true,
        menuName: item.appName,
        menuId: item.appId,
      }))

      const loop = (list: IMenu[], parent?: IMenu) =>
        list.reduce((total: string[], item) => {
          item.parent = parent
          if (item?.menuList?.length) {
            item.children = item.menuList
            loop(item.children, item)
          }
          delete item.menuList

          return total
        }, [])

      loop(list)

      console.log(list)

      return list
    }

    return []
  },
  // 获取按钮列表
  async fetchMenuBtn() {
    const { data } = await request({
      url: '/MenuBtn/GetList',
      method: 'POST',
      data: {},
      baseURL: envAuthUrl,
    })

    if (data?.list && data?.list.length) {
      return data.list
    }

    return []
  },
  // 获取已有按钮列表
  async fetchOwnerBtn(params) {
    const { data } = await request({
      url: '/RoleMenuBtn/GetList',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    if (data?.list && data?.list.length) {
      return data.list
    }

    return []
  },
  // 绑定按钮
  async fetchBtnAdd(params) {
    const { data } = await request({
      url: '/RoleMenuBtn/BatchAddBtn',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 解绑按钮
  async fetchBtnDelete(params) {
    const { data } = await request({
      url: '/RoleMenuBtn/BatchDelBtn',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 获取主菜单列表
  async fetchParents() {
    const { data } = await request({
      url: '/App/GetAllList',
      method: 'POST',
      data: {
        pageIndex: 1,
        pageSize: 999,
      },
      baseURL: envAuthUrl,
    })

    if (data?.list && data?.list.length) {
      return data.list
    }

    return []
  },
  // 获取已绑定主菜单列表
  async fetchOwnerParents(params) {
    const { data } = await request({
      url: '/RoleApp/GetAllList',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    if (data?.list && data?.list.length) {
      return data.list
    }

    return []
  },
  // 绑定主菜单
  async fetchParentsAdd(params) {
    const { data } = await request({
      url: '/RoleApp/BatchAdd',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 解绑主菜单
  async fetchParentsDelete(params) {
    const { data } = await request({
      url: '/RoleApp/BatchDelete',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 获取某角色下已关联的菜单列表
  async fetchRoleTree(params) {
    const { data } = await request({
      url: '/RoleApp/GetRoleAppMenuList',
      method: 'POST',
      data: { ...params, pageIndex: 1, pageSize: 999 },
      baseURL: envAuthUrl,
    })

    let list: any

    list = data?.list || []

    const loop = (list: IMenu[], parent?: IMenu) =>
      list.reduce((total: string[], item) => {
        item.parent = parent
        item.children = item.menuList
        item.name = item.appName || item.menuName
        item.id = item.menuId || item.appId
        item.checkable = !item?.menuList?.length

        if (item.children?.length) {
          loop(item.children, item)
        }

        return total
      }, [])

    loop(list)

    return list
  },
  // 保存角色权限
  async fetchSaveRoleTree(params) {
    const { data } = await request({
      url: '/RoleRight/BatchSave',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 获取角色已授权的菜单
  async fetchMenuTreeByRoleId(params) {
    const { data } = await request({
      url: '/Menu/GetMenuTreeByRoleId',
      method: 'POST',
      data: { ...params, pageIndex: 1, pageSize: 999 },
      baseURL: envAuthUrl,
    })

    let list: any

    list = data?.list || []
    let arr: any

    arr = []

    const loop = (list: IMenu[], parent?: IMenu) =>
      list.reduce((total: string[], item) => {
        if (item.menuId) {
          arr.push(item.menuId)
        }
        if (item.menuList?.length) {
          loop(item.menuList, item)
        }

        return total
      }, [])

    loop(list)

    return arr
  },
}))
