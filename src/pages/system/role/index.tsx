import { DEFAULT_PAGINATION } from '@/consts/index'
import useSyncParams from '@/hooks/useSyncParams'
import paramUtils from '@/utils/paramUtils'
import { PlusOutlined } from '@ant-design/icons'
import { ListHeader } from '@fe/rockrose'
import { Flex, Form, message } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import ChildrenModal from './components/ChildrenModal'
import Detail from './components/Detail'
import List from './components/List'
import MenuModal from './components/MenuModal'
import ParentsModal from './components/ParentsModal'
import UserAuthModal from './components/UserAuthModal'
import useStore, { ICreate, IEdit, IListItem, ISearchParams } from './store'

const Role: React.FC = () => {
  const [form] = Form.useForm()
  const [urlState, setUrlState] = useSyncParams<ISearchParams>()
  const {
    list,
    pageSize: storePageSize,
    current: storeCurrent,
    total,
    loading,
    fetchList,
    fetchUpdate,
    fetchAdd,
    fetchDelete,
  } = useStore()

  const menuModalRef = useRef()
  const userAuthModalRef = useRef()
  const parentsModalRef = useRef()
  const childrenModalRef = useRef()

  const [modal, setModal] = useState<{ title?: string; show: boolean; data: IListItem | undefined; type?: string }>({
    show: false,
    data: {} as any,
  })

  const initParams = () => {
    const { current, pageSize } = urlState
    const formData: any = { ...urlState }

    delete formData.current
    delete formData.pageSize

    paramUtils.parseNumbers(['id', 'code', 'status'], formData)
    form.setFieldsValue({ ...formData })

    if (!list?.length) {
      handleSearch(current ? +current : DEFAULT_PAGINATION.current, pageSize ? +pageSize : DEFAULT_PAGINATION.pageSize)
    }
  }
  // 提交搜索
  const handleSearch = async (current = DEFAULT_PAGINATION.current, pageSize = DEFAULT_PAGINATION.pageSize) => {
    const params = form.getFieldsValue()

    await fetchList({
      ...params,
      pageIndex: current,
      pageSize,
    })

    setUrlState({
      ...params,
      pageIndex: current,
      pageSize,
    })
  }

  // 删除
  const handleRemove = async (values: IListItem) => {
    const { roleId } = values

    await fetchDelete({ roleId })
      .then(() => {
        message.success('删除成功')
        handleSearch()
      })
      .catch(() => {
        message.success('删除失败')
      })
  }
  const handleOperate = (event: string, data?: IListItem) => {
    switch (event) {
      case 'create':
        setModal({ title: '添加角色', show: true, data })
        break
      case 'edit':
        setModal({ title: '编辑角色', show: true, data })
        break
      case 'copy':
        setModal({ title: '复制角色', show: true, data })
        break
      case 'user':
        userAuthModalRef && (userAuthModalRef.current as any).show(data)
        break
      case 'button':
        menuModalRef && (menuModalRef.current as any).show(data)
        break
      case 'remove':
        data && handleRemove(data)
        break
      case 'parent':
        openParents(data)
        break
      case 'child':
        childrenModalRef && (childrenModalRef.current as any).show(data)
        break
      default:
        break
    }
  }

  const openParents = (data: any) => {
    parentsModalRef && (parentsModalRef.current as any).show(data)
  }

  const handleCancel = () => {
    setModal({ ...modal, show: false })
  }

  // 编辑弹框提交
  const handleSubmit = async (values: ICreate | IEdit) => {
    const methodApi = modal.title === '编辑角色' ? fetchUpdate : fetchAdd

    await methodApi(values as any)
      .then(() => {
        message.success('操作成功')
        handleCancel()
        handleSearch()
      })
      .catch(() => {
        message.error('操作失败')
      })
  }

  useEffect(() => {
    initParams()
  }, [])

  return (
    <Flex vertical>
      <div>
        <ListHeader
          title="角色列表"
          total={total}
          actions={[
            {
              label: '添加角色',
              icon: <PlusOutlined />,
              onClick: () => handleOperate('create'),
            },
          ]}
        />
        <List
          data={list}
          loading={loading}
          pagination={{ current: storeCurrent, pageSize: storePageSize, total }}
          onChange={handleSearch}
          onOperate={handleOperate}
        />
      </div>
      {/* <Config open={stepModal.show} item={stepModal.data} onClose={handleCancel} /> */}
      <Detail
        title={modal.title}
        show={modal.type !== 'setting' && modal.show}
        data={modal.data}
        onCancel={handleCancel}
        onSubmit={handleSubmit}
      />

      <UserAuthModal ref={userAuthModalRef} />
      <MenuModal ref={menuModalRef} />
      <ParentsModal ref={parentsModalRef} />
      <ChildrenModal ref={childrenModalRef} openParents={openParents} />
    </Flex>
  )
}

export default Role
