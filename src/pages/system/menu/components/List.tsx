import React from 'react'
import { Table, Popconfirm, Typography, Divider, Tooltip, Space } from 'antd'
import dayjs from 'dayjs'
import type { ColumnsType } from 'antd/es/table'
import { DATE_FORMAT_BASE } from '@/consts/date'
import { IMenu } from '../store'

interface IListProps {
  data: IMenu[]
  loading: boolean
  onOperate: (event: string, data?: IMenu) => void
}

const List: React.FC<IListProps> = ({ data, loading, onOperate }) => {
  const columns: ColumnsType<IMenu> = [
    {
      title: '菜单',
      dataIndex: 'menuName',
      key: 'menuName',
      width: 200,
      align: 'center',
    },
    {
      title: '控制器',
      dataIndex: 'controllerName',
      key: 'controllerName',
      align: 'center',
      width: 150,
      ellipsis: { showTitle: false },
      render: (text: string) => (
        <Tooltip placement="topLeft" title={text}>
          {text || ''}
        </Tooltip>
      ),
    },
    {
      title: '类型',
      dataIndex: '_menuList',
      key: '_menuList',
      width: 150,
      align: 'center',
      render: (text, record) => (record?.children?.length ? '分组' : '菜单'),
    },
    {
      title: '路径',
      dataIndex: 'url',
      key: 'url',
      width: 200,
      align: 'center',
      render: (text: string) => text || '',
    },
    {
      title: '排序',
      dataIndex: 'sort',
      key: 'sort',
      align: 'center',
      width: 90,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      width: 150,
      align: 'center',
      render: (text: string) => (text ? dayjs(text).format(DATE_FORMAT_BASE) : ''),
    },

    {
      key: 'operate',
      title: '操作',
      width: 230,
      align: 'center',
      render: (row: IMenu) => (
        <Space size={0} split={<Divider type="vertical" />}>
          <Popconfirm
            key="remove"
            title="注意"
            description={
              <>
                确定删除 <Typography.Text strong>{row.menuName}</Typography.Text> 吗？
              </>
            }
            okButtonProps={{ shape: 'round' }}
            cancelButtonProps={{ type: 'link' }}
            onConfirm={() => onOperate('remove', row)}
            okText="确定删除"
            okType="danger"
            cancelText="取消">
            <Typography.Link>删除</Typography.Link>
          </Popconfirm>
          <Typography.Link onClick={() => onOperate('edit', row)}>修改</Typography.Link>
          <Typography.Link onClick={() => onOperate('add', { ...row, _isTop: false })}>添加子菜单</Typography.Link>
          {!row.children && <Typography.Link onClick={() => onOperate('bindButton', row)}>绑定按钮</Typography.Link>}
        </Space>
      ),
    },
  ]

  return (
    <Table
      rowKey="menuId"
      loading={loading}
      dataSource={data}
      columns={columns}
      sticky={{ offsetHeader: -24 }}
      pagination={false}
      expandable={{ defaultExpandAllRows: false }}
    />
  )
}

export default List
