import React, { useEffect, useState } from 'react'
import { Modal } from 'antd'
import { ProForm, ProFormDigit, ProFormText } from '@ant-design/pro-components'
import { useThrottleFn } from 'ahooks'
import { required } from '@/consts/rules'
import useStore, { IMenu } from '../store'

interface IDetailProps {
  title?: string
  show?: boolean
  type?: string
  data?: IMenu
  onSubmit: (data: any) => Promise<any>
  onCancel: () => void
}
const Add: React.FC<IDetailProps> = ({ title = '添加', show = false, type, data, onSubmit, onCancel }) => {
  const [loading, setLoading] = useState(false)
  const [info, setInfo] = useState({})
  const [form] = ProForm.useForm()
  const { fetchMenuInfo, fetchChildInfo } = useStore()

  // modal提交
  const { run: handleSubmit } = useThrottleFn(
    async () => {
      const values = await form.validateFields().catch(() => false)

      if (!values) {
        return
      }
      setLoading(true)

      const params = { ...values }
      // TODO: 待优化

      if (data?._isTop) {
        params._isTop = data?._isTop
        params.appName = values.menuName
        // 编辑主菜单
        if (type === 'edit') {
          params.appId = data?.menuId
          delete params.menuName
        }
      }

      // 添加子菜单
      if (type === 'add') {
        params.appId = data?.appId || data?.menuId
        params.layer = data?.layer ? data?.layer + 1 : 1
        params.pMenuId = params.layer > 1 ? data?.menuId : undefined
      }
      if (type === 'edit') {
        params.menuId = data?.menuId
      }

      await onSubmit(params)
      setLoading(false)
    },
    { wait: 3000, trailing: false }
  )

  // 重置表单数据
  const handleReset = () => form.resetFields()

  const init = async () => {
    const _res = data?._isTop
      ? await fetchMenuInfo({ appId: data.menuId })
      : await fetchChildInfo({ menuId: data?.menuId })

    let res = _res

    if (data?._isTop) {
      res = {
        ..._res,
        menuName: _res.appName,
        menuId: _res.appId,
      }
    }

    setInfo(res)
    form.setFieldsValue(res)
  }

  useEffect(() => {
    if (show && type === 'edit') {
      init()
    }

    console.log('data: ', data)
  }, [show])

  return (
    <Modal
      title={title}
      open={show}
      width={700}
      okText="保存"
      cancelText="取消"
      confirmLoading={loading}
      maskClosable={false}
      onOk={handleSubmit}
      onCancel={onCancel}
      afterClose={handleReset}>
      <ProForm
        form={form}
        submitter={false}
        layout="horizontal"
        labelCol={{ span: 4, offset: 1 }}
        wrapperCol={{ span: 16 }}
        style={{ padding: 20 }}>
        <ProFormText
          name="menuName"
          label="菜单"
          placeholder="输入菜单名"
          rules={[required]}
          fieldProps={{ maxLength: 32, min: 2 }}
        />
        {!data?._isTop && (
          <>
            <ProFormText
              name="url"
              label="路径"
              placeholder="例如：/system/menu"
              fieldProps={{ maxLength: 250, min: 2 }}
            />
            <ProFormText name="controllerName" label="控制器" placeholder="输入控制器名" />
          </>
        )}
        <ProFormDigit rules={[required]} name="sort" label="排序" placeholder="整数值越小，展示越靠前" />
      </ProForm>
    </Modal>
  )
}

export default Add
