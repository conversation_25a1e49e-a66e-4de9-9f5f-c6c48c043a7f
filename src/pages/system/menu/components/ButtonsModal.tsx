import React, { useEffect, useState, useImperative<PERSON>andle } from 'react'
import { Modal, Transfer } from 'antd'
import useStore from '../../role/store'
import useCurrentStore from '../store'

interface RecordType {
  key: string
  title: string
}

const ButtonsModal = React.forwardRef<any, any>((props, ref) => {
  const [mockData, setMockData] = useState<RecordType[]>([])
  const [targetKeys, setTargetKeys] = useState<string[]>([])
  const [data, setData] = useState({} as any)
  const [open, setOpen] = useState(false)
  const { fetchMenuBtn } = useStore()
  const { fetchButtonList, addButton, removeButton } = useCurrentStore()

  useImperativeHandle(ref, () => ({
    show(params: any) {
      setData(params)
      setOpen(true)
    },
    close() {
      hide()
    },
  }))

  const hide = () => {
    setOpen(false)
  }

  const fetchAll = async () => {
    const list = await fetchMenuBtn()

    setMockData(
      list.map((item: { id: any; btnName: any }) => ({
        ...item,
        key: item.id,
        title: item.btnName,
      }))
    )
  }

  const fetchOwn = async () => {
    const list = await fetchButtonList({
      menuId: data.menuId,
      pageIndex: 1,
      pageSize: 1000,
    })

    setTargetKeys(list.map(({ btnId }: { btnId: number }) => btnId))
  }

  const add = async (ids: string[]) => {
    const { menuId } = data

    await addButton({ menuId, ids })
    init()
  }
  const remove = async (ids: string[]) => {
    const { menuId } = data

    await removeButton({ menuId, ids })
    init()
  }

  const handleChange = (newTargetKeys: string[], direction: string, moveKeys: string[]) => {
    direction === 'right' ? add(moveKeys) : remove(moveKeys)

    // setTargetKeys(newTargetKeys)
  }

  const init = () => {
    fetchAll()
    fetchOwn()
  }

  useEffect(() => {
    if (open) {
      init()
    }
  }, [open])

  return (
    <Modal
      title={`${data?.menuName} - 按钮列表`}
      open={open}
      width={700}
      okText="保存"
      destroyOnClose
      footer={null}
      maskClosable={false}
      onCancel={hide}>
      <Transfer
        dataSource={mockData}
        showSearch
        listStyle={{
          width: 300,
          height: '70vh',
        }}
        titles={['按钮列表', '已绑定按钮']}
        operations={['添加', '移除']}
        targetKeys={targetKeys}
        onChange={handleChange}
        render={item => item.title}
      />
    </Modal>
  )
})

ButtonsModal.displayName = 'ButtonsModal'

export default ButtonsModal
