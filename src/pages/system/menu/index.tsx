import { PlusOutlined } from '@ant-design/icons'
import { ListHeader } from '@fe/rockrose'
import { message } from 'antd'
import React, { useEffect, useRef, useState } from 'react'
import ButtonsModal from './components/ButtonsModal'
import Detail from './components/Detail'
import List from './components/List'
import useStore, { ICreate, IEdit, IMenu } from './store'

const Menu: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [modal, setModal] = useState<{ title?: string; type?: string; show: boolean; data?: IMenu }>({ show: false })
  const buttonModalRef = useRef()

  const {
    tree,
    fetchTree,
    fetchUpdate,
    fetchUpdateMenu,
    fetchCreate,
    fetchCreateMenu,
    fetchMenuDelete,
    fetchChildDelete,
  } = useStore()

  const handleOperate = (event: string, data?: IMenu) => {
    switch (event) {
      case 'create':
        setModal({ title: '添加菜单', type: 'create', show: true, data })
        break
      case 'edit':
        setModal({ title: '编辑', type: 'edit', show: true, data })
        break

      /*
       * case 'copy':
       *   setModal({ title: '复制', type: 'copy', show: true, data })
       *   break
       */
      case 'remove':
        data && handleRemove(data)
        break
      case 'bindButton':
        buttonModalRef && (buttonModalRef.current as any).show(data)
        break
      case 'add':
        setModal({
          title: '添加子菜单',
          type: 'add',
          show: true,
          data,
        })
        break
      default:
        break
    }
  }
  const handleCancel = () => {
    setModal({ ...modal, show: false })
  }

  // 编辑弹框提交
  const handleSubmit = async (values: ICreate | IEdit) => {
    let methodApi = '' as any

    const { type } = modal

    if (type === 'edit') {
      methodApi = values?._isTop ? fetchUpdate : fetchUpdateMenu
    } else {
      const _create = values?._isTop ? fetchCreate : fetchCreateMenu

      methodApi = _create
    }

    const res = await methodApi(values as any)

    res && message.success('操作成功')

    handleCancel()
    getTree()
  }

  const getTree = async () => {
    setLoading(true)
    await fetchTree()
    setLoading(false)
  }

  // 删除
  const handleRemove = async (values: IMenu) => {
    const { menuId, _isTop } = values

    const res = _isTop ? await fetchMenuDelete({ appId: menuId }) : await fetchChildDelete({ menuId })

    res && message.success('删除成功')

    getTree()
  }

  useEffect(() => {
    getTree()
  }, [])

  return (
    <>
      <div>
        <ListHeader
          title="菜单列表"
          actions={[
            {
              icon: <PlusOutlined />,
              label: '添加菜单',
              onClick: () => handleOperate('create', { _isTop: true } as any),
            },
          ]}
        />
        <List data={tree} loading={loading} onOperate={handleOperate} />
      </div>
      {/* <Config open={stepModal.show} item={stepModal.data} onClose={handleCancel} /> */}
      <Detail
        title={modal.title}
        show={modal.show}
        type={modal.type}
        data={modal.data}
        onCancel={handleCancel}
        onSubmit={handleSubmit}
      />
      <ButtonsModal ref={buttonModalRef} />
    </>
  )
}

export default Menu
