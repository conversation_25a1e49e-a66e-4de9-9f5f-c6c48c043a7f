import request, { envAuthUrl } from '@/utils/request'
import { create } from 'zustand'

export interface IPageState {
  tree: []
  fetchTree: () => Promise<any>
  fetchMenuInfo: (params: any) => Promise<any>
  fetchChildInfo: (params: any) => Promise<any>
  fetchCreate: (params: ICreate) => Promise<any>
  fetchCreateMenu: (params: ICreate) => Promise<any>
  fetchUpdate: (params: ICreate) => Promise<any>
  fetchUpdateMenu: (params: ICreate) => Promise<any>
  fetchMenuDelete: (params: any) => Promise<any>
  fetchChildDelete: (params: any) => Promise<any>
  fetchButtonList: (params: any) => Promise<any>
  addButton: (params: any) => Promise<any>
  removeButton: (params: any) => Promise<any>
}

export interface ICreate {
  code: string
  name: string
  parent_id: number
  icon: string
  description: string
  type: number
  route: string
  path: string
  url: string
  show: number
  seq: number
  status: number
  appId: any
  [key: string]: any
}
export interface IEdit {
  id: number
  code: string
  name: string
  parent_id: number
  icon: string
  description: string
  type: number
  route: string
  path: string
  url: string
  show: number
  seq: number
  status: number
  [key: string]: any
}
export interface IMenu {
  id: number
  code: string
  name: string
  parent_id: number
  icon: string
  description: string
  type: number
  route: string
  path: string
  url: string
  show: number
  seq: number
  status: number
  children?: IMenu[] | null
  creator: string
  create_time: Date
  updater: string
  update_time: Date
  parent?: IMenu | undefined
  [key: string]: any
}

export default create<IPageState>(set => ({
  tree: [],
  current: 1,
  pageSize: 10,

  async fetchTree() {
    const { data } = await request({
      url: '/Menu/GetMenuTreeList',
      method: 'POST',
      data: {
        pageIndex: 1,
        pageSize: 999,
      },
      baseURL: envAuthUrl,
    })

    if (data?.list && data?.list.length) {
      let list = data.list

      list = list.map((item: any) => ({
        ...item,
        _isTop: true,
        menuName: item.appName,
        menuId: item.id,
      }))

      const loop = (list: IMenu[], parent?: IMenu) =>
        list.reduce((total: string[], item) => {
          item.parent = parent
          if (item?.menuList?.length) {
            item.children = item.menuList
            loop(item.children, item)
          }

          delete item.menuList

          return total
        }, [])

      loop(list)

      // console.log('menuList', list)

      set(() => ({ tree: list }))
    } else {
      set(() => ({ tree: [] }))
    }
  },

  // 获取单个菜单详情
  async fetchMenuInfo(params) {
    const { data } = await request({
      url: '/App/GetInfo',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data || {}
  },

  // 获取单个菜单详情
  async fetchChildInfo(params) {
    const { data } = await request({
      url: '/Menu/GetInfo',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data || {}
  },
  // 创建主菜单
  async fetchCreate(params) {
    delete params._isTop

    const { data } = await request({
      url: '/App/Add',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 创建子菜单
  async fetchCreateMenu(params) {
    delete params._isTop
    delete params.parent_id
    delete params.type

    const { data } = await request({
      url: '/Menu/Add',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 编辑主菜单
  async fetchUpdate(params) {
    delete params._isTop

    const { data } = await request({
      url: '/App/Update?opttype=modify',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 编辑子菜单
  async fetchUpdateMenu(params) {
    delete params._isTop

    const { data } = await request({
      url: '/Menu/Update?opttype=modify',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 删除主菜单
  async fetchMenuDelete(params: any) {
    const { data } = await request({
      url: '/App/Update?opttype=del',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 删除子菜单
  async fetchChildDelete(params: any) {
    const { data } = await request({
      url: '/Menu/Update?opttype=del',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },

  async fetchButtonList(params: any) {
    const { data } = await request({
      url: '/MenuBtnRight/GetList',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.list || []
  },

  async addButton(params: any) {
    const { data } = await request({
      url: '/MenuBtnRight/Add',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },

  async removeButton(params: any) {
    const { data } = await request({
      url: '/MenuBtnRight/Delete',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
}))
