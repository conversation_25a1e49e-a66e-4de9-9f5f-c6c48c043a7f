import { create } from 'zustand'
import { post, get as requestGet } from '../../../utils/request'

// 小区分页查询参数
export interface ICommunitySearchParams {
  pageIndex: number
  pageSize: number
  communityName?: string // 小区名称搜索
  communityArea?: string // 小区地址搜索
}

// 小区信息
export interface ICommunityListItem {
  id?: number // 小区主键ID
  communityName?: string // 小区名称
  communityArea?: string // 小区地址
  mapCoordinate?: string // 地图坐标（纬度,经度）
  photos?: string // 小区图片(逗号分割)
  photoList?: string[] // 照片列表（序列化处理）
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  creator?: string // 创建人
  lastModifier?: string // 最后修改人
}

// 小区名称选项
export interface ICommunityOption {
  id?: number
  communityName?: string
  communityArea?: string
  mapCoordinate?: string
  photos?: string
  photoList?: string[]
}

// API响应类型
export interface ICommunityListResponse {
  list: ICommunityListItem[]
  total: number
  pageIndex: number
  pageSize: number
}

export type IOperateType = 'create' | 'edit' | 'view' | 'delete'

export interface ICommunityStore {
  allCommunityOptions: ICommunityOption[]

  fetchCommunityList: (params: ICommunitySearchParams) => Promise<ICommunityListResponse | null>
  saveCommunity: (communityData: ICommunityListItem) => Promise<boolean>
  getAllCommunityNames: () => Promise<ICommunityOption[] | null>
}

export default create<ICommunityStore>(set => ({
  allCommunityOptions: [],

  // 获取小区列表
  fetchCommunityList: async (params: ICommunitySearchParams) => {
    try {
      const { data, status } = await post<any, any>('/PrDormRoom/GetCommunityList', params)

      if (status && data) {
        return {
          list: data.dataList || [],
          total: data.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      return null
    }
  },

  // 保存小区信息 (新增/编辑)
  saveCommunity: async (communityData: ICommunityListItem) => {
    try {
      const { status } = await post<any, any>('/PrDormRoom/SaveCommunity', communityData)

      return !!status
    } catch (error) {
      return false
    }
  },

  // 获取所有小区名称(不分页)
  getAllCommunityNames: async () => {
    try {
      const { data, status } = await requestGet<any, any>('/PrDormRoom/AllCommunityName')

      if (status && data) {
        set({ allCommunityOptions: Array.isArray(data?.dataList) ? data.dataList : [] })

        return Array.isArray(data) ? data : []
      }

      return null
    } catch (error) {
      return null
    }
  },
}))
