import NoImage from '@/components/NoImage'
import { Dict } from '@fe/rockrose'
import { Card, Divider, Flex, Image, List, Space, Typography } from 'antd'
import dayjs from 'dayjs'
import React from 'react'
import { DATE_FORMAT_BASE } from '../../../../consts/date'
import { IPagination } from '../../../../store'
import { envUrl } from '../../../../utils/request'
import { ICommunityListItem, IOperateType } from '../store'

interface ICommunityListProps {
  data: ICommunityListItem[]
  loading?: boolean
  pagination: IPagination
  onOperate: (type: IOperateType, record?: ICommunityListItem) => void
  onChange: (page: number, pageSize: number) => void
}

const CommunityList: React.FC<ICommunityListProps> = ({ data, loading, pagination, onOperate, onChange }) => {
  const renderCommunityCard = (item: ICommunityListItem) => {
    return (
      <List.Item>
        <Card hoverable size="small" className="full-h hover-move" onClick={e => onOperate('view', item)}>
          <Flex gap={24}>
            <Image.PreviewGroup items={item.photoList?.map(url => `${envUrl}${url}`)}>
              {item?.photoList?.[0] ? (
                <Image
                  width={75}
                  height={100}
                  className="radius img-cover"
                  preview={{ getContainer: document.body }}
                  onClick={e => e.stopPropagation()}
                  src={`${item?.photoList?.[0] ? envUrl + item?.photoList?.[0] : ''}`}
                />
              ) : (
                <NoImage />
              )}
            </Image.PreviewGroup>

            <Flex vertical gap={12}>
              <Space size={2} split={<Divider type="vertical" />}>
                <Typography.Text strong className="fs-lg">
                  {item.communityName}
                </Typography.Text>
              </Space>
              {item.communityArea && <Typography.Text>{item.communityArea}</Typography.Text>}
              <Space>
                {item.lastModifier && <Typography.Text>{item.lastModifier}</Typography.Text>}
                <Dict
                  title="最近更新于"
                  value={
                    <Typography.Text type="secondary">
                      {dayjs(item.updateTime).format(DATE_FORMAT_BASE)}
                    </Typography.Text>
                  }
                />
              </Space>
            </Flex>
          </Flex>
        </Card>
      </List.Item>
    )
  }

  return (
    <List
      loading={loading}
      dataSource={data}
      renderItem={renderCommunityCard}
      split={false}
      rowKey="id"
      className="list-sm"
      pagination={{
        ...pagination,
        onChange,
        onShowSizeChange: onChange,
      }}
    />
  )
}

export default CommunityList
