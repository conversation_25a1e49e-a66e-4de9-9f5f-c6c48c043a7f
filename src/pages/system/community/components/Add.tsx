import AmapDisplay from '@/components/AmapDisplay'
import EnvImage from '@/components/EnvImage'
import { envUrl } from '@/utils/request'
import { CloseCircleOutlined, EditOutlined } from '@ant-design/icons'
import { Fieldset } from '@fe/rockrose'
import { Button, Descriptions, Drawer, Flex, Form, Image, Input, Popconfirm, Space } from 'antd'
import React, { useEffect } from 'react'
import AmapPicker from '../../../../components/AmapPicker'
import UploadComponent from '../../../../components/Upload'
import { ICommunityListItem } from '../store'

const { TextArea } = Input

interface IAddProps {
  open: boolean
  data?: ICommunityListItem
  operateType: 'create' | 'edit' | 'view' | 'delete'
  onEdit: () => void
  onCancel: () => void
  onSubmit: (values: ICommunityListItem) => void
}

const CommunityAdd: React.FC<IAddProps> = ({ open, data, operateType, onEdit, onCancel, onSubmit }) => {
  const [form] = Form.useForm()
  const isView = operateType === 'view'
  const isEdit = operateType === 'edit'
  const formCommunityArea = Form.useWatch('communityArea', form)

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        ...data,
        photos: data.photoList || [],
      })
    } else if (open && !data) {
      form.resetFields()
    } else if (!open) {
      form.resetFields()
    }
  }, [open, data, form])

  const handleSubmit = async () => {
    if (isView) {
      return
    }

    try {
      const values = await form.validateFields()
      const submitData: ICommunityListItem = {
        ...values,
        photos: Array.isArray(values.photos) ? values.photos.join(',') : values.photos,
        ...(isEdit && data?.id ? { id: data.id } : {}),
      }

      delete submitData.createTime
      delete submitData.updateTime
      delete submitData.lastModifier

      onSubmit(submitData)
    } catch (error) {
      // 表单验证失败
    }
  }

  const handleMapCoordinateSelect = (location: any) => {
    if (location?.address) {
      form.setFieldValue('communityArea', location.address)
      form.setFieldValue('communityName', location.name)
    }
  }

  const getTitle = () => {
    switch (operateType) {
      case 'create':
        return '添加小区'
      case 'edit':
        return '编辑小区'
      case 'view':
        return '查看小区'
      default:
        return '小区信息'
    }
  }

  // 查看模式渲染
  if (isView && data) {
    return (
      <Drawer
        title={data.communityName}
        width={600}
        open={open}
        onClose={onCancel}
        extra={
          <Button type="primary" ghost icon={<EditOutlined />} shape="round" onClick={onEdit}>
            编辑
          </Button>
        }>
        <Flex vertical gap={24}>
          <Descriptions size="small" column={2} bordered>
            <Descriptions.Item label="小区">{data.communityName || '-'}</Descriptions.Item>
            <Descriptions.Item label="地图坐标">{data.mapCoordinate || '-'}</Descriptions.Item>
            <Descriptions.Item label="详细地址" span={2}>
              {data.communityArea || '-'}
            </Descriptions.Item>
          </Descriptions>
          {data.mapCoordinate && (
            <AmapDisplay
              showLocationInfo={false}
              dest={data.mapCoordinate}
              destName={data.communityArea || ''}
              height={300}
            />
          )}
          {data.photoList && data.photoList.length > 0 && (
            <Fieldset title="图片">
              <Image.PreviewGroup items={data.photoList.map(url => `${envUrl}${url}`)}>
                <Space wrap>
                  {data.photoList.map((url, idx) => (
                    <div key={url} className="hover-move hover-show-remove">
                      <EnvImage
                        width={135}
                        height={180}
                        src={url}
                        className="radius img-cover"
                        preview={{ getContainer: document.body }}
                      />
                      <Popconfirm title="警告" description="确定要删除该照片吗？" okText="确定删除" cancelText="取消">
                        <Button
                          type="text"
                          icon={<CloseCircleOutlined className="fs-xlg text-secondary" />}
                          className="remove"
                        />
                      </Popconfirm>
                    </div>
                  ))}
                </Space>
              </Image.PreviewGroup>
            </Fieldset>
          )}
        </Flex>
      </Drawer>
    )
  }

  // 编辑/新增模式渲染
  return (
    <Drawer
      title={getTitle()}
      width={600}
      open={open}
      onClose={onCancel}
      extra={
        <Button type="primary" onClick={handleSubmit}>
          立即保存
        </Button>
      }>
      <Form form={form} layout="vertical">
        <Form.Item name="mapCoordinate" label="地图坐标">
          <AmapPicker
            defaultValue={{
              location: '',
              address: formCommunityArea || '',
              name: '',
            }}
            onSelect={handleMapCoordinateSelect}
            buttonText="选择小区位置"
          />
        </Form.Item>
        <Form.Item name="communityName" label="小区" rules={[{ required: true, message: '请输入小区名称' }]}>
          <Input placeholder="请输入小区名称" />
        </Form.Item>

        <Form.Item name="communityArea" label="详细地址" rules={[{ required: true, message: '请输入小区地址' }]}>
          <TextArea placeholder="请输入小区地址" autoSize={{ minRows: 2, maxRows: 4 }} />
        </Form.Item>

        <Form.Item name="photos" label="图片">
          <UploadComponent
            type="image"
            action="/PrActors/UploadFile"
            multiple
            maxCount={10}
            accept=".png,.jpg,.jpeg,.gif,.webp"
          />
        </Form.Item>
      </Form>
    </Drawer>
  )
}

export default CommunityAdd
