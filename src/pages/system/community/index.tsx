import { PlusOutlined } from '@ant-design/icons'
import { ListHeader } from '@fe/rockrose'
import { Button, Flex, Form, message } from 'antd'
import React, { useEffect, useState } from 'react'
import { PAGINATION } from '../../../consts'
import useSyncParams, { initFormFromUrlState, parsePagination } from '../../../hooks/useSyncParams'
import CommunityAdd from './components/Add'
import CommunityList from './components/List'
import CommunitySearch from './components/Search'
import useCommunityStore, { ICommunityListItem, ICommunitySearchParams, IOperateType } from './store'

const CommunityPage: React.FC = () => {
  const { fetchCommunityList, saveCommunity } = useCommunityStore()

  // 表单和状态管理
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<ICommunityListItem[]>([])
  const [pagination, setPagination] = useState(PAGINATION)

  // 操作相关状态
  const [show, setShow] = useState(false)
  const [operateType, setOperateType] = useState<IOperateType>('create')
  const [operateData, setOperateData] = useState<ICommunityListItem | null>(null)

  // URL同步
  const [urlState, setUrlState] = useSyncParams<ICommunitySearchParams>()

  // 搜索处理
  const handleSearch = async (current = pagination.current) => {
    setLoading(true)
    try {
      const formValues = form.getFieldsValue()
      const params: ICommunitySearchParams = {
        pageIndex: current,
        pageSize: pagination.pageSize || 20,
        ...formValues,
      }

      const response = await fetchCommunityList(params)

      if (response) {
        setDataSource(response.list)
        setPagination({
          ...pagination,
          current: response.pageIndex,
          pageSize: response.pageSize,
          total: response.total,
        })

        // 更新URL状态
        setUrlState({
          ...formValues,
          pageIndex: response.pageIndex,
          pageSize: response.pageSize,
        })
      }
    } catch (error) {
      message.error('获取小区列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 操作处理
  const handleOperate = (type: IOperateType, record?: ICommunityListItem) => {
    setOperateType(type)
    setOperateData(record || null)
    setShow(true)
  }

  // 关闭操作弹窗
  const handleOperateClose = () => {
    setShow(false)
    setOperateData(null)
  }

  // 表单提交
  const handleFormSubmit = async (values: ICommunityListItem) => {
    try {
      const success = await saveCommunity(values)

      if (success) {
        message.success(operateType === 'edit' ? '编辑成功' : '添加成功')
        handleOperateClose()
        handleSearch()
      } else {
        message.error('保存失败')
      }
    } catch (error) {
      message.error('保存失败')
    }
  }

  // 初始化参数
  const initParams = () => {
    // 使用工具函数简化表单初始化
    initFormFromUrlState(urlState, form, { excludeFields: ['pageIndex', 'pageSize', 'current'] })

    // 使用工具函数处理分页
    const { current, pageSize } = parsePagination(urlState, {
      current: pagination.current,
      pageSize: pagination.pageSize,
    })

    setPagination({ ...pagination, current, pageSize })
    handleSearch(current)
  }

  useEffect(() => {
    initParams()
  }, [])

  return (
    <Flex vertical gap={24}>
      <CommunitySearch form={form} loading={loading} onSearch={handleSearch} />
      <div>
        <ListHeader title="小区字典" total={pagination.total}>
          <Button type="primary" ghost icon={<PlusOutlined />} onClick={() => handleOperate('create')}>
            添加字典
          </Button>
        </ListHeader>
        <CommunityList
          data={dataSource}
          loading={loading}
          onOperate={handleOperate}
          onChange={handleSearch}
          pagination={pagination}
        />
      </div>

      <CommunityAdd
        open={show}
        data={operateData || {}}
        operateType={operateType}
        onEdit={() => setOperateType('edit')}
        onCancel={handleOperateClose}
        onSubmit={handleFormSubmit}
      />
    </Flex>
  )
}

export default CommunityPage
