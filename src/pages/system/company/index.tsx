import { PlusOutlined } from '@ant-design/icons'
import { ListHeader } from '@fe/rockrose'
import { Button, Flex, Form, message } from 'antd'
import React, { useEffect, useState } from 'react'
import { PAGINATION } from '../../../consts'
import useSyncParams, { formatUrlState, initFormFromUrlState, parsePagination } from '../../../hooks/useSyncParams'
import CompanyAdd from './components/Add'
import CompanyList from './components/List'
import Search from './components/Search'
import useCompanyStore, { ICompanyListItem, ICompanySearchParams, IOperateType } from './store'

const CompanyPage: React.FC = () => {
  const [urlState, setUrlState] = useSyncParams<ICompanySearchParams>()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<ICompanyListItem[]>([])
  const [pagination, setPagination] = useState(PAGINATION)

  const [show, setShow] = useState(false)
  const [operateData, setOperateData] = useState<ICompanyListItem | null>(null)
  const [operateType, setOperateType] = useState<'create' | 'edit' | 'view'>('create')

  const { fetchCompanyList, saveCompany } = useCompanyStore()

  // 初始化URL数据
  const initParams = () => {
    // 使用工具函数简化表单初始化
    initFormFromUrlState(urlState, form, { excludeFields: ['pageIndex', 'pageSize'] })

    // 使用工具函数处理分页
    const { current, pageSize } = parsePagination(urlState, {
      current: pagination.current,
      pageSize: pagination.pageSize,
    })

    handleSearch(current, pageSize)
  }

  const handleSearch = async (current = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true)
    try {
      const values = form.getFieldsValue()
      const searchParams: ICompanySearchParams = {
        pageIndex: current,
        pageSize,
        ...values,
      }

      const result = await fetchCompanyList(searchParams)

      if (result) {
        setDataSource(result.list)
        setPagination(prev => ({
          ...prev,
          total: result.total,
          current: result.pageIndex,
          pageSize: result.pageSize,
        }))

        // 同步URL参数 - 使用工具函数格式化
        setUrlState(
          formatUrlState(
            {
              companyName: values.companyName || '',
              pageSize: result.pageSize,
              pageIndex: result.pageIndex,
            },
            {}
          )
        )
      } else {
        setDataSource([])
      }
    } catch (error) {
      setDataSource([])
    } finally {
      setLoading(false)
    }
  }

  const handleOperate = (type: IOperateType, data?: ICompanyListItem) => {
    if (['create', 'edit', 'view'].includes(type)) {
      setOperateData(data || null)
      setOperateType(type as 'create' | 'edit' | 'view')
      setShow(true)
    }
  }

  const handleOperateClose = (fresh = true) => {
    setOperateData(null)
    setOperateType('create')
    setShow(false)
    if (fresh) {
      handleSearch()
    }
  }

  const handleReset = () => {
    form.resetFields()
    handleSearch(1)
  }

  // 表单提交
  const handleFormSubmit = async (values: ICompanyListItem) => {
    try {
      const result = await saveCompany(values)

      if (result) {
        message.success(operateType === 'edit' ? '更新成功' : '添加成功')
        handleOperateClose()
        await handleSearch(1)
      } else {
        message.error('操作失败')
      }
    } catch (error) {
      message.error('操作失败')
    }
  }

  useEffect(() => {
    initParams()
  }, [])

  return (
    <Flex vertical gap={24}>
      <Search form={form} loading={loading} onSearch={handleSearch} />
      <div>
        <ListHeader title="公司字典" total={pagination.total}>
          <Button type="primary" ghost icon={<PlusOutlined />} onClick={() => handleOperate('create')}>
            添加字典
          </Button>
        </ListHeader>
        <CompanyList
          data={dataSource}
          loading={loading}
          onOperate={handleOperate}
          onChange={handleSearch}
          pagination={pagination}
        />
      </div>

      <CompanyAdd
        open={show}
        data={operateData || {}}
        operateType={operateType}
        onCancel={handleOperateClose}
        onSubmit={handleFormSubmit}
      />
    </Flex>
  )
}

export default CompanyPage
