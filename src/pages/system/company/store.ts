import { create } from 'zustand'
import { post, get as requestGet } from '../../../utils/request'

// 公司分页查询参数
export interface ICompanySearchParams {
  pageIndex: number
  pageSize: number
  companyName?: string // 公司全称搜索
}

// 公司信息
export interface ICompanyListItem {
  id?: number // 公司主键ID
  companyName?: string // 公司全称
  companyShort?: string // 公司简称
  companyCode?: string // 公司编码
  companyAddress?: string // 公司联系地址
  phone?: string // 联系电话
  email?: string // 公司邮箱
  remark?: string // 备注信息
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  creator?: string // 创建人
  lastModifier?: string // 最后修改人
}

// 公司名称选项
export interface ICompanyOption {
  companyName?: string
  companyShort?: string
  companyCode?: string
}

// API响应类型
export interface ICompanyListResponse {
  list: ICompanyListItem[]
  total: number
  pageIndex: number
  pageSize: number
}

export type IOperateType = 'create' | 'edit' | 'view' | 'delete'

export interface ICompanyStore {
  allCompanyOptions: ICompanyOption[]

  fetchCompanyList: (params: ICompanySearchParams) => Promise<ICompanyListResponse | null>
  saveCompany: (companyData: ICompanyListItem) => Promise<boolean>
  getAllCompanyNames: () => Promise<ICompanyOption[] | null>
}

export default create<ICompanyStore>((set, get) => ({
  allCompanyOptions: [],

  // 获取公司列表
  fetchCompanyList: async (params: ICompanySearchParams) => {
    try {
      const { data, status } = await post<any, any>('/PrCompanyInfo/GetList', params)

      if (status && data) {
        return {
          list: data.dataList || [],
          total: data.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      console.error('获取公司列表失败:', error)

      return null
    }
  },

  // 保存公司信息 (新增/编辑)
  saveCompany: async (companyData: ICompanyListItem) => {
    try {
      const { data, status } = await post<any, any>('/PrCompanyInfo/Save', companyData)

      return !!status
    } catch (error) {
      console.error('保存公司信息失败:', error)

      return false
    }
  },

  // 获取所有公司名称(不分页)
  getAllCompanyNames: async () => {
    try {
      const { data, status } = await requestGet<any, any>('/PrCompanyInfo/AllName')

      if (status && data) {
        set({ allCompanyOptions: Array.isArray(data?.dataList) ? data.dataList : [] })

        return Array.isArray(data) ? data : []
      }
    } catch (error) {
      console.error('获取公司名称失败:', error)

      return []
    }
  },
}))
