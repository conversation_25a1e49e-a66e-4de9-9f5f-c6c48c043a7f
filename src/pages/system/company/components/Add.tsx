import { DATE_FORMAT_BASE } from '@/consts/date'
import { Button, Descriptions, Drawer, Form, Input } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect } from 'react'
import { ICompanyListItem } from '../store'

const { TextArea } = Input

interface IAddProps {
  open: boolean
  data?: ICompanyListItem
  operateType: 'create' | 'edit' | 'view'
  onCancel: () => void
  onSubmit: (values: ICompanyListItem) => void
}

const CompanyAdd: React.FC<IAddProps> = ({ open, data, operateType, onCancel, onSubmit }) => {
  const [form] = Form.useForm()
  const isView = operateType === 'view'
  const isEdit = operateType === 'edit'

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue(data)
    } else if (open && !data) {
      form.resetFields()
    }
  }, [open, data, form])

  const handleSubmit = async () => {
    if (isView) {
      return
    }

    try {
      const values = await form.validateFields()
      const submitData: ICompanyListItem = {
        ...values,
        ...(isEdit && data?.id ? { id: data.id } : {}),
      }

      onSubmit(submitData)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const getTitle = () => {
    switch (operateType) {
      case 'create':
        return '添加字典'
      case 'edit':
        return '编辑字典'
      case 'view':
        return '查看详情'
      default:
        return '公司信息'
    }
  }

  // 查看模式渲染
  if (isView && data) {
    return (
      <Drawer title={getTitle()} width={600} open={open} onClose={onCancel}>
        <Descriptions column={1} bordered>
          <Descriptions.Item label="公司">{data.companyName || '-'}</Descriptions.Item>
          <Descriptions.Item label="简称">{data.companyShort || '-'}</Descriptions.Item>
          <Descriptions.Item label="编码">{data.companyCode || '-'}</Descriptions.Item>
          <Descriptions.Item label="联系电话">{data.phone || '-'}</Descriptions.Item>
          <Descriptions.Item label="公司邮箱">{data.email || '-'}</Descriptions.Item>
          <Descriptions.Item label="详细地址">{data.companyAddress || '-'}</Descriptions.Item>
          <Descriptions.Item label="备注">{data.remark || '-'}</Descriptions.Item>
          <Descriptions.Item label="创建人">{data.creator}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{dayjs(data.createTime).format(DATE_FORMAT_BASE)}</Descriptions.Item>
          <Descriptions.Item label="更新时间">{dayjs(data.updateTime).format(DATE_FORMAT_BASE)}</Descriptions.Item>
        </Descriptions>
      </Drawer>
    )
  }

  // 编辑/新增模式渲染
  return (
    <Drawer
      title={getTitle()}
      width={600}
      open={open}
      onClose={onCancel}
      extra={
        <Button type="primary" onClick={handleSubmit}>
          立即保存
        </Button>
      }>
      <Form form={form} layout="vertical">
        <Form.Item name="companyName" label="公司名" rules={[{ required: true, message: '请输入公司全称' }]}>
          <Input placeholder="请输入公司全称" />
        </Form.Item>

        <Form.Item name="companyShort" label="简称">
          <Input placeholder="请输入公司简称" />
        </Form.Item>

        <Form.Item name="companyCode" label="编码" rules={[{ required: true, message: '请输入公司编码' }]}>
          <Input placeholder="请输入公司编码" />
        </Form.Item>

        <Form.Item
          name="phone"
          label="联系电话"
          rules={[{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式' }]}>
          <Input placeholder="请输入联系电话" />
        </Form.Item>

        <Form.Item name="email" label="邮箱" rules={[{ type: 'email', message: '请输入正确的邮箱格式' }]}>
          <Input placeholder="请输入公司邮箱" />
        </Form.Item>

        <Form.Item name="companyAddress" label="详细地址">
          <TextArea rows={4} placeholder="请输入公司地址" />
        </Form.Item>

        <Form.Item name="remark" label="备注信息">
          <TextArea rows={4} placeholder="请输入备注信息" />
        </Form.Item>
      </Form>
    </Drawer>
  )
}

export default CompanyAdd
