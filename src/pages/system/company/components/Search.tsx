import { useDebounceFn } from 'ahooks'
import { Button, Form, Input, Space } from 'antd'
import type { FormInstance } from 'antd/lib'
import React from 'react'

interface ISearchProps {
  form: FormInstance
  loading?: boolean
  onSearch: (current?: number) => void
}

const CompanySearch: React.FC<ISearchProps> = ({ form, loading = false, onSearch }) => {
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(1), { wait: 500 })

  return (
    <Form form={form} onValuesChange={onSearchDebounce} colon={false}>
      <Space size={36}>
        <Form.Item name="companyName" label="公司">
          <Input className="w200" placeholder="输入公司名称" allowClear />
        </Form.Item>

        <Form.Item>
          <Button type="primary" onClick={onSearchDebounce} loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Space>
    </Form>
  )
}

export default CompanySearch
