import useSyncParams from '@/hooks/useSyncParams'
import { App, Form, Space } from 'antd'
import React, { useEffect, useState } from 'react'
import Detail from './components/Detail'
import List from './components/List'
import Search from './components/Search'
import useStore, { ICreate, IListItem, ISearchParams } from './store'

const Bottom: React.FC = () => {
  const { message } = App.useApp()
  const [form] = Form.useForm()
  const { list, total, fetchList, create, edit, fetchDelete } = useStore()

  const [urlState, setUrlState] = useSyncParams<ISearchParams>()
  const [type, setType] = useState<string>()
  const [loading, setLoading] = useState(false)
  const [btnLoading, setBtnLoading] = useState(false)
  const [pagination, setPagination] = useState({ pageIndex: 1, pageSize: 20 })
  const [modalShow, setModalShow] = useState(false)
  const [modalData, setModalData] = useState<IListItem | null>(null)

  const initParams = () => {
    const { btnName, pageIndex = pagination.pageIndex, pageSize = pagination.pageSize } = urlState

    form.setFieldsValue({ btnName })

    setPagination({ pageIndex: Number(pageIndex), pageSize: Number(pageSize) })

    handleSearch(pageIndex ? +pageIndex : pagination.pageIndex, pageSize ? +pageSize : pagination.pageSize)
  }

  // 提交搜索
  const handleSearch = async (pageIndex = pagination.pageIndex, pageSize = pagination.pageSize) => {
    const { btnName } = form.getFieldsValue()

    const params = {
      btnName,
      pageIndex,
      pageSize,
    }

    setLoading(true)
    await fetchList(params)

    setLoading(false)

    setPagination({ pageIndex, pageSize })
    setUrlState({ ...params, pageIndex, pageSize })
  }

  // 列表操作
  const handleOperate = (event: string, data?: IListItem) => {
    setType(event)
    switch (event) {
      case 'create':
        setModalShow(true)
        setModalData(null)
        break
      case 'edit':
        setModalShow(true)
        setModalData(data)
        break
      case 'delete':
        handleDelete(data)
        break

      default:
        break
    }
  }

  // 删除
  const handleDelete = async (data: IListItem) => {
    const res = await fetchDelete({ id: data.id })

    res && message.success('删除成功')
    handleSearch()
  }

  // 取消弹窗
  const handleCancelModal = () => {
    setModalShow(false)
    setModalData(null)
  }

  // 编辑弹框提交
  const handleSubmit = async (values: ICreate) => {
    setBtnLoading(true)
    const result = modalData ? await edit({ ...values, id: modalData.id }) : await create({ ...values })

    setBtnLoading(false)

    result && message.success('操作成功')

    handleCancelModal()
    handleSearch()
  }

  useEffect(() => {
    initParams()
  }, [])

  return (
    <Space size="large" direction="vertical" style={{ width: '100%' }}>
      <Search form={form} onSearch={handleSearch} loading={loading} />
      <List
        data={list}
        loading={loading}
        pagination={{
          current: pagination.pageIndex,
          pageSize: pagination.pageSize,
          total,
        }}
        onChange={handleSearch}
        onOperate={handleOperate}
      />
      <Detail
        type={type}
        show={modalShow}
        data={modalData}
        onCancel={handleCancelModal}
        onSubmit={handleSubmit}
        loading={btnLoading}
      />
    </Space>
  )
}

export default Bottom
