import request, { envAuthUrl } from '@/utils/request'
import { create } from 'zustand'

export interface ISearchParams {
  btnName?: string

  pageIndex?: number
  pageSize?: number
  total?: number
}

export interface IListItem {
  action: number
  actionName: string
  btnName: string
  btnPosition: number
  btnPositionStr: string
  createTime: string
  creator: string
  id: number
  isOpenType: number
}

export interface ICreate {
  btnName: string
  btnPosition: number
}
export interface IEdit extends ICreate {
  id: number
}

export interface IUserState {
  list: IListItem[]
  total: number
  fetchList: (params: ISearchParams) => Promise<void>
  create: (params: ICreate) => Promise<any>
  edit: (params: IEdit) => Promise<any>
  fetchDelete: (params: IEdit) => Promise<any>
}

export default create<IUserState>(set => ({
  list: [],
  total: 0,

  fetchList: async params => {
    const { data } = await request({
      url: '/MenuBtn/GetList',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    const { list = [] } = data

    if (list.length) {
      set(() => ({ list, total: list.length }))
    } else {
      set(() => ({ list: [], total: 0 }))
    }
  },
  // 添加按钮
  async create(params) {
    const { data } = await request({
      url: '/MenuBtn/Add',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 编辑按钮
  async edit(params) {
    const { data } = await request({
      url: '/MenuBtn/Update?opttype=modify',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
  // 删除按钮
  async fetchDelete(params) {
    const { data } = await request({
      url: '/MenuBtn/Update?opttype=del',
      method: 'POST',
      data: params,
      baseURL: envAuthUrl,
    })

    return data?.statusCode === 1
  },
}))
