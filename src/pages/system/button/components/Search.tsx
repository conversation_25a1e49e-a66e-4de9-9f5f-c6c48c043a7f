import React from 'react'
import { Form, Input, Row, Col } from 'antd'
import { useDebounceFn } from 'ahooks'
import ActionView from '@/components/ActionView'

const Search: React.FC<ISearchProps> = ({ form, loading, onSearch }) => {
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(1), { wait: 500 })

  return (
    <Form layout="vertical" form={form} className="search-form">
      <Row gutter={36}>
        <Col span={4}>
          <Form.Item label="按钮" name="btnName">
            <Input allowClear placeholder="输入按钮名称" onChange={() => onSearchDebounce()} />
          </Form.Item>
        </Col>

        <Col span={4}>
          <Form.Item label=" " name="times">
            <ActionView loading={loading} form={form} onSearch={onSearch} />
          </Form.Item>
        </Col>
      </Row>
    </Form>
  )
}

export default React.memo(Search)
