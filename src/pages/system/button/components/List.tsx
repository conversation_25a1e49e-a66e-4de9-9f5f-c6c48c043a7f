import { EditOutlined, PlusOutlined } from '@ant-design/icons'
import { ActionCard, ListHeader } from '@fe/rockrose'
import { List, Skeleton, Tooltip } from 'antd'
import { PaginationConfig } from 'antd/es/pagination'
import React from 'react'
import { IListItem } from '../store'

interface IListProps {
  data: IListItem[]
  loading: boolean
  pagination: PaginationConfig
  onChange: (page: number, pageSize: number) => void
  onOperate: (event: string, data?: IListItem) => void
}

const ListWrap: React.FC<IListProps> = ({ data, loading, pagination, onChange, onOperate }) => (
  <div>
    <ListHeader
      title="按钮列表"
      unitText="个"
      total={pagination?.total}
      actions={[
        {
          label: '添加按钮',
          icon: <PlusOutlined />,
          onClick: () => onOperate('create'),
        },
      ]}
    />
    {loading && data?.length === 0 ? (
      <Skeleton active />
    ) : (
      <List
        dataSource={data}
        grid={{ gutter: 36, column: 5 }}
        loading={loading}
        pagination={{
          ...pagination,
          onChange: (current, pageSize) => onChange(current ?? 1, pageSize),
        }}
        renderItem={(item: any) => (
          <List.Item>
            <ActionCard
              className="hover-move"
              title={item.btnName}
              subTitle={<Tooltip title="显示位置">{item.btnPositionStr}</Tooltip>}
              actions={[
                {
                  key: 'edit',
                  label: '',
                  icon: <EditOutlined />,
                },

                /*
                 * {
                 *   key: 'delete',
                 *   label: '',
                 *   icon: <DeleteOutlined />,
                 *   showPopconfirm: true,
                 * },
                 */
              ]}
              onClick={(key, data) => onOperate(data.key, item)}
            />
          </List.Item>
        )}
      />
    )}
  </div>
)

export default ListWrap
