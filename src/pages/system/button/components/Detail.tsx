import React, { useEffect } from 'react'
import { Modal, Space, Button } from 'antd'
import { ProForm, ProFormText, ProFormSelect } from '@ant-design/pro-components'
import { required } from '@/consts/rules'
import { ICreate, IListItem } from '../store'

interface IDetailProps {
  type: string | undefined
  show?: boolean
  data?: IListItem | null
  loading?: boolean
  onSubmit: (data: ICreate) => void
  onCancel: () => void
}

const Detail: React.FC<IDetailProps> = ({
  type = 'create',
  show = false,
  data = null,
  loading = false,
  onSubmit,
  onCancel,
}) => {
  const [form] = ProForm.useForm()

  // modal提交
  const handleSubmit = async () => {
    const values = await form.validateFields().catch(() => false)

    values && onSubmit({ ...values, btnPositionStr: values ? 'Right' : 'Top' })
  }

  // 重置表单数据
  const handleReset = () => form.resetFields()

  useEffect(() => {
    data && form.setFieldsValue(data)
  }, [data])

  return (
    <Modal
      title={type === 'create' ? '添加按钮' : '编辑按钮'}
      open={show}
      width={600}
      keyboard={false}
      maskClosable={false}
      onCancel={onCancel}
      afterClose={handleReset}
      footer={
        <Space size={'small'}>
          <Button type="default" onClick={onCancel}>
            取消
          </Button>
          <Button type="primary" loading={loading} onClick={handleSubmit}>
            保存
          </Button>
        </Space>
      }>
      <ProForm
        form={form}
        layout="horizontal"
        submitter={false}
        labelCol={{ span: 4, offset: 2 }}
        wrapperCol={{ span: 14 }}
        style={{ padding: 20 }}>
        <ProFormText
          label="按钮名称"
          name="btnName"
          rules={[required]}
          placeholder="输入按钮名称"
          fieldProps={{ allowClear: true }}
        />
        <ProFormSelect
          label="显示位置"
          rules={[required]}
          name="btnPosition"
          placeholder="选择显示位置"
          options={[
            { value: 0, label: 'Top' },
            { value: 1, label: 'Right' },
          ]}
          fieldProps={{ allowClear: true }}
        />
      </ProForm>
    </Modal>
  )
}

export default Detail
