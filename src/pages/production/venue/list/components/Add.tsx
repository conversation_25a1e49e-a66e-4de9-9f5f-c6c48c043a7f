import AmapPicker from '@/components/AmapPicker'
import filterMatch from '@/utils/filterMatch'
import { Button, Drawer, Form, Input, InputNumber, Radio, Select, message } from 'antd'
import React, { useEffect } from 'react'
import { IS_INTERNAL_OPTIONS } from '../../../../../consts'
import { CHINA_CITY_OPTIONS } from '../../../../../consts/city'
import { IVenueListItem } from '../store'

const { TextArea } = Input

// 场地适合类型选项
const SUITABLE_TYPE_OPTIONS = [
  { label: '古装', value: '古装' },
  { label: '现代', value: '现代' },
  { label: '武侠', value: '武侠' },
  { label: '科幻', value: '科幻' },
  { label: '民国', value: '民国' },
  { label: '乡村', value: '乡村' },
]

interface IVenueFormProps {
  open: boolean
  data?: IVenueListItem
  onCancel: () => void
  onSubmit: (values: IVenueListItem) => void
}

const VenueForm: React.FC<IVenueFormProps> = ({ open, data, onCancel, onSubmit }) => {
  const [form] = Form.useForm()
  const isEdit = !!data?.id
  const formAddress = Form.useWatch('address', form)

  useEffect(() => {
    if (open) {
      if (data) {
        // 编辑模式，填充表单
        const formData = {
          ...data,
          suitableTypeList: data.suitableTypeList || (data.suitableType ? data.suitableType.split(',') : []),
        }

        form.setFieldsValue(formData)
      } else {
        // 新增模式，重置表单
        form.resetFields()
      }
    }
  }, [open, data, form])

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()

      const suitableTypeList = values.suitableTypeList || []
      const suitableType = suitableTypeList.join(',')

      const submitData = {
        id: data?.id,
        venueName: values.venueName,
        address: values.address,
        cityName: values.cityName,
        contactPerson: values.contactPerson,
        contactPhone: values.contactPhone,
        suitableType,
        cost: values.cost,
        isInternal: values.isInternal,
        mapCoordinate: values.mapCoordinate || '',
      } as IVenueListItem

      onSubmit(submitData)
    } catch (error) {
      message.error('请检查表单填写是否正确')
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  const handleMapCoordinateSelect = mapInfo => {
    if (mapInfo) {
      form.setFieldValue('address', mapInfo.address)
    }
  }

  return (
    <Drawer
      title={isEdit ? '编辑场地' : '添加场地'}
      open={open}
      onClose={handleCancel}
      width={800}
      destroyOnHidden
      extra={
        <Button type="primary" onClick={handleSubmit}>
          立即保存
        </Button>
      }>
      <Form form={form} layout="vertical" colon={false}>
        <Form.Item
          name="venueName"
          label="场地名"
          rules={[{ required: true }, { max: 100, message: '场地名称不能超过100个字符' }]}>
          <Input placeholder="输入场地名称" />
        </Form.Item>
        <Form.Item name="isInternal" label="是否内部" initialValue={0}>
          <Radio.Group options={IS_INTERNAL_OPTIONS} />
        </Form.Item>
        <Form.Item name="suitableTypeList" label="适合类型">
          <Select mode="multiple" placeholder="选择适合的拍摄类型" options={SUITABLE_TYPE_OPTIONS} allowClear />
        </Form.Item>
        <Form.Item name="cost" label="费用(元)">
          <InputNumber
            placeholder="输入场地使用费"
            min={0}
            className="full-h"
            prefix="￥"
            formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={(value?: string) => Number(value?.replace(/\$\s?|(,*)/g, '')) || 0}
          />
        </Form.Item>
        <Form.Item name="contactPerson" label="联系人">
          <Input placeholder="输入联系人姓名" />
        </Form.Item>
        <Form.Item name="contactPhone" label="联系电话">
          <Input placeholder="输入联系电话" />
        </Form.Item>

        <Form.Item name="cityName" label="所在城市">
          <Select
            placeholder="选择所在城市"
            options={[...CHINA_CITY_OPTIONS]}
            showSearch
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.label || '')

              return Boolean(result)
            }}
          />
        </Form.Item>
        <Form.Item name="mapCoordinate" label="地图坐标">
          <AmapPicker
            defaultValue={{
              location: '',
              address: formAddress || '',
              name: '',
            }}
            onSelect={handleMapCoordinateSelect}
          />
        </Form.Item>
        <Form.Item
          name="address"
          label="详细地址"
          rules={[{ required: true }, { max: 200, message: '地址不能超过200个字符' }]}>
          <TextArea placeholder="输入详细地址，无需重复填写城市名" autoSize={{ minRows: 4, maxRows: 6 }} />
        </Form.Item>
      </Form>
    </Drawer>
  )
}

export default VenueForm
