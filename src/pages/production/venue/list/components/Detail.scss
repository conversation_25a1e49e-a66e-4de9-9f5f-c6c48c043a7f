.videoCard {
  position: relative;
  width: 240px;
  height: 180px;
  border-radius: 8px;
  overflow: visible;
  background-color: #f0f0f0;

  &:hover {
    .remove {
      opacity: 1;
    }
  }

  .remove {
    position: absolute;
    top: 8px;
    right: 8px;
    opacity: 0;
    transition: opacity 0.3s;
    z-index: 1;
  }

  .videoContainer {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    video {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .errorContainer {
      position: absolute;
      top: 0;
      left: 0;
      display: none;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background-color: #f5f5f5;
      flex-direction: column;
      gap: 8px;

      &.show {
        display: flex;
      }
    }
  }

  .videoTitle {
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 1px solid #f0f0f0;
  }
}

.mediaGrid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}