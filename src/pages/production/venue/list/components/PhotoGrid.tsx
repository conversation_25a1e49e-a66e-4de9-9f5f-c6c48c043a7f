import EnvImage from '@/components/EnvImage'
import { envUrl } from '@/utils/request'
import { CloseCircleOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons'
import { ListHeader } from '@fe/rockrose'
import { Button, Empty, Flex, Image, Popconfirm, Space, Typography } from 'antd'
import React, { useMemo } from 'react'
import { IVenueMedia } from '../store'

interface IPhotoGridProps {
  mediaList: IVenueMedia[]
  currentCoverPhoto?: string // 当前封面图URL
  onAdd: () => void
  onDelete: (mediaId: number, url: string) => void
  onRename: (oldSubclass: string) => void
  onSetCover: (url: string) => void // 设为封面的回调
}

const PhotoGrid: React.FC<IPhotoGridProps> = ({
  mediaList,
  currentCoverPhoto,
  onAdd,
  onDelete,
  onRename,
  onSetCover,
}) => {
  // 使用 memo 优化照片数据的计算
  const { photos, photoCount } = useMemo(() => {
    const filteredPhotos = mediaList.filter(m => m.mediaType === 1 && !m.isDelete)
    const count = filteredPhotos.reduce(
      (sum, m) => sum + (m.mediaUrl ? m.mediaUrl.split(',').filter(Boolean).length : 0),
      0
    )
    const photosByCategory = filteredPhotos.map(photo => ({
      subclass: photo.subclass || '默认',
      id: photo.id || 0,
      urls: (photo.mediaUrl || '')
        .split(',')
        .map(url => url.trim())
        .filter(Boolean),
    }))

    return { photos: photosByCategory, photoCount: count }
  }, [mediaList])

  // 使用 memo 优化渲染内容
  const content = useMemo(() => {
    if (photos.length === 0) {
      return <Empty />
    }

    return (
      <Flex vertical gap={24}>
        {photos.map(category => (
          <Flex vertical gap={6} key={`${category.id}-${category.subclass}`}>
            <Space align="center" size={2}>
              <Typography.Title level={5} className="no-margin">
                {category.subclass}
              </Typography.Title>
              <Button
                type="text"
                size="small"
                icon={
                  <Typography.Text type="secondary">
                    <EditOutlined />
                  </Typography.Text>
                }
                onClick={() => onRename(category.subclass)}
              />
            </Space>
            <Image.PreviewGroup items={category.urls.map(url => `${envUrl}${url}`)}>
              <Flex gap={12} wrap>
                {category.urls.map((url, idx) => {
                  const isCover = currentCoverPhoto === url
                  return (
                    <div
                      key={`${category.id}-${idx}`}
                      className="hover-move hover-show-remove"
                      style={{ position: 'relative' }}>
                      <EnvImage width={135} height={180} src={url} className="radius img-cover" scaleSize={20} />
                      {/* 操作按钮 */}
                      <Flex className="remove" gap={4}>
                        {!isCover && (
                          <Button
                            color="primary"
                            variant="filled"
                            size="small"
                            onClick={() => onSetCover(url)}
                            title="设为封面">
                            设为封面
                          </Button>
                        )}
                        <Popconfirm
                          title="警告"
                          description="确定要删除该照片吗？"
                          onConfirm={() => onDelete(category.id, url)}
                          okText="确定删除"
                          cancelText="取消">
                          <Button type="text" icon={<CloseCircleOutlined className="fs-xlg text-secondary" />} />
                        </Popconfirm>
                      </Flex>
                    </div>
                  )
                })}
              </Flex>
            </Image.PreviewGroup>
          </Flex>
        ))}
      </Flex>
    )
  }, [photos, onDelete])

  return (
    <Flex vertical>
      <ListHeader title="照片" unitText="张" total={photoCount}>
        <Button type="primary" ghost icon={<PlusOutlined />} onClick={onAdd}>
          添加照片
        </Button>
      </ListHeader>
      {content}
    </Flex>
  )
}

export default PhotoGrid
