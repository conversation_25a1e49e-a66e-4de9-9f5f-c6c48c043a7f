import UploadImage from '@/components/Upload'
import { AutoComplete, Form, Modal, type FormInstance } from 'antd'
import React from 'react'
import { prVenueUploadAPI } from '../store'

interface IAddMediaProps {
  form: FormInstance
  open: boolean
  type: 'photo' | 'video'
  loading: boolean
  onOk: () => void
  onCancel: () => void
  categoryOptions: string[]
}

const AddMedia: React.FC<IAddMediaProps> = ({ form, type, open, loading, onOk, onCancel, categoryOptions = [] }) => (
  <Modal
    title={`添加${type === 'photo' ? '照片' : '视频'}`}
    open={open}
    okText="立即添加"
    onCancel={onCancel}
    getContainer={() => document.body}
    onOk={onOk}
    confirmLoading={loading}
    width={600}>
    <Form form={form} layout="vertical" style={{ marginTop: 20 }}>
      <Form.Item name="subclass" label="类别" rules={[{ required: true, message: '请输入或选择类别' }]}>
        <AutoComplete
          allowClear
          placeholder="请选择或输入类别，如：默认、客厅、卧室等"
          options={categoryOptions?.map(opt => ({ value: opt }))}
          filterOption={(inputValue, option) =>
            option?.value?.toLowerCase().includes(inputValue.toLowerCase()) ?? false
          }
        />
      </Form.Item>
      <Form.Item name="mediaList" rules={[{ required: true, message: `请上传${type === 'photo' ? '照片' : '视频'}` }]}>
        <UploadImage
          action={prVenueUploadAPI}
          type={type === 'photo' ? 'image' : 'video'}
          multiple={true}
          accept={type === 'photo' ? '.jpg,.jpeg,.png,.gif' : '.mp4,.avi,.mov,.wmv'}
        />
      </Form.Item>
    </Form>
  </Modal>
)

export default AddMedia
