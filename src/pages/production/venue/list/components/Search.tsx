import filterMatch from '@/utils/filterMatch'
import { useDebounceFn, useMount } from 'ahooks'
import { Button, Form, Input, Select, Space } from 'antd'
import React from 'react'
import { CHINA_CITY_OPTIONS } from '../../../../../consts/city'
import useStore from '../store'

const { Option } = Select

// 场地适合类型选项
const SUITABLE_TYPE_OPTIONS = [
  { label: '古装', value: '古装' },
  { label: '现代', value: '现代' },
  { label: '武侠', value: '武侠' },
  { label: '科幻', value: '科幻' },
  { label: '民国', value: '民国' },
  { label: '乡村', value: '乡村' },
]

const VenueSearch: React.FC<ISearchProps> = ({ loading = false, onSearch, form, onReset }) => {
  const { allVenueOptions, getAllVenueNames } = useStore()
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(1), { wait: 500 })

  useMount(() => {
    if (!allVenueOptions || allVenueOptions.length === 0) {
      getAllVenueNames()
    }
  })

  return (
    <Form form={form} onValuesChange={onSearchDebounce} colon={false}>
      <Space size={36}>
        <Form.Item name="venueName" label="场地名">
          <Input className="w200" placeholder="输入场地名称" allowClear />
        </Form.Item>
        <Form.Item name="suitableType" label="适合类型">
          <Select className="w200" mode="tags" placeholder="选择适合类型" allowClear options={SUITABLE_TYPE_OPTIONS} />
        </Form.Item>
        <Form.Item name="cityName" label="所在城市">
          <Select
            mode="multiple"
            placeholder="选择所在城市"
            options={[...CHINA_CITY_OPTIONS]}
            showSearch
            allowClear
            className="w200"
            filterOption={(inputValue, option) => Boolean(filterMatch(inputValue, option?.label || ''))}
          />
        </Form.Item>
        <Form.Item>
          <Button type="primary" onClick={onSearchDebounce} loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Space>
    </Form>
  )
}

export default VenueSearch
