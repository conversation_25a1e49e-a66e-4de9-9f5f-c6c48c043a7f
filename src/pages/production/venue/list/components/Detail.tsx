import AmapDisplay from '@/components/AmapDisplay'
import EnvImage from '@/components/EnvImage'
import { DATE_FORMAT_DAY } from '@/consts/date'
import useIndexStore from '@/store'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import {
  Button,
  Card,
  Descriptions,
  Divider,
  Drawer,
  Flex,
  Form,
  List,
  message,
  Popconfirm,
  Space,
  Spin,
  Tabs,
  Typography,
} from 'antd'
import dayjs from 'dayjs'
import React, { useMemo, useState } from 'react'
import useVenueListStore, { IVenueListItem, IVenueMedia, IVenueProductionItem } from '../store'
import AddMedia from './AddMedia'
import PhotoGrid from './PhotoGrid'
import RenameSubclassModal from './RenameSubclassModal'
import VideoGrid from './VideoGrid'

interface IVenueDetailProps {
  open: boolean
  venueId?: number
  isShow?: boolean // 是否纯展示不可操作
  onClose: () => void
  onEdit?: (venue: IVenueListItem) => void
  onDelete?: (venue: IVenueListItem) => void
  onRefresh?: () => void
}

const VenueDetail: React.FC<IVenueDetailProps> = ({ open, venueId, isShow = false, onClose, onEdit, onDelete, onRefresh }) => {
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [addModalType, setAddModalType] = useState<'photo' | 'video'>('photo')
  const [loading, setLoading] = useState(false)
  const [form] = Form.useForm()
  const [currentVenue, setCurrentVenue] = useState<IVenueListItem | undefined>()
  const [activeKey, setActiveKey] = useState('productions')

  // 重命名相关状态
  const [renameModalOpen, setRenameModalOpen] = useState(false)
  const [renameLoading, setRenameLoading] = useState(false)
  const [currentSubclass, setCurrentSubclass] = useState('')

  const { getVenueMediaByVenueId, saveVenueMedia, saveVenue, getVenueById } = useVenueListStore()
  const { authorBtn } = useIndexStore()

  // 新增：媒体列表和加载状态
  const [mediaList, setMediaList] = useState<IVenueMedia[]>([])

  // 获取场地详情数据
  const fetchVenueDetail = async (id?: number) => {
    if (!id) {
      setCurrentVenue(undefined)
      return
    }
    setLoading(true)
    try {
      const venueData = await getVenueById(id)
      setCurrentVenue(venueData || undefined)
    } catch (error) {
      console.error('获取场地详情失败:', error)
      setCurrentVenue(undefined)
    } finally {
      setLoading(false)
    }
  }

  // 当 venueId 变化时获取场地数据
  React.useEffect(() => {
    if (venueId) {
      fetchVenueDetail(venueId)
    } else {
      setCurrentVenue(undefined)
    }
  }, [venueId])

  // 拉取媒体列表
  const fetchMediaList = async (venueId?: number) => {
    if (!venueId) {
      return
    }
    setLoading(true)
    try {
      const res = await getVenueMediaByVenueId(venueId)

      if (res) {
        setMediaList(Array.isArray(res) ? res : [res])
      } else {
        setMediaList([])
      }
    } finally {
      setLoading(false)
    }
  }

  // 弹窗打开时拉取媒体并重置 tab
  React.useEffect(() => {
    if (open && currentVenue) {
      // 如果有拍摄项目，默认选择 productions，否则选择 photos
      const defaultKey = currentVenue?.productionsInfos?.length ? 'productions' : 'photos'
      setActiveKey(defaultKey)
      if (currentVenue?.id) {
        fetchMediaList(currentVenue.id)
      }
    }
  }, [open, currentVenue?.id, currentVenue?.productionsInfos?.length])

  // 渲染关联项目卡片
  const renderProductionCard = (production: IVenueProductionItem) => (
    <List.Item>
      <Card size="small" className="full-h">
        <Flex justify="space-between">
          <Space size={0} split={<Divider type="vertical" />}>
            <strong>《{production.productionName}》</strong>
            {production.productionCode && <Typography.Text>{production.productionCode}</Typography.Text>}
          </Space>
          {production.startDate && production.endDate && (
            <Typography.Text type="secondary">
              {`${dayjs(production.startDate).format(DATE_FORMAT_DAY)} ~ ${dayjs(production.endDate).format(
                DATE_FORMAT_DAY
              )}`}
            </Typography.Text>
          )}
        </Flex>
      </Card>
    </List.Item>
  )

  // 删除照片/视频
  const handleDeleteMedia = async (mediaId: number, urlToDelete?: string) => {
    try {
      if (!currentVenue?.id) {
        message.error('场地信息不完整')

        return
      }

      // 找到对应的 media 实体
      const media = mediaList.find(m => m.id === mediaId)

      if (!media) {
        message.error('未找到媒体类别')

        return
      }

      // mediaUrl 拆分
      const urls = (media.mediaUrl || '')
        .split(',')
        .map(u => u.trim())
        .filter(Boolean)
      // 如果没传 urlToDelete，默认删第一个
      const targetUrl = urlToDelete || urls[0]
      const newUrls = urls.filter(u => u !== targetUrl)
      const { deleteVenueMedia, saveVenueMedia } = useVenueListStore.getState()
      let result = false

      if (urls.length === 1) {
        // 只剩一个，直接删除整个 media
        result = await deleteVenueMedia(mediaId)
      } else {
        // 多个，移除后保存
        const newMedia: IVenueMedia = { ...media, mediaUrl: newUrls.join(',') }

        delete newMedia.mediaList
        result = await saveVenueMedia({ venueId: currentVenue.id, medias: [newMedia] })
      }

      if (result) {
        message.success('删除成功')
        fetchMediaList(currentVenue.id)
        onRefresh?.()
      }
    } catch (error) {
      message.error('删除失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 添加照片/视频
  const handleAddMedia = (type: 'photo' | 'video') => {
    setAddModalType(type)
    setAddModalOpen(true)
    form.resetFields()
  }

  // 保存新增媒体
  const handleSaveMedia = async () => {
    try {
      const values = await form.validateFields()

      if (!currentVenue?.id) {
        message.error('场地信息不完整')

        return
      }
      setLoading(true)

      let { subclass } = values
      const fileList = Array.isArray(values.mediaList) ? values.mediaList : [values.mediaList]

      if (Array.isArray(subclass)) {
        subclass = subclass[0]
      }
      const mediaType = addModalType === 'photo' ? 1 : 2
      const newUrls = fileList

      if (newUrls.length === 0) {
        message.error('请上传媒体文件')

        return
      }
      // 查找是否已有该类别
      const existMedia = mediaList.find(m => m.mediaType === mediaType && m.subclass === subclass)
      let medias: IVenueMedia[] = []

      if (existMedia) {
        // 追加到已有类别
        const oldUrls = (existMedia.mediaUrl || '')
          .split(',')
          .map(u => u.trim())
          .filter(Boolean)
        const mergedUrls = [...oldUrls, ...newUrls]
        const newItem = { ...existMedia, mediaUrl: mergedUrls.join(',') }

        delete newItem.mediaList
        medias = [newItem]
      } else {
        // 新建类别
        medias = [
          {
            venueId: currentVenue.id,
            mediaType,
            mediaUrl: newUrls.join(','),
            subclass,
            isDelete: false,
          },
        ]
      }
      const { saveVenueMedia } = useVenueListStore.getState()
      const result = await saveVenueMedia({ venueId: currentVenue.id, medias })

      if (result) {
        message.success('添加成功')
        fetchMediaList(currentVenue.id)
        setAddModalOpen(false)
        form.resetFields()
      }
    } catch (error) {
      message.error('添加失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 处理重命名分组
  const handleRename = (oldSubclass: string) => {
    setCurrentSubclass(oldSubclass)
    setRenameModalOpen(true)
  }

  // 保存重命名
  const handleSaveRename = async (newSubclass: string) => {
    if (!currentVenue?.id || !currentSubclass) {
      message.error('重命名信息不完整')
      return
    }

    if (newSubclass === currentSubclass) {
      setRenameModalOpen(false)
      return
    }

    try {
      setRenameLoading(true)

      // 找到需要重命名的媒体项
      const mediaToUpdate = mediaList.filter(media =>
        media.subclass === currentSubclass && !media.isDelete
      )

      if (mediaToUpdate.length === 0) {
        message.warning('没有找到需要重命名的媒体项')
        setRenameModalOpen(false)
        return
      }

      // 更新所有匹配的媒体项的subclass
      const updatedMedias: IVenueMedia[] = mediaToUpdate.map(media => ({
        ...media,
        subclass: newSubclass,
      }))

      // 调用保存API
      const result = await saveVenueMedia({
        venueId: currentVenue.id,
        medias: updatedMedias
      })

      if (result) {
        message.success('重命名成功')
        setRenameModalOpen(false)
        // 重新获取媒体列表以刷新数据
        fetchMediaList(currentVenue.id)
        onRefresh?.() // 刷新父组件数据
      } else {
        message.error('重命名失败，请重试')
      }
    } catch (error) {
      message.error('重命名失败，请重试')
    } finally {
      setRenameLoading(false)
    }
  }

  // 设为封面
  const handleSetCover = async (url: string) => {
    if (!currentVenue?.id) {
      message.error('场地信息不完整')
      return
    }

    try {
      setLoading(true)

      // 更新场地的封面图
      const updatedVenue = {
        ...currentVenue,
        photos: url
      }

      const result = await saveVenue(updatedVenue)

      if (result) {
        message.success('设置封面成功')
        // 更新当前场地数据
        setCurrentVenue(updatedVenue)
        onRefresh?.() // 刷新父组件数据
      }
    } catch (error) {
      message.error('设置封面失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 计算当前类型下所有类别（去重），使用 memo 缓存
  const categoryOptions = useMemo(() => {
    const options = mediaList.filter(m => !m.isDelete && m.subclass).map(m => m.subclass as string)

    // 去重
    return Array.from(new Set(options))
  }, [mediaList, addModalType])

  const tabItems = useMemo(() => {
    const items = []

    // 只有当有拍摄项目时才显示拍摄项目tab
    if (currentVenue?.productionsInfos?.length) {
      items.push({
        key: 'productions',
        label: '拍摄项目',
        children: (
          <List dataSource={currentVenue.productionsInfos} renderItem={renderProductionCard} pagination={false} />
        ),
      })
    }

    items.push(
      {
        key: 'photos',
        label: '照片',
        children: <PhotoGrid
          mediaList={mediaList}
          currentCoverPhoto={currentVenue?.photos}
          onAdd={() => handleAddMedia('photo')}
          onDelete={handleDeleteMedia}
          onRename={handleRename}
          onSetCover={handleSetCover}
        />,
      },
      {
        key: 'videos',
        label: '视频',
        children: <VideoGrid mediaList={mediaList} onAdd={() => handleAddMedia('video')} onDelete={handleDeleteMedia} onRename={handleRename} />,
      }
    )

    return items
  }, [currentVenue?.productionsInfos, mediaList])

  return (
    <>
      <Drawer
        title={currentVenue?.venueName}
        width={800}
        open={open}
        onClose={onClose}
        destroyOnHidden
        loading={loading}
        extra={
          currentVenue && !isShow && (
            <Space.Compact>
              {authorBtn.includes('删除') && !currentVenue?.productionsInfos?.length ? (
                <Popconfirm
                  title="警告"
                  description={`确定要删除【${currentVenue.venueName}】 吗？`}
                  onConfirm={() => onDelete?.(currentVenue)}
                  okText="确定删除"
                  cancelText="取消">
                  <Button type="default" shape="round" icon={<DeleteOutlined />} className="text-primary">
                    删除
                  </Button>
                </Popconfirm>
              ) : null}
              <Button
                type="default"
                shape="round"
                icon={<EditOutlined />}
                className="text-primary"
                onClick={() => onEdit?.(currentVenue)}>
                编辑
              </Button>
            </Space.Compact>
          )
        }>
        <Spin spinning={loading}>
          <Flex vertical gap={24}>
            <Descriptions bordered size="small" column={2}>
              <Descriptions.Item label="场地">{currentVenue?.venueName}</Descriptions.Item>
              <Descriptions.Item label="所在城市">{currentVenue?.cityName || '暂无'}</Descriptions.Item>
              <Descriptions.Item label="适合类型">
                {(currentVenue?.suitableTypeList || []).join('、')}
              </Descriptions.Item>
              <Descriptions.Item label="拍摄费用">
                {currentVenue?.cost ? `${currentVenue.cost}元` : '面议'}
              </Descriptions.Item>
              <Descriptions.Item label="联系人">{currentVenue?.contactPerson || '暂无'}</Descriptions.Item>
              <Descriptions.Item label="联系电话">{currentVenue?.contactPhone || '暂无'}</Descriptions.Item>
              <Descriptions.Item label="详细地址" span={2}>
                {currentVenue?.address || '暂无'}
              </Descriptions.Item>
            </Descriptions>
            {/* 地图位置 */}
            {currentVenue?.mapCoordinate && (
              <AmapDisplay
                showLocationInfo={false}
                dest={currentVenue.mapCoordinate}
                destName={currentVenue?.address || ''}
                height={300}
              />
            )}
            <Tabs activeKey={activeKey} onChange={setActiveKey} indicator={{ size: 32 }} items={tabItems} />
          </Flex>
        </Spin>
      </Drawer>
      {addModalOpen ? (
        <AddMedia
          form={form}
          type={addModalType}
          open={addModalOpen}
          onOk={handleSaveMedia}
          loading={loading}
          onCancel={() => {
            setAddModalOpen(false)
            form.resetFields()
          }}
          categoryOptions={categoryOptions}
        />
      ) : null}
      {renameModalOpen ? (
        <RenameSubclassModal
          open={renameModalOpen}
          loading={renameLoading}
          currentName={currentSubclass}
          onOk={handleSaveRename}
          onCancel={() => setRenameModalOpen(false)}
        />
      ) : null}
    </>
  )
}

export default VenueDetail
