import { Form, Input, Modal } from 'antd'
import React from 'react'

interface IRenameSubclassModalProps {
  open: boolean
  loading: boolean
  currentName: string
  onOk: (newName: string) => void
  onCancel: () => void
}

const RenameSubclassModal: React.FC<IRenameSubclassModalProps> = ({
  open,
  loading,
  currentName,
  onOk,
  onCancel,
}) => {
  const [form] = Form.useForm()

  // 当Modal打开时，设置初始值
  React.useEffect(() => {
    if (open && currentName) {
      form.setFieldsValue({ newName: currentName })
    }
  }, [open, currentName])

  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      onOk(values.newName)
    } catch (error) {
      // Form validation failed
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title="重命名分组"
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      okText="确定"
      cancelText="取消"
      width={400}>
      <Form form={form} layout="vertical" style={{ marginTop: 20 }}>
        <Form.Item
          name="newName"
          label="分组名称"
          rules={[
            { required: true, message: '请输入分组名称' },
            { max: 50, message: '分组名称不能超过50个字符' },
            { whitespace: true, message: '分组名称不能为空格' },
          ]}>
          <Input placeholder="请输入新的分组名称" autoFocus />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default RenameSubclassModal
