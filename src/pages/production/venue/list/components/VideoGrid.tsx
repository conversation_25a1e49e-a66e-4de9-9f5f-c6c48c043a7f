import VideoView from '@/components/VideoView'
import { CloseCircleOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons'
import { ListHeader } from '@fe/rockrose'
import { Button, Empty, Flex, Popconfirm, Space, Typography } from 'antd'
import React, { useMemo } from 'react'
import { IVenueMedia } from '../store'
import styles from './Detail.scss'

interface IVideoGridProps {
  mediaList: IVenueMedia[]
  onAdd: () => void
  onDelete: (mediaId: number, url: string) => void
  onRename: (oldSubclass: string) => void
}

const VideoGrid: React.FC<IVideoGridProps> = ({ mediaList, onAdd, onDelete, onRename }) => {
  // 使用 memo 优化视频数据的计算
  const { videos, videoCount } = useMemo(() => {
    const filteredVideos = mediaList.filter(m => m.mediaType === 2 && !m.isDelete)
    const count = filteredVideos.reduce(
      (sum, m) => sum + (m.mediaUrl ? m.mediaUrl.split(',').filter(Boolean).length : 0),
      0
    )
    const videosByCategory = filteredVideos.map(video => ({
      subclass: video.subclass || '默认',
      id: video.id || 0,
      urls: (video.mediaUrl || '')
        .split(',')
        .map(url => url.trim())
        .filter(Boolean),
    }))

    return { videos: videosByCategory, videoCount: count }
  }, [mediaList])

  // 使用 memo 优化渲染内容
  const content = useMemo(() => {
    if (videos.length === 0) {
      return <Empty />
    }

    return (
      <Flex vertical gap={24}>
        {videos.map(category => (
          <div key={`${category.id}-${category.subclass}`}>
            <Space align="center" style={{ margin: '0 0 16px' }}>
              <Typography.Title level={5} style={{ margin: 0 }}>
                {category.subclass}
              </Typography.Title>
              <Button
                type="text"
                size="small"
                icon={<EditOutlined />}
                onClick={() => onRename(category.subclass)}
                style={{ color: '#666' }}
                title="重命名分组"
              />
            </Space>
            <Space className={styles.mediaGrid} wrap>
              {category.urls.map((url, idx) => (
                <div className={`${styles.videoCard} hover-move hover-show-remove`} key={`${category.id}-${idx}`}>
                  <div className={styles.videoContainer}>
                    <VideoView videoUrl={url} width={'100%'} height={'100%'}></VideoView>
                  </div>
                  <Popconfirm
                    title="警告"
                    description="确定要删除该视频吗？"
                    onConfirm={() => onDelete(category.id, url)}
                    okText="确定删除"
                    cancelText="取消">
                    <Button
                      type="text"
                      icon={<CloseCircleOutlined className="fs-xlg text-secondary" />}
                      className="remove"
                    />
                  </Popconfirm>
                </div>
              ))}
            </Space>
          </div>
        ))}
      </Flex>
    )
  }, [videos, onDelete])

  return (
    <Flex vertical>
      <ListHeader title="视频" total={videoCount}>
        <Button type="primary" ghost icon={<PlusOutlined />} onClick={onAdd}>
          添加视频
        </Button>
      </ListHeader>
      {content}
    </Flex>
  )
}

export default VideoGrid
