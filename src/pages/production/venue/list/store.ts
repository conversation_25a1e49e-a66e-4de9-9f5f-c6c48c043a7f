import { create } from 'zustand'
import { post, get as requestGet } from '../../../../utils/request'

export const prVenueUploadAPI = '/PrActors/UploadFile'

// 场地分页查询参数（对应API的PagePrVenueDto）
export interface IVenueListSearchParams {
  pageIndex: number // 页码
  pageSize: number // 每页数量
  venueName?: string // 场地名称搜索关键词
  suitableType?: string[] // 场地类型数组
  cityName?: string[] // 城市数组
}

// 关联项目信息（对应API的PrProductionsByVenueId）
export interface IVenueProductionItem {
  id: number // 项目ID
  venueId: number // 场地ID
  productionName?: string // 短剧名称
  description?: string // 项目描述
  productionCode?: string // 代号
  startDate?: string // 开机日期
  endDate?: string // 结束日期
}

// API响应的场地信息（对应API的PrVenue）
export interface IVenueListItem {
  id: number // 场地ID
  venueName: string // 场地名称（必填）
  address?: string // 详细位置
  cityName?: string // 城市
  contactPerson?: string // 场地联系人
  contactPhone?: string // 场地联系人电话
  suitableType?: string // 适合拍摄类型(古装,现代, 武侠, 科幻, 民国, 乡村)多个逗号分割
  cost?: number // 费用
  photos?: string // 封面图（单张）
  videos?: string // 视频（多个逗号分割）
  isDelete: boolean // 是否删除
  creator?: string
  lastModifier?: string
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  suitableTypeList?: string[] // 适合拍摄类型列表（序列化处理）
  photoList?: string[] // 照片列表（序列化处理）
  videoList?: string[] // 视频列表（序列化处理）
  productionsInfos?: IVenueProductionItem[] // 短剧项目列表
  isInternal?: number // 是否内部 (0否，1是)
  mapCoordinate?: string // 地址坐标
}

// 场地名称选项（对应API的OptionDto）
export interface IVenueOptionItem {
  val?: string // 键（场地ID）
  label?: string // 值（场地名称）
}

// API响应类型
export interface IVenueListResponse {
  list: IVenueListItem[]
  total: number
  pageIndex: number
  pageSize: number
}

// 场地媒体信息（对应API的PrVenueMedia）
export interface IVenueMedia {
  creator?: string
  lastModifier?: string
  id?: number
  venueId: number
  mediaType: number // 1照片,2视频
  mediaUrl?: string
  subclass?: string // 子分类：默认、客厅、卧室、走廊
  sort?: number
  createTime?: string
  updateTime?: string
  isDelete: boolean
  mediaList?: string[]
}

// 保存场地媒体参数
export interface SavePrVenueMediaDto {
  venueId: number
  medias: IVenueMedia[]
}

export interface IVenueListStore {
  allVenueOptions: IVenueOptionItem[] // 所有场地选项列表
  venueClassMap: { [key: string]: Array<string> }

  /* API调用方法 */
  fetchVenueList: (params: IVenueListSearchParams) => Promise<IVenueListResponse | null>
  getVenueById: (id: number) => Promise<IVenueListItem | null>
  saveVenue: (venueData: IVenueListItem) => Promise<boolean>
  deleteVenue: (id: number) => Promise<boolean>
  getAllVenueNames: () => Promise<IVenueOptionItem[] | null>
  getVenueMediaByVenueId: (venueId: number) => Promise<Array<IVenueMedia>>
  saveVenueMedia: (params: SavePrVenueMediaDto) => Promise<boolean>
  deleteVenueMedia: (id: number) => Promise<boolean>

}

export default create<IVenueListStore>((set, get) => ({
  allVenueOptions: [],
  venueClassMap: {},
  // 获取场地列表
  fetchVenueList: async (params: IVenueListSearchParams) => {
    try {
      const { data, status } = await post<any, any>('/PrVenue/GetList', params)

      if (status && data) {
        return {
          list: data.dataList || [],
          total: data.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      console.error('获取场地列表失败:', error)

      return null
    }
  },

  // 根据ID获取场地详情
  getVenueById: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>('/PrVenue/GetVenueById', { params: { id } })

      if (status && data?.dataList) {
        return data.dataList
      }

      return null
    } catch (error) {
      console.error('获取场地详情失败:', error)

      return null
    }
  },

  // 保存场地信息
  saveVenue: async (venueData: IVenueListItem) => {
    try {
      // 处理数据：photos是单张封面图，videos是多张视频
      const submitData = {
        ...venueData,
        // photos字段现在是单张封面图，直接使用
        photos: venueData.photos || '',
        // videos字段保持原有逻辑，支持多张视频
        videos: Array.isArray(venueData.videoList) ? venueData.videoList.join(',') : venueData.videos,
      }

      const { data, status } = await post<any, any>('/PrVenue/Save', submitData)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('保存场地信息失败:', error)

      return false
    }
  },

  // 删除场地（软删除）
  deleteVenue: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrVenue/Delete?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除场地失败:', error)

      return false
    }
  },

  // 获取所有场地名称(不分页)
  getAllVenueNames: async () => {
    try {
      const { data, status } = await requestGet<any, any>('/PrVenue/AllName')

      if (status && data) {
        set({ allVenueOptions: Array.isArray(data) ? data : [] })

        return Array.isArray(data) ? data : []
      }

      return []
    } catch (error) {
      console.error('获取场地名称失败:', error)

      return []
    }
  },

  // 获取场地媒体列表
  getVenueMediaByVenueId: async (venueId: number) => {
    try {
      const { data, status } = await requestGet<any, any>('/PrVenue/GetMediaByVenueId', { params: { id: venueId } })

      if (status && data) {
        const arr: Array<any> = data?.dataList || []
        const newVenueMpa = { ...get().venueClassMap }

        newVenueMpa[venueId] = arr.filter(item => !!item?.subclass).map(item => item?.subclass || '')
        set({ venueClassMap: newVenueMpa })

        return data?.dataList || []
      }

      return []
    } catch (error) {
      console.error('获取场地媒体失败:', error)

      return []
    }
  },

  // 保存场地媒体
  saveVenueMedia: async (params: SavePrVenueMediaDto) => {
    try {
      const { status } = await post<any, any>('/PrVenue/SaveVenueMedia', params)

      return !!status
    } catch (error) {
      console.error('保存场地媒体失败:', error)

      return false
    }
  },

  // 删除场地媒体
  deleteVenueMedia: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrVenue/DeleteVenueMedia?id=${id}`)

      // WebResponseContent.status 为 true 视为成功
      return !!(status && data && data.status)
    } catch (error) {
      console.error('删除场地媒体失败:', error)

      return false
    }
  },


}))
