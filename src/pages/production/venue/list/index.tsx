import useSyncParams, { formatUrlState, initFormFromUrlState, parsePagination } from '@/hooks/useSyncParams'
import { Flex, Form, message } from 'antd'
import React, { useEffect, useState } from 'react'
import { PAGINATION } from '../../../../consts'
import Edit from './components/Add'
import Detail from './components/Detail'
import List from './components/List'
import Search from './components/Search'
import useVenueListStore, { IVenueListItem, IVenueListSearchParams } from './store'

// 不需要重新定义，使用全局的 IOperateType

const VenueInfo: React.FC = () => {
  const [urlState, setUrlState] = useSyncParams<IVenueListSearchParams>()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<IVenueListItem[]>([])
  const [pagination, setPagination] = useState(PAGINATION)

  const [show, setShow] = useState(false)
  const [operateData, setOperateData] = useState<any | IVenueListItem>()
  const [operateType, setOperateType] = useState<any | IOperateType>()

  const { fetchVenueList, saveVenue, deleteVenue } = useVenueListStore()

  // 初始化URL数据
  const initParams = () => {
    // 使用工具函数简化表单初始化
    initFormFromUrlState(urlState, form, {
      arrayFields: ['suitableType', 'cityName'],
      excludeFields: ['pageIndex', 'pageSize'],
    })

    // 使用工具函数处理分页
    const { current, pageSize } = parsePagination(urlState, {
      current: pagination.current,
      pageSize: pagination.pageSize,
    })

    handleSearch(current, pageSize)
  }

  const handleSearch = async (current = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true)
    try {
      const values = form.getFieldsValue()
      const searchParams: IVenueListSearchParams = {
        pageIndex: current,
        pageSize,
        ...values,
      }

      const result = await fetchVenueList(searchParams)

      if (result) {
        setDataSource(result.list)
        setPagination(prev => ({
          ...prev,
          total: result.total,
          current: result.pageIndex,
          pageSize: result.pageSize,
        }))

        // 同步URL参数 - 使用工具函数格式化
        setUrlState(
          formatUrlState(
            {
              venueName: values.venueName || '',
              suitableType: values.suitableType,
              cityName: values.cityName,
              pageSize: result.pageSize,
              pageIndex: result.pageIndex,
            },
            { arrayFields: ['suitableType', 'cityName'] }
          )
        )
      } else {
        setDataSource([])
      }
    } catch (error) {
      setDataSource([])
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (data: IVenueListItem) => {
    try {
      const result = await deleteVenue(data.id)

      if (result) {
        message.success('删除成功')
        await handleSearch(1)
        handleOperateClose(false)
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  const handleOperate = (type: string, data?: IVenueListItem) => {
    if (type === 'delete') {
      data && handleDelete(data)
    } else if (['create', 'edit', 'view'].includes(type)) {
      setOperateData(data || null)
      setOperateType(type as IOperateType)
      setShow(true)
    }
  }

  const handleOperateClose = (fresh = true) => {
    setOperateData(null)
    setOperateType('')
    setShow(false)
    if (fresh) {
      handleSearch()
    }
  }

  // 从详情页面编辑
  const handleEditFromDetail = (venue: IVenueListItem) => {
    setOperateData(venue)
    setOperateType('edit')
    // 保持抽屉打开状态，但切换到编辑模式
  }

  // 从详情页面删除
  const handleDeleteFromDetail = (venue: IVenueListItem) => {
    handleDelete(venue)
  }

  // 从详情页面刷新数据
  const handleRefreshFromDetail = () => {
    handleSearch()
  }

  // 表单提交
  const handleFormSubmit = async (values: IVenueListItem) => {
    try {
      const result = await saveVenue(values)

      if (result) {
        message.success(operateType == 'edit' ? '更新成功' : '添加成功')
        handleOperateClose()
        await handleSearch(1)
      } else {
        message.error('操作失败')
      }
    } catch (error) {
      message.error('操作失败')
    }
  }

  useEffect(() => {
    initParams()
  }, [])

  return (
    <Flex vertical>
      <Search form={form} onSearch={handleSearch} onOperate={handleOperate} onReset={() => handleSearch(1)} />
      <List
        data={dataSource}
        loading={loading}
        onOperate={handleOperate}
        onChange={handleSearch}
        pagination={pagination}
      />
      <Detail
        open={show && operateType === 'view'}
        venueId={operateData?.id}
        onClose={handleOperateClose}
        onEdit={handleEditFromDetail}
        onDelete={handleDeleteFromDetail}
        onRefresh={handleRefreshFromDetail}
      />
      <Edit
        open={show && ['edit', 'create'].includes(operateType)}
        data={operateData ?? void 0}
        onCancel={handleOperateClose}
        onSubmit={handleFormSubmit}
      />
    </Flex>
  )
}

export default VenueInfo
