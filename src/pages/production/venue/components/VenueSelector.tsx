import { useDebounceFn } from 'ahooks'
import { Select } from 'antd'
import React, { useEffect, useState } from 'react'
import useVenueStore from '../list/store'

interface VenueSelectorProps {
  value?: number
  onChange?: (value: number) => void
  onSelect?: (venueId: number, option: any) => void
  placeholder?: string
  disabled?: boolean
  initOption?: any // 初始化选项，用于编辑时回填
}

// 场地选择器
const VenueSelector: React.FC<VenueSelectorProps> = ({
  value,
  onChange,
  onSelect,
  placeholder = '请选择场地',
  disabled = false,
  initOption,
}) => {
  const [options, setOptions] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const { fetchVenueList } = useVenueStore()

  // 初始化选项
  useEffect(() => {
    if (initOption) {
      setOptions([initOption])
    } else {
      loadDefaultVenues()
    }
  }, [initOption])

  // 加载默认场地列表
  const loadDefaultVenues = async () => {
    setLoading(true)
    try {
      const result = await fetchVenueList({
        pageIndex: 1,
        pageSize: 20, // 默认加载20个场地
        venueName: '', // 空搜索，获取所有场地
      })

      if (result?.list) {
        const formattedOptions = result.list.map(venue => ({
          id: venue.id,
          label: venue.venueName,
          val: venue.id,
          venueName: venue.venueName,
          address: venue.address,
          suitableType: venue.suitableType,
          cost: venue.cost,
          photos: venue.photos,
          videos: venue.videos,
        }))

        setOptions(formattedOptions)
      }
    } catch (error) {
      console.error('加载默认场地列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 防抖搜索
  const { run: debouncedSearch } = useDebounceFn(
    async (searchText: string) => {
      if (!searchText.trim()) {
        // 如果清空搜索，恢复初始选项或重新加载默认列表
        if (initOption) {
          setOptions([initOption])
        } else {
          await loadDefaultVenues()
        }

        return
      }

      setLoading(true)
      try {
        const result = await fetchVenueList({
          pageIndex: 1,
          pageSize: 50,
          venueName: searchText.trim(), // 按场地名称模糊搜索
        })

        if (result?.list) {
          // 格式化选项以匹配原有结构
          const formattedOptions = result.list.map(venue => ({
            id: venue.id,
            label: venue.venueName,
            val: venue.id,
            venueName: venue.venueName,
            address: venue.address,
            suitableType: venue.suitableType,
            cost: venue.cost,
            photos: venue.photos,
            videos: venue.videos,
          }))

          // 如果有初始选项且当前值匹配，确保初始选项在结果中
          if (initOption && value === initOption.id && !formattedOptions.find(opt => opt.id === initOption.id)) {
            formattedOptions.unshift(initOption)
          }

          setOptions(formattedOptions)
        } else {
          setOptions(initOption ? [initOption] : [])
        }
      } catch (error) {
        console.error('搜索场地失败:', error)
        setOptions(initOption ? [initOption] : [])
      } finally {
        setLoading(false)
      }
    },
    { wait: 300 }
  )

  const handleSearch = (searchText: string) => {
    setSearchValue(searchText)
    debouncedSearch(searchText)
  }

  const handleChange = (selectedValue: number) => {
    onChange?.(selectedValue)
  }

  const handleSelect = (selectedValue: number, option: any) => {
    onSelect?.(selectedValue, option)
  }

  const getNotFoundContent = () => {
    if (loading) {
      return '搜索中...'
    }
    if (searchValue) {
      return '暂无匹配结果'
    }
    if (options.length === 0) {
      return '暂无场地数据'
    }

    return '请输入搜索关键词以筛选场地'
  }

  return (
    <Select
      placeholder={placeholder}
      showSearch
      loading={loading}
      allowClear
      disabled={disabled}
      options={options}
      fieldNames={{ label: 'label', value: 'id' }}
      value={value}
      onChange={handleChange}
      onSelect={handleSelect}
      onSearch={handleSearch}
      searchValue={searchValue}
      filterOption={false} // 禁用本地过滤
      notFoundContent={getNotFoundContent()}
    />
  )
}

export default VenueSelector
