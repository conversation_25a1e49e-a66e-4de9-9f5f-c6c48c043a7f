import { create } from 'zustand'
import { post, get as requestGet } from '../../../../utils/request'

export const commContractUploadAPI = '/PrActors/UploadFile'

// 合同分页查询参数（对应API的PageCommContractDto）
export interface IContractListSearchParams {
  pageIndex: number // 页码
  pageSize: number // 页面大小
  productionId?: Array<number> // 项目ID
  contractName?: string // 合同名称
  verify?: number[] // 合同类型
  contractNumb?: string // 合同编号
  personName?: string // 姓名
  status?: Array<number>
}

// API响应的合同信息（对应API的PrCommContract）
export interface IContractListItem {
  endDate?: any
  startDate?: any
  id?: number // 合同ID
  contractNumb?: string // 合同编号
  contractName?: string // 合同名称
  partyB?: string // 乙方
  partyBContacts?: string // 乙方联系人
  representative?: string // 乙方授权代表
  partyADate?: string // 签署日期 终止：（甲方）单个时间都用这个
  partyBDate?: string // 终止：终止日期 收款协议变更：乙方（签署时间）
  authorId?: number // 人员id
  verify?: number // 合同类型名称 101保密协议
  editName?: string // 编辑者
  platForm?: number // 101制片平台
  tempFile?: string // 临时文件
  formalFile?: string // 正式文件
  formalFileView?: string // 正式文件查看地址
  penNames?: string // 人员名称
  greement?: string // 协议
  greement2?: string // 协议2
  greementDate?: any // 终止：乙方签署日期
  greementDate2?: string // 协议日期2
  novelName?: string // 项目名称
  bookId?: string // 项目id
  status?: number // 合同状态 0待责编审核 1等待作者签章 2作者签署完成 3责编审核拒绝 4OA审批中 5OA审批通过 6OA审批拒绝 11公司正在签署 12签章完成 100合同归档 101作废
  ext?: string // 签约合同地址
  ext1?: string // 扩展字段1
  ext2?: string // 扩展字段2
  ext3?: string // 扩展字段3
  ext4?: string // 扩展字段4
  ext5?: string // oa审批单号
  ext6?: string // 责编id
  ext7?: string // 作者id
  ext8?: string // oa审批实例号
  ext9?: string // 签约地址
  ext10?: string // 签约地址
  reNovelName?: string // 重新小说名
  channel?: number // 渠道
  optionTime?: string // 操作时间
  sendTime?: string // 合同发送时间
  operator?: string // 操作人
  promiseStatus?: number // 承诺函状态 0待归档 1已归档
  promiseFile?: string // 承诺函地址
  signTime?: string // 签订完毕时间
  cpMode?: number // 合作模式 0保底 1分成
  scoreType?: number // D= 0, S = 1, A = 2, B = 3, C = 4
  storyType?: number // 类型0长篇小说 1短篇小说
  isExecuted?: number // 是否执行过，新掌中使用
  // 只读字段
  greementYear?: number // 协议年份
  greementYear2?: number // 协议年份2
  greementMon?: number // 协议月份
  greementMon2?: number // 协议月份2
  greementDay?: number // 协议日期
  greementDay2?: number // 协议日期2
  canEdit?: boolean // 是否可编辑
  canReSign?: boolean // 是否可重签
  isFile?: boolean // 是否有文件
  admSign?: boolean // 管理员签署
  canView?: boolean // 是否可查看
  canLoad?: boolean // 是否可加载
  creator?: string // 创建人
  lastModifier?: string // 最后修改人
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 合同名称选项（对应API的OptionDto）
export interface IContractOptionItem {
  val?: string // 键（合同ID）
  label?: string // 值（合同名称）
}

// API响应类型
export interface IContractListResponse {
  list: IContractListItem[]
  total: number
  pageIndex: number
  pageSize: number
}

export interface IContractListStore {
  allContractOptions: IContractOptionItem[] // 所有合同选项列表
  /* API调用方法 */
  fetchContractList: (params: IContractListSearchParams) => Promise<IContractListResponse | null>
  createContract: (contractData: IContractListItem) => Promise<boolean>
  updateContract: (contractData: IContractListItem) => Promise<boolean>
  checkContract: (id: number) => Promise<boolean>
  resignContract: (contractData: IContractListItem) => Promise<boolean>
  deleteContract: (id: number) => Promise<boolean>
  getAllContractNames: () => Promise<IContractOptionItem[] | null>
  fetchDownloadDocFile: (link: string) => Promise<string>
  sendContractMail: (id: number) => Promise<boolean>
  signContract: (id: number) => Promise<boolean>
  autoSignContract: (id: number) => Promise<boolean>
}

export default create<IContractListStore>(set => ({
  allContractOptions: [],

  // 获取合同列表
  fetchContractList: async (params: IContractListSearchParams) => {
    try {
      const { data, status } = await post<any, any>('/CommContract/GetList', params)

      if (status && data) {
        return {
          list: data.dataList || [],
          total: data.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      console.error('获取合同列表失败:', error)

      return null
    }
  },

  // 创建合同
  createContract: async (contractData: IContractListItem) => {
    try {
      const { data, status } = await post<any, any>('/CommContract/CreateContract', contractData)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('创建合同失败:', error)

      return false
    }
  },

  // 更新合同（使用创建接口，通过ID判断）
  updateContract: async (contractData: IContractListItem) => {
    try {
      const { data, status } = await post<any, any>('/CommContract/Resign', contractData)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('更新合同失败:', error)

      return false
    }
  },

  // 发送合同
  checkContract: async (id: number) => {
    try {
      const { data, status } = await post<any, any>(`/CommContract/Check?id=${id}`)

      return !!status
    } catch (error) {
      console.error('发送合同失败:', error)

      return false
    }
  },

  // 重签或更新合同
  resignContract: async (contractData: IContractListItem) => {
    try {
      const { data, status } = await post<any, any>('/CommContract/Resign', contractData)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('重签合同失败:', error)

      return false
    }
  },

  // 删除合同
  deleteContract: async (id: number) => {
    try {
      const { status } = await requestGet(`/CommContract/DeleteContract?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除合同失败:', error)

      return false
    }
  },

  // 获取所有合同名称(不分页) - 注意：API文档中没有提供此接口，可能需要后端补充
  getAllContractNames: async () => {
    try {
      // 暂时使用获取列表接口模拟，实际使用时可能需要后端提供专门的接口
      const { data, status } = await post<any, any>('/CommContract/GetList', {
        pageIndex: 1,
        pageSize: 1000, // 获取大量数据模拟全部
      })

      if (status && data) {
        const options = (data.dataList || []).map((item: IContractListItem) => ({
          val: item.id?.toString(),
          label: item.contractName || `合同${item.id}`,
        }))

        set({ allContractOptions: options })

        return options
      }

      return []
    } catch (error) {
      console.error('获取合同名称失败:', error)

      return []
    }
  },
  // 获取doc文件流
  fetchDownloadDocFile: async link => {
    const { data } = await requestGet('/PrCommons/DownloadDocFile', {
      responseType: 'arraybuffer',
      params: { link },
    })

    return data
  },

  // 签约地址邮件发送
  sendContractMail: async (id: number) => {
    try {
      const { status } = await requestGet(`/CommContract/SendMail?id=${id}`)

      return !!status
    } catch (error) {
      console.error('发送合同邮件失败:', error)

      return false
    }
  },

  // 公司签章
  signContract: async (id: number) => {
    try {
      const { status } = await post(`/CommContract/Sign?id=${id}`)

      return !!status
    } catch (error) {
      console.error('公司签章失败:', error)

      return false
    }
  },

  // 自动
  autoSignContract: async (id: number) => {
    try {
      const { status } = await post(`/CommContract/AutoSign?id=${id}`)

      return !!status
    } catch (error) {
      console.error('公司签章失败:', error)

      return false
    }
  },
}))
