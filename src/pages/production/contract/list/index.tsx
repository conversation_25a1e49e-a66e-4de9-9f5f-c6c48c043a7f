import useSyncParams, { initFormFromUrlState, parsePagination } from '@/hooks/useSyncParams'
import { envUrl } from '@/utils/request'
import { Flex, Form, message } from 'antd'
import React, { useEffect, useState } from 'react'
import { PAGINATION } from '../../../../consts'

import AddContract from './components/Add'
import ContractList from './components/List'
import PreviewContract from './components/Preview'
import ContractSearch from './components/Search'
import useContractListStore, { IContractListItem, IContractListSearchParams } from './store'

const ContractInfo: React.FC = () => {
  const [urlState, setUrlState] = useSyncParams<IContractListSearchParams>()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<IContractListItem[]>([])
  const [pagination, setPagination] = useState(PAGINATION)

  const [show, setShow] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [operateData, setOperateData] = useState<any | IContractListItem>()
  const [operateType, setOperateType] = useState<any | string>()
  const [previewData, setPreviewData] = useState<string | null>(null)

  const { fetchContractList, checkContract, resignContract, sendContractMail, signContract, autoSignContract, deleteContract } =
    useContractListStore()

  // 初始化URL数据
  const initParams = () => {
    // 使用工具函数简化表单初始化
    initFormFromUrlState(urlState, form, {
      numberArrayFields: ['verify', 'status'],
      arrayFields: ['productionId'],
      excludeFields: ['pageIndex', 'pageSize'],
    })

    // 使用工具函数处理分页
    const { current, pageSize } = parsePagination(urlState, {
      current: pagination.current,
      pageSize: pagination.pageSize,
    })

    handleSearch(current, pageSize)
  }

  const handleSearch = async (current = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true)
    try {
      const values = form.getFieldsValue()
      const searchParams: IContractListSearchParams = {
        pageIndex: current,
        pageSize,
        ...values,
      }

      const result = await fetchContractList(searchParams)

      if (result) {
        setDataSource(result.list)
        setPagination(prev => ({
          ...prev,
          total: result.total,
          current: result.pageIndex,
          pageSize: result.pageSize,
        }))

        // 同步URL参数
        setUrlState({
          productionId: values.productionId || [],
          contractName: values.contractName || '',
          verify: values.verify || [],
          contractNumb: values.contractNumb || '',
          personName: values.personName || '',
          status: values.status || [],
          pageSize: result.pageSize,
          pageIndex: result.pageIndex,
        })
      } else {
        setDataSource([])
      }
    } catch (error) {
      setDataSource([])
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (data: IContractListItem) => {
    if (!data.id) {
      message.error('合同ID不存在')
      return
    }

    try {
      const result = await deleteContract(data.id)

      if (result) {
        message.success('删除成功')
        await handleSearch(1)
        handleOperateClose(true)
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  const handleSend = async (record: IContractListItem) => {
    setLoading(true)
    const hide = message.loading('正在发送合同...', 0)

    try {
      const result = await checkContract(record.id!)

      if (result) {
        message.success('合同发送成功')
        handleSearch()
      }
    } catch (error) {
      message.error('发送失败')
    } finally {
      hide()
      setLoading(false)
    }
  }

  const handleSendMail = async (record: IContractListItem) => {
    if (!record.id) {
      message.error('合同ID不存在')

      return
    }

    const hide = message.loading('正在发送邮件...', 0)

    try {
      const result = await sendContractMail(record.id)

      if (result) {
        message.success('邮件发送成功')
      } else {
        message.error('邮件发送失败')
      }
    } catch (error) {
      message.error('邮件发送失败')
    } finally {
      hide()
    }
  }

  const handleSign = async (record: IContractListItem) => {
    if (!record.id) {
      message.error('合同ID不存在')

      return
    }

    const hide = message.loading('正在生成...', 0)

    try {
      const result = await signContract(record.id)

      if (result) {
        message.success('生成成功')
        handleSearch()
      } else {
        message.error('失败了')
      }
    } catch (error) {
      message.error('失败了')
    } finally {
      hide()
    }
  }
  const handleAutoSign = async (record: IContractListItem) => {
    if (!record.id) {
      message.error('合同ID不存在')

      return
    }

    const hide = message.loading('正在公司签章...', 0)

    try {
      const result = await autoSignContract(record.id)

      if (result) {
        message.success('公司签章成功')
        handleSearch()
      } else {
        message.error('公司签章失败')
      }
    } catch (error) {
      message.error('公司签章失败')
    } finally {
      hide()
    }
  }

  const handleResign = async (record: IContractListItem) => {
    try {
      const result = await resignContract(record)

      if (result) {
        message.success('重签成功')
        handleSearch()
      }
    } catch (error) {
      message.error('重签失败')
    }
  }

  const handleView = (record: IContractListItem) => {
    const previewFile = record.formalFileView || record.tempFile

    if (previewFile) {
      setPreviewData(previewFile.startsWith('http') ? previewFile : envUrl + previewFile)
      setShowPreview(true)
    }
  }

  const handleOperate = (type: string, data?: IContractListItem) => {
    if (type === 'delete') {
      data && handleDelete(data)
    } else if (type === 'send') {
      data && handleSend(data)
    } else if (type === 'sendMail') {
      data && handleSendMail(data)
    } else if (type === 'sign') {
      data && handleSign(data)
    } else if (type === 'autoSign') {
      data && handleAutoSign(data)
    } else if (type === 'resign') {
      data && handleResign(data)
    } else if (type === 'view') {
      data && handleView(data)
    } else if (['edit', 'detail', 'create'].includes(type)) {
      setOperateData(data || null)
      setOperateType(type)
      setShow(true)
    }
  }

  const handleOperateClose = (fresh = true) => {
    setOperateData(null)
    setOperateType('')
    setShow(false)
    if (fresh) {
      handleSearch()
    }
  }

  useEffect(() => {
    initParams()
  }, [])

  return (
    <Flex vertical gap={24}>
      <ContractSearch form={form} onSearch={handleSearch} onReset={() => handleSearch(1)} loading={loading} />

      <ContractList
        data={dataSource}
        loading={loading}
        total={pagination.total}
        pageIndex={pagination.current}
        pageSize={pagination.pageSize}
        onChange={handleSearch}
        onOperate={handleOperate}
      />

      <AddContract
        visible={show && ['edit', 'create'].includes(operateType)}
        editData={operateData ?? void 0}
        operateType={operateType}
        onCancel={() => handleOperateClose(false)}
        onSuccess={handleOperateClose}
        loading={loading}
      />

      {/* 合同预览弹窗 */}
      <PreviewContract show={showPreview} data={previewData} onCancel={() => setShowPreview(false)} />
    </Flex>
  )
}

export default ContractInfo
