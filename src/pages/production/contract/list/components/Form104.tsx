import filterMatch from '@/utils/filterMatch'
import { validateFddRealName } from '@/utils/validateFddRealName'
import { AutoComplete, DatePicker, Form, Input, InputNumber, Select, message, type FormInstance } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { ACTOR_ROLE_TYPE_CONFIG, CONTRACT_TYPE_OPTIONS } from '../../../../../consts'
import PersonForm from '../../../person/list/components/Add'
import usePersonListStore from '../../../person/list/store'
import usePersonStore from '../../../person/store'
import ProjectSelector from '../../../project/components/ProjectSelector'
import useProjectStore from '../../../project/list/store'
import { IContractListItem } from '../store'

interface Form102Props {
  form: FormInstance
  isEdit: boolean
  editData?: IContractListItem | null
}
// 制作方式选项
const productionModeOptions = [
  { value: '自制' },
  { value: '合作制作' },
  { value: '委托制作' },
  { value: '联合制作' },
  { value: '定制制作' },
  { value: '代理制作' },
]
const Form104: React.FC<Form102Props> = ({ form, isEdit, editData }) => {
  const [personOptions, setPersonOptions] = useState<any[]>([])
  const [showPersonForm, setShowPersonForm] = useState(false)
  const [editingPerson, setEditingPerson] = useState<any>(null)
  const { getProductionActorList } = useProjectStore()
  const { getPersonById, savePerson } = usePersonListStore()
  const { validRealName } = usePersonStore()

  const formBookId = Form.useWatch('bookId', form)

  // 处理编辑模式下的初始化
  useEffect(() => {
    if (editData?.bookId) {
      handleProjectChange(Number(editData.bookId), {
        label: editData.novelName,
        productionName: editData.novelName,
        id: Number(editData.bookId),
        startDate: editData.startDate,
        endDate: editData.endDate,
      })
    }
  }, [isEdit, editData])

  // 处理信息不全的情况
  const handleIncomplete = async (personId: number) => {
    message.info('该用户身份信息不全，未实名，请补全身份证信息')

    try {
      const personDetail = await getPersonById(personId)

      if (personDetail) {
        setEditingPerson(personDetail)
        setShowPersonForm(true)
      }
    } catch (error) {
      console.error('获取人员信息失败:', error)
    }
  }

  // 处理人员信息更新
  const handlePersonUpdate = async (values: any) => {
    try {
      form.resetFields(['authorId', 'penNames'])

      // 调用更新人员接口
      const success = await savePerson(values)

      if (success) {
        setShowPersonForm(false)
        setEditingPerson(null)
        message.success('人员信息更新成功')

        // 刷新人员列表
        if (formBookId) {
          await handleProjectChange(formBookId, {
            label: form.getFieldValue('novelName'),
            productionName: form.getFieldValue('novelName'),
            id: formBookId,
          })
        }
      } else {
        message.error('更新失败，请重试')
      }
    } catch (error) {
      console.error('更新人员信息失败:', error)
    }
  }

  // 监听项目选择变化
  const handleProjectChange = async (projectId: number, option: any) => {
    if (!projectId) {
      setPersonOptions([])
      form.resetFields(['novelName', 'authorId', 'penNames'])

      return
    }

    // 设置项目名称
    form.setFieldsValue({ novelName: option?.label || option?.productionName })
    form.setFieldsValue({ greementDate2: dayjs(option?.startDate || undefined) })
    form.setFieldsValue({ greementDate4: dayjs(option?.startDate || undefined) })
    form.setFieldsValue({ partyBDate: dayjs(option?.startDate || undefined) })
    form.setFieldsValue({ partyADate: dayjs(option?.startDate || undefined) })
    form.setFieldsValue({ greementDate5: dayjs(option?.startDate || undefined) })
    form.setFieldsValue({ greementDate3: dayjs(option?.endDate || undefined) })

    try {
      /*
       * 获取项目演员列表
       */
      const actorList = await getProductionActorList(projectId)
      const personIdMap: any = {}
      // 处理演员数据
      const actorOptions = (actorList || [])
        .filter(item => {
          if (item.personId) {
            if (personIdMap[item.personId]) {
              return false
            }
            personIdMap[item.personId] = true

            return true
          }

          return false
        })
        .map(actor => ({
          label: `${actor.personName}（${ACTOR_ROLE_TYPE_CONFIG[actor.roleType]?.label || '未知角色'}）${
            actor.isInternal ? '内部员工' : '外部人员'
          }_${actor.personId}`,
          value: actor.personId,
          personId: actor.personId,
          name: actor.personName,
          isInternal: actor.isInternal,
          type: 'actor',
          roleType: actor.roleType,
          roleTypeLabel: ACTOR_ROLE_TYPE_CONFIG?.[actor.roleType]?.label || '',
          stageName: actor.stageName,
          fddVerifyStatus: actor.fddVerifyStatus,
          fddCustomerVerifyUrl: actor.fddCustomerVerifyUrl,
          playRole: actor.playRole || '',
          quotedPrice: actor.quotedPrice || 0,
          dayCount: actor.dayCount || 0,
        }))

      // 合并选项
      setPersonOptions(actorOptions)
    } catch (error) {
      console.error('获取项目人员列表失败:', error)
      setPersonOptions([])
    }
  }

  const handleAuthorChange = (value: number, option: any) => {
    form.setFieldsValue({ penNames: option?.name })
    form.setFieldsValue({ ext1: option?.playRole || option?.roleTypeLabel || '' })
    form.setFieldsValue({ ext3: option?.quotedPrice || 0 })
    form.setFieldsValue({ ext2: option?.dayCount || 1 })
  }

  // 实名校验规则
  const validateRealName = (_: any, value: number) => {
    if (!value) {
      return Promise.resolve()
    }

    const selectedPerson = personOptions.find(item => item.value === value)

    if (!selectedPerson) {
      return Promise.reject(new Error('未找到选中的人员信息'))
    }

    return validateFddRealName(value, selectedPerson, handleIncomplete, validRealName)
  }

  return (
    <>
      <Form
        form={form}
        colon={false}
        layout="vertical"
        initialValues={{
          verify: 102, // 短剧演员剧组聘用合同
          greementDate: dayjs(), // 默认今天
        }}>
        <Form.Item name="bookId" label="项目" rules={[{ required: true, message: '请选择项目' }]}>
          <ProjectSelector
            placeholder="请选择项目"
            initOption={
              editData?.bookId
                ? {
                    id: Number(editData.bookId),
                    label: editData.novelName,
                    productionName: editData.novelName,
                    startDate: editData.startDate,
                    endDate: editData.endDate,
                  }
                : null
            }
            onSelect={(projectId, option) => handleProjectChange(projectId, option)}
          />
        </Form.Item>

        <Form.Item name="verify" label="合同类型" hidden>
          <Select placeholder="请选择合同类型" options={CONTRACT_TYPE_OPTIONS} />
        </Form.Item>
        <Form.Item name="novelName" label="项目名称" hidden></Form.Item>
        <Form.Item name="penNames" label="用户名称" hidden></Form.Item>

        <Form.Item
          name="authorId"
          label="签约演员"
          rules={[{ required: true, message: '请选择演员' }, { validator: validateRealName }]}>
          <Select
            placeholder="请选择演员"
            options={personOptions}
            showSearch
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.label || '')

              return Boolean(result)
            }}
            onSelect={handleAuthorChange}
            disabled={!formBookId}
          />
        </Form.Item>

        <Form.Item name="ext" label="制作方式" rules={[{ required: true, message: '请输入制作方式' }]}>
          <AutoComplete
            placeholder="请输入或选择制作方式"
            options={productionModeOptions}
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.value || '')

              return Boolean(result)
            }}
          />
        </Form.Item>

        <Form.Item name="ext1" label="担任角色" rules={[{ required: true, message: '请输入担任角色' }]}>
          <Input placeholder="如：女一、男一、女配等" />
        </Form.Item>

        <Form.Item name="ext2" label="拍摄天数" rules={[{ required: true, message: '请输入拍摄天数' }]}>
          <InputNumber placeholder="天数" min={0} precision={0} className="w200" />
        </Form.Item>

        <Form.Item name="ext3" label="日薪(元/日)" rules={[{ required: true, message: '请输入日薪' }]}>
          <InputNumber placeholder="日薪" min={0} precision={0} className="w200" />
        </Form.Item>
         <Form.Item name="ext4" label="每日工作时长" rules={[{ required: true, message: '请输入每日工作时长' }]}>
          <InputNumber placeholder="每日工作时长" min={0} precision={0} className="w200" addonAfter='时'/>
        </Form.Item>
        <Form.Item name="ext5" label="每日超时计费起点" rules={[{ required: true, message: '每日超时计费起点' }]}>
          <InputNumber placeholder="每日超时计费起点" min={0} precision={0} className="w200" addonAfter='时'/>
        </Form.Item>
          <Form.Item name="ext6" label="超时部分时薪" rules={[{ required: true, message: '请输入超过N小时部分' }]}>
          <InputNumber placeholder="超时部分时薪" min={0} precision={0} className="w200" addonAfter='元'/>
        </Form.Item>
        

        <Form.Item name="greementDate" label="定妆和围读日期" rules={[{ required: true, message: '请选择合同签署日期' }]}>
          <DatePicker placeholder="请选择定妆和围读日期" style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>

        <Form.Item
          name="greementDate2"
          label="拍摄开始日期"
          rules={[{ required: true, message: '请选择拍摄开始日期' }]}>
          <DatePicker placeholder="请选择拍摄开始日期" style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>

        <Form.Item
          name="greementDate3"
          label="拍摄结束日期"
          rules={[{ required: true, message: '请选择拍摄结束日期' }]}>
          <DatePicker placeholder="请选择拍摄结束日期" style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>

        <Form.Item name="greementDate4" label="定妆日期" rules={[{ required: true, message: '请选择定妆日期' }]}>
          <DatePicker placeholder="请选择定妆日期" style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>

        <Form.Item name="greementDate5" label="开机日期" rules={[{ required: true, message: '请选择开机日期' }]}>
          <DatePicker placeholder="请选择开机日期" style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>

        <Form.Item name="partyADate" label="甲方签署日期" rules={[{ required: true, message: '请选择甲方签署日期' }]}>
          <DatePicker placeholder="请选择甲方签署日期" style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>

        <Form.Item name="partyBDate" label="乙方报到日期" rules={[{ required: true, message: '请选择乙方报到日期' }]}>
          <DatePicker placeholder="请选择乙方到达指定地点报到日期" style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>
      </Form>

      {/* 人员编辑表单 */}
      {showPersonForm ? (
        <PersonForm
          open={showPersonForm}
          person={editingPerson}
          onCancel={() => {
            setShowPersonForm(false)
            setEditingPerson(null)
          }}
          onSubmit={handlePersonUpdate}
        />
      ) : null}
    </>
  )
}

export default Form104
