import useIndexStore from '@/store'
import { MailOutlined, PlusOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  Col,
  Divider,
  Dropdown,
  Flex,
  List,
  Row,
  Space,
  Steps,
  Typography,
  type MenuProps,
} from 'antd'
import dayjs from 'dayjs'
import React from 'react'
import { CONTRACT_STATUS_CONFIG, CONTRACT_TYPE_OPTIONS, PAGINATION } from '../../../../../consts'
import { IContractListItem } from '../store'

interface ContractListProps {
  loading?: boolean
  data: IContractListItem[]
  total: number
  pageIndex: number
  pageSize: number
  onChange: (page: number, size: number) => void
  onOperate: (type: string, data?: IContractListItem) => void
}

interface StepItem {
  title: string
  description?: string | any
}

const ContractList: React.FC<ContractListProps> = ({
  loading = false,
  data = [],
  total,
  pageIndex,
  pageSize,
  onChange,
  onOperate,
}) => {
  const { authorBtn } = useIndexStore()

  // 合同类型下拉菜单项
  const contractTypeItems: MenuProps['items'] = CONTRACT_TYPE_OPTIONS.map(option => ({
    key: option.value,
    label: option.label,
    disabled: ![101, 102, 103,104,105].includes(option.value), // 101 是保密合同，其他类型禁用
    onClick: () => onOperate('create', { verify: option.value, greementDate: dayjs() } as IContractListItem),
  }))

  const stepItems = [
    { title: '合同' },
    // { title: 'OA审批' },
    { title: '发送合同' },
    { title: '用户签约完成' },
    { title: '待公司签约' },
    { title: '归档' },
    // { title: '承诺函归档' },
  ]

  function getStepIndex(status?: number) {
    switch (status) {
      case 0:
        return 0 // 责编审核
      case 1:
        return 1 // 发送合同
      case 2:
        return 2 // 签约
      case 3:
        return 0 // 责编审核（异常/驳回）
      case 4:
        return 0 // OA审批
      case 5:
        return 1 // 归档
      case 6:
        return 0 // OA审批（异常）
      case 11:
        return 3 // 签约
      case 12:
        return 4 // 归档
      case 100:
        return 5 // 归档
      case 101:
        return -1 // 作废
      default:
        return 0
    }
  }

  function getStepErrorState(status?: number) {
    return [3, 6, 101].includes(status ?? 0) ? 'error' : 'process'
  }

  function getStepStatusLabel(status?: number) {
    // 可根据实际业务调整
    switch (status) {
      case 0:
        // return '待审核'
        return '待发送'
      case 1:
        return ''
      // return '待发送合同'
      case 2:
        // return '用户签约完成'
        return ''
      case 3:
        return '已驳回'
      case 4:
        return '待OA审批'
      case 5:
        return '已归档'
      case 6:
        return 'OA异常'
      case 11:
        // return '待公司签约'
        return ''
      case 12:
        return '公司签约完成'
      case 100:
        return '归档'
      case 101:
        return '作废'
      default:
        return ''
    }
  }

  function getStepItems(record: IContractListItem): StepItem[] {
    const status = record?.status

    if (status === 101) {
      return [{ title: '作废' }]
    }

    const currentIndex = getStepIndex(status)

    return stepItems.map((item, idx) => {
      if (idx === currentIndex && currentIndex == 1 && record.ext9) {
        return {
          ...item,
          description: (
            <Space direction="vertical" size={3}>
              <Typography.Text copyable={{ text: record.ext9 }}>签约地址</Typography.Text>
              <Typography.Link onClick={() => onOperate('sendMail', record)}>
                <Space>
                  <MailOutlined />
                  发送邮件
                </Space>
              </Typography.Link>
            </Space>
          ),
        }
      }
      if (idx === currentIndex && currentIndex == 3 && record.ext10) {
        return {
          ...item,
          description: (
            <Typography.Link
              onClick={() => window.open(record.ext10, '_blank')}
              style={{ textDecoration: 'underline' }}>
              签约地址
            </Typography.Link>
          ),
        }
      }

      return {
        ...item,
        description: idx === currentIndex ? getStepStatusLabel(status) : '',
      }
    })
  }

  function getActions(record: IContractListItem) {
    const currentIndex = getStepIndex(record.status ?? 0)

    return [
      <Space key="actions" size={0} split={<Divider type="vertical" />} wrap>
        {currentIndex === 0 && <Typography.Link onClick={() => onOperate('edit', record)}>编辑</Typography.Link>}
        {(currentIndex === 0 || currentIndex === -1) && (
          <Typography.Link onClick={() => onOperate('delete', record)} type="danger">
            删除
          </Typography.Link>
        )}
        {![0, 101].includes(record.status ?? 0) && (
          <Typography.Link onClick={() => onOperate('resign', record)}>重签</Typography.Link>
        )}
        {[0].includes(currentIndex) && (
          <Typography.Link onClick={() => onOperate('send', record)}>发送合同</Typography.Link>
        )}
        {(currentIndex === 2 || (currentIndex == 3 && !record.ext10)) && authorBtn.includes('生成签章') && (
          <Typography.Link onClick={() => onOperate('sign', record)}>生成签章</Typography.Link>
        )}
        {(currentIndex === 2 || (currentIndex == 3 && !record.ext10)) && authorBtn.includes('自动签章') && (
          <Typography.Link onClick={() => onOperate('autoSign', record)}>自动签章</Typography.Link>
        )}
        {(record.formalFileView || record.tempFile) && (
          <Typography.Link onClick={() => onOperate('view', record)}>查看</Typography.Link>
        )}
      </Space>,
    ]
  }

  return (
    <Flex vertical>
      <ListHeader title="合同列表" total={total} unitText="个">
        <Dropdown menu={{ items: contractTypeItems }} placement="bottomRight">
          <Button type="primary" ghost icon={<PlusOutlined />}>
            添加合同
          </Button>
        </Dropdown>
      </ListHeader>
      <List
        loading={loading}
        dataSource={data}
        split={false}
        size="small"
        className="list-sm"
        pagination={
          total
            ? {
                ...PAGINATION,
                total,
                current: pageIndex,
                pageSize,
                onChange,
              }
            : false
        }
        renderItem={record => {
          const currentIndex = getStepIndex(record.status ?? 0)

          return (
            <List.Item>
              <Card className="full-h hover-move" size="small">
                <Row>
                  <Col span={8}>
                    <Flex vertical gap={12}>
                      <Typography.Text strong>{record.contractName}</Typography.Text>
                      <Dict title="编号" value={record.contractNumb} />
                      <Space size={0} split={<Divider type="vertical" />}>
                        <Badge
                          status={
                            typeof record.status === 'number' && CONTRACT_STATUS_CONFIG?.[record.status]
                              ? (CONTRACT_STATUS_CONFIG as any)[record.status]?.color || 'default'
                              : 'default'
                          }
                          text={
                            typeof record.status === 'number' && CONTRACT_STATUS_CONFIG?.[record.status]
                              ? (CONTRACT_STATUS_CONFIG as any)[record.status]?.label || '未知'
                              : '未知'
                          }></Badge>
                        {record.optionTime && (
                          <Dict title="发起于" value={dayjs(record.optionTime).format('YYYY-MM-DD HH:mm:ss')} />
                        )}
                      </Space>
                    </Flex>
                  </Col>
                  <Col span={12}>
                    <Steps
                      size="small"
                      labelPlacement="vertical"
                      current={getStepIndex(record.status ?? 0)}
                      status={getStepErrorState(record.status ?? 0)}
                      items={getStepItems(record)}
                    />
                  </Col>
                  <Col span={4} className="text-right">
                    {getActions(record)}
                  </Col>
                </Row>
              </Card>
            </List.Item>
          )
        }}
      />
    </Flex>
  )
}

export default ContractList
