import { CONTRACT_TYPE_CONFIG } from '@/consts'
import { <PERSON><PERSON>, Drawer, Form, message } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import usePersonStore from '../../../person/store'
import useContractListStore, { IContractListItem } from '../store'
import Form101 from './Form101'
import Form102 from './Form102'
import Form103 from './Form103'
import Form104 from './Form104'
import Form105 from './Form105'

interface ContractAddProps {
  visible: boolean
  editData?: IContractListItem | null
  operateType?: string
  onCancel: () => void
  onSuccess: () => void
  loading?: boolean
}

const ContractAdd: React.FC<ContractAddProps> = ({
  visible,
  editData,
  operateType,
  onCancel,
  onSuccess,
  loading = false,
}) => {
  const [form] = Form.useForm()
  const isEdit = operateType === 'edit'
  const { validRealName } = usePersonStore()
  const { createContract, updateContract } = useContractListStore()
  const [submitting, setSubmitting] = useState(false)

  useEffect(() => {
    if (visible && editData) {
      // 编辑模式：填充表单数据
      const formValues: any = {
        ...editData,
        bookId: editData.bookId ? Number(editData.bookId) : null,
      }

      // 处理日期字段格式化
      if (editData.verify === 101) {
        // 101类型：只处理greementDate
        formValues.greementDate = editData.greementDate ? dayjs(editData.greementDate) : null
      } else if (editData.verify === 102 || editData.verify === 104) {
        // 102类型：处理多个日期字段
        const dateFields = [
          'greementDate',
          'greementDate2',
          'greementDate3',
          'greementDate4',
          'greementDate5',
          'partyADate',
          'partyBDate',
        ]

        dateFields.forEach(field => {
          const value = (editData as any)[field]

          if (value) {
            formValues[field] = dayjs(value)
          }
        })
        if (editData.ext2) {
          formValues.ext2 = Number(editData.ext2)
        }
        if (editData.ext3) {
          formValues.ext3 = Number(editData.ext3)
        }
        if (editData.verify === 104) {
          if (editData.ext4) {
            formValues.ext4 = Number(editData.ext4)
          }
          if (editData.ext5) {
            formValues.ext5 = Number(editData.ext5)
          }
          if (editData.ext6) {
            formValues.ext6 = Number(editData.ext6)
          }
        }
      } else if (editData.verify === 103) {
        // 102类型：处理多个日期字段
        const dateFields = ['greementDate', 'greementDate2', 'partyADate']

        dateFields.forEach(field => {
          const value = (editData as any)[field]

          if (value) {
            formValues[field] = dayjs(value)
          }
        })

        if (editData.ext2) {
          formValues.ext2 = Number(editData.ext2)
        }
      } else if (editData.verify === 105) {
        // 102类型：处理多个日期字段
        const dateFields = ['greementDate', 'greementDate2']

        dateFields.forEach(field => {
          const value = (editData as any)[field]

          if (value) {
            formValues[field] = dayjs(value)
          }
        })
      }

      form.setFieldsValue(formValues)
    } else if (!visible) {
      setSubmitting(false)
      form.resetFields()
    }
  }, [visible, editData, form])

  const handleOk = async () => {
    try {
      if (submitting) {
        return
      }

      const values = await form.validateFields()

      // 处理表单数据
      const formData = {
        ...editData,
        ...values,
        authorType: 0,
      }

      // 对 101 表单的数据进行特殊处理
      if (formData.verify === 101) {
        // bookId 转为字符串
        if (formData.bookId) {
          formData.bookId = String(formData.bookId)
        }
        // 格式化日期
        if (formData.greementDate) {
          formData.greementDate = dayjs(formData.greementDate).format('YYYY-MM-DD')
        }
      }

      // 对 102 表单的数据进行特殊处理
      if (formData.verify === 102 || formData.verify === 104) {
        // bookId 转为字符串
        if (formData.bookId) {
          formData.bookId = String(formData.bookId)
        }
        if (formData.ext2) {
          formData.ext2 = String(formData.ext2)
        }
        if (formData.ext3) {
          formData.ext3 = String(formData.ext3)
        }
        if (formData.verify === 104) {
          if (formData.ext4) {
            formData.ext4 = String(formData.ext4)
          }
          if (formData.ext5) {
            formData.ext5 = String(formData.ext5)
          }
          if (formData.ext6) {
            formData.ext6 = String(formData.ext6)
          }
        }

        // 格式化多个日期字段
        const dateFields = [
          'greementDate',
          'greementDate2',
          'greementDate3',
          'greementDate4',
          'greementDate5',
          'partyADate',
          'partyBDate',
        ]

        dateFields.forEach(field => {
          if (formData[field]) {
            formData[field] = dayjs(formData[field]).format('YYYY-MM-DD')
          }
        })
      }

      if (formData.verify === 103) {
        // bookId 转为字符串
        if (formData.bookId) {
          formData.bookId = String(formData.bookId)
        }

        if (formData.ext2) {
          formData.ext2 = String(formData.ext2)
        }
        // 格式化多个日期字段
        const dateFields = ['greementDate', 'greementDate2', 'partyADate']

        dateFields.forEach(field => {
          if (formData[field]) {
            formData[field] = dayjs(formData[field]).format('YYYY-MM-DD')
          }
        })
      }
      if (formData.verify === 105) {
        // bookId 转为字符串
        if (formData.bookId) {
          formData.bookId = String(formData.bookId)
        }

        // 格式化多个日期字段
        const dateFields = ['greementDate', 'greementDate2']

        dateFields.forEach(field => {
          if (formData[field]) {
            formData[field] = dayjs(formData[field]).format('YYYY-MM-DD')
          }
        })
      }

      setSubmitting(true)
      try {
        const result = isEdit ? await updateContract(formData) : await createContract(formData)

        if (result) {
          message.success(isEdit ? '更新成功' : '新建合同成功')
          onSuccess()
        }
      } catch (error) {
        message.error('操作失败')
      } finally {
        setSubmitting(false)
      }
    } catch (error) {
      message.error('请检查表单填写是否正确')
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  // 根据合同类型渲染不同的表单
  const renderForm = () => {
    const verify = editData?.verify || 101 // 默认保密合同

    switch (verify) {
      case 101:
        return <Form101 form={form} isEdit={isEdit} editData={editData} />
      case 102:
        return <Form102 form={form} isEdit={isEdit} editData={editData} />
      case 103:
        return <Form103 form={form} isEdit={isEdit} editData={editData} />
      case 104:
        return <Form104 form={form} isEdit={isEdit} editData={editData} />
      case 105:
        return <Form105 form={form} isEdit={isEdit} editData={editData} />

      default:
        return null
    }
  }

  return (
    <Drawer
      title={`${isEdit ? '编辑' : '签约'}${CONTRACT_TYPE_CONFIG[editData?.verify || 101]}`}
      open={visible}
      width={800}
      destroyOnHidden
      onClose={handleCancel}
      extra={
        <Button type="primary" onClick={() => handleOk()} loading={submitting}>
          立即保存
        </Button>
      }>
      {renderForm()}
    </Drawer>
  )
}

export default ContractAdd
