import filterMatch from '@/utils/filterMatch'
import { validateFddRealName } from '@/utils/validateFddRealName'
import { DatePicker, Form, InputNumber, Select, message, type FormInstance } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { CONTRACT_TYPE_OPTIONS, ROLE_TYPE_CONFIG, RoleType } from '../../../../../consts'
import PersonForm from '../../../person/list/components/Add'
import usePersonListStore from '../../../person/list/store'
import usePersonStore from '../../../person/store'
import ProjectSelector from '../../../project/components/ProjectSelector'
import useProjectStore from '../../../project/list/store'
import { IContractListItem } from '../store'

interface Form102Props {
  form: FormInstance
  isEdit: boolean
  editData?: IContractListItem | null
}

const Form102: React.FC<Form102Props> = ({ form, isEdit, editData }) => {
  const [personOptions, setPersonOptions] = useState<any[]>([])
  const [showPersonForm, setShowPersonForm] = useState(false)
  const [editingPerson, setEditingPerson] = useState<any>(null)
  const { getProductionPersonBasicList } = useProjectStore()
  const { getPersonById, savePerson } = usePersonListStore()
  const { validRealName } = usePersonStore()

  const formBookId = Form.useWatch('bookId', form)

  // 处理编辑模式下的初始化
  useEffect(() => {
    if (editData?.bookId) {
      handleProjectChange(Number(editData.bookId), {
        label: editData.novelName,
        productionName: editData.novelName,
        id: Number(editData.bookId),
        startDate: editData.startDate,
        endDate: editData.endDate,
      })
    }
  }, [isEdit, editData])

  // 处理信息不全的情况
  const handleIncomplete = async (personId: number) => {
    message.info('该用户身份信息不全，未实名，请补全身份证信息')

    try {
      const personDetail = await getPersonById(personId)

      if (personDetail) {
        setEditingPerson(personDetail)
        setShowPersonForm(true)
      }
    } catch (error) {
      console.error('获取人员信息失败:', error)
    }
  }

  // 处理人员信息更新
  const handlePersonUpdate = async (values: any) => {
    try {
      form.resetFields(['authorId', 'penNames'])

      // 调用更新人员接口
      const success = await savePerson(values)

      if (success) {
        setShowPersonForm(false)
        setEditingPerson(null)
        message.success('人员信息更新成功')

        // 刷新人员列表
        if (formBookId) {
          await handleProjectChange(formBookId, {
            label: form.getFieldValue('novelName'),
            productionName: form.getFieldValue('novelName'),
            id: formBookId,
          })
        }
      } else {
        message.error('更新失败，请重试')
      }
    } catch (error) {
      console.error('更新人员信息失败:', error)
    }
  }

  // 监听项目选择变化
  const handleProjectChange = async (projectId: number, option: any) => {
    if (!projectId) {
      setPersonOptions([])
      form.resetFields(['novelName', 'authorId', 'penNames'])

      return
    }

    // 设置项目名称
    form.setFieldsValue({ novelName: option?.label || option?.productionName })
    form.setFieldsValue({ greementDate: dayjs(option?.startDate || undefined) })
    form.setFieldsValue({ greementDate2: dayjs(option?.endDate || undefined) })
    form.setFieldsValue({ partyADate: dayjs(option?.startDate || undefined) })

    try {
      /*
       * 获取项目工作人员列表
       */
      const personList = await getProductionPersonBasicList(projectId)
      const personIdMap: any = {}
      // 处理工作人员数据
      const personOptions = (personList || [])
        .filter(item => {
          if (item.personId) {
            if (personIdMap[item.personId]) {
              return false
            }
            personIdMap[item.personId] = true

            return true
          }

          return false
        })
        .map(person => ({
          label: `${person.personName}（${ROLE_TYPE_CONFIG[person.roleType]?.label || '未知角色'}）${
            person.isInternal ? '内部员工' : '外部人员'
          }_(${person.personId})`,
          value: person.personId,
          personId: person.personId,
          name: person.personName,
          isInternal: person.isInternal,
          type: 'person',
          roleType: person.roleType,
          roleTypeLabel: ROLE_TYPE_CONFIG?.[person.roleType]?.label || '',
          fddVerifyStatus: person.fddVerifyStatus,
          fddCustomerVerifyUrl: person.fddCustomerVerifyUrl,
          quotedPrice: person.quotedPrice || 0,
          dayCount: person.dayCount || 0,
        }))

      // 合并选项
      setPersonOptions(personOptions)
    } catch (error) {
      console.error('获取项目人员列表失败:', error)
      setPersonOptions([])
    }
  }

  const handleAuthorChange = (value: number, option: any) => {
    form.setFieldsValue({ penNames: option?.name })
    form.setFieldsValue({ ext1: ROLE_TYPE_CONFIG?.[option?.roleType as RoleType]?.label || '' })
    form.setFieldsValue({ ext2: option?.quotedPrice || 0 })
  }

  // 实名校验规则
  const validateRealName = (_: any, value: number) => {
    if (!value) {
      return Promise.resolve()
    }

    const selectedPerson = personOptions.find(item => item.value === value)

    if (!selectedPerson) {
      return Promise.reject(new Error('未找到选中的人员信息'))
    }

    return validateFddRealName(value, selectedPerson, handleIncomplete, validRealName)
  }

  return (
    <>
      <Form
        form={form}
        colon={false}
        layout="vertical"
        initialValues={{
          verify: 102, // 短剧演员剧组聘用合同
          greementDate: dayjs(), // 默认今天
        }}>
        <Form.Item name="bookId" label="项目" rules={[{ required: true, message: '请选择项目' }]}>
          <ProjectSelector
            placeholder="请选择项目"
            initOption={
              editData?.bookId
                ? {
                    id: Number(editData.bookId),
                    label: editData.novelName,
                    productionName: editData.novelName,
                    startDate: editData.startDate,
                    endDate: editData.endDate,
                  }
                : null
            }
            onSelect={(projectId, option) => handleProjectChange(projectId, option)}
          />
        </Form.Item>

        <Form.Item name="verify" label="合同类型" hidden>
          <Select placeholder="请选择合同类型" options={CONTRACT_TYPE_OPTIONS} />
        </Form.Item>
        <Form.Item name="novelName" label="项目名称" hidden></Form.Item>
        <Form.Item name="penNames" label="用户名称" hidden></Form.Item>

        <Form.Item
          name="authorId"
          label="工作人员"
          rules={[{ required: true, message: '请选择工作人员' }, { validator: validateRealName }]}>
          <Select
            placeholder="请选择工作人员"
            options={personOptions}
            showSearch
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.label || '')

              return Boolean(result)
            }}
            onSelect={handleAuthorChange}
            disabled={!formBookId}
          />
        </Form.Item>

        <Form.Item name="ext1" label="担任角色" hidden></Form.Item>
        <Form.Item name="ext2" label="日薪(元/日)" rules={[{ required: true, message: '请输入日薪' }]}>
          <InputNumber placeholder="日薪" min={0} precision={0} className="w200" />
        </Form.Item>

        <Form.Item name="greementDate" label="开始日期" rules={[{ required: true, message: '请选择开始日期' }]}>
          <DatePicker placeholder="请选择开始日期" style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>

        <Form.Item name="greementDate2" label="结束日期" rules={[{ required: true, message: '请选择结束日期' }]}>
          <DatePicker placeholder="请选择结束日期" style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>

        <Form.Item name="partyADate" label="甲方签署日期" rules={[{ required: true, message: '请选择甲方签署日期' }]}>
          <DatePicker placeholder="请选择甲方签署日期" style={{ width: '100%' }} format="YYYY-MM-DD" />
        </Form.Item>
      </Form>

      {/* 人员编辑表单 */}
      {showPersonForm ? (
        <PersonForm
          open={showPersonForm}
          person={editingPerson}
          onCancel={() => {
            setShowPersonForm(false)
            setEditingPerson(null)
          }}
          onSubmit={handlePersonUpdate}
        />
      ) : null}
    </>
  )
}

export default Form102
