import filterMatch from '@/utils/filterMatch'
import { useDebounceFn } from 'ahooks'
import { Button, Form, Input, Select, Space } from 'antd'
import React, { useEffect } from 'react'
import { CONTRACT_TYPE_OPTIONS } from '../../../../../consts'
import usePersonStore from '../../../person/list/store'
import useProjectStore from '../../../project/list/store'

interface ContractSearchProps {
  loading?: boolean
  form: any
  onReset?: () => void
  onSearch: (pageIndex: number) => void
}

// 基于CONTRACT_STATUS_CONFIG生成状态选项数组
const CONTRACT_STATUS_OPTIONS = [
  { value: 0, label: '待发送' },
  { value: 1, label: '发送合同' },
  { value: 2, label: '用户签约完成' },
  { value: 11, label: '待公司签约' },
  { value: 12, label: '公司签约完成' },
  { value: 100, label: '归档' },
  { value: 101, label: '作废' },
]

const ContractSearch: React.FC<ContractSearchProps> = ({ loading = false, onSearch, form, onReset }) => {
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(1), { wait: 500 })
  const { allProductionOptions, getAllProductionNames } = useProjectStore()
  const { allPersonOptions, getAllPersonNames } = usePersonStore()

  useEffect(() => {
    getAllProductionNames()
    getAllPersonNames()
    form.setFieldsValue({ status: [0, 1, 2, 11, 12] })
  }, [getAllProductionNames, getAllPersonNames, form])

  return (
    <Form form={form} layout="vertical" onValuesChange={onSearchDebounce} colon={false} className="search-form">
      <Space size={24} wrap>
        <Form.Item name="productionId" label="项目">
          <Select
            mode="multiple"
            className="w300"
            placeholder="选择项目"
            allowClear
            showSearch
            maxTagCount={1}
            fieldNames={{ label: 'labelStr', value: 'val' }}
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.label || '')

              return Boolean(result)
            }}
            options={allProductionOptions}
          />
        </Form.Item>
        <Form.Item name="personName" label="签约人">
          <Select
            className="w200"
            placeholder="选择人员"
            allowClear
            showSearch
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.label || '')

              return Boolean(result)
            }}
            fieldNames={{ label: 'label', value: 'label' }}
            options={allPersonOptions}
          />
        </Form.Item>
        <Form.Item name="contractName" label="合同">
          <Input className="w200" placeholder="输入合同名称" allowClear />
        </Form.Item>
        <Form.Item name="verify" label="合同类型">
          <Select
            className="w200"
            mode="multiple"
            placeholder="选择合同类型"
            allowClear
            options={CONTRACT_TYPE_OPTIONS}
          />
        </Form.Item>
        <Form.Item name="status" label="合同状态">
          <Select
            className="w200"
            mode="multiple"
            placeholder="选择合同状态"
            allowClear
            maxTagCount={1}
            options={CONTRACT_STATUS_OPTIONS}
          />
        </Form.Item>
        <Form.Item name="contractNumb" label="合同编号">
          <Input className="w200" placeholder="输入合同编号" allowClear />
        </Form.Item>

        <Form.Item label=" ">
          <Button type="primary" onClick={onSearchDebounce} loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Space>
    </Form>
  )
}

export default ContractSearch
