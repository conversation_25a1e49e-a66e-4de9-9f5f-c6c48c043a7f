import { dialogReadonlyProps } from '@/consts/antd'
import jsPreviewDocx from '@js-preview/docx'
import '@js-preview/docx/lib/index.css'
import { Modal } from 'antd'
import React, { useEffect, useState } from 'react'
import useStore from '../store'
import styles from './Preview.scss'

interface IPreviewProps {
  show?: boolean
  data?: string | null
  onCancel: () => void
}

const Preview: React.FC<IPreviewProps> = ({ show = false, data = null, onCancel }) => {
  const [showDoc, setShowDoc] = useState(false)
  const [iframeUri, setIframeUri] = useState<string | null>(null)
  const { fetchDownloadDocFile } = useStore()

  const getDownloadDocFile = async link => {
    const data = await fetchDownloadDocFile(link)
    const myDocxPreviewer = jsPreviewDocx.init(document.getElementById('docx') as HTMLElement)

    myDocxPreviewer.preview(data)
  }

  useEffect(() => {
    if (show) {
      // 法大大及uploadfile文件无法预览,走iframe显示
      const isFddUri = data && (data.indexOf('fadada') > -1 || data.indexOf('uploadfile.51changdu.com') > -1)

      if (isFddUri) {
        setIframeUri(data || null)
        setShowDoc(false)
      } else {
        setIframeUri(null)
        setShowDoc(true)
        getDownloadDocFile(data)
      }
    } else {
      document.querySelectorAll('.vue-office-docx')?.forEach(item => {
        item.remove()
      })
    }
  }, [show])

  return (
    <Modal
      title="查看合同"
      centered
      zIndex={10001}
      open={show}
      width={1600}
      {...dialogReadonlyProps}
      onCancel={onCancel}>
      {showDoc && <div className={styles.content} id="docx" />}
      {iframeUri && <iframe className={styles.content} src={iframeUri} />}
    </Modal>
  )
}

export default Preview
