import { Card, Descriptions, Drawer, Flex, List, Spin, Tabs, Tag, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import EnvImage from '../../../../components/EnvImage'
import EvaluationHistory from '../../../../components/EvaluationHistory'
import { GENDER_OPTIONS, ROLE_TYPE_CONFIG } from '../../../../consts'
import usePersonListStore, { IPersonListItem, IPrBankAccountInfo } from '../list/store'

interface PersonDetailProps {
  open: boolean
  personId?: number
  onCancel: () => void
}

const PersonDetail: React.FC<PersonDetailProps> = ({ open, personId, onCancel }) => {
  const { getPersonById } = usePersonListStore()
  const [person, setPerson] = useState<IPersonListItem | null>(null)
  const [loading, setLoading] = useState(false)

  // 获取人员详情
  const fetchPersonDetail = async () => {
    if (!personId) {
      return
    }

    try {
      setLoading(true)
      const result = await getPersonById(personId)

      setPerson(result || null)
    } catch (error) {
      console.error('获取人员详情失败:', error)
      setPerson(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open && personId) {
      fetchPersonDetail()
    }
  }, [open, personId])

  const handleCancel = () => {
    setPerson(null)
    onCancel()
  }

  // 渲染历史评价
  const renderEvaluationHistory = () => {
    const evaluations = person?.personEvaluationList || []

    return <EvaluationHistory evaluations={evaluations} parentType={1} />
  }

  const renderPersonContent = () => {
    if (!person) {
      return null
    }

    const roleTypeInfo = ROLE_TYPE_CONFIG[person.roleType] || { label: '未知', color: 'default' }
    const genderInfo = GENDER_OPTIONS.find(item => item.value === person.gender)

    // Tab 项配置
    const tabItems = [
      {
        key: 'evaluations',
        label: `历史评价${person?.personEvaluationList?.length ? `(${person.personEvaluationList.length})` : ''}`,
        children: renderEvaluationHistory(),
      },
    ]

    return (
      <Flex vertical gap={24}>
        {/* 基本信息 */}
        <Descriptions size="small" column={3} bordered title="1、基本信息">
          <Descriptions.Item label="姓名">
            <Typography.Text strong>{person.personName}</Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="类型">
            <Typography.Text strong>{roleTypeInfo.label}</Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="性别">{genderInfo?.label || '-'}</Descriptions.Item>
          <Descriptions.Item label="出生年份">{person.dateOfBirth || '-'}</Descriptions.Item>
          <Descriptions.Item label="是否儿童">{person.isChildActor ? '是' : '否'}</Descriptions.Item>
          <Descriptions.Item label="邮箱">
            {person.eMail ? (
              <Typography.Link href={`mailto:${person.eMail}`} copyable>
                {person.eMail}
              </Typography.Link>
            ) : (
              '-'
            )}
          </Descriptions.Item>
          <Descriptions.Item label="身份证号">
            {person.idNumber ? <Typography.Text copyable>{person.idNumber}</Typography.Text> : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="手机号">
            {person.phone ? <Typography.Text copyable>{person.phone}</Typography.Text> : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="所属公司" span={3}>
            {person.company || '-'}
          </Descriptions.Item>
          <Descriptions.Item label="联系地址" span={3}>
            {person.address || '-'}
          </Descriptions.Item>
        </Descriptions>

        {/* 银行卡信息 */}
        <Descriptions size="small" column={1} bordered title="2、银行卡">
          <Descriptions.Item label="银行卡" span={1}>
            {person.bankInfos && person.bankInfos.length > 0 ? (
              <List
                size="small"
                dataSource={person.bankInfos}
                renderItem={(bank: IPrBankAccountInfo, index) => (
                  <List.Item>
                    <Card size="small" style={{ width: '100%' }}>
                      <Flex vertical gap={8}>
                        <Flex justify="space-between">
                          <Typography.Text strong>银行卡 {index + 1}</Typography.Text>
                          {bank.isDefault && <Tag color="blue">默认</Tag>}
                        </Flex>
                        <Typography.Text>账户名：{bank.accountName || '-'}</Typography.Text>
                        <Typography.Text>卡号：{bank.accountNumber || '-'}</Typography.Text>
                        <Typography.Text>开户行：{bank.bankName || '-'}</Typography.Text>
                      </Flex>
                    </Card>
                  </List.Item>
                )}
              />
            ) : (
              <Typography.Text type="secondary">暂无银行卡信息</Typography.Text>
            )}
          </Descriptions.Item>
        </Descriptions>

        {/* 身份证附件 */}
        <Descriptions size="small" column={2} bordered title="3、身份证附件">
          <Descriptions.Item label="正面照" span={2}>
            {person.idCardFrontPhoto ? (
              <EnvImage width={120} src={person.idCardFrontPhoto} alt="正面照" className="radius" />
            ) : (
              '-'
            )}
          </Descriptions.Item>

          <Descriptions.Item label="反面照" span={2}>
            {person.idCardVersoPhoto ? (
              <EnvImage width={120} src={person.idCardVersoPhoto} alt="反面照" className="radius" />
            ) : (
              '-'
            )}
          </Descriptions.Item>
        </Descriptions>

        {/* Tabs 区域 */}
        <Tabs items={tabItems} />
      </Flex>
    )
  }

  return (
    <Drawer
      title={`人员详情 - ${person?.personName || '加载中'}`}
      open={open}
      onClose={handleCancel}
      width={900}
      destroyOnHidden>
      <Spin spinning={loading} tip="加载中...">
        <div style={{ height: '100%', overflow: 'auto' }}>
          {person ? renderPersonContent() : <div style={{ height: 200 }} />}
        </div>
      </Spin>
    </Drawer>
  )
}

export default PersonDetail
