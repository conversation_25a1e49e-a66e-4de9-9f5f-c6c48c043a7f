import { useDebounceFn } from 'ahooks'
import { Select } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import usePersonStore from '../list/store'

interface PersonSelectorProps {
  value?: number
  onChange?: (value: number) => void
  onSelect?: (personId: number, option: any) => void
  placeholder?: string
  disabled?: boolean
  roleType?: number // 可选的角色类型筛选
  initOption?: any // 初始化选项，用于编辑时回填
  isInitFetch?: boolean
}

// 人员选择器
const PersonSelector: React.FC<PersonSelectorProps> = ({
  value,
  onChange,
  onSelect,
  placeholder = '请选择人员',
  disabled = false,
  roleType,
  initOption,
  isInitFetch,
  ...otherProps
}) => {
  const [options, setOptions] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const { fetchPersonList, allPersonRoleMap } = usePersonStore()
  const formattedOptions = useMemo(
    () =>
      options.map(person => {
        const item = {
          ...person,
          id: person.id,
          label: person.personName,
          val: person.id,
          disabled: person.status == 2,
        }

        item.labelStr = item.personName
        if (item.isInternal) {
          item.labelStr = item.jobNumber ? `${item.personName}(内部_${item.jobNumber})` : `${item.personName}(内部)`
        } else {
          item.labelStr = `${item.personName}${typeof item.isInternal === 'number' ? '(外部)' : ''}`
        }

        return item
      }),
    [options]
  )

  // 初始化选项
  useEffect(() => {
    if (initOption) {
      setOptions([initOption])
    } else if (!options.length) {
      if (roleType && allPersonRoleMap?.[roleType]) {
        setOptions(allPersonRoleMap?.[roleType])
      } else if (isInitFetch) {
        loadDefaultPersons()
      }
    }
  }, [initOption])

  // 加载默认人员列表
  const loadDefaultPersons = async () => {
    setLoading(true)
    try {
      const result = await fetchPersonList(
        {
          pageIndex: 1,
          pageSize: 20, // 默认加载20个人员
          personName: '', // 空搜索，获取所有人员
          roleType: roleType ? [roleType] : [], // 按角色类型过滤
        },
        true
      )

      if (result?.list) {
        setOptions(result.list || [])
      }
    } catch (error) {
      console.error('加载默认人员列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 防抖搜索
  const { run: debouncedSearch } = useDebounceFn(
    async (searchText: string) => {
      if (!searchText.trim()) {
        // 如果清空搜索，恢复初始选项或重新加载默认列表
        if (initOption) {
          setOptions([initOption])
        } else {
          await loadDefaultPersons()
        }

        return
      }
      setLoading(true)
      try {
        const result = await fetchPersonList(
          {
            pageIndex: 1,
            pageSize: 50,
            personName: searchText.trim(),
            roleType: roleType ? [roleType] : [], // 按角色类型过滤
          },
          true
        )

        if (result?.list) {
          // 格式化选项以匹配原有结构
          const opts = result.list

          // 如果有初始选项且当前值匹配，确保初始选项在结果中
          if (initOption && value === initOption.id && !opts.find(opt => opt.id === initOption.id)) {
            opts.unshift(initOption)
          }

          setOptions(opts)
        } else {
          setOptions(initOption ? [initOption] : [])
        }
      } catch (error) {
        console.error('搜索人员失败:', error)
        setOptions(initOption ? [initOption] : [])
      } finally {
        setLoading(false)
      }
    },
    { wait: 300 }
  )

  const handleSearch = (searchText: string) => {
    setSearchValue(searchText)
    debouncedSearch(searchText)
  }

  const handleChange = (selectedValue: number) => {
    onChange?.(selectedValue)
  }

  const handleSelect = (selectedValue: number, option: any) => {
    onSelect?.(selectedValue, option)
  }

  const getNotFoundContent = () => {
    if (loading) {
      return '搜索中...'
    }

    if (searchValue) {
      return '暂无匹配结果'
    }
    if (options.length === 0) {
      return '输入名称搜索人员'
    }

    return '请输入搜索关键词以筛选人员'
  }

  return (
    <Select
      {...otherProps}
      placeholder={placeholder}
      showSearch
      loading={loading}
      allowClear
      disabled={disabled}
      options={formattedOptions}
      fieldNames={{ label: 'labelStr', value: 'id' }}
      value={value}
      onChange={handleChange}
      onSelect={handleSelect}
      onSearch={handleSearch}
      searchValue={searchValue}
      filterOption={false} // 禁用本地过滤
      notFoundContent={getNotFoundContent()}
    />
  )
}

export default PersonSelector
