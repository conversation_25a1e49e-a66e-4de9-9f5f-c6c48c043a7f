import { ValidRealNameResult } from '@/consts'
import { get as apiGet } from '@/utils/request'
import { create } from 'zustand'

// OptionDto类型声明
export interface OptionDto {
  jobNumber: any
  personName: any
  isDeparted: any
  depart: any
  val?: string
  label?: string
}

// 实名验证响应类型
export interface IValidRealNameResponse {
  validResult: ValidRealNameResult
  url?: string // 实名认证或完善信息的链接
}

export interface IPersonStore {
  // 判断用户是否已实名
  validRealName: (id: number) => Promise<IValidRealNameResponse | null>
  // 获取所有内部人员（接口）
  getAllInteriorUser: () => Promise<OptionDto[] | null>
  // 内部员工选项缓存
  allInteriorUserOptions: OptionDto[]
}

export default create<IPersonStore>((set, get) => ({
  // 内部员工选项缓存
  allInteriorUserOptions: [],

  // 判断用户是否已实名
  validRealName: async (id: number) => {
    try {
      const { data, status } = await apiGet<any, { data: IValidRealNameResponse; status: boolean }>(
        `/PrPerson/ValidRealName?id=${id}`
      )

      if (status && data) {
        return data
      }

      return null
    } catch (error) {
      console.error('验证实名信息失败:', error)

      return null
    }
  },
  // 获取所有内部人员（接口）
  getAllInteriorUser: async () => {
    try {
      const { data, status } = await apiGet<any, { data: OptionDto[]; status: boolean }>('/PrPerson/AllInteriorUser')

      if (status) {
        set({
          allInteriorUserOptions: (data || []).map(item => ({
            ...item,
            label: item.personName,
            val: item.jobNumber,
            labelStr: `${item.personName}(${item.jobNumber}${item.depart ? '_已离职' : ''})`,
          })),
        })

        return data || []
      }

      return []
    } catch (error) {
      console.error('获取所有内部人员失败:', error)

      return []
    }
  },
}))
