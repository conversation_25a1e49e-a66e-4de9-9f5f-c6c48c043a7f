import { create } from 'zustand'
import { RoleType } from '../../../../consts'
import { post, get as requestGet } from '../../../../utils/request'
import type { IPrPersonEvaluation } from '../../project/list/store'

export const prPersonUploadAPI = '/PrActors/UploadFile'

// 银行账户信息接口
export interface IPrBankAccountInfo {
  id?: number // 银行卡信息Id
  personId?: number // 关联人员ID
  isDefault: boolean // 是否默认
  bankName?: string // 开户行名称
  accountName?: string // 账户名称
  accountNumber?: string // 卡号
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 人员分页查询参数（对应API的PagePrPersonBasicDto）
export interface IPersonListSearchParams {
  pageIndex: number // 页码
  pageSize: number // 每页数量
  personName?: string // 人员名称
  isInternal?: number // 是否内部 (0否，1是)
  roleType?: RoleType[] // 人员类型数组（1导演、2编剧、3制片人、4摄影、5灯光、6服装、7道具、8化妆、9演员）
  isWxUser?: number // 是否微信注册用户
  wxNickName?: string // 微信账号昵称
  status?: number // 人员状态 (1正常，2禁用)
}

// API响应的人员信息（对应API的PrPersonBasic）
export interface IPersonListItem {
  creator?: string // 创建人
  lastModifier?: string // 最后修改人
  id: number // 人员ID
  personName?: string // 人员姓名
  jobNumber?: string // 人员工号
  dateOfBirth?: number // 出生年份
  isChildActor: boolean // 是否儿童演员 (0否，1是)
  gender: number // 性别
  idNumber?: string // 身份证号
  idCardFrontPhoto?: string // 身份证正面照
  idCardVersoPhoto?: string // 身份证反面照
  address?: string // 联系地址
  phone?: string // 手机号码
  eMail?: string // 邮箱地址
  roleType: RoleType // 人员类型
  company?: string // 所属公司，仅导演有此信息
  isDelete: boolean // 是否删除
  createTime: string // 创建时间
  updateTime: string // 更新时间
  bankInfos?: IPrBankAccountInfo[] // 银行卡信息列表
  fddVerifyStatus?: number // 实名认证状态个人 0：未激活； 1：未认证； 2：审核通过； 3：已提交待审核； 4：审核不通过
  fddCustomerVerifyUrl?: string // 法大大用户实名认证地址
  isInternal?: number // 是否内部 (0否，1是)
  wxAccount?: string // 微信注册用户账号
  wxNickName?: string // 微信账号昵称
  isWxUser?: number // 是否微信注册用户（只读）
  personType?: number // 人员类型（1个人，2团体）
  status?: number // 人员状态 (1正常，2禁用)
  depart?: boolean // 已离职
  personEvaluationList?: Array<IPrPersonEvaluation>
}

// 人员名称选项（对应API的OptionDto）
export interface IPersonOptionItem {
  val?: string // 键（人员ID）
  label?: string // 值（人员名称）
}

// API响应类型
export interface IPersonListResponse {
  list: IPersonListItem[]
  total: number
  pageIndex: number
  pageSize: number
}

export interface IPersonListStore {
  allPersonOptions: IPersonOptionItem[] // 所有人员选项列表
  allPersonRoleMap: { [key: string]: IPersonListItem[] }
  // API调用方法
  fetchPersonList: (params: IPersonListSearchParams, hasSingleMap?: boolean) => Promise<IPersonListResponse | null>
  getPersonById: (id: number) => Promise<IPersonListItem | null>
  savePerson: (personData: IPersonListItem) => Promise<boolean>
  deletePerson: (id: number) => Promise<boolean>
  getAllPersonNames: (roleType?: RoleType) => Promise<IPersonOptionItem[] | null>
}

export default create<IPersonListStore>((set, get) => ({
  allPersonOptions: [],
  allPersonRoleMap: {},
  // 获取人员列表
  fetchPersonList: async (params: IPersonListSearchParams, hasSingleMap = false) => {
    try {
      const { data, status } = await post<any, any>('/PrPerson/GetList', params)

      if (status && data) {
        if (hasSingleMap) {
          const personRoleMap = get().allPersonRoleMap

          if (params?.roleType?.[0]) {
            set({ allPersonRoleMap: { ...personRoleMap, [params?.roleType?.[0]]: data?.dataList || [] } })
          }
        }

        return {
          list: data.dataList || [],
          total: data.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      console.error('获取人员列表失败:', error)

      return null
    }
  },

  // 根据ID获取人员详情
  getPersonById: async (id: number) => {
    try {
      const { data, status } = await requestGet(`/PrPerson/GetById?id=${id}`)

      if (status && data) {
        return data?.dataList || null
      }

      return null
    } catch (error) {
      console.error('获取人员详情失败:', error)

      return null
    }
  },

  // 保存人员信息
  savePerson: async (personData: IPersonListItem) => {
    try {
      const { data, status } = await post<any, any>('/PrPerson/Save', personData)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('保存人员信息失败:', error)

      return false
    }
  },

  // 删除人员
  deletePerson: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrPerson/Delete?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除人员失败:', error)

      return false
    }
  },

  // 获取所有人员名称(不分页)
  getAllPersonNames: async (roleType?: RoleType) => {
    try {
      const url = roleType ? `/PrPerson/AllName?personBasicType=${roleType}` : '/PrPerson/AllName'
      const { data, status } = await requestGet<any, any>(url)

      if (status && data) {
        const options = Array.isArray(data?.dataList) ? data?.dataList : []

        set({ allPersonOptions: options })

        return options
      }

      return []
    } catch (error) {
      console.error('获取人员名称失败:', error)

      return []
    }
  },
}))
