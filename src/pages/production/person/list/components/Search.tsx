import { useDebounceFn } from 'ahooks'
import { Button, Form, Input, Select, Space } from 'antd'
import React from 'react'
import {
  IS_INTERNAL_OPTIONS,
  IS_WX_USER_OPTIONS,
  PERSON_STATUS_OPTIONS,
  ROLE_TYPE_OPTIONS_SELECT,
} from '../../../../../consts'

interface PersonSearchProps {
  loading?: boolean
  form: any
  onReset?: () => void
  onSearch: (pageIndex: number) => void
}

const PersonSearch: React.FC<PersonSearchProps> = ({ loading = false, onSearch, form }) => {
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(1), { wait: 500 })

  return (
    <Form form={form} layout="vertical" onValuesChange={onSearchDebounce} colon={false} className="search-form">
      <Space wrap size={24}>
        <Form.Item name="personName" label="姓名">
          <Input className="w200" placeholder="支持模糊查询" allowClear />
        </Form.Item>
        <Form.Item name="wxNickName" label="微信昵称">
          <Input className="w200" placeholder="支持模糊查询" allowClear />
        </Form.Item>
        <Form.Item name="roleType" label="工种">
          <Select
            className="w200"
            mode="multiple"
            placeholder="选择人员类型"
            allowClear
            options={ROLE_TYPE_OPTIONS_SELECT}
          />
        </Form.Item>

        <Form.Item name="isInternal" label="是否内部">
          <Select className="w200" placeholder="默认全选" allowClear options={IS_INTERNAL_OPTIONS} />
        </Form.Item>

        <Form.Item name="status" label="人员状态">
          <Select className="w200" placeholder="默认全选" allowClear options={PERSON_STATUS_OPTIONS} />
        </Form.Item>

        <Form.Item name="isWxUser" label="来源">
          <Select className="w200" placeholder="默认全选" allowClear options={IS_WX_USER_OPTIONS} />
        </Form.Item>
        <Form.Item label=" ">
          <Button type="primary" onClick={onSearchDebounce} loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Space>
    </Form>
  )
}

export default PersonSearch
