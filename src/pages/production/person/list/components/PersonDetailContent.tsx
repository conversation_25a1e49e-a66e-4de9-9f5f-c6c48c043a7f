import EnvImage from '@/components/EnvImage'
import EvaluationHistory from '@/components/EvaluationHistory'
import { Dict } from '@fe/rockrose'
import { Badge, Card, Descriptions, Flex, Space, Tabs, Tag, Typography } from 'antd'
import React from 'react'
import {
  GENDER_OPTIONS,
  IS_WX_USER_CONFIG,
  PERSON_STATUS_CONFIG,
  PERSON_TYPE_CONFIG,
  ROLE_TYPE_CONFIG,
} from '../../../../../consts'
import { IPersonListItem } from '../store'

interface PersonDetailContentProps {
  person?: IPersonListItem
}

const PersonDetailContent: React.FC<PersonDetailContentProps> = ({ person }) => {
  if (!person) {
    return null
  }

  const roleTypeInfo = ROLE_TYPE_CONFIG[person.roleType] || { label: '未知', color: 'default' }
  const personTypeInfo = person.personType ? PERSON_TYPE_CONFIG[person.personType] : null
  const genderInfo = GENDER_OPTIONS.find(item => item.value === person.gender)
  const statusInfo = person.status ? PERSON_STATUS_CONFIG[person.status as keyof typeof PERSON_STATUS_CONFIG] : null
  const isInternal = person.isInternal === 1

  // 渲染历史评价
  const renderEvaluationHistory = () => {
    const evaluations = person?.personEvaluationList || []
    return <EvaluationHistory evaluations={evaluations} parentType={1} />
  }

  const descriptions = (
    <Descriptions size="small" column={3} bordered title="1、基本信息">
      <Descriptions.Item label="姓名">
        <Typography.Text strong>{person.personName}</Typography.Text>
      </Descriptions.Item>
      <Descriptions.Item label="状态">
        {statusInfo && <Badge status={statusInfo.color} text={statusInfo.label}></Badge>}
      </Descriptions.Item>
      <Descriptions.Item label="工种">
        <Typography.Text strong>{roleTypeInfo.label}</Typography.Text>
      </Descriptions.Item>

      {personTypeInfo && <Descriptions.Item label="人员类型">{personTypeInfo.label}</Descriptions.Item>}
      {person.jobNumber && <Descriptions.Item label="工号">{person.jobNumber}</Descriptions.Item>}

      {/* 微信用户信息 */}
      {typeof person.isWxUser === 'number' && (
        <Descriptions.Item label="来源">{IS_WX_USER_CONFIG[person.isWxUser]?.label || '未知'}</Descriptions.Item>
      )}
      {person.isWxUser === 1 && person.wxAccount && (
        <Descriptions.Item label="系统账号">
          <Typography.Text copyable>{person.wxAccount}</Typography.Text>
        </Descriptions.Item>
      )}
      {person.isWxUser === 1 && person.wxNickName && (
        <Descriptions.Item label="微信昵称">{person.wxNickName}</Descriptions.Item>
      )}
      {isInternal ? <Descriptions.Item label="邮箱">
        {person.eMail ? (
          <Typography.Link href={`mailto:${person.eMail}`} copyable>
            {person.eMail}
          </Typography.Link>
        ) : (
          '暂无'
        )}
      </Descriptions.Item> : null}

      {/* 内部员工不显示以下字段 */}
      {!isInternal && (
        <>
          <Descriptions.Item label="性别">{genderInfo?.label || '暂无'}</Descriptions.Item>
          <Descriptions.Item label="出生年份">{person.dateOfBirth || '暂无'}</Descriptions.Item>
          <Descriptions.Item label="是否儿童">{person.isChildActor ? '是' : '否'}</Descriptions.Item>
          <Descriptions.Item label="邮箱">
            {person.eMail ? (
              <Typography.Link href={`mailto:${person.eMail}`} copyable>
                {person.eMail}
              </Typography.Link>
            ) : (
              '暂无'
            )}
          </Descriptions.Item>
          <Descriptions.Item label="身份证号">
            {person.idNumber ? <Typography.Text copyable>{person.idNumber}</Typography.Text> : '暂无'}
          </Descriptions.Item>
          <Descriptions.Item label="手机号">
            {person.phone ? <Typography.Text copyable>{person.phone}</Typography.Text> : '暂无'}
          </Descriptions.Item>
          {person.company && <Descriptions.Item label="所属公司">{person.company}</Descriptions.Item>}
          <Descriptions.Item label="联系地址" span={3}>
            {person.address || '-'}
          </Descriptions.Item>
        </>
      )}
    </Descriptions>
  )

  const tabItems = [
    {
      key: 'info',
      label: '基本信息',
      children: (
        <Flex vertical gap={24}>
          {/* 基本信息 */}
          {person.depart ? (
            <Badge.Ribbon color="#999" text="已离职">
              {descriptions}
            </Badge.Ribbon>
          ) : (
            descriptions
          )}

          {/* 外部员工才显示银行卡信息 */}
          {!isInternal && (
            <Descriptions size="small" column={1} bordered title="2、银行卡">
              <Descriptions.Item label="银行卡" span={1}>
                {person.bankInfos && person.bankInfos?.length > 0 ? (
                  <Flex vertical gap={12}>
                    {person.bankInfos?.map((bank, bankIndex) => {
                      const bankInfoCard = (
                        <Flex vertical gap={8} key={`${bankIndex}_flex`}>
                          <Dict title="账户" value={bank.accountName || '-'}></Dict>
                          <Dict
                            title="卡号"
                            value={
                              <Space>
                                <Typography.Text>{bank.accountNumber || '-'}</Typography.Text>
                                {bank.isDefault && <Tag color="blue">默认</Tag>}
                              </Space>
                            }></Dict>
                          <Dict title="开户行" value={bank.bankName || '-'}></Dict>
                        </Flex>
                      )

                      return person.bankInfos?.length === 1 ? (
                        bankInfoCard
                      ) : (
                        <Card size="small" key={bankIndex}>
                          {bankInfoCard}
                        </Card>
                      )
                    })}
                  </Flex>
                ) : (
                  <Typography.Text>暂无</Typography.Text>
                )}
              </Descriptions.Item>
            </Descriptions>
          )}

          {/* 外部员工才显示身份证附件 */}
          {!isInternal && (
            <Descriptions size="small" column={2} bordered title="3、身份证附件">
              <Descriptions.Item label="正面照" span={2}>
                {person.idCardFrontPhoto ? (
                  <EnvImage width={120} src={person.idCardFrontPhoto} alt="正面照" className="radius" />
                ) : (
                  '暂无'
                )}
              </Descriptions.Item>

              <Descriptions.Item label="反面照" span={2}>
                {person.idCardVersoPhoto ? (
                  <EnvImage width={120} src={person.idCardVersoPhoto} alt="反面照" className="radius" />
                ) : (
                  '暂无'
                )}
              </Descriptions.Item>
            </Descriptions>
          )}
        </Flex>
      ),
    },
    {
      key: 'evaluations',
      label: (
        <Space size={4}>
          用户评价
          {!!person.personEvaluationList?.length && <Badge color="#ccc" count={person.personEvaluationList?.length} />}
        </Space>
      ),
      children: renderEvaluationHistory(),
    },
  ]

  return <Tabs items={tabItems} indicator={{ size: 32 }} />
}

export default PersonDetailContent
