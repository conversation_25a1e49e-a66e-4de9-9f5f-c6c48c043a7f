import useIndexStore from '@/store'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, <PERSON>er, Popconfirm, Space } from 'antd'
import React from 'react'
import { IPersonListItem } from '../store'
import PersonDetailContent from './PersonDetailContent'

interface PersonDetailProps {
  open: boolean
  person?: IPersonListItem
  onClose: () => void
  onEdit: (person: IPersonListItem) => void
  onDelete: (person: IPersonListItem) => void
}

const PersonDetail: React.FC<PersonDetailProps> = ({ open, person, onClose, onEdit, onDelete }) => {
  const { authorBtn } = useIndexStore()

  return (
    <Drawer
      title={`详情 - ${person?.personName || ''}`}
      placement="right"
      onClose={onClose}
      open={open}
      width={900}
      destroyOnHidden
      styles={{ body: { paddingTop: 0 } }}
      extra={
        person && (
          <Space.Compact>
            {authorBtn.includes('删除') ? (
              <Popconfirm
                title="警告"
                description={`确定要删除【${person.personName}】吗？`}
                onConfirm={() => onDelete?.(person)}
                okText="确定删除"
                cancelText="取消">
                <Button type="default" shape="round" className="text-primary" icon={<DeleteOutlined />}>
                  删除
                </Button>
              </Popconfirm>
            ) : null}
            <Button
              type="default"
              shape="round"
              className="text-primary"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(person)}>
              编辑
            </Button>
          </Space.Compact>
        )
      }>
      <PersonDetailContent person={person} />
    </Drawer>
  )
}

export default PersonDetail
