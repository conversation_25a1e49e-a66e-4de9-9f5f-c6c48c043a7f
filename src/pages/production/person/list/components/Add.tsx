import useCompanyStore from '@/pages/system/company/store'
import { parseIdCard } from '@/utils'
import filterMatch from '@/utils/filterMatch'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { Fieldset } from '@fe/rockrose'
import { useDebounceFn } from 'ahooks'
import { AutoComplete, Button, Card, Drawer, Flex, Form, Input, InputNumber, Radio, Select, Switch, Tag } from 'antd'
import React, { useCallback, useEffect } from 'react'
import UploadComponent from '../../../../../components/Upload'
import {
  GENDER_OPTIONS,
  IS_INTERNAL_OPTIONS,
  IS_WX_USER_CONFIG,
  PERSON_STATUS_OPTIONS,
  PERSON_TYPE_OPTIONS,
  ROLE_TYPE_OPTIONS_SELECT,
} from '../../../../../consts'
import usePersonStore from '../../../person/store'
import { IPersonListItem, IPrBankAccountInfo, prPersonUploadAPI } from '../store'

// 上传API - 使用演员的上传接口
interface PersonFormProps {
  open: boolean
  person?: IPersonListItem
  onCancel: () => void
  onSubmit: (values: IPersonListItem) => void
}

const PersonForm: React.FC<PersonFormProps> = ({ open, person, onCancel, onSubmit }) => {
  const [form] = Form.useForm()

  const { getAllInteriorUser, allInteriorUserOptions } = usePersonStore()
  const { getAllCompanyNames, allCompanyOptions } = useCompanyStore()
  const formIsInternal = Form.useWatch('isInternal', form)

  // 处理内部员工选择
  const handleInternalUserSelect = (val: any, opt: any) => {
    if (val) {
      form.setFieldValue('jobNumber', opt?.val || null)
      form.setFieldValue('gender', opt?.gender || null)
    }
  }

  // 处理是否内部变更
  const handleIsInternalChange = (val: any) => {
    form.resetFields(['personName', 'jobNumber'])
  }

  // 身份证解析函数
  const parseIdCardAndFillForm = useCallback(
    (idNumber: string) => {
      if (idNumber && idNumber.length >= 15) {
        // 至少15位才开始解析
        const result = parseIdCard(idNumber)

        if (result.check) {
          // 身份证验证通过，自动填充相关字段
          form.setFieldsValue({
            gender: result.gender,
            dateOfBirth: result.dateOfBirth,
            isChildActor: result.isChild,
          })
        }
      }
    },
    [form]
  )

  // 使用ahooks的防抖hook
  const { run: debouncedParseIdCard } = useDebounceFn(parseIdCardAndFillForm, { wait: 500 })

  // 处理身份证号变化
  const handleIdNumberChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const idNumber = e.target.value.trim()

      debouncedParseIdCard(idNumber)
    },
    [debouncedParseIdCard]
  )

  // 处理默认银行卡的互斥设置
  const handleDefaultBankChange = useCallback(
    (changedIndex: number, isDefault: boolean) => {
      if (isDefault) {
        // 当前银行卡设置为默认时，将其他银行卡设置为非默认
        const bankInfos = form.getFieldValue('bankInfos') || []
        const updatedBankInfos = bankInfos.map((bank: IPrBankAccountInfo, index: number) => ({
          ...bank,
          isDefault: index === changedIndex,
        }))

        form.setFieldValue('bankInfos', updatedBankInfos)
      }
    },
    [form]
  )

  useEffect(() => {
    if (open && person) {
      form.setFieldsValue(person)
    } else {
      form.resetFields()
    }
    if (open) {
      getAllInteriorUser()
      getAllCompanyNames()
    }
  }, [open, person, form, getAllInteriorUser])

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      const params = { ...person, ...values }

      // 处理银行卡信息
      if (params.bankInfos && params.bankInfos.length > 0) {
        // 清理银行卡信息中的时间字段
        params.bankInfos = params.bankInfos.map((bank: IPrBankAccountInfo) => {
          const cleanBank = { ...bank }

          delete cleanBank.createTime
          delete cleanBank.updateTime

          return cleanBank
        })

        // 确保只有一张银行卡被设为默认
        const defaultCards = params.bankInfos.filter((bank: IPrBankAccountInfo) => bank.isDefault)

        if (defaultCards.length > 1) {
          // 如果有多张默认卡，只保留第一张为默认
          params.bankInfos.forEach((bank: IPrBankAccountInfo, index: number) => {
            bank.isDefault = index === 0 && bank.isDefault
          })
        } else if (defaultCards.length === 0 && params.bankInfos.length === 1) {
          // 如果只有一张卡且没有设为默认，自动设为默认
          params.bankInfos[0].isDefault = true
        }
      }

      delete params.createTime
      delete params.updateTime
      delete params.creator
      delete params.lastModifier

      onSubmit(params)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  return (
    <Drawer
      title={person ? '编辑人员' : '添加人员'}
      open={open}
      onClose={onCancel}
      width={800}
      destroyOnHidden
      extra={
        <Button type="primary" onClick={handleSubmit}>
          立即保存
        </Button>
      }>
      <Form
        form={form}
        layout="horizontal"
        colon={false}
        preserve={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 16 }}>
        <Flex vertical gap={24}>
          <Fieldset title="1、基础信息" direction="vertical" className="no-padding-b">
            <Form.Item name="isInternal" label="是否内部" initialValue={0} rules={[{ required: true }]}>
              <Radio.Group options={IS_INTERNAL_OPTIONS} onChange={handleIsInternalChange} />
            </Form.Item>

            {formIsInternal === 1 ? (
              <>
                <Form.Item name="personName" label="姓名" rules={[{ required: true, message: '请选择内部员工' }]}>
                  <Select
                    placeholder="请选择内部员工"
                    options={allInteriorUserOptions}
                    showSearch
                    filterOption={(inputValue, option) => {
                      const result = filterMatch(inputValue, (option as any)?.labelStr || '')

                      return Boolean(result)
                    }}
                    fieldNames={{ label: 'labelStr', value: 'label' }}
                    onSelect={handleInternalUserSelect}
                  />
                </Form.Item>
                <Form.Item name="jobNumber" label="工号" hidden>
                  <Input placeholder="工号将自动填充" disabled />
                </Form.Item>
                <Form.Item name="roleType" label="类型" rules={[{ required: true, message: '请选择人员类型' }]}>
                  <Select placeholder="请选择人员类型" options={ROLE_TYPE_OPTIONS_SELECT} />
                </Form.Item>
                <Form.Item name="gender" label="性别" initialValue={3} hidden></Form.Item>

              </>
            ) : (
              <>
                <Form.Item name="personName" label="姓名" rules={[{ required: true, message: '请输入姓名' }]}>
                  <Input placeholder="请输入姓名" />
                </Form.Item>
                <Form.Item name="jobNumber" label="工号" hidden>
                  <Input placeholder="工号将自动填充" disabled />
                </Form.Item>
                <Form.Item name="roleType" label="类型" rules={[{ required: true, message: '请选择人员类型' }]}>
                  <Select placeholder="请选择人员类型" options={ROLE_TYPE_OPTIONS_SELECT} />
                </Form.Item>
                {/* 微信信息 - 只读显示 */}
                {person && typeof person.isWxUser === 'number' && (
                  <Form.Item label="微信用户">
                    <Tag color={IS_WX_USER_CONFIG[person.isWxUser]?.color || 'default'}>
                      {IS_WX_USER_CONFIG[person.isWxUser]?.label || '未知'}
                    </Tag>
                  </Form.Item>
                )}
                {person && person.isWxUser === 1 && person.wxAccount && (
                  <Form.Item label="系统账号">
                    <Input value={person.wxAccount} disabled />
                  </Form.Item>
                )}
                {person && person.isWxUser === 1 && person.wxNickName && (
                  <Form.Item label="微信昵称">
                    <Input value={person.wxNickName} disabled />
                  </Form.Item>
                )}

                <Form.Item name="idNumber" label="身份证号">
                  <Input placeholder="请输入身份证号" onChange={handleIdNumberChange} />
                </Form.Item>
                <Form.Item name="gender" label="性别" initialValue={3}>
                  <Radio.Group options={GENDER_OPTIONS} />
                </Form.Item>
                <Form.Item name="dateOfBirth" label="出生年份">
                  <InputNumber placeholder="请输入出生年份" min={1900} max={2025} className="full-h" />
                </Form.Item>
                <Form.Item name="isChildActor" label="是否儿童" valuePropName="checked">
                  <Switch checkedChildren="是" unCheckedChildren="否" />
                </Form.Item>
                <Form.Item name="phone" label="手机号">
                  <Input placeholder="请输入手机号" />
                </Form.Item>
                <Form.Item name="company" label="所属公司">
                  <AutoComplete
                    placeholder="请输入或选择公司"
                    allowClear
                    showSearch
                    filterOption={(inputValue, option) => {
                      const result = filterMatch(inputValue, option?.value?.toString() || '')

                      return Boolean(result)
                    }}
                    options={
                      allCompanyOptions?.map(company => ({
                        label: company.companyName,
                        value: company.companyName,
                      })) || []
                    }
                  />
                </Form.Item>
                <Form.Item name="address" label="联系地址">
                  <Input.TextArea placeholder="请输入联系地址" rows={2} />
                </Form.Item>
              </>
            )}

            <Form.Item
              name="personType"
              label="人员类型"
              initialValue={1}
              rules={[{ required: true, message: '请选择人员类型' }]}>
              <Select placeholder="请选择人员类型" options={PERSON_TYPE_OPTIONS} />
            </Form.Item>

            <Form.Item name="status" label="人员状态" initialValue={1}>
              <Radio.Group options={PERSON_STATUS_OPTIONS} />
            </Form.Item>
            <Form.Item name="eMail" label="邮箱" required>
              <Input placeholder="请输入邮箱地址" />
            </Form.Item>
          </Fieldset>
          {formIsInternal !== 1 && (
            <>
              <Fieldset title="2、银行卡" direction="vertical" className="no-padding-b">
                <Form.List name="bankInfos">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <Card
                          key={key}
                          size="small"
                          title={`银行卡 ${name + 1}`}
                          className="mb-lg no-padding-b"
                          extra={
                            <Button
                              type="text"
                              size="small"
                              icon={<DeleteOutlined />}
                              onClick={() => remove(name)}></Button>
                          }>
                          <Form.Item
                            {...restField}
                            name={[name, 'accountName']}
                            label="账户"
                            rules={[{ required: true, message: '请输入账户名称' }]}>
                            <Input placeholder="请输入账户名称" />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            name={[name, 'accountNumber']}
                            label="卡号"
                            rules={[{ required: true, message: '请输入卡号' }]}>
                            <Input placeholder="请输入卡号" />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            name={[name, 'bankName']}
                            label="开户行"
                            rules={[{ required: true, message: '请输入开户行' }]}>
                            <Input placeholder="请输入开户行" />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            name={[name, 'isDefault']}
                            valuePropName="checked"
                            label="默认银行卡">
                            <Switch
                              checkedChildren="默认"
                              unCheckedChildren="普通"
                              onChange={checked => handleDefaultBankChange(name, checked)}
                            />
                          </Form.Item>
                        </Card>
                      ))}
                      <Form.Item>
                        <Button type="default" onClick={() => add()} className="w200" icon={<PlusOutlined />}>
                          添加银行卡
                        </Button>
                      </Form.Item>
                    </>
                  )}
                </Form.List>
              </Fieldset>
              <Fieldset title="3、身份证附件" direction="vertical" className="no-padding-b">
                <Form.Item name="idCardFrontPhoto" label="正面照">
                  <UploadComponent action={prPersonUploadAPI} type="image" accept=".png,.jpg,.jpeg" multiple={false} />
                </Form.Item>
                <Form.Item name="idCardVersoPhoto" label="反面照">
                  <UploadComponent action={prPersonUploadAPI} type="image" accept=".png,.jpg,.jpeg" multiple={false} />
                </Form.Item>
              </Fieldset>
            </>
          )}
        </Flex>
      </Form>
    </Drawer>
  )
}

export default PersonForm
