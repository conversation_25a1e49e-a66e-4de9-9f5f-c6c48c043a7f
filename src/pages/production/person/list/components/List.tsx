import { FddVerifyStatus, IS_INTERNAL_CONFIG, ROLE_TYPE_CONFIG } from '@/consts'
import { DATE_FORMAT_BASE } from '@/consts/date'
import { copy } from '@/utils/copy'
import { ManOutlined, PlusOutlined, We<PERSON>tOutlined, WomanOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Badge, Button, Card, Divider, Flex, List, message, Space, Tag, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useState } from 'react'
import usePersonStore from '../../store'
import { IPersonListItem } from '../store'
import styles from './List.scss'

// 人员列表
const PersonList: React.FC<any> = ({ data, pagination, loading, onChange, onOperate }) => {
  const [fetching, setFetching] = useState<number | null>(null)
  const { validRealName } = usePersonStore()

  // 独立实名处理函数
  const handleRealName = async (person: IPersonListItem) => {
    if (fetching) {
      return
    }

    try {
      // 如果已有url，直接使用
      if (person.fddCustomerVerifyUrl) {
        copy(person.fddCustomerVerifyUrl)
        message.success('实名认证链接已复制到剪贴板')

        return
      }
      setFetching(person.id)

      const res = await validRealName(person.id)

      setFetching(null)

      if (res?.url) {
        copy(res?.url)
        message.success('实名认证链接已复制到剪贴板')
      } else {
        message.error('获取实名认证链接异常')
      }
    } finally {
      setFetching(null)
    }
  }

  // 渲染人员卡片
  const renderPersonCard = (person: IPersonListItem) => {
    const roleTypeInfo = ROLE_TYPE_CONFIG[person.roleType] || { label: '未知', color: 'default' }
    const verifyStatus = person.fddVerifyStatus

    const ListItemCard = (
      <Card hoverable size="small" className="full-h hover-move" onClick={() => onOperate && onOperate('view', person)}>
        <Flex justify="space-between" align="center">
          <Flex flex={1} gap={16} vertical>
            <Space size={2} split={<Divider type="vertical" />}>
              <Typography.Text strong className="fs-lg">
                {person.personName}
              </Typography.Text>
              <Tag className="no-margin">
                <span className="text-primary">{roleTypeInfo.label}</span>
              </Tag>
              {typeof person.isInternal === 'number'
                ? `${IS_INTERNAL_CONFIG[person.isInternal]?.label}${person.isInternal ? '员工' : '人员'}` || '未知'
                : null}

              {person.isInternal === 1 && person.jobNumber ? <Dict title="工号" value={person.jobNumber} /> : null}
              {typeof person.isWxUser === 'number' && person.isWxUser === 1 ? (
                <Dict
                  title="来源"
                  value={
                    <Typography.Text type="success">
                      <WechatOutlined />
                    </Typography.Text>
                  }
                />
              ) : null}
              {person.isWxUser === 1 && person.wxNickName ? <Dict title="微信昵称" value={person.wxNickName} /> : null}
              {person.dateOfBirth ? <Typography.Text>{person.dateOfBirth}年</Typography.Text> : null}
              {person.isChildActor ? <Typography.Text type="danger">儿童</Typography.Text> : null}
              {person.gender && [1, 2].includes(person.gender) ? (
                <Typography.Text className={person.gender === 1 ? 'text-processing' : 'text-danger'}>
                  {person.gender === 1 ? <ManOutlined /> : <WomanOutlined />}
                </Typography.Text>
              ) : null}
              {!!person.company && person.isInternal === 0 ? <Dict title="所属公司" value={person.company} /> : null}
            </Space>
            <Space size={24}>
              {person.updateTime ? (
                <Space>
                  {person.lastModifier ? <Typography.Text>{person.lastModifier}</Typography.Text> : null}
                  <Dict
                    title="最近更新于"
                    value={
                      <Typography.Text type="secondary">
                        {dayjs(person.updateTime).format(DATE_FORMAT_BASE)}
                      </Typography.Text>
                    }
                  />
                </Space>
              ) : (
                <Space>
                  {person.creator ? <Typography.Text>{person.creator}</Typography.Text> : null}
                  <Dict
                    title="添加于"
                    value={
                      <Typography.Text type="secondary">
                        {dayjs(person.createTime).format(DATE_FORMAT_BASE)}
                      </Typography.Text>
                    }
                  />
                </Space>
              )}
            </Space>
          </Flex>
          <Space className="w100">
            {verifyStatus === FddVerifyStatus.Verified ? <Badge status="success" text="实名通过" /> : null}
            {person.isInternal === 0 &&
            verifyStatus !== FddVerifyStatus.Verified &&
            verifyStatus !== FddVerifyStatus.Incomplete ? (
              <Button
                size="small"
                type="link"
                loading={person.id === fetching}
                disabled={loading}
                onClick={e => {
                  e.stopPropagation()
                  handleRealName(person)
                }}>
                去实名认证
              </Button>
            ) : null}
          </Space>
        </Flex>
      </Card>
    )

    return (
      <List.Item>
        {person.depart ? (
          <Badge.Ribbon color="#999" text="已离职">
            {ListItemCard}
          </Badge.Ribbon>
        ) : (
          ListItemCard
        )}
      </List.Item>
    )
  }

  return (
    <Flex vertical>
      <ListHeader title="人员列表" total={pagination?.total} unitText="个">
        <Button type="primary" ghost icon={<PlusOutlined />} onClick={() => onOperate && onOperate('create')}>
          添加人员
        </Button>
      </ListHeader>
      <List
        loading={loading}
        dataSource={data}
        split={false}
        renderItem={renderPersonCard}
        rowKey="id"
        className={`${styles.list} list-sm`}
        pagination={{
          ...pagination,
          onChange,
          onShowSizeChange: onChange,
        }}
      />
    </Flex>
  )
}

export default PersonList
