import useSyncParams, { initFormFromUrlState, parsePagination } from '@/hooks/useSyncParams'
import { Flex, Form, message } from 'antd'
import React, { useEffect, useState } from 'react'
import { PAGINATION } from '../../../../consts'
import usePersonStore from '../store'
import PersonForm from './components/Add'
import PersonDetail from './components/Detail'
import PersonList from './components/List'
import PersonSearch from './components/Search'
import usePersonListStore, { IPersonListItem, IPersonListSearchParams } from './store'

const PersonInfo: React.FC = () => {
  const [urlState, setUrlState] = useSyncParams<IPersonListSearchParams>()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<IPersonListItem[]>([])
  const [pagination, setPagination] = useState(PAGINATION)

  const [show, setShow] = useState(false)
  const [operateData, setOperateData] = useState<any | IPersonListItem>()
  const [operateType, setOperateType] = useState<any | IOperateType>()

  const { fetchPersonList, savePerson, deletePerson } = usePersonListStore()
  const { validRealName } = usePersonStore()

  // 初始化URL数据
  const initParams = () => {
    // 使用工具函数简化表单初始化
    initFormFromUrlState(urlState, form, {
      numberFields: ['isInternal'],
      numberArrayFields: ['roleType'],
      excludeFields: ['pageIndex', 'pageSize'],
    })

    // 使用工具函数处理分页
    const { current, pageSize } = parsePagination(urlState, {
      current: pagination.current,
      pageSize: pagination.pageSize,
    })

    handleSearch(current, pageSize)
  }

  const handleSearch = async (current = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true)
    try {
      const values = form.getFieldsValue()
      const searchParams: IPersonListSearchParams = {
        pageIndex: current,
        pageSize,
        ...values,
      }

      const result = await fetchPersonList(searchParams)

      if (result) {
        setDataSource(result.list)
        setPagination(prev => ({
          ...prev,
          total: result.total,
          current: result.pageIndex,
          pageSize: result.pageSize,
        }))

        // 同步URL参数
        setUrlState({
          personName: values.personName || '',
          roleType: values.roleType || [],
          isInternal: values.isInternal,
          pageSize: result.pageSize,
          pageIndex: result.pageIndex,
        })
      } else {
        setDataSource([])
      }
    } catch (error) {
      setDataSource([])
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (data: IPersonListItem) => {
    try {
      const result = await deletePerson(data.id)

      if (result) {
        message.success('删除成功')
        handleOperateClose(false)
        await handleSearch(1)
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 主分发
  const handleOperate = (type: string, data?: IPersonListItem) => {
    if (type === 'delete') {
      data && handleDelete(data)
    } else if (['create', 'edit', 'view'].includes(type)) {
      
      setOperateData(data || null)
      setOperateType(type)
      setShow(true)
    }
  }

  const handleOperateClose = (fresh = true) => {
    setOperateData(null)
    setOperateType('')
    setShow(false)
    if (fresh) {
      handleSearch()
    }
  }

  // 表单提交
  const handleFormSubmit = async (values: IPersonListItem) => {
    try {
      let param = {...operateData,...values,}

      const result = await savePerson(param)

      if (result) {
        message.success(operateType == 'edit' ? '更新成功' : '新增成功')
        handleOperateClose()
        await handleSearch(1)
      }
    } catch (error) {
      message.error('操作失败')
    }
  }

  // 从详情页面编辑
  const handleEditFromDetail = (person: IPersonListItem) => {
    setOperateData(person)
    setOperateType('edit')
    // 保持抽屉打开状态，但切换到编辑模式
  }

  // 从详情页面删除
  const handleDeleteFromDetail = (person: IPersonListItem) => {
    handleDelete(person)
  }

  useEffect(() => {
    initParams()
  }, [])

  return (
    <Flex vertical gap={24}>
      <PersonSearch form={form} onSearch={handleSearch} onReset={() => handleSearch(1)} loading={loading} />
      <PersonList
        data={dataSource}
        loading={loading}
        onOperate={handleOperate}
        onChange={handleSearch}
        pagination={pagination}
      />

      {/* 人员详情抽屉 */}
      <PersonDetail
        open={show && operateType === 'view'}
        person={operateData ?? void 0}
        onClose={handleOperateClose}
        onEdit={handleEditFromDetail}
        onDelete={handleDeleteFromDetail}
      />

      {/* 人员表单模态框 */}
      <PersonForm
        open={show && ['edit', 'create'].includes(operateType)}
        person={operateData ?? void 0}
        onCancel={handleOperateClose}
        onSubmit={handleFormSubmit}
      />
    </Flex>
  )
}

export default PersonInfo
