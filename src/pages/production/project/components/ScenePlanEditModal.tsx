import { Form, Input, Modal } from 'antd'
import React, { useEffect } from 'react'
import { type IPrScenePlan } from '../store'

interface IScenePlanEditModalProps {
  visible: boolean
  item: IPrScenePlan | null
  onOk: (values: Partial<IPrScenePlan>) => Promise<void>
  onCancel: () => void
  isAdd?: boolean
}

export const ScenePlanEditModal: React.FC<IScenePlanEditModalProps> = ({
  visible,
  item,
  onOk,
  onCancel,
  isAdd = false,
}) => {
  const [form] = Form.useForm()

  // 当弹窗打开时设置表单值
  useEffect(() => {
    if (visible && item) {
      form.setFieldsValue({
        specialActors: item.specialActors || '',
        groupExtraActors: item.groupExtraActors || '',
        costumeMakeupTip: item.costumeMakeupTip || '',
        remark: item.remark || '',
      })
    } else if (visible && !item) {
      // 清空表单
      form.resetFields()
    }
  }, [visible, item, form])

  // 处理确定按钮
  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      await onOk(values)
    } catch (error) {
      // 表单验证失败，不关闭弹窗
    }
  }

  // 处理取消按钮
  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={isAdd ? '新增日期计划' : '编辑场次'}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      okText="立即保存"
      width={600}>
      <Form form={form} layout="horizontal" colon={false} labelCol={{ span: 4 }} wrapperCol={{ span: 18 }}>
        {/* planType=0 场次明细时显示所有可编辑字段 */}
        {item?.planType === 0 && (
          <>
            <Form.Item
              label="特约/群特"
              name="specialActors"
              rules={[{ max: 500, message: '特约/群特不能超过500个字符' }]}>
              <Input placeholder="请输入特约/群特" />
            </Form.Item>

            <Form.Item label="群演" name="groupExtraActors" rules={[{ max: 500, message: '群演不能超过500个字符' }]}>
              <Input placeholder="请输入群演" />
            </Form.Item>

            <Form.Item
              label="服化道提示"
              name="costumeMakeupTip"
              rules={[{ max: 1000, message: '服化道提示不能超过1000个字符' }]}>
              <Input.TextArea rows={3} placeholder="请输入服化道提示" />
            </Form.Item>
          </>
        )}

        {/* 备注字段对所有类型都显示 */}
        <Form.Item label={'备注'} name="remark">
          <Input.TextArea rows={3} placeholder={'请输入备注'} />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default ScenePlanEditModal
