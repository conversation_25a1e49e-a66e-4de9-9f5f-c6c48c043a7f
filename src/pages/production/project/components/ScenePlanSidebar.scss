.scenePlanContainer {
  height: 100%;
  overflow-y: auto;
}

.scenePlanGroup {
}

.scenePlanGroupSimple {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fff;

  .scenePlanGroupHeader {
    background-color: #fafafa;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 6px 6px 0 0;

    &:hover {
      background-color: #f0f0f0;
    }
  }

  .scenePlanGroupContent {
  }
}

.scenePlanItem {
  cursor: move;
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }


}

// 拖拽时的样式
.dragOver {
  border: 2px dashed #1890ff;
  background-color: #f6ffed;
}

.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

// 通用样式
.dragItem {
  opacity: 0.5;
}

.fullWidth {
  width: 100%;
}

.spaceBetween {
  width: 100%;
  justify-content: space-between;
}

.dividerContainer {
  margin: 0 0;
  position: relative;
}

.dividerStyle {
  margin: 0;
  border-color: #1890ff;
}

.dividerText {
  color: #1890ff;
  font-size: 12px;
  padding: 0 8px;
}

.actionButtons {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: white;
}

.deleteButton {
  cursor: pointer;
  color: #ff4d4f;
}

.editButton {
  cursor: pointer;
}

.dragHandle {
  cursor: move;
  color: #1890ff;
}