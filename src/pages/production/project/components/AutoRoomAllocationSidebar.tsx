import { Button, Checkbox, DatePicker, Drawer, Flex, Form, message, Select, Space } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useMemo, useState } from 'react'
import useRoomStore, { IBatchPersonRoomDto, IGroupCommunityDto } from '../../../room/store'
import useProjectListStore, { IProductionListItem, IPrProductionAllPerson } from '../list/store'

const { RangePicker } = DatePicker

interface IAutoRoomAllocationSidebarProps {
  open: boolean
  productionId: number
  project?: IProductionListItem
  onClose: () => void
  onSuccess: () => void
}

const AutoRoomAllocationSidebar: React.FC<IAutoRoomAllocationSidebarProps> = ({
  open,
  productionId,
  project,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [allPersons, setAllPersons] = useState<IPrProductionAllPerson[]>([])
  const [groupList, setGroupList] = useState<IGroupCommunityDto[]>([])

  const { getAllPersonList } = useProjectListStore()
  const { fetchGroupList, batchPersonRoom } = useRoomStore()

  // 监听小区选择
  const selectedCommunityId = Form.useWatch('communityId', form)
  // 监听人员选择
  const selectedPersonIds = Form.useWatch('personId', form) || []

  // 计算所有人员ID
  const allPersonIds = useMemo(() =>
    allPersons.map(person => person.personId).filter(Boolean) as number[],
    [allPersons]
  )

  // 获取项目所有人员
  const fetchAllPersons = async () => {
    if (!productionId) return

    try {
      const result = await getAllPersonList(productionId)
      if (result) {
        setAllPersons(result)
      }
    } catch (error) {
      console.error('获取项目人员失败:', error)
      message.error('获取项目人员失败')
    }
  }

  // 获取小区楼栋房间数据
  const fetchRoomData = async () => {
    try {
      const result = await fetchGroupList({})
      if (result) {
        setGroupList(result)
      }
    } catch (error) {
      console.error('获取房间数据失败:', error)
      message.error('获取房间数据失败')
    }
  }

  // 生成楼栋选项
  const buildingOptions = useMemo(() => {
    if (!selectedCommunityId) return []

    const selectedCommunity = groupList.find(community => community.id.toString() === selectedCommunityId)
    if (selectedCommunity?.roomInfos) {
      // 获取该小区的所有楼栋号
      return Array.from(
        new Set(selectedCommunity.roomInfos.map(room => room.buildingNumber))
      ).map(buildingNumber => ({
        label: `${buildingNumber}栋`,
        value: buildingNumber as number,
      }))
    }
    return []
  }, [selectedCommunityId, groupList])

  // 处理小区选择变化
  const handleCommunityChange = () => {
    // 清空楼栋选择
    form.setFieldsValue({ buildingNumber: undefined })
  }

  // 处理全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      form.setFieldsValue({ personId: allPersonIds })
    } else {
      form.setFieldsValue({ personId: [] })
    }
  }

  // 处理反选
  const handleInvertSelection = () => {
    const currentSelected = form.getFieldValue('personId') || []
    const invertedSelection = allPersonIds.filter(id => !currentSelected.includes(id))
    form.setFieldsValue({ personId: invertedSelection })
  }

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields().catch(() => false)
      if (!values) {

        return
      }
      setLoading(true)

      const [startDate, endDate] = values.dateRange

      const params: IBatchPersonRoomDto = {
        personId: values.personId,
        productionId,
        communityId: values.communityId,
        buildingNumber: values.buildingNumber,
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
      }

      const success = await batchPersonRoom(params)
      if (success) {
        message.success('自动分配房间成功')
        onSuccess()
      }
    } catch (error) {
      console.error('自动分配房间失败:', error)
      message.error('自动分配房间失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化数据
  useEffect(() => {
    if (open && productionId) {
      fetchAllPersons()
      fetchRoomData()

      // 设置默认日期范围
      if (project?.startDate && project?.endDate) {
        form.setFieldsValue({
          dateRange: [dayjs(project.startDate), dayjs(project.endDate)],
        })
      }
    }
  }, [open, productionId, project])

  // 初始化人员选择
  useEffect(() => {
    if (open && allPersons.length > 0) {
      form.setFieldsValue({ personId: allPersons.filter(item => !item.isInternal && !item.personRoom).map(item => item.personId) })
    }
  }, [allPersons, open])

  // 生成小区选项
  const communityOptions = useMemo(() =>
    groupList.map(community => ({
      label: community.communityName || `小区${community.id}`,
      value: community.id.toString(),
    })), [groupList]
  )

  // 生成人员选项
  const personOptions = useMemo(() =>
    allPersons.map(person => ({
      label: (person.personName || `人员${person.personId}`) + `${person?.isInternal ? '（内部）' : ''}`,
      value: person.personId,
    })), [allPersons]
  )

  // 计算全选状态
  const selectAllState = useMemo(() => {
    const selectedCount = selectedPersonIds.length
    const totalCount = allPersonIds.length

    return {
      indeterminate: selectedCount > 0 && selectedCount < totalCount,
      checked: selectedCount === totalCount && totalCount > 0
    }
  }, [selectedPersonIds.length, allPersonIds.length])

  return (
    <Drawer
      title="自动分配房间"
      open={open}
      onClose={onClose}
      width={800}
      footer={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" loading={loading} onClick={handleSubmit}>
            确认分配
          </Button>
        </Space>
      }
    >
      <Form form={form} layout="vertical" requiredMark={false}>
        <Form.Item
          name="personId"
          label={
            <Flex justify="space-between" align="center" gap={10}>
              <span>选择人员</span>
              <Space size="small">
                <Button
                  size="small"
                  type={selectAllState.checked ? "primary" : "default"}
                  onClick={() => handleSelectAll(!selectAllState.checked)}
                >
                  全选
                </Button>
                <Button
                  size="small"
                  onClick={handleInvertSelection}
                >
                  反选
                </Button>
              </Space>
            </Flex>
          }
          rules={[{ required: true, message: '请选择人员' }]}
        >
          <Checkbox.Group
            options={personOptions}
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
              gap: '8px 16px',
              width: '100%'
            }}
          />
        </Form.Item>

        <Form.Item
          name="communityId"
          label="选择小区"
          rules={[{ required: true, message: '请选择小区' }]}
        >
          <Select
            placeholder="请选择小区"
            options={communityOptions}
            onChange={handleCommunityChange}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>

        <Form.Item
          name="buildingNumber"
          label="选择楼栋"
          rules={[{ required: true, message: '请选择楼栋' }]}
        >
          <Select
            placeholder="请选择楼栋"
            options={buildingOptions}
            disabled={buildingOptions.length === 0}
          />
        </Form.Item>

        <Form.Item
          name="dateRange"
          label="入住日期范围"
          rules={[{ required: true, message: '请选择入住日期范围' }]}
        >
          <RangePicker
            className="w-full"
            placeholder={['开始日期', '结束日期']}
            disabledDate={(current) => current && current < dayjs().startOf('day')}
          />
        </Form.Item>
      </Form>
    </Drawer>
  )
}

export default AutoRoomAllocationSidebar
