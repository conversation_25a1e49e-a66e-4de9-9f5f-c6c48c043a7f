import InputTag from '@/components/InputTag'
import { PlusOutlined } from '@ant-design/icons'
import { Dict } from '@fe/rockrose'
import {
  Button,
  Divider,
  Drawer,
  Empty,
  Flex,
  Form,
  Input,
  message,
  Modal,
  Select,
  Space,
  Table,
  Typography,
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { CONTENT_TYPE_CONFIG, CONTENT_TYPE_OPTIONS, ContentType, PAGINATION } from '../../../../consts'
import { DATE_FORMAT_BASE } from '../../../../consts/date'
import useProjectStore, { type IPrProductionNoun, type IPrProductionNounDto } from '../store'

interface DictionaryManagementProps {
  visible: boolean
  onClose: () => void
  productionId: number
  productionName?: string
}

const DictionaryManagement: React.FC<DictionaryManagementProps> = ({
  visible,
  onClose,
  productionId,
  productionName,
}) => {
  const { getProductionNounList, saveProductionNoun, deleteProductionNoun } = useProjectStore()

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [deleting, setDeleting] = useState<number | null>(null)

  // 数据状态
  const [nounList, setNounList] = useState<IPrProductionNoun[]>([])
  const [total, setTotal] = useState(0)
  const [pageIndex, setPageIndex] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // 搜索状态
  const [searchForm] = Form.useForm()
  const [searchParams, setSearchParams] = useState<Partial<IPrProductionNounDto>>({})

  // 编辑状态
  const [editModalVisible, setEditModalVisible] = useState(false)
  const [editForm] = Form.useForm()
  const [editingNoun, setEditingNoun] = useState<IPrProductionNoun | null>(null)

  const formNounType = Form.useWatch('nounType', editForm)
  // 获取字典列表
  const fetchNounList = async (params?: Partial<IPrProductionNounDto>) => {
    if (!productionId) {
      return
    }

    setLoading(true)
    try {
      const queryParams: IPrProductionNounDto = {
        pageIndex,
        pageSize,
        productionId,
        ...searchParams,
        ...params,
      }

      const response = await getProductionNounList(queryParams)

      if (response) {
        setNounList(response.list)
        setTotal(response.total)
        setPageIndex(queryParams.pageIndex)
        setPageSize(queryParams.pageSize)
      }
    } catch (error) {
      message.error('获取字典列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化数据
  useEffect(() => {
    if (visible && productionId) {
      fetchNounList()
    }
    // 组件关闭时重置loading状态
    if (!visible) {
      setLoading(false)
      setPageIndex(1)
      setSearchParams({})
      setTotal(0)
      setNounList([])
    }
  }, [visible, productionId])

  // 搜索处理
  const handleSearch = () => {
    const values = searchForm.getFieldsValue()

    setSearchParams(values)
    setPageIndex(1)
    fetchNounList({ ...values, pageIndex: 1 })
  }

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields()
    setSearchParams({})
    setPageIndex(1)
    fetchNounList({ pageIndex: 1 })
  }

  // 分页处理
  const handlePageChange = (page: number, size: number) => {
    setPageIndex(page)
    setPageSize(size)

    fetchNounList({ pageIndex: page, pageSize: size })
  }

  // 新增字典
  const handleAdd = () => {
    setEditingNoun(null)
    editForm.resetFields()
    setEditModalVisible(true)
  }

  // 编辑字典
  const handleEdit = (record: IPrProductionNoun) => {
    setEditingNoun(record)
    editForm.setFieldsValue(record)
    setEditModalVisible(true)
  }

  // 保存字典
  const handleSave = async () => {
    try {
      const values = await editForm.validateFields()

      setSaving(true)

      const nounData: IPrProductionNoun = {
        ...values,
        productionId,
        id: editingNoun?.id,
      }

      const success = await saveProductionNoun({
        productionId,
        nouns: [nounData],
      })

      if (success) {
        message.success(editingNoun ? '编辑成功' : '新增成功')
        setEditModalVisible(false)
        fetchNounList()
      } else {
        message.error('保存失败')
      }
    } catch (error) {
      // Form validation error or API error
    } finally {
      setSaving(false)
    }
  }

  // 删除字典
  const handleDelete = async (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个字典吗？',
      onOk: async () => {
        setDeleting(id)
        try {
          const success = await deleteProductionNoun(id)

          if (success) {
            message.success('删除成功')
            fetchNounList()
          } else {
            message.error('删除失败')
          }
        } catch (error) {
          message.error('删除失败')
        } finally {
          setDeleting(null)
        }
      },
    })
  }

  // 表格列定义
  const columns: ColumnsType<IPrProductionNoun> = [
    {
      title: '字典',
      dataIndex: 'noun',
      key: 'noun',
      ellipsis: true,
      align: 'center',
      render: (text: string, record) => (
        <Space direction="vertical">
          <Typography.Text strong>{text}</Typography.Text>
          {record.firstNoun && <Dict title="主场景" value={record.firstNoun} />}
          {record.nounType === ContentType.SCENE && record.secondNoun && (
            <Dict title="分场景" value={record.secondNoun} />
          )}
          {record.alias && <Dict title="别名" value={record.alias} />}
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'nounType',
      key: 'nounType',
      width: 200,
      align: 'center',
      render: (type: ContentType) => CONTENT_TYPE_CONFIG[type]?.label || '未知类型',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 200,
      align: 'center',
      render: (time: string) => (time ? dayjs(time).format(DATE_FORMAT_BASE) : '-'),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      align: 'center',
      render: (_, record) => (
        <Space size={0} split={<Divider type="vertical" />}>
          <Typography.Link onClick={() => record.id && handleDelete(record.id)}>删除</Typography.Link>
          <Typography.Link onClick={() => handleEdit(record)}>编辑</Typography.Link>
        </Space>
      ),
    },
  ]

  return (
    <Drawer
      title={`《${productionName || '项目'}》字典`}
      width={1100}
      open={visible}
      onClose={onClose}
      extra={
        <Button type="primary" ghost icon={<PlusOutlined />} onClick={handleAdd}>
          添加字典
        </Button>
      }>
      <Flex vertical gap={16}>
        {/* 搜索区域 */}
        <Form form={searchForm} layout="inline" colon={false}>
          <Form.Item name="noun" label="字典">
            <Input placeholder="请输入字典" allowClear />
          </Form.Item>
          <Form.Item name="nounType" label="类型">
            <Select
              placeholder="请选择类型"
              mode="multiple"
              allowClear
              style={{ width: 180 }}
              options={CONTENT_TYPE_OPTIONS}
              maxTagCount={1}
            />
          </Form.Item>
          <Form.Item>
            <Button type="primary" onClick={handleSearch}>
              查询
            </Button>
          </Form.Item>
        </Form>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={nounList}
          rowKey="id"
          loading={loading}
          pagination={{
            ...PAGINATION,
            current: pageIndex,
            total,
            pageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            onChange: handlePageChange,
          }}
          locale={{ emptyText: <Empty /> }}
        />
      </Flex>

      {/* 编辑弹窗 */}
      <Modal
        title={editingNoun ? '编辑字典' : '添加字典'}
        open={editModalVisible}
        onOk={handleSave}
        okText="立即保存"
        onCancel={() => setEditModalVisible(false)}
        confirmLoading={saving}>
        <Form form={editForm} layout="vertical">
          <Form.Item name="nounType" label="类型" rules={[{ required: true, message: '请选择类型' }]}>
            <Select placeholder="选择字典类型" options={CONTENT_TYPE_OPTIONS} />
          </Form.Item>
          <Form.Item name="noun" label="字典" rules={[{ required: true, message: '请输入字典' }]}>
            <Input placeholder="先选择类型，再输入字典" disabled={!formNounType} />
          </Form.Item>
          <Form.Item name="firstNoun" label="主场景" hidden={formNounType !== ContentType.SCENE}>
            <Input placeholder="请输入主场景" />
          </Form.Item>
          <Form.Item name="secondNoun" label="分场景" hidden={formNounType !== ContentType.SCENE}>
            <Input placeholder="请输入分场景" />
          </Form.Item>
          <Form.Item name="alias" label="别名">
            <InputTag placeholder="每输入1个别名，按回车确定" mode="string" />
          </Form.Item>
        </Form>
      </Modal>
    </Drawer>
  )
}

export default DictionaryManagement
