import { ATMOSPHERE_TYPE_CONFIG, AtmosphereType, LOCATION_TYPE_CONFIG, LocationType, PlanType } from '@/consts'
import { Dict } from '@fe/rockrose'
import { Badge, Drawer, Flex, Space, Table, Typography } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import React, { useEffect, useState } from 'react'
import useProjectStore, { type IPrScenePlan } from '../store'
import styles from './SceneDataSidebar.scss'

const { Text } = Typography

interface ISceneDataSidebarProps {
  productionId: number
  visible: boolean
  onClose: () => void
}

const SceneDataSidebar: React.FC<ISceneDataSidebarProps> = ({ productionId, visible, onClose }) => {
  const { getSceneData } = useProjectStore()

  // 状态管理
  const [loading, setLoading] = useState(false)
  const [sceneData, setSceneData] = useState<IPrScenePlan[]>([])

  // 获取顺场表数据
  const fetchSceneData = async () => {
    if (!productionId) {
      return
    }

    setLoading(true)
    try {
      const data = await getSceneData(productionId)
      if (data) {
        setSceneData(data)
      }
    } catch (error) {
      console.error('获取顺场表数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 定义表格列
  const columns: ColumnsType<IPrScenePlan> = [
    {
      title: '场次',
      dataIndex: 'sceneNumber',
      key: 'sceneNumber',
      width: 80,
      fixed: 'left',
      align: 'center',
      render: (text: string, record: IPrScenePlan) => (
        <div>
          <Text strong>{text}</Text>
          {record.verification && <Badge status="success" text="已销场" style={{ marginLeft: 8 }} />}
        </div>
      ),
    },
    {
      title: '气氛',
      dataIndex: 'atmosphere',
      key: 'atmosphere',
      width: 60,
      align: 'center',
      render: (value: AtmosphereType) => (value ? ATMOSPHERE_TYPE_CONFIG?.[value]?.label : '-'),
    },
    {
      title: '内/外景',
      dataIndex: 'locationType',
      key: 'locationType',
      width: 60,
      align: 'center',
      render: (value: LocationType) => (value ? LOCATION_TYPE_CONFIG?.[value]?.label : '-'),
    },
    {
      title: '页数',
      dataIndex: 'pageNumber',
      key: 'pageNumber',
      width: 60,
      align: 'center',
      render: (value: number) => value || '-',
    },
    {
      title: '拍摄场地',
      dataIndex: 'shootingLocation',
      key: 'shootingLocation',
      width: 120,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '场景',
      key: 'scriptLocation',
      width: 200,
      align: 'center',
      render: (item: IPrScenePlan) => (
        <Space split="/" size={2} className="text-left full-h">
          {item.scriptLocation}
          <span>{item.scene}</span>
        </Space>
      ),
    },
    {
      title: '内容',
      dataIndex: 'mainContent',
      key: 'mainContent',
      width: 240,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '演员',
      key: 'mainActors',
      width: 250,
      align: 'center',
      render: (item: IPrScenePlan) => (
        <Flex vertical gap={4} className="text-left">
          <Dict title="主演" value={item.mainActors} />
          {!!item.specialActors && <Dict title="群特" value={item.specialActors} />}
          {!!item.groupExtraActors && <Dict title="群演" value={item.groupExtraActors} />}
        </Flex>
      ),
    },
    {
      title: '提示',
      dataIndex: '',
      width: 250,
      align: 'center',
      render: (item: IPrScenePlan) => (
        <Flex vertical gap={4} className="text-left">
          <Dict title="服化道" value={item.costumeMakeupTip || '无'}></Dict>
          {!!item.remark && <Dict title="备注" value={item.remark}></Dict>}
        </Flex>
      ),
    },
  ]

  // 初始化数据
  useEffect(() => {
    if (visible && productionId) {
      fetchSceneData()
    }
  }, [visible, productionId])

  return (
    <Drawer title="查看顺场表" placement="right" width={'80vw'} open={visible} onClose={onClose} loading={loading}>
      <Table
        columns={columns}
        dataSource={sceneData}
        sticky={{ offsetHeader: -24 }}
        rowKey={record => record.id || record.sort || Math.random()}
        pagination={false}
        size="small"
        rowClassName={record => {
          if (
            record.planType === PlanType.SCENE_DAY_DIVISION ||
            record.planType === PlanType.SCENE_TRANSITION_DIVISION
          ) {
            return styles.dividerRow
          }
          if (record.verification) {
            return styles.verifiedRow
          }
          return ''
        }}
      />
    </Drawer>
  )
}

export default SceneDataSidebar
