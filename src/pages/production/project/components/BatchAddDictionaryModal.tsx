import InputTag from '@/components/InputTag'
import { Button, Checkbox, Col, Drawer, Form, Input, Row, Select, Space, Switch, Typography, message } from 'antd'
import React, { useEffect, useState } from 'react'
import { CONTENT_TYPE_OPTIONS, ContentType } from '../../../../consts'
import useProjectStore, { type IPrProductionNoun } from '../store'

interface BatchAddDictionaryDrawerProps {
  visible: boolean
  onClose: () => void
  productionId: number
  batchData: any[] // 从接口返回的字典数组
}

interface BatchDictionaryFormItem {
  noun: string
  secondNoun: string
  firstNoun: string
  alias: string
  nounType: ContentType
  selected: boolean
}

interface BatchAddFormValues {
  dictionaries: BatchDictionaryFormItem[]
  isUpdateAnalysis: boolean
}

const BatchAddDictionaryModal: React.FC<BatchAddDictionaryDrawerProps> = ({
  visible,
  onClose,
  productionId,
  batchData,
}) => {
  const { saveProductionNoun } = useProjectStore()
  const [form] = Form.useForm()

  // 状态管理
  const [loading, setLoading] = useState(false)

  // 初始化表单数据
  useEffect(() => {
    if (visible && batchData && batchData.length > 0) {
      const formattedData: BatchDictionaryFormItem[] = batchData.map(item => ({
        noun: item.noun || '',
        secondNoun: item.secondNoun || '',
        firstNoun: item.firstNoun || '',
        alias: item.alias || '',
        nounType: item.nounType || ContentType.SCENE,
        selected: true, // 默认全选
      }))

      form.setFieldsValue({
        dictionaries: formattedData,
        isUpdateAnalysis: true, // 默认更新分析
      })
    }
  }, [visible, batchData, form])

  // 批量保存
  const handleBatchSave = async () => {
    try {
      const values: BatchAddFormValues = await form.validateFields()
      const selectedItems = values.dictionaries?.filter((item: BatchDictionaryFormItem) => item.selected) || []

      if (selectedItems.length === 0) {
        message.warning('请至少选择一个字典进行保存')

        return
      }

      setLoading(true)

      const nounsToSave: IPrProductionNoun[] = selectedItems.map((item: BatchDictionaryFormItem) => ({
        productionId,
        noun: item.noun,
        secondNoun: item.secondNoun,
        firstNoun: item.firstNoun,
        alias: item.alias,
        nounType: item.nounType,
      }))

      const success = await saveProductionNoun({
        productionId,
        nouns: nounsToSave,
        isUpdateAnalysis: values.isUpdateAnalysis,
      })

      if (success) {
        message.success(`成功保存 ${selectedItems.length} 个字典`)
        onClose()
      } else {
        message.error('批量保存失败')
      }
    } catch (error) {
      message.error('批量保存失败')
    } finally {
      setLoading(false)
    }
  }

  // 全选/取消全选
  const handleSelectAll = (checked: boolean) => {
    const dictionaries = form.getFieldValue('dictionaries') || []
    const updatedDictionaries = dictionaries.map((item: BatchDictionaryFormItem) => ({
      ...item,
      selected: checked,
    }))

    form.setFieldsValue({ dictionaries: updatedDictionaries })
  }

  // 获取选中数量
  const getSelectedCount = () => {
    const dictionaries = form.getFieldValue('dictionaries') || []

    return dictionaries.filter((item: BatchDictionaryFormItem) => item.selected).length
  }

  return (
    <Drawer
      title="批量新增字典"
      open={visible}
      onClose={onClose}
      width={1100}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={onClose} disabled={loading}>
              取消
            </Button>
            <Button type="primary" loading={loading} onClick={handleBatchSave}>
              保存选中的字典 ({getSelectedCount()})
            </Button>
          </Space>
        </div>
      }>
      <div style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Typography.Text type="secondary">
            从上传的文件中识别到 {batchData.length} 个字典，请选择需要保存的字典并确认类型：
          </Typography.Text>
          <Space>
            <Button size="small" onClick={() => handleSelectAll(true)}>
              全选
            </Button>
            <Button size="small" onClick={() => handleSelectAll(false)}>
              取消全选
            </Button>
          </Space>
        </Space>
      </div>

      <Form form={form} layout="vertical">
        {/* 是否更新分析字段 */}
        <Row gutter={16}>
          <Col>保存后更新场次分析</Col>
          <Col>
            <Form.Item name="isUpdateAnalysis" valuePropName="checked" style={{ marginBottom: 0 }}>
              <Switch />
            </Form.Item>
          </Col>
        </Row>

        <Form.List name="dictionaries">
          {fields => (
            <div>
              {fields.map(({ key, name, ...restField }) => {
                const currentSecondNoun = form.getFieldValue(['dictionaries', name, 'secondNoun'])
                const currentNounType = form.getFieldValue(['dictionaries', name, 'nounType'])

                return (
                  <div
                    key={key}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: 8,
                      padding: 8,
                      border: '1px solid #f0f0f0',
                      borderRadius: 4,
                      backgroundColor: '#fafafa',
                    }}>
                    <Form.Item
                      {...restField}
                      name={[name, 'selected']}
                      valuePropName="checked"
                      style={{ margin: 0, marginRight: 12 }}>
                      <Checkbox />
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'noun']}
                      style={{ margin: 0, marginRight: 12 }}
                      rules={[{ required: true, message: '请输入' }]}>
                      <Input placeholder="请输入" className="w200" />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'firstNoun']}
                      style={{ margin: 0, marginRight: 12 }}
                      hidden={currentNounType !== ContentType.SCENE}>
                      <Input placeholder="请输入主场景" className="w200" />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'secondNoun']}
                      style={{ margin: 0, marginRight: 12 }}
                      hidden={currentNounType !== ContentType.SCENE}>
                      <Input placeholder="请输入分场景" className="w200" />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'alias']} style={{ margin: 0, marginRight: 12 }}>
                      <InputTag placeholder="每输入1个别名，按回车确定" mode="string" />
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'nounType']}
                      style={{ width: 150, margin: 0, marginRight: 12 }}>
                      <Select options={CONTENT_TYPE_OPTIONS} size="small" placeholder="选择类型" disabled />
                    </Form.Item>
                  </div>
                )
              })}
            </div>
          )}
        </Form.List>
      </Form>
    </Drawer>
  )
}

export default BatchAddDictionaryModal
