import {
  DeleteOutlined,
  DragOutlined,
  DownloadOutlined,
  EditOutlined,
  PlusOutlined,
  ReloadOutlined,
  SaveOutlined,
} from '@ant-design/icons'
import { Dict } from '@fe/rockrose'
import {
  But<PERSON>,
  Card,
  Collapse,
  Divider,
  Drawer,
  Empty,
  message,
  Space,
  Typography,
} from 'antd'
import React, { useCallback, useEffect, useState, useRef } from 'react'
import { useDrag, useDrop } from 'react-dnd'
import useProjectStore, { type IPrScenePlan } from '../store'
import { ScenePlanEditModal } from './ScenePlanEditModal'
import styles from './ScenePlanSidebar.scss'
import { uuid } from '@/utils'
import { ATMOSPHERE_TYPE_CONFIG, LOCATION_TYPE_CONFIG, AtmosphereType, LocationType, PlanType } from '@/consts'
import { exportPlanTemplate } from '@/utils/export'

const { Text } = Typography

// 格式化后的数据结构
interface IFormattedScenePlan {
  scriptLocation: string
  planType: PlanType
  list: IPrScenePlan[]
  // 其他字段从第一个item中获取
  sort?: number
  atmosphere?: number
  locationType?: number
  pageNumber?: number
  shootingLocation?: string
  scene?: string
  mainContent?: string
  costumeMakeupTip?: string
  groupExtraActors?: string
  specialActors?: string
  mainActors?: string
  remark?: string
}

interface IScenePlanSidebarProps {
  productionId: number
  visible: boolean
  onClose: () => void
}

// 拖拽项目类型
const DRAG_TYPES = {
  SCENE_PLAN: 'scenePlan',
  SCENE_GROUP: 'sceneGroup',
}

// 单个场次计划项组件
const ScenePlanItem: React.FC<{
  item: IPrScenePlan
  index: number
  onEdit: (item: IPrScenePlan) => void
  onDelete: (item: IPrScenePlan) => void
  onMove: (dragIndex: number, hoverIndex: number) => void
  onAddDivider?: (planType: PlanType, scriptLocation?: string, sort?: number) => void
}> = ({ item, index, onEdit, onDelete, onMove, onAddDivider }) => {
  const [{ isDragging }, drag] = useDrag({
    type: DRAG_TYPES.SCENE_PLAN,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  const [, drop] = useDrop({
    accept: DRAG_TYPES.SCENE_PLAN,
    hover: (draggedItem: { index: number }) => {
      if (draggedItem.index !== index) {
        onMove(draggedItem.index, index)
        draggedItem.index = index
      }
    },
  })

  // 如果是分割线类型，渲染分割线样式
  if (item.planType === PlanType.SCENE_DAY_DIVISION || item.planType === PlanType.SCENE_TRANSITION_DIVISION) {
    const dividerText = item.planType === PlanType.SCENE_DAY_DIVISION ? '日期分割线' : '转场分割线'
    return (
      <div ref={(node) => drag(drop(node))} className={isDragging ? styles.dragItem : ''}>
        <div className={styles.dividerContainer}>
          <Divider className={styles.dividerStyle}>
            <Text className={styles.dividerText}>
              {dividerText}
            </Text>
          </Divider>
          <Space className={styles.actionButtons}>
            <DeleteOutlined
              className={styles.deleteButton}
              onClick={() => onDelete(item)}
              title="删除"
            />
            <DragOutlined
              className={styles.dragHandle}
            />
          </Space>
        </div>
      </div>
    )
  }

  return (
    <div ref={(node) => drag(drop(node))} className={isDragging ? styles.dragItem : ''}>
      <Card size="small" className={styles.scenePlanItem}>
        <Space direction="vertical" size={6} className={styles.fullWidth}>
          {item.planType === PlanType.SCENE_DETAIL ? <Space className={styles.spaceBetween}>
            <Text strong>{item.sceneNumber}</Text>
            <Space size={4}>
              {onAddDivider && (
                <>
                  <Button
                    type="text"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={() => onAddDivider(PlanType.SCENE_DAY_DIVISION, item.scriptLocation, (index || 0) + 1)}
                    title="添加日期分割线">
                    日期线
                  </Button>
                  <Button
                    type="text"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={() => onAddDivider(PlanType.SCENE_TRANSITION_DIVISION, item.scriptLocation, (index || 0) + 1)}
                    title="添加转场分割线">
                    转场线
                  </Button>
                </>
              )}
              {/* <DeleteOutlined
                onClick={() => onDelete(item)}
                className={styles.deleteButton}
                title="删除"
              /> */}
              <EditOutlined onClick={() => onEdit(item)} className={styles.editButton} />
              <DragOutlined className={styles.dragHandle} />
            </Space>
          </Space> : null}
          {item.planType === PlanType.SCENE_DETAIL && (
            <Space size={0} split={<Divider type="vertical" />} wrap>
              {item.atmosphere ? <Dict title="气氛" value={ATMOSPHERE_TYPE_CONFIG?.[item.atmosphere as AtmosphereType]?.label || item.atmosphere} /> : null}
              {item.locationType ? <Dict title="内/外景" value={LOCATION_TYPE_CONFIG?.[item.locationType as LocationType]?.label || item.locationType} /> : null}
              {item.pageNumber ? <Dict title="页数" value={item.pageNumber} /> : null}
              {item.shootingLocation ? <Dict title="拍摄场景" value={item.shootingLocation} /> : null}
              {item.scene ? <Dict title="场景" value={item.scene} /> : null}
              {item.mainContent ? <Dict title="主要内容" value={item.mainContent} /> : null}
              {item.costumeMakeupTip ? <Dict title="服化道提示" value={item.costumeMakeupTip} /> : null}
              {item.groupExtraActors ? <Dict title="群演" value={item.groupExtraActors} /> : null}
              {item.specialActors ? <Dict title="特约/群特" value={item.specialActors} /> : null}
              {item.mainActors ? <Dict title="主演" value={item.mainActors} /> : null}
            </Space>
          )}
          {item.remark && <Dict title="备注" value={item.remark} />}
        </Space>
      </Card>
    </div>
  )
}

// 场次组组件
const SceneGroup: React.FC<{
  group: IFormattedScenePlan
  index: number
  onEditItem: (item: IPrScenePlan) => void
  onDeleteItem: (item: IPrScenePlan) => void
  onMoveItem: (groupIndex: number, dragIndex: number, hoverIndex: number) => void
  onMoveGroup: (dragIndex: number, hoverIndex: number) => void
  onAddDivider: (planType: PlanType, scriptLocation?: string, sort?: number) => void
  expandedKeys: string[]
  onExpandChange: (keys: string[]) => void
}> = ({ group, index, onEditItem, onDeleteItem, onMoveItem, onMoveGroup, onAddDivider, expandedKeys, onExpandChange }) => {
  const [{ isDragging }, drag] = useDrag({
    type: DRAG_TYPES.SCENE_GROUP,
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  })

  const [, drop] = useDrop({
    accept: DRAG_TYPES.SCENE_GROUP,
    hover: (draggedItem: { index: number }) => {
      if (draggedItem.index !== index) {
        onMoveGroup(draggedItem.index, index)
        draggedItem.index = index
      }
    },
  })

  const handleMoveItem = useCallback((dragIndex: number, hoverIndex: number) => {
    onMoveItem(index, dragIndex, hoverIndex)
  }, [index, onMoveItem])

  // 如果 planType != 0，不使用 Collapse，直接显示分割线内容
  if (group.planType != PlanType.SCENE_DETAIL) {
    return (
      <div ref={(node) => drag(drop(node))} className={isDragging ? styles.dragItem : ''}>
        <div className={styles.scenePlanGroupSimple}>
          <div className={styles.scenePlanGroupContent}>
            <Space direction="vertical" size={8} className={styles.fullWidth}>
              {group.list.map((item, itemIndex) => {
                // 渲染分割线样式
                const dividerText = item.planType === PlanType.SCENE_DAY_DIVISION ? '日期分割线' : '转场分割线'
                return (
                  <div key={item.id || itemIndex} className={styles.dividerContainer}>
                    <Divider className={styles.dividerStyle}>
                      <Text className={styles.dividerText}>
                        {dividerText}
                      </Text>
                    </Divider>
                    <Space className={styles.actionButtons}>
                      <DeleteOutlined
                        className={styles.deleteButton}
                        onClick={() => onDeleteItem(item)}
                        title="删除"
                      />
                      <DragOutlined
                        className={styles.dragHandle}
                      />
                    </Space>
                  </div>
                )
              })}
            </Space>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div ref={(node) => drag(drop(node))} className={isDragging ? styles.dragItem : ''}>
      <Collapse
        size="small"
        className={styles.scenePlanGroup}
        activeKey={expandedKeys.includes(group.scriptLocation) ? [group.scriptLocation] : []}
        onChange={(keys) => {
          const newKeys = Array.isArray(keys) ? keys : [keys].filter(Boolean)
          const isExpanded = newKeys.includes(group.scriptLocation)
          let updatedKeys = [...expandedKeys]

          if (isExpanded && !expandedKeys.includes(group.scriptLocation)) {
            updatedKeys.push(group.scriptLocation)
          } else if (!isExpanded && expandedKeys.includes(group.scriptLocation)) {
            updatedKeys = updatedKeys.filter(key => key !== group.scriptLocation)
          }

          onExpandChange(updatedKeys)
        }}
        items={[
          {
            key: group.scriptLocation,
            label: (
              <Space className={styles.spaceBetween}>
                <Space>
                  <DragOutlined />
                  <Text strong>{group.scriptLocation}</Text>
                  <Text type="secondary">({group.list.filter(item => item.planType === PlanType.SCENE_DETAIL).length}个场次)</Text>
                </Space>
                <Space size={4}>
                  <Button
                    type="text"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={(e) => {
                      e.stopPropagation()
                      onAddDivider(PlanType.SCENE_DAY_DIVISION, '', index + 1)
                    }}
                    title="添加日期分割线">
                    日期线
                  </Button>
                  <Button
                    type="text"
                    size="small"
                    icon={<PlusOutlined />}
                    onClick={(e) => {
                      e.stopPropagation()
                      onAddDivider(PlanType.SCENE_TRANSITION_DIVISION, '', index + 1)
                    }}
                    title="添加转场分割线">
                    转场线
                  </Button>
                </Space>
              </Space>
            ),
            children: (
              <Space direction="vertical" size={8} className={styles.fullWidth}>
                {group.list.map((item, itemIndex) => (
                  <ScenePlanItem
                    key={item.id || itemIndex}
                    item={item}
                    index={itemIndex}
                    onEdit={onEditItem}
                    onDelete={onDeleteItem}
                    onMove={handleMoveItem}
                    onAddDivider={onAddDivider}
                  />
                ))}
              </Space>
            ),
          },
        ]}
      />
    </div>
  )
}

export const ScenePlanSidebar: React.FC<IScenePlanSidebarProps> = ({
  productionId,
  visible,
  onClose,
}) => {
  const { getScenePlan, refreshScenePlan, saveScenePlan, deleteScenePlan } = useProjectStore()

  // 状态管理
  const [formattedData, setFormattedData] = useState<IFormattedScenePlan[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [saving, setSaving] = useState(false)
  const [exporting, setExporting] = useState(false)

  // Collapse展开状态和滚动位置状态
  const [expandedKeys, setExpandedKeys] = useState<string[]>([])
  const scrollPositionRef = useRef(0)
  const containerRef = useRef<HTMLDivElement>(null)

  // 编辑弹窗状态
  const [editModal, setEditModal] = useState<{
    visible: boolean
    item: IPrScenePlan | null
    isAdd?: boolean
  }>({
    visible: false,
    item: null,
    isAdd: false,
  })



  // 格式化数据
  const formatData = (data: IPrScenePlan[]): IFormattedScenePlan[] => {
    const grouped = data.reduce((acc, item) => {
      const key = item.scriptLocation || '未分组'
      if (!acc[key]) {
        acc[key] = []
      }
      acc[key].push(item)
      return acc
    }, {} as Record<string, IPrScenePlan[]>)

    return Object.entries(grouped).map(([scriptLocation, list]) => {
      const firstItem = list[0]
      return {
        scriptLocation,
        planType: (firstItem.planType ?? PlanType.SCENE_DETAIL) as PlanType,
        list: list.sort((a, b) => (a.sort || 0) - (b.sort || 0)),
        sort: firstItem.sort,
        atmosphere: firstItem.atmosphere,
        locationType: firstItem.locationType,
        pageNumber: firstItem.pageNumber,
        shootingLocation: firstItem.shootingLocation,
        scene: firstItem.scene,
        mainContent: firstItem.mainContent,
        costumeMakeupTip: firstItem.costumeMakeupTip,
        groupExtraActors: firstItem.groupExtraActors,
        specialActors: firstItem.specialActors,
        mainActors: firstItem.mainActors,
        remark: firstItem.remark,
      }
    })
  }

  // 保存滚动位置
  const saveScrollPosition = () => {
    if (containerRef.current) {
      scrollPositionRef.current = containerRef.current.scrollTop
    }
  }

  // 恢复滚动位置
  const restoreScrollPosition = () => {
    if (containerRef.current && scrollPositionRef.current > 0) {
      setTimeout(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = scrollPositionRef.current
        }
      }, 100) // 延迟一点确保DOM已更新
    }
  }

  // 获取数据
  const fetchData = useCallback(async () => {
    if (!productionId) return

    // 保存当前滚动位置
    saveScrollPosition()

    setLoading(true)
    try {
      const data = await getScenePlan(productionId)
      if (data) {
        setFormattedData(formatData(data))
        // 数据更新后恢复滚动位置
        setTimeout(() => {
          restoreScrollPosition()
        }, 50)
      }
    } catch (error) {
      message.error('获取大计划数据失败')
    } finally {
      setLoading(false)
    }
  }, [productionId])

  // 刷新数据
  const handleRefresh = useCallback(async () => {
    if (!productionId) return

    setRefreshing(true)
    try {
      const success = await refreshScenePlan(productionId)
      if (success) {
        message.success('刷新成功')
        await fetchData()
      } else {
        message.error('刷新失败')
      }
    } catch (error) {
      message.error('刷新失败')
    } finally {
      setRefreshing(false)
    }
  }, [productionId])

  // 保存数据
  const handleSave = useCallback(async (newData?: IFormattedScenePlan[]) => {
    if (!productionId) return
    if (saving) {
      message.info('保存中')
      return
    }

    // 保存当前滚动位置
    saveScrollPosition()

    setSaving(true)
    try {
      // 将格式化数据转换回原始数据格式
      const flatData: IPrScenePlan[] = []
      let sortIndex = 0
      let arrGroup = newData || formattedData
      arrGroup.forEach((group) => {
        group.list.forEach((item) => {
          let cacheItem = { ...item, sort: sortIndex++, }
          delete cacheItem.updateTime
          delete cacheItem.createTime
          flatData.push(cacheItem)
        })
      })
      const success = await saveScenePlan({
        productionId,
        scenePlans: flatData,
      })

      if (success) {
        message.success('保存成功')
        await fetchData()
      } else {
        setSaving(false)
      }
    } catch (error) {
      message.error('保存失败')
    } finally {
      setSaving(false)
    }
  }, [productionId, formattedData])

  // 导出大计划
  const handleExport = useCallback(async () => {
    if (!productionId) return

    setExporting(true)
    try {
      await exportPlanTemplate(productionId)
      // message.success('导出成功')
    } catch (error) {
      message.error('导出失败')
    } finally {
      setExporting(false)
    }
  }, [productionId])

  // 初始化数据
  useEffect(() => {
    if (visible && productionId) {
      fetchData()
    } else {
      setExpandedKeys([])
      scrollPositionRef.current = 0
    }
  }, [visible, productionId, fetchData])

  // 编辑项目
  const handleEditItem = useCallback((item: IPrScenePlan) => {
    setEditModal({
      visible: true,
      item,
      isAdd: false,
    })
  }, [])

  // 删除项目
  const handleDeleteItem = async (item: IPrScenePlan) => {
    if (!item.id) {
      message.error('无法删除：ID不存在')
      return
    }

    // 保存当前滚动位置
    saveScrollPosition()

    try {
      const success = await deleteScenePlan(item.id)
      if (success) {
        message.success('删除成功')
        await fetchData()
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 处理展开状态变化
  const handleExpandChange = (keys: string[]) => {
    setExpandedKeys(keys)
  }

  // 添加分割线项目
  const handleAddDividerItem = (planType: PlanType, scriptLocation?: string, index?: number) => {
    // 创建一个新的分割线项目
    const newItem: IPrScenePlan = {
      productionId,
      planType: planType,
      sceneNumber: ``, // 临时场次编号
      scriptLocation: scriptLocation || uuid(),
      remark: '',
    }
    saveScrollPosition()
    handleSaveEdit(newItem, true, index)
  }

  // 移动组内项目
  const handleMoveItem = useCallback((groupIndex: number, dragIndex: number, hoverIndex: number) => {
    setFormattedData((prevData) => {
      const newData = [...prevData]
      const group = newData[groupIndex]
      const draggedItem = group.list[dragIndex]

      group.list.splice(dragIndex, 1)
      group.list.splice(hoverIndex, 0, draggedItem)

      return newData
    })
  }, [])

  // 移动组
  const handleMoveGroup = useCallback((dragIndex: number, hoverIndex: number) => {
    setFormattedData((prevData) => {
      const newData = [...prevData]
      const draggedGroup = newData[dragIndex]

      newData.splice(dragIndex, 1)
      newData.splice(hoverIndex, 0, draggedGroup)

      return newData
    })
  }, [])

  // 保存编辑
  const handleSaveEdit = async (values: Partial<IPrScenePlan>, isAdd: boolean = false, newIndex = 0) => {
    let updatedData: IFormattedScenePlan[]

    setFormattedData((prevData) => {
      const newData = [...prevData]

      if (editModal.isAdd || isAdd) {
        // 新增模式：创建新项目
        const newItem: IPrScenePlan = {
          ...editModal.item!,
          ...values,
          id: undefined, // 新增项目没有ID
        }

        // 查找或创建对应的组
        const scriptLocation = newItem.scriptLocation || '日期'
        let targetGroup = newData.find(group => group.scriptLocation === scriptLocation)
        if (!targetGroup) {
          // 创建新组
          targetGroup = {
            scriptLocation,
            planType: (newItem.planType ?? PlanType.SCENE_DAY_DIVISION) as PlanType,
            list: [],
          }
          newData.splice(newIndex, 0, targetGroup)
        }
        targetGroup.list.splice(newIndex, 0, newItem)
      } else {
        // 编辑模式：更新现有项目
        for (const group of newData) {
          const itemIndex = group.list.findIndex(item => item.id === editModal.item?.id)
          if (itemIndex !== -1) {
            group.list[itemIndex] = { ...group.list[itemIndex], ...values }
            break
          }
        }
      }

      updatedData = newData
      return newData
    })

    setEditModal({ visible: false, item: null, isAdd: false })
    // if (!editModal.isAdd && !isAdd) {
    handleSave(updatedData!)
    // }
  }

  return (
    <Drawer
      title="大计划详情"
      width="80vw"
      open={visible}
      onClose={onClose}
      loading={loading}
      destroyOnHidden
      extra={
        <Space>
          <Button
            type="default"
            icon={<DownloadOutlined />}
            loading={exporting}
            disabled={loading || saving || refreshing}
            onClick={handleExport}>
            导出大计划
          </Button>
          <Button
            type="default"
            icon={<ReloadOutlined />}
            loading={refreshing}
            disabled={loading || saving}
            onClick={handleRefresh}>
            生成或刷新
          </Button>

          <Button
            type="primary"
            icon={<SaveOutlined />}
            loading={saving}
            disabled={loading || refreshing}
            onClick={() => handleSave()}>
            保存
          </Button>
        </Space>
      }>
      <div className={styles.scenePlanContainer} ref={containerRef}>
        {formattedData.length > 0 ? (
          <Space direction="vertical" size={16} className={styles.fullWidth}>
            {formattedData.map((group, index) => (
              <SceneGroup
                key={group.scriptLocation}
                group={group}
                index={index}
                onEditItem={handleEditItem}
                onDeleteItem={handleDeleteItem}
                onMoveItem={handleMoveItem}
                onMoveGroup={handleMoveGroup}
                onAddDivider={handleAddDividerItem}
                expandedKeys={expandedKeys}
                onExpandChange={handleExpandChange}
              />
            ))}
          </Space>
        ) : (
          <Empty description="暂无大计划数据" />
        )}
      </div>

      {/* 编辑弹窗 */}
      <ScenePlanEditModal
        visible={editModal.visible}
        item={editModal.item}
        onOk={handleSaveEdit}
        onCancel={() => setEditModal({ visible: false, item: null, isAdd: false })}
        isAdd={editModal.isAdd}
      />
    </Drawer>
  )
}

export default ScenePlanSidebar
