// 主容器布局
.container {
  :global {
    .ant-splitter-bar-dragger::before {
      width: 1px !important;
    }
  }
}

.left {
  overflow-y: auto; // 文本区域独立滚动
  height: calc(100% - 40px);
  font-size: 15px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  white-space: pre-wrap;
  line-height: 2;
  user-select: text;
  cursor: text;
}

.right {
  padding-bottom: 42px;
  padding-left: 16px;

  :global {
    .ant-collapse-content-box {
      padding-block: 0 !important;
    }
  }
}

// 标注文本样式
.annotatedText {
  padding: 3px 6px;
  font-weight: 500;
  border: 1px solid var(--annotation-color);
  border-radius: 4px;
  background-color: var(--annotation-color);
  box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 6px rgb(0 0 0 / 15%);
  }
}
