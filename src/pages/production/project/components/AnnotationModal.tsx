import filterMatch from '@/utils/filterMatch'
import { Form, Input, Modal, Radio, Select, type FormInstance } from 'antd'
import React, { useEffect, useState } from 'react'
import { CONTENT_TYPE_OPTIONS, ContentType } from '../../../../consts'
import useStore from '../store'

interface AnnotationModalProps {
  visible: boolean
  form: FormInstance
  saving: boolean
  productionId: any
  onSave: () => void
  onCancel: () => void
}

export const AnnotationModal: React.FC<AnnotationModalProps> = ({
  visible,
  form,
  saving,
  productionId,
  onSave,
  onCancel,
}) => {
  const { getProductionNounList } = useStore()
  // 监听类型变化
  const selectedType = Form.useWatch('type', form)
  const [opts, setOpts] = useState<Array<any>>([])
  const initOpts = async () => {
    if (productionId) {
      const res = await getProductionNounList({
        pageIndex: 1,
        pageSize: 10000,
        productionId,
      })

      if (res?.list?.length) {
        setOpts(
          res.list.map(item => ({
            ...item,
            labelStr: `${item.noun}${
              item.nounType == ContentType.SCENE && item.firstNoun && item.secondNoun
                ? `(${item.firstNoun}/${item.secondNoun})`
                : ''
            }`,
            val: item.noun,
          }))
        )
      }
    }
  }

  // 当弹窗关闭时重置表单
  useEffect(() => {
    if (visible) {
      initOpts()
    } else {
      form.resetFields()
      setOpts([])
    }
  }, [visible, form])

  return (
    <Modal
      title="添加标注"
      open={visible}
      width={650}
      okText="立即保存"
      onOk={onSave}
      onCancel={onCancel}
      confirmLoading={saving}
      destroyOnHidden
      centered>
      <Form form={form} layout="vertical">
        <Form.Item label="类型" name="type" initialValue={ContentType.SCENE}>
          <Radio.Group options={CONTENT_TYPE_OPTIONS} />
        </Form.Item>
        <Form.Item label="文本" name="selectedText" rules={[{ required: true, message: '请输入选中文本' }]}>
          <Input />
        </Form.Item>
        {/* 当类型为场景时显示分场景字段 */}
        {selectedType === ContentType.SCENE && (
          <Form.Item label="主场景" name="firstText">
            <Input placeholder="请输入主场景信息" />
          </Form.Item>
        )}
        {selectedType === ContentType.SCENE && (
          <Form.Item label="分场景" name="secondText">
            <Input placeholder="请输入分场景信息" />
          </Form.Item>
        )}
        <Form.Item label="别名所属字典" name="mainText" tooltip="如果标注文本属于字典别名，请选择对应字典">
          <Select
            options={opts.filter(item => item.nounType == selectedType)}
            allowClear
            showSearch
            placeholder="如果选中文本属于字典别名，请选择对应字典"
            fieldNames={{ label: 'labelStr', value: 'val' }}
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.label || '')

              return Boolean(result)
            }}
          />
        </Form.Item>
        <Form.Item label="备注" name="description">
          <Input.TextArea placeholder="添加备注信息" rows={3} />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default AnnotationModal
