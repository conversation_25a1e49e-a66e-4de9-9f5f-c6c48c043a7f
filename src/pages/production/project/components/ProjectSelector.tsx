import { useDebounceFn } from 'ahooks'
import { Select, type SelectProps } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import useProjectStore, { IProductionListItem } from '../list/store'

interface ProjectSelectorProps extends SelectProps {
  value?: number
  onChange?: (value: number) => void
  onSelect?: (projectId: number, option: any) => void
  placeholder?: string
  disabled?: boolean
  allowClear?: boolean
  initOption?: any // 初始化选项，用于编辑时回填
  otherParams?:any
  filterFunc?:(items:IProductionListItem)=>IProductionListItem
}

// 项目选择器
const ProjectSelector: React.FC<ProjectSelectorProps> = ({
  value,
  onChange,
  onSelect,
  placeholder = '请选择项目',
  disabled = false,
  initOption,
  allowClear = true,
  otherParams={},
  filterFunc,
  ...props
}) => {
  const [options, setOptions] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const { fetchProductionList } = useProjectStore()
  const formatOpts = useMemo(() => {
    const opts = options.map(opt => {
      const item = { ...opt }

      item.labelStr = `${opt.productionName}${opt.productionCode ? `(${opt.productionCode})` : ''}`

      return item
    })
    if(filterFunc){
      return opts.filter(filterFunc)
    }

    return opts
  }, [options])

  // 初始化选项
  useEffect(() => {
    if (initOption) {
      setOptions([initOption])
    } else {
      loadDefaultProjects()
    }
  }, [initOption])

  // 加载默认项目列表
  const loadDefaultProjects = async () => {
    setLoading(true)
    try {
      const result = await fetchProductionList({
        pageIndex: 1,
        pageSize: 20, // 默认加载20个项目
        productionName: '', // 空搜索，获取所有项目
        ...otherParams,
      })

      if (result?.list) {
        const resOpts = result.list || []

        setOptions(resOpts)
      }
    } catch (error) {
      console.error('加载默认项目列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 防抖搜索
  const { run: debouncedSearch } = useDebounceFn(
    async (searchText: string) => {
      if (!searchText.trim()) {
        // 如果清空搜索，恢复初始选项或重新加载默认列表
        if (initOption) {
          setOptions([initOption])
        } else {
          await loadDefaultProjects()
        }

        return
      }

      setLoading(true)
      try {
        const result = await fetchProductionList({
          pageIndex: 1,
          pageSize: 50,
          productionName: searchText.trim(), // 按项目名称模糊搜索
          ...otherParams
        })

        if (result?.list) {
          // 格式化选项以匹配原有结构
          const resOpts = result.list || []

          // 如果有初始选项且当前值匹配，确保初始选项在结果中
          if (initOption && value === initOption.id && !resOpts.find(opt => opt.id === initOption.id)) {
            resOpts.unshift(initOption)
          }

          setOptions(resOpts)
        } else {
          setOptions(initOption ? [initOption] : [])
        }
      } catch (error) {
        console.error('搜索项目失败:', error)
        setOptions(initOption ? [initOption] : [])
      } finally {
        setLoading(false)
      }
    },
    { wait: 300 }
  )

  const handleSearch = (searchText: string) => {
    setSearchValue(searchText)
    debouncedSearch(searchText)
  }

  const handleChange = (selectedValue: number) => {
    onChange?.(selectedValue)
  }

  const handleSelect = (selectedValue: number, option: any) => {
    onSelect?.(selectedValue, option)
  }

  const getNotFoundContent = () => {
    if (loading) {
      return '搜索中...'
    }
    if (searchValue) {
      return '暂无匹配结果'
    }
    if (options.length === 0) {
      return '暂无项目数据'
    }

    return '请输入搜索关键词以筛选项目'
  }

  return (
    <Select
      {...props}
      placeholder={placeholder}
      showSearch
      loading={loading}
      allowClear={allowClear}
      disabled={disabled}
      options={formatOpts}
      fieldNames={{ label: 'labelStr', value: 'id' }}
      value={value}
      onChange={handleChange}
      onSelect={handleSelect}
      onSearch={handleSearch}
      searchValue={searchValue}
      filterOption={false} // 禁用本地过滤
      notFoundContent={getNotFoundContent()}
    />
  )
}

export default ProjectSelector
