import type { CameraShot, CameraStatus, ContentType } from '@/consts'
import { uuid } from '@/utils'
import { create } from 'zustand'
import { post, get as requestGet } from '../../../utils/request'

// 剧本场次信息（对应API的PrSceneInfo）
export interface IPrSceneInfo {
  id?: number // 主键ID
  productionId: number // 关联项目ID
  sort?: number // 排序
  sceneNumber?: string // 场次编号
  venueContent?: string // 场景内容
  content?: string // 内容描述
  contentAnalysis?: string // 内容解析
  robotContentAnalysis?: string // 服务端解析结果
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 场次分页查询参数（对应API的PagePrSceneInfoDto）
export interface IPagePrSceneInfoDto {
  pageIndex: number // 页码
  pageSize: number // 每页数量
  productionId: number // 项目ID
}

// 保存场次信息参数（对应API的SavePrSceneInfo）
export interface ISavePrSceneInfo {
  id: number // 主键ID
  contentAnalysis?: string // 内容解析
}

// 场次列表分页响应
export interface IPrSceneInfoListResponse {
  list: IPrSceneInfo[]
  total: number
  pageIndex: number
  pageSize: number
}

// 导入场次参数
export interface IImportSceneParams {
  productionId: number // 项目ID
  file: File // 上传的文件
}

// 名词信息（对应API的PrProductionNoun）
export interface IPrProductionNoun {
  id?: number // 自增主键ID
  productionId: number // 关联项目ID
  noun: string // 名词内容
  nounType: ContentType // 名词类型（0表示默认类型）
  secondNoun?: string
  firstNoun?: string // 一级名词内容
  alias?: string // 别名
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 名词分页查询参数（对应API的PrProductionNounDto）
export interface IPrProductionNounDto {
  pageIndex: number // 页码
  pageSize: number // 每页数量
  productionId: number // 项目ID
  noun?: string // 名词
  nounType?: ContentType[] // 名词类型
}

// 保存项目名词信息参数（对应API的SavePrProductionNounDto）
export interface ISavePrProductionNounDto {
  productionId: number // 项目ID
  nouns: IPrProductionNoun[] // 名词列表
  isUpdateAnalysis?: boolean // 是否更新分析
}

// 名词列表分页响应
export interface IPrProductionNounListResponse {
  list: IPrProductionNoun[]
  total: number
  pageIndex: number
  pageSize: number
}

// 大计划信息（对应API的PrScenePlan）
export interface IPrScenePlan {
  id?: number // 主键
  productionId: number // 关联项目ID
  sort?: number // 序号
  planType?: number // 计划类型（0场次明细，1场次汇总，2场次转场）
  sceneNumber?: string // 场次编号
  atmosphere?: number // 气氛（1日、2夜、3日转夜、4夜转日）
  atmosphereStr?: string // 气氛（日、夜、日转夜、夜转日）
  locationType?: number // 内/外景（1内、2外）
  locationTypeStr?: string // 内/外景（内、外）
  pageNumber?: number // 页数
  lineNumber?: number // 内容行数
  shootingLocation?: string // 拍摄场景
  scriptLocation?: string // 剧本场景
  scene?: string // 场景
  mainContent?: string // 主要内容
  costumeMakeupTip?: string // 服化道提示（参考）
  groupExtraActors?: string // 群演
  specialActors?: string // 特约/群特
  mainActors?: string // 主演
  remark?: string // 备注
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  mainActorsList?: string[] // 主演列表（只读）
  verification?: IPrSceneVerification // 消场记录
}

// 保存大计划参数（对应API的SavePrScenePlanDto）
export interface ISavePrScenePlanDto {
  productionId: number // 项目Id
  scenePlans?: IPrScenePlan[] // 计划列表
}

// 通告单用餐信息（对应API的PrSceneCallMeal）
export interface IPrSceneCallMeal {
  id?: number // 主键
  callId?: number // 通告单Id
  mealType: number // 用餐类型(1早餐，2午餐，3晚餐)
  mealTypeStr?: string // 用餐类型字符串（只读）
  mealTime?: string // 用餐时间
  location?: string // 用餐地点
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 通告单演员信息（对应API的PrSceneCallActor）
export interface IPrSceneCallActor {
  id?: number // 主键
  callId?: number // 通告单Id
  sort?: number // 排序
  roleType: number // 角色类型(1协领主演 2主演 3特邀)
  roleName?: string // 角色名
  actorName?: string // 演员名
  departureTime?: string // 出发时间
  arrivalTime?: string // 到场时间
  makeupStartTime?: string // 化妆开始时间
  makeupEndTime?: string // 化妆结束时间
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 通告单明细信息（对应API的PrSceneCallInfo）
export interface IPrSceneCallInfo {
  id?: number // 主键
  callId?: number // 通告单Id
  sort?: number // 序号
  planType?: number // 计划类型（0场次明细，1场次备注，2转场）
  sceneNumber?: string // 场次编号
  atmosphere?: number // 气氛（1日、2夜、3日转夜、4夜转日）
  atmosphereStr?: string // 气氛（日、夜、日转夜、夜转日）
  locationType?: number // 内/外景（1内、2外）
  locationTypeStr?: string // 内/外景（内、外）
  pageNumber?: number // 页数
  lineNumber?: number // 场景内容行数
  shootingLocation?: string // 拍摄场景
  scriptLocation?: string // 剧本场景
  scene?: string // 场景
  mainContent?: string // 主要内容
  costumeMakeupTip?: string // 服化道提示（参考）
  mainActors?: string // 主演
  groupExtraActors?: string // 群演
  specialActors?: string // 特约/群特
  remark?: string // 备注
  consultation?: string // 参考内容
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  mainActorsList?: string[] // 主演列表（只读）
  specialActorsList?: string[] // 特约列表（只读）
  verification?: IPrSceneVerification // 消场记录
  filmShotLogs?: IPrSceneFilmShotLog[] // 场记记录
}

// 通告单信息（对应API的PrSceneCall）
export interface IPrSceneCall {
  id?: number // 主键
  productionId: number // 关联项目ID
  dayNumber?: number // 第几天
  scheduleRemark?: string // 行程安排
  remark?: string // 备注
  responsibleDept?: string // 责任部门
  contact?: string // 联系方式
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  meals?: IPrSceneCallMeal[] // 用餐信息
  actors?: IPrSceneCallActor[] // 演员信息
  sceneInfos?: IPrSceneCallInfo[] // 信息
}

// 创建通告单参数（对应API的CreateSceneCallDto）
export interface ICreateSceneCallDto {
  productionId: number // 项目Id
  scheduleRemark?: string // 行程安排
  remark?: string // 备注
  responsibleDept?: string // 责任部门
  contact?: string // 联系方式
  meals?: IPrSceneCallMeal[] // 用餐信息
}

// 保存通告单餐饮参数（对应API的SaveSceneCallMealDto）
export interface ISaveSceneCallMealDto {
  callId: number // 通告单Id
  meals?: IPrSceneCallMeal[] // 用餐信息
}

// 保存通告单演员参数（对应API的SaveSceneCallActorDto）
export interface ISaveSceneCallActorDto {
  callId: number // 通告单Id
  actors?: IPrSceneCallActor[] // 演员时刻表
}

// 保存通告单场次参数（对应API的SaveSceneCallInfoDto）
export interface ISaveSceneCallInfoDto {
  callId: number // 通告单Id
  infos?: IPrSceneCallInfo[] // 通告单场次
}

// 消场记录信息（对应API的PrSceneVerification）
export interface IPrSceneVerification {
  id?: number // 销场记录ID
  productionId: number // 关联项目ID
  sceneNumber?: string // 场次编号
  callId?: number // 通告单Id
  creator?: string // 创建人
  remark?: string // 备注（异常原因等）
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 场记信息（对应API的PrSceneFilmShotLog）
export interface IPrSceneFilmShotLog {
  id?: number // 场记记录ID
  productionId: number // 关联项目ID
  sceneNumber?: string // 场次编号
  callId?: number // 通告单Id
  shotNumber?: number // 镜号
  takeNumber?: number // 次数（同镜号补拍次数）
  atmosphere?: string // 氛围描述（如 雨夜）
  cameraShotByA?: CameraShot // A机景别（使用CameraShot枚举）
  cameraShotByB?: CameraShot // B机景别（使用CameraShot枚举）
  content?: string // 拍摄内容描述
  cameraFileByA?: string // A机文件号（如 524）
  cameraFileByB?: string // B机文件号（如 441）
  cameraStatusByA?: CameraStatus // A机状态(0 pending, 1 ok ,2 ng)
  cameraStatusByB?: CameraStatus // B机状态(0 pending, 1 ok ,2 ng)
  remark?: string // 备注
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  creator?: string // 创建人
}

// 单日销场进度信息
export interface IDayVerificationProgress {
  day?: number // 第几天
  verificationCount?: number // 销场条数
  verificationPageCount?: number // 销场页数
  totalCount?: number // 总场次
  totalPageNum?: number // 总页数
}

// 销场进度信息（对应API的VerificationProgress）
export interface IVerificationProgress {
  totalPageNum?: number // 总页数
  totalCount?: number // 总场次
  dayTotal?: IDayVerificationProgress[] // 每日进度数据
}

// 场地统计信息（对应API的SceneVenueInfoDto）
export interface ISceneVenueInfoDto {
  mainVenueByPage?: string
  mainVenue?: string // 主场景
  venue?: string // 分场景
  pageCount?: number // 页数
  count?: number // 场次
  sort?: number // 序号
}

export interface IProjectStore {
  /* API调用方法 */
  // 导入场次
  importScene: (params: IImportSceneParams) => Promise<boolean>

  // 获取场次列表（分页）
  getSceneInfoList: (params: IPagePrSceneInfoDto) => Promise<IPrSceneInfoListResponse | null>
  // 根据ID获取场次详情
  getSceneInfoById: (id: number) => Promise<IPrSceneInfo | null>
  // 保存场次内容解析
  saveSceneInfo: (params: ISavePrSceneInfo) => Promise<boolean>
  // 刷新机器解析
  refreshRobotAnalysisById: (id: number) => Promise<boolean>

  // 获取名词列表（分页）
  getProductionNounList: (params: IPrProductionNounDto) => Promise<IPrProductionNounListResponse | null>
  getAllVenueNoun: (id: number) => Promise<Array<{ label: string; val: any }> | null>
  // 保存项目名词信息
  saveProductionNoun: (params: ISavePrProductionNounDto) => Promise<boolean>
  // 删除项目名词
  deleteProductionNoun: (id: number) => Promise<boolean>

  // 获取大计划
  getScenePlan: (productionId: number) => Promise<IPrScenePlan[] | null>
  // 获取顺场表预览
  getSceneData: (productionId: number) => Promise<IPrScenePlan[]>
  // 生成或刷新大计划
  refreshScenePlan: (productionId: number) => Promise<boolean>
  // 更新大计划
  saveScenePlan: (params: ISavePrScenePlanDto) => Promise<boolean>
  // 删除项目大计划场次
  deleteScenePlan: (id: number) => Promise<boolean>

  // 生成通告单
  createSceneCall: (params: ICreateSceneCallDto) => Promise<boolean>
  // 获取项目通告单列表
  getSceneCall: (productionId: number) => Promise<IPrSceneCall[]>
  // 获取项目通告单
  getSceneCallById: (id: number) => Promise<IPrSceneCall>
  // 保存项目通告单
  saveSceneCall: (params: IPrSceneCall) => Promise<boolean>
  // 保存项目通告单餐饮模块
  saveSceneCallMeal: (params: ISaveSceneCallMealDto) => Promise<boolean>
  // 保存项目通告单演员时刻模块
  saveSceneCallActor: (params: ISaveSceneCallActorDto) => Promise<boolean>
  // 保存项目通告单场次模块
  saveSceneCallInfo: (params: ISaveSceneCallInfoDto) => Promise<boolean>
  // 删除项目通告单
  deleteSceneCall: (id: number) => Promise<boolean>
  // 删除项目通告单场次
  deleteSceneCallInfo: (id: number) => Promise<boolean>

  // 保存项目消场记录
  saveSceneVerification: (params: IPrSceneVerification) => Promise<boolean>
  // 删除项目消场记录
  deleteSceneVerification: (id: number) => Promise<boolean>

  // 获取项目场记列表
  getFilmShotLogsById: (id: number, sceneNumber: string) => Promise<IPrSceneFilmShotLog[]>
  // 保存项目场记
  saveSceneFilmShotLog: (params: IPrSceneFilmShotLog) => Promise<boolean>
  // 删除项目场记
  deleteSceneFilmShotLog: (id: number) => Promise<boolean>

  // 获取项目剧本场次与场记日志
  getSceneAndFilmShotLogs: (productionId: number) => Promise<IPrSceneCallInfo[]>

  // 根据Id获取项目进度
  getProductionTrackProgress: (id: number) => Promise<IVerificationProgress | null>

  // 获取顺产表场地统计
  getScenePlanVenue: (productionId: number) => Promise<ISceneVenueInfoDto[]>

  // 变更场地场景关联的选择类型为主选
  updateVenueSelectionType: (id: number) => Promise<boolean>
}

export default create<IProjectStore>(() => ({
  // 导入场次
  importScene: async (params: IImportSceneParams) => {
    try {
      const formData = new FormData()

      formData.append('file', params.file)

      const { status } = await post<any, any>(
        `/PrProductions/ImportScene?productionId=${params.productionId}`,
        formData,
        { headers: { 'Content-Type': 'multipart/form-data' } }
      )

      return !!status
    } catch (error) {
      console.error('导入场次失败:', error)

      return false
    }
  },

  // 获取场次列表（分页）
  getSceneInfoList: async (params: IPagePrSceneInfoDto) => {
    try {
      const { data, status } = await post<any, any>('/PrProductions/GetSceneInfoList', params)

      if (status && data) {
        const list = Array.isArray(data?.dataList) ? data?.dataList : []

        return {
          list,
          total: data?.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      console.error('获取场次列表失败:', error)

      return null
    }
  },

  // 根据ID获取场次详情
  getSceneInfoById: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetSceneInfoById?id=${id}`)

      if (status && data) {
        return data?.dataList || null
      }

      return null
    } catch (error) {
      console.error('获取场次详情失败:', error)

      return null
    }
  },

  // 保存场次内容解析
  saveSceneInfo: async (params: ISavePrSceneInfo) => {
    try {
      const { status } = await post<any, any>('/PrProductions/SaveSceneInfo', params)

      return !!status
    } catch (error) {
      console.error('保存场次内容解析失败:', error)

      return false
    }
  },

  // 刷新机器解析
  refreshRobotAnalysisById: async (id: number) => {
    try {
      const { status } = await post<any, any>(`/PrProductions/RefreshRobotAnalysisById?id=${id}`)

      return !!status
    } catch (error) {
      console.error('刷新机器解析失败:', error)

      return false
    }
  },

  // 获取名词列表（分页）
  getProductionNounList: async (params: IPrProductionNounDto) => {
    try {
      const { data, status } = await post<any, any>('/PrProductions/GetProductionNounList', params)

      if (status && data) {
        const list = Array.isArray(data?.dataList) ? data?.dataList : []

        return {
          list,
          total: data?.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      console.error('获取名词列表失败:', error)

      return null
    }
  },
  getAllVenueNoun: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>('/PrProductions/AllVenueNoun', { params: { productionId } })

      if (status && data) {
        const list = Array.isArray(data?.dataList) ? data?.dataList : []

        return list
      }

      return null
    } catch (error) {
      console.error('获取名词列表失败:', error)

      return null
    }
  },

  // 保存项目名词信息
  saveProductionNoun: async (params: ISavePrProductionNounDto) => {
    try {
      const { status } = await post<any, any>('/PrProductions/SaveProductionNoun', params)

      return !!status
    } catch (error) {
      console.error('保存项目名词信息失败:', error)

      return false
    }
  },

  // 删除项目名词
  deleteProductionNoun: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrProductions/DeleteProductionNoun?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目名词失败:', error)

      return false
    }
  },

  // 获取大计划
  getScenePlan: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetScenePlan?productionId=${productionId}`)

      if (status && data?.dataList) {
        const arr: IPrScenePlan[] = Array.isArray(data.dataList) ? data.dataList : [data.dataList]

        arr.map(item => {
          if (!item.scriptLocation) {
            return (item.scriptLocation = uuid())
          }
        })
        arr.sort((a, b) => (a.sort || 0) - (b.sort || 0))

        return arr
      }

      return null
    } catch (error) {
      console.error('获取大计划失败:', error)

      return null
    }
  },

  // 获取顺场表预览
  getSceneData: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetSceneData?productionId=${productionId}`)

      if (status && data) {
        return data?.dataList || []
      }

      return null
    } catch (error) {
      console.error('获取顺场表预览失败:', error)

      return null
    }
  },

  // 生成或刷新大计划
  refreshScenePlan: async (productionId: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrProductions/RefreshScenePlan?productionId=${productionId}`)

      return !!status
    } catch (error) {
      console.error('生成或刷新大计划失败:', error)

      return false
    }
  },

  // 更新大计划
  saveScenePlan: async (params: ISavePrScenePlanDto) => {
    try {
      const { status } = await post<any, any>('/PrProductions/SaveScenePlan', params)

      return !!status
    } catch (error) {
      console.error('更新大计划失败:', error)

      return false
    }
  },

  // 删除项目大计划场次
  deleteScenePlan: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrProductions/DeleteScenePlan?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目大计划场次失败:', error)

      return false
    }
  },

  // 生成通告单
  createSceneCall: async (params: ICreateSceneCallDto) => {
    try {
      const { status } = await post<any, any>('/PrProductions/CreateSceneCall', params)

      return !!status
    } catch (error) {
      console.error('生成通告单失败:', error)

      return false
    }
  },

  // 获取项目通告单
  getSceneCall: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetSceneCall?productionId=${productionId}`)

      if (status && data?.dataList) {
        return data?.dataList || []
      }

      return []
    } catch (error) {
      console.error('获取项目通告单失败:', error)

      return []
    }
  },
  getSceneCallById: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrProductions/GetSceneCallById?id=${id}`)

      if (status && data?.dataList) {
        return data?.dataList || null
      }

      return null
    } catch (error) {
      console.error('获取项目通告单失败:', error)

      return null
    }
  },

  // 保存项目通告单
  saveSceneCall: async (params: IPrSceneCall) => {
    try {
      const { status } = await post<any, any>('/PrProductions/SaveSceneCall', params)

      return !!status
    } catch (error) {
      console.error('保存项目通告单失败:', error)

      return false
    }
  },

  // 保存项目通告单餐饮模块
  saveSceneCallMeal: async (params: ISaveSceneCallMealDto) => {
    try {
      const { status } = await post<any, any>('/PrProductions/SaveSceneCallMeal', params)

      return !!status
    } catch (error) {
      console.error('保存项目通告单餐饮模块失败:', error)

      return false
    }
  },

  // 保存项目通告单演员时刻模块
  saveSceneCallActor: async (params: ISaveSceneCallActorDto) => {
    try {
      const { status } = await post<any, any>('/PrProductions/SaveSceneCallActor', params)

      return !!status
    } catch (error) {
      console.error('保存项目通告单演员时刻模块失败:', error)

      return false
    }
  },

  // 保存项目通告单场次模块
  saveSceneCallInfo: async (params: ISaveSceneCallInfoDto) => {
    try {
      const { status } = await post<any, any>('/PrProductions/SaveSceneCallInfo', params)

      return !!status
    } catch (error) {
      console.error('保存项目通告单场次模块失败:', error)

      return false
    }
  },

  // 删除项目通告单
  deleteSceneCall: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrProductions/DeleteSceneCall?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目通告单失败:', error)

      return false
    }
  },

  // 删除项目通告单场次
  deleteSceneCallInfo: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrProductions/DeleteSceneCallInfo?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目通告单场次失败:', error)

      return false
    }
  },

  // 保存项目消场记录
  saveSceneVerification: async (params: IPrSceneVerification) => {
    try {
      const { status } = await post<any, any>('/PrProductions/SaveSceneVerification', params)

      return !!status
    } catch (error) {
      console.error('保存项目消场记录失败:', error)

      return false
    }
  },

  // 删除项目消场记录
  deleteSceneVerification: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrProductions/DeleteSceneVerification?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目消场记录失败:', error)

      return false
    }
  },

  // 获取项目场记列表
  getFilmShotLogsById: async (id: number, sceneNumber: string) => {
    try {
      const { data, status } = await requestGet<any, any>(
        `/PrProductions/GetFilmShotLogsById?id=${id}&sceneNumber=${encodeURIComponent(sceneNumber)}`
      )

      if (status && data?.dataList) {
        return Array.isArray(data.dataList) ? data.dataList : []
      }

      return []
    } catch (error) {
      console.error('获取项目场记列表失败:', error)

      return []
    }
  },

  // 保存项目场记
  saveSceneFilmShotLog: async (params: IPrSceneFilmShotLog) => {
    try {
      const { status } = await post<any, any>('/PrProductions/SaveSceneFilmShotLog', params)

      return !!status
    } catch (error) {
      console.error('保存项目场记失败:', error)

      return false
    }
  },

  // 删除项目场记
  deleteSceneFilmShotLog: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrProductions/DeleteSceneFilmShotLog?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除项目场记失败:', error)

      return false
    }
  },

  // 获取项目剧本场次与场记日志
  getSceneAndFilmShotLogs: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<any, any>(
        `/PrProductions/GetSceneAndFilmShotLogs?productionId=${productionId}`
      )

      if (status && data?.dataList) {
        return Array.isArray(data?.dataList) ? data.dataList : []
      }

      return []
    } catch (error) {
      console.error('获取项目剧本场次与场记日志失败:', error)

      return []
    }
  },

  // 根据Id获取项目进度
  getProductionTrackProgress: async (id: number) => {
    try {
      const { data, status } = await requestGet<IVerificationProgress, any>(
        `/PrProductions/GetProductionTrackProgress?id=${id}`
      )

      if (status && data) {
        return data
      }

      return null
    } catch (error) {
      console.error('获取项目进度失败:', error)

      return null
    }
  },

  // 获取顺产表场地统计
  getScenePlanVenue: async (productionId: number) => {
    try {
      const { data, status } = await requestGet<ISceneVenueInfoDto[], any>(
        `/PrProductions/GetScenePlanVenue?productionId=${productionId}`
      )

      if (status && data?.dataList) {
        return data?.dataList
      }

      return [null]
    } catch (error) {
      console.error('获取顺产表场地统计失败:', error)

      return []
    }
  },

  // 变更场地场景关联的选择类型为主选
  updateVenueSelectionType: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrVenue/UpdateVenueSelectionType?id=${id}`)

      return !!status
    } catch (error) {
      console.error('变更场地场景关联的选择类型为主选失败:', error)

      return false
    }
  },
}))
