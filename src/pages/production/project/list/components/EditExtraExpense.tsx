import { EXPENSE_TYPE_OPTIONS } from '@/consts'
import { Col, Form, Input, InputNumber, Modal, Row, Select } from 'antd'
import React, { useEffect } from 'react'
import { IProductionExtraExpensesItem } from '../store'

const { TextArea } = Input

interface IEditExtraExpenseProps {
  open: boolean
  expense?: IProductionExtraExpensesItem | null
  onCancel: () => void
  onOk: (values: any) => void
  loading?: boolean
}

const EditExtraExpense: React.FC<IEditExtraExpenseProps> = ({ open, expense, onCancel, onOk, loading = false }) => {
  const [form] = Form.useForm()
  const isEdit = !!expense?.id

  useEffect(() => {
    if (open) {
      if (isEdit && expense) {
        form.setFieldsValue({
          expenseType: expense.expenseType,
          quotedPrice: expense.quotedPrice,
          personCount: expense.personCount || 1,
          hasInvoice: expense.hasInvoice || false,
          description: expense.description || '',
          dayCount: expense.dayCount || 1,
          totalPrice: expense.totalPrice,
        })
      } else {
        form.resetFields()
        form.setFieldsValue({
          personCount: 1,
          hasInvoice: false,
          dayCount: 1,
        })
      }
    }
  }, [open, expense, form, isEdit])

  const handleOk = async () => {
    try {
      const values = await form.validateFields()

      onOk(values)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={isEdit ? '编辑费用' : '添加费用'}
      open={open}
      onCancel={handleCancel}
      onOk={handleOk}
      width={600}
      okText="立即保存"
      cancelText="取消"
      confirmLoading={loading}>
      <Form form={form} layout="vertical" style={{ marginTop: 20 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="expenseType" label="费用类型" rules={[{ required: true, message: '请选择费用类型' }]}>
              <Select
                placeholder="请选择费用类型"
                options={EXPENSE_TYPE_OPTIONS}
                showSearch
                filterOption={(input, option) =>
                  (option?.label?.toString() ?? '').toLowerCase().includes(input.toLowerCase())
                }
                disabled
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="hasInvoice" label="是否正规发票">
              <Select
                placeholder="请选择"
                options={[
                  { value: true, label: '是' },
                  { value: false, label: '否' },
                ]}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="personCount" label="人数" rules={[{ required: true, message: '请输入人数' }]}>
              <InputNumber className="full-h" placeholder="请输入人数" min={1} precision={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="dayCount" label="天数" rules={[{ required: true, message: '请输入天数' }]}>
              <InputNumber className="full-h" placeholder="请输入天数" min={1} precision={0} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="quotedPrice" label="单价(元)">
              <InputNumber className="full-h" placeholder="请输入单价" min={0} precision={2} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="totalPrice" label="总价(元)">
              <InputNumber className="full-h" placeholder="请输入总价" min={0} precision={2} />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name="description" label="备注">
          <TextArea rows={3} placeholder="请输入备注信息" maxLength={500} showCount />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default EditExtraExpense
