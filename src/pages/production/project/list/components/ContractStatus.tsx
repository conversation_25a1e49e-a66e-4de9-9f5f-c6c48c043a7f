import { CONTRACT_STATUS_CONFIG, CONTRACT_TYPE_CONFIG, ContractStatusEnum } from '@/consts'
import { Tag, Tooltip } from 'antd'
import React from 'react'
import { ICommContract } from '../store'

interface ContractStatusProps {
  contracts?: ICommContract[]
}

const ContractStatus: React.FC<ContractStatusProps> = ({ contracts }) => {
  if (!contracts || contracts.length === 0) {
    return <Tag>未签约</Tag>
  }

  // 找到保密合同
  const ndaContract = contracts.find(contract => contract.verify === 101)
  // 找到演员合同
  const actorContract = contracts.find(contract => contract.verify === 102)
  // 找到劳务合同
  const laborContract = contracts.find(contract => contract.verify === 103)
  // 其他合同
  const otherContracts = contracts.filter(contract => ![101, 102, 103].includes(contract.verify || 0))

  // 获取状态配置
  const getStatusConfig = (status: number | undefined) => {
    const contractStatus = (status || 0) as ContractStatusEnum

    return CONTRACT_STATUS_CONFIG[contractStatus] || { label: '未知状态', color: 'default' }
  }

  // 获取合同类型名称
  const getContractTypeName = (verify: number | undefined) => {
    if (!verify) {
      return '未知合同'
    }

    return CONTRACT_TYPE_CONFIG[verify] || `${verify}号合同`
  }

  return (
    <>
      {ndaContract && (
        <Tooltip title={`保密合同：${ndaContract.contractName || '未命名'}`}>
          <Tag color={getStatusConfig(ndaContract.status).color}>
            保密合同{getStatusConfig(ndaContract.status).label}
          </Tag>
        </Tooltip>
      )}
      {actorContract && (
        <Tooltip title={`演员合同：${actorContract.contractName || '未命名'}`}>
          <Tag color={getStatusConfig(actorContract.status).color}>
            演员合同{getStatusConfig(actorContract.status).label}
          </Tag>
        </Tooltip>
      )}
      {laborContract && (
        <Tooltip title={`劳务合同：${laborContract.contractName || '未命名'}`}>
          <Tag color={getStatusConfig(laborContract.status).color}>
            劳务合同{getStatusConfig(laborContract.status).label}
          </Tag>
        </Tooltip>
      )}
      {otherContracts.map((contract, index) => (
        <Tooltip key={contract.id} title={contract.contractName || '未命名'}>
          <Tag color={getStatusConfig(contract.status).color}>
            {getContractTypeName(contract.verify)}
            {getStatusConfig(contract.status).label}
          </Tag>
        </Tooltip>
      ))}
    </>
  )
}

export default ContractStatus
