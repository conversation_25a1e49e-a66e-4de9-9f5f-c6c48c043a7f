import { ROLE_TYPE_CONFIG } from '@/consts'
import PersonSelector from '@/pages/production/person/components/PersonSelector'
import { Button, Form, Input, InputNumber, Modal, Select, Space } from 'antd'
import React, { useEffect } from 'react'
import { IProductionListItem, IProductionPersonBasicItem } from '../store'

interface IEditPersonModalProps {
  open: boolean
  onCancel: () => void
  onSuccess: (values: any) => void
  loading?: boolean
  editingPerson: IProductionPersonBasicItem | null
  project?: IProductionListItem
}

const EditPersonModal: React.FC<IEditPersonModalProps> = ({
  open,
  onCancel,
  onSuccess,
  loading = false,
  editingPerson,
  project,
}) => {
  const [form] = Form.useForm()

  useEffect(() => {
    if (open && editingPerson) {
      form.setFieldsValue({
        personId: editingPerson.personId ? editingPerson.personId : null,
        personName: editingPerson.personName,
        roleType: editingPerson.roleType,
        quotedPrice: editingPerson.quotedPrice,
        isInternal: editingPerson.isInternal,
        personCount: editingPerson.personCount,
        hasInvoice: editingPerson.hasInvoice,
        description: editingPerson.description,
        dayCount: editingPerson.dayCount,
        totalPrice: editingPerson.totalPrice,
      })
    }
  }, [open, editingPerson, form])

  // 处理人员选择
  const handlePersonSelect = (personId: number, option: any) => {
    if (personId) {
      // 自动填充人员信息
      form.setFieldValue('personName', option.personName)
      form.setFieldValue('isInternal', option.isInternal)
    }
  }

  // 角色类型选项
  const roleTypeOptions = Object.entries(ROLE_TYPE_CONFIG).map(([key, value]) => ({
    value: parseInt(key, 10),
    label: value.label,
  }))

  const handleSubmit = () => {
    form.submit()
  }

  const handleFinish = (values: any) => {
    onSuccess(values)
  }

  return (
    <Modal
      title="编辑项目人员"
      open={open}
      onCancel={onCancel}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" onClick={handleSubmit} loading={loading}>
              立即保存
            </Button>
          </Space>
        </div>
      }
      width={600}
      destroyOnHidden>
      <Form
        form={form}
        layout="horizontal"
        colon={false}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 16 }}
        onFinish={handleFinish}>
        <Form.Item name="roleType" label="角色类型" rules={[{ required: true, message: '请选择角色类型' }]}>
          <Select placeholder="请选择角色类型" options={roleTypeOptions} disabled />
        </Form.Item>

        <Form.Item
          name="personId"
          label="选择人员"
          // rules={[{ required: true, message: '请选择人员' }]}
        >
          <PersonSelector
            roleType={editingPerson?.roleType}
            onSelect={handlePersonSelect}
            initOption={
              editingPerson && editingPerson.personId
                ? {
                    ...editingPerson,
                    id: editingPerson.personId ?? null,
                    val: editingPerson.personId ?? null,
                    personName: editingPerson?.personName,
                    label: editingPerson?.personName,
                  }
                : null
            }
          />
        </Form.Item>

        {/* 隐藏字段存储人员信息 */}
        <Form.Item name="personName" hidden>
          <Input />
        </Form.Item>

        <Form.Item name="personCount" label="人数" initialValue={1}>
          <InputNumber className="full-h" placeholder="请输入人数" min={0} precision={0} />
        </Form.Item>

        <Form.Item name="dayCount" label="天数" initialValue={1}>
          <InputNumber className="full-h" placeholder="请输入天数" min={1} precision={0} />
        </Form.Item>
        <Form.Item name="quotedPrice" label="单价">
          <InputNumber
            className="full-h"
            placeholder="请输入单价"
            min={0}
            precision={2}
            prefix={project?.currencySymbol || '¥'}
          />
        </Form.Item>
        <Form.Item name="totalPrice" label="总价">
          <InputNumber
            className="full-h"
            placeholder="请输入总价"
            min={0}
            precision={2}
            prefix={project?.currencySymbol || '¥'}
          />
        </Form.Item>

        <Form.Item name="hasInvoice" label="是否正规发票">
          <Select
            placeholder="请选择"
            options={[
              { value: true, label: '是' },
              { value: false, label: '否' },
            ]}
          />
        </Form.Item>

        <Form.Item name="description" label="备注">
          <Input.TextArea rows={4} placeholder="请输入备注信息" />
        </Form.Item>
        <Form.Item name="isInternal" hidden></Form.Item>
      </Form>
    </Modal>
  )
}

export default EditPersonModal
