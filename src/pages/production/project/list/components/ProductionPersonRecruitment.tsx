import QRCodeModal from '@/components/QRCodeModal'
import {
  ACTOR_ROLE_TYPE_CONFIG,
  ActorRoleType,
  GENDER_CONFIG,
  GENDER_MAP,
  PRICE_CURRENCY_CONFIG,
  RECRUITMENT_PARENT_TYPE_CONFIG,
  ROLE_TYPE_CONFIG,
  RoleType,
  SALARY_TYPE_CONFIG,
} from '@/consts'
import { MoreOutlined, PlusOutlined, QrcodeOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Button, Card, Divider, Dropdown, Empty, Flex, List, Popconfirm, Space, Tag, Typography, message } from 'antd'
import React, { useEffect, useState } from 'react'
import useProjectListStore, { IProductionListItem, IProductionPersonRecruitmentItem } from '../store'
import AddEditPersonRecruitment from './AddEditPersonRecruitment'

interface IProductionPersonRecruitmentProps {
  productionId: number
  loading?: boolean
  projectName?: string
  project?: IProductionListItem
}

const ProductionPersonRecruitment: React.FC<IProductionPersonRecruitmentProps> = ({
  productionId,
  loading = false,
  projectName,
  project,
}) => {
  const [recruitments, setRecruitments] = useState<IProductionPersonRecruitmentItem[]>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingRecruitment, setEditingRecruitment] = useState<IProductionPersonRecruitmentItem | null>(null)
  const [loadingData, setLoadingData] = useState(false)
  const [saving, setSaving] = useState(false)
  const [qrCodeModalOpen, setQrCodeModalOpen] = useState(false)

  const { getProductionPersonRecruitmentList, saveProductionPersonRecruitment, deleteProductionPersonRecruitment } =
    useProjectListStore()

  useEffect(() => {
    if (productionId) {
      loadRecruitments()
    }
  }, [productionId])

  // 加载招募人员列表
  const loadRecruitments = async () => {
    setLoadingData(true)
    try {
      const result = await getProductionPersonRecruitmentList(productionId)

      setRecruitments(result || [])
    } catch (error) {
      console.error('加载招募人员列表失败:', error)
      message.error('加载招募人员列表失败')
    } finally {
      setLoadingData(false)
    }
  }

  // 获取角色类型信息
  const getRoleTypeInfo = (parentType: number, roleType: number) => {
    if (parentType === 1) {
      // 人员
      return ROLE_TYPE_CONFIG[roleType as RoleType] || { label: '未知', color: 'default' }
    } else if (parentType === 2) {
      // 演员
      return ACTOR_ROLE_TYPE_CONFIG[roleType as ActorRoleType] || { label: '未知', color: 'default' }
    }

    return { label: '未知', color: 'default' }
  }

  // 处理添加招募人员
  const handleAddRecruitment = async (values: any) => {
    setSaving(true)
    try {
      const newRecruitment: IProductionPersonRecruitmentItem = {
        productionId,
        parentType: values.parentType,
        roleType: values.roleType,
        personCount: values.personCount || 1,
        gender: values.gender,
        description: values.description,
        salaryMin: values.salaryMin,
        salaryMax: values.salaryMax,
        salaryType: values.salaryType,
        currency: values.currency || 1,
        isAccommodation: values.isAccommodation || false,
        isMeal: values.isMeal || false,
        specialRequirements: values.specialRequirements,
      }

      const success = await saveProductionPersonRecruitment({
        productionId,
        personRecruitment: [newRecruitment],
      })

      if (success) {
        message.success('添加招募人员成功')
        setIsModalOpen(false)
        await loadRecruitments()
      }
    } catch (error) {
      console.error('添加招募人员失败:', error)
      message.error('添加招募人员失败')
    } finally {
      setSaving(false)
    }
  }

  // 处理编辑招募人员
  const handleEditRecruitment = (recruitment: IProductionPersonRecruitmentItem) => {
    setEditingRecruitment(recruitment)
    setIsModalOpen(true)
  }

  // 保存编辑
  const handleSaveEdit = async (values: any) => {
    if (!editingRecruitment) {
      return
    }

    setSaving(true)
    try {
      const updatedRecruitment: IProductionPersonRecruitmentItem = {
        ...editingRecruitment,
        parentType: values.parentType,
        roleType: values.roleType,
        personCount: values.personCount || 1,
        gender: values.gender,
        description: values.description,
        salaryMin: values.salaryMin,
        salaryMax: values.salaryMax,
        salaryType: values.salaryType,
        currency: values.currency || 1,
        isAccommodation: values.isAccommodation || false,
        isMeal: values.isMeal || false,
        specialRequirements: values.specialRequirements,
      }
      const success = await saveProductionPersonRecruitment({
        productionId,
        personRecruitment: [updatedRecruitment],
      })

      if (success) {
        message.success('招募人员信息更新成功')
        setIsModalOpen(false)
        setEditingRecruitment(null)
        await loadRecruitments()
      }
    } catch (error) {
      console.error('更新招募人员信息失败:', error)
      message.error('更新失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  // 处理删除招募人员
  const handleDeleteRecruitment = async (recruitment: IProductionPersonRecruitmentItem) => {
    try {
      if (!recruitment.id) {
        message.error('删除失败')

        return
      }
      const success = await deleteProductionPersonRecruitment(recruitment.id)

      if (success) {
        message.success('删除成功')
        await loadRecruitments()
      }
    } catch (error) {
      console.error('删除招募人员失败:', error)
      message.error('删除失败，请重试')
    }
  }

  // 处理弹窗关闭
  const handleModalCancel = () => {
    setIsModalOpen(false)
    setEditingRecruitment(null)
  }

  // 处理弹窗保存
  const handleModalOk = async (values: any) => {
    if (editingRecruitment) {
      await handleSaveEdit(values)
    } else {
      await handleAddRecruitment(values)
    }
  }

  // 渲染招募人员卡片
  const renderRecruitmentCard = (recruitment: IProductionPersonRecruitmentItem) => {
    const parentTypeInfo = RECRUITMENT_PARENT_TYPE_CONFIG[recruitment.parentType] || { label: '未知', color: 'default' }
    const roleInfo = getRoleTypeInfo(recruitment.parentType, recruitment.roleType)
    const genderInfo = GENDER_CONFIG[recruitment.gender || 3] || { label: '保密', color: 'purple' }
    const salaryTypeInfo = SALARY_TYPE_CONFIG[recruitment.salaryType || 1] || { label: '单价', color: 'blue' }
    const currencyInfo = PRICE_CURRENCY_CONFIG[recruitment.currency || 1] || { label: '人民币 (¥)', color: 'green' }

    return (
      <List.Item key={recruitment.id}>
        <Card size="small" className="full-h hover-move">
          <Flex justify="space-between" gap={12}>
            <Flex vertical flex={1} gap={8}>
              <Flex justify="space-between">
                <Space size={0} split={<Divider type="vertical" />}>
                  <Typography.Text strong>{roleInfo.label}</Typography.Text>
                  <Dict
                    title="性别"
                    value={recruitment.gender ? GENDER_MAP[recruitment.gender].label || '不限' : '不限'}
                  />
                  {recruitment.personCount && recruitment.personCount > 1 && (
                    <Dict title="人数" value={recruitment.personCount}></Dict>
                  )}
                  {(recruitment.salaryMin || recruitment.salaryMax) && (
                    <Dict
                      title={salaryTypeInfo.label}
                      value={
                        <>
                          {currencyInfo.label.split(' ')[1]?.replace(/[()]/g, '')}
                          {recruitment.salaryMin || 0}
                          {recruitment.salaryMax &&
                            recruitment.salaryMax !== recruitment.salaryMin &&
                            ` ~ ${recruitment.salaryMax}`}
                        </>
                      }></Dict>
                  )}
                </Space>
                <Space>
                  {recruitment.isAccommodation && <Tag className="text-primary">包住宿</Tag>}
                  {recruitment.isMeal && <Tag className="text-primary">包餐食</Tag>}
                </Space>
              </Flex>

              {recruitment.description && (
                <Typography.Text type="secondary" ellipsis={{ tooltip: true }}>
                  {recruitment.description}
                </Typography.Text>
              )}
              {recruitment.specialRequirements && (
                <Typography.Text ellipsis={{ tooltip: true }}>
                  <Space>
                    <Typography.Text type="warning">特殊要求</Typography.Text>
                    {recruitment.specialRequirements}
                  </Space>
                </Typography.Text>
              )}
            </Flex>
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'edit',
                    label: '编辑',
                    onClick: () => handleEditRecruitment(recruitment),
                  },
                  {
                    key: 'delete',
                    disabled: project?.feNoEdit,
                    label: (
                      <Popconfirm
                        title="警告"
                        description={'确定要删除这个招募信息吗？'}
                        onConfirm={() => handleDeleteRecruitment(recruitment)}
                        okText="确定删除"
                        cancelText="取消">
                        <Typography.Text type="danger">删除</Typography.Text>
                      </Popconfirm>
                    ),
                  },
                ],
              }}
              trigger={['click']}
              placement="bottomRight">
              <Button type="text" icon={<MoreOutlined />} />
            </Dropdown>
          </Flex>
        </Card>
      </List.Item>
    )
  }

  const qrCodeUrl = `https://mpr.cdreader.com/#/login?projectId=${productionId}`

  return (
    <Flex vertical gap={0}>
      <ListHeader title="招募人员" total={recruitments.length}>
        <Space>
          <Button
            color="primary"
            variant="filled"
            shape="round"
            icon={<QrcodeOutlined />}
            onClick={() => setQrCodeModalOpen(true)}
            title="招募二维码">
            招募二维码
          </Button>
          {!project?.feNoEdit && (
            <Button type="primary" ghost shape="round" icon={<PlusOutlined />} onClick={() => setIsModalOpen(true)}>
              添加招募
            </Button>
          )}
        </Space>
      </ListHeader>
      {recruitments.length > 0 ? (
        <List
          className="list-sm"
          size="small"
          split={false}
          dataSource={recruitments}
          renderItem={renderRecruitmentCard}
          pagination={false}
          loading={loadingData}
        />
      ) : (
        <Empty />
      )}

      {/* 添加/编辑招募人员弹窗 */}
      {isModalOpen ? (
        <AddEditPersonRecruitment
          open={isModalOpen}
          recruitment={editingRecruitment}
          onCancel={handleModalCancel}
          onOk={handleModalOk}
          loading={saving}
          priceCurrency={project?.priceCurrency}
        />
      ) : null}

      {/* 招募二维码弹窗 */}
      {qrCodeModalOpen ? (
        <QRCodeModal
          open={qrCodeModalOpen}
          onCancel={() => setQrCodeModalOpen(false)}
          url={qrCodeUrl}
          title="招募二维码"
          description="扫描二维码或分享链接进行招募"
          fileName={`项目${productionId}_招募二维码`}
        />
      ) : null}
    </Flex>
  )
}

export default ProductionPersonRecruitment
