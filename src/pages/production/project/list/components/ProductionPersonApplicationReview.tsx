import { ACTOR_ROLE_TYPE_CONFIG, ActorRoleType, APPLICATION_STATUS_CONFIG, ROLE_TYPE_CONFIG, RoleType } from '@/consts'
import ActorDetail from '@/pages/production/actor/components/ActorDetail'
import PersonDetail from '@/pages/production/person/components/PersonDetail'
import { ListHeader } from '@fe/rockrose'
import { Badge, Divider, Empty, Flex, message, Space, Table, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import useProjectListStore, {
  IPersonApplicationItem,
  IPersonApplicationSearchParams,
  IProductionListItem,
  IUpdatePersonApplicationStatusParams,
} from '../store'
import PersonApplicationDetailDrawer from './PersonApplicationDetailDrawer'
import PersonApplicationReviewModal from './PersonApplicationReviewModal'

interface IProductionPersonApplicationReviewProps {
  productionId: number
  loading?: boolean
  projectName?: string
  project?: IProductionListItem
}

const ProductionPersonApplicationReview: React.FC<IProductionPersonApplicationReviewProps> = ({
  productionId,
  loading = false,
  projectName,
  project,
}) => {
  const [applications, setApplications] = useState<IPersonApplicationItem[]>([])
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  })
  const [loadingData, setLoadingData] = useState(false)
  const [reviewing, setReviewing] = useState(false)
  const [reviewModalOpen, setReviewModalOpen] = useState(false)
  const [currentApplication, setCurrentApplication] = useState<IPersonApplicationItem | null>(null)
  const [detailDrawerOpen, setDetailDrawerOpen] = useState(false)
  const [isPersonDetailOpen, setIsPersonDetailOpen] = useState(false)
  const [isActorDetailOpen, setIsActorDetailOpen] = useState(false)
  const [selectedPersonId, setSelectedPersonId] = useState<number | null>(null)
  const [selectedActorId, setSelectedActorId] = useState<number | null>(null)

  const { getPersonApplicationList, updatePersonApplicationStatus } = useProjectListStore()

  useEffect(() => {
    if (productionId) {
      loadApplications()
    }
  }, [productionId, pagination.current, pagination.pageSize])

  // 加载申请列表
  const loadApplications = async () => {
    setLoadingData(true)
    try {
      const params: IPersonApplicationSearchParams = {
        pageIndex: pagination.current,
        pageSize: pagination.pageSize,
        productionId,
      }

      const result = await getPersonApplicationList(params)

      if (result) {
        setApplications(result.list || [])
        setPagination(prev => ({
          ...prev,
          total: result.total,
        }))
      }
    } catch (error) {
      console.error('加载申请列表失败:', error)
      message.error('加载申请列表失败')
    } finally {
      setLoadingData(false)
    }
  }

  // 获取角色类型信息
  const getRoleTypeInfo = (parentType: number, roleType: number) => {
    if (parentType === 1) {
      // 人员
      return ROLE_TYPE_CONFIG[roleType as RoleType] || { label: '未知', color: 'default' }
    } else if (parentType === 2) {
      // 演员
      return ACTOR_ROLE_TYPE_CONFIG[roleType as ActorRoleType] || { label: '未知', color: 'default' }
    }

    return { label: '未知', color: 'default' }
  }

  // 处理查看详情
  const handleViewDetail = (application: IPersonApplicationItem) => {
    setCurrentApplication(application)
    setDetailDrawerOpen(true)
  }

  // 处理审核操作
  const handleReview = (application: IPersonApplicationItem) => {
    setCurrentApplication(application)
    setReviewModalOpen(true)
  }

  // 处理申请人姓名点击
  const handlePersonNameClick = (application: IPersonApplicationItem, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!application.personId) {
      return
    }

    if (application.parentType === 1) {
      // 工作人员
      setSelectedPersonId(application.personId)
      setIsPersonDetailOpen(true)
    } else if (application.parentType === 2) {
      // 演员 - 需要使用 actorId，如果没有则使用 personId
      const actorId = (application as any).actorId || application.personId

      setSelectedActorId(actorId)
      setIsActorDetailOpen(true)
    }
  }

  // 提交审核
  const handleSubmitReview = async (values: { status: number; reviewComment: string }) => {
    if (!currentApplication) {
      return
    }

    setReviewing(true)
    try {
      const params: IUpdatePersonApplicationStatusParams = {
        id: currentApplication.id!,
        status: values.status,
        reviewComment: values.reviewComment,
      }

      const success = await updatePersonApplicationStatus(params)

      if (success) {
        message.success('审核完成')
        setReviewModalOpen(false)
        setCurrentApplication(null)
        await loadApplications()
      }
    } catch (error) {
      console.error('审核失败:', error)
      message.error('审核失败，请重试')
    } finally {
      setReviewing(false)
    }
  }

  // 分页改变处理
  const handlePaginationChange = (page: number, pageSize?: number) => {
    setPagination(prev => ({
      ...prev,
      current: page,
      pageSize: pageSize || prev.pageSize,
    }))
  }

  // 表格列配置
  const columns = [
    {
      title: '申请人',
      dataIndex: 'personName',
      key: 'personName',
      width: 120,
      align: 'center' as const,
      render: (text: string, record: IPersonApplicationItem) => (
        <Space size={8}>
          <div>
            {record.personId ? (
              <Typography.Link onClick={e => handlePersonNameClick(record, e)}>{text || '未知'}</Typography.Link>
            ) : (
              <Typography.Text>{text || '未知'}</Typography.Text>
            )}
          </div>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'parentType',
      key: 'parentType',
      width: 100,
      align: 'center',
      render: (parentType: number, record: IPersonApplicationItem) => {
        const typeInfo = getRoleTypeInfo(parentType, record.roleType)

        return <Typography.Text>{typeInfo.label}</Typography.Text>
      },
    },
    {
      title: '期望薪资',
      dataIndex: 'expectedSalary',
      key: 'expectedSalary',
      width: 100,
      align: 'center',
      render: (salary: number) =>
        salary ? (
          <Typography.Text>
            {project?.currencySymbol || '¥'}
            {salary.toLocaleString()}
          </Typography.Text>
        ) : (
          '面议'
        ),
    },
    {
      title: '申请时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 100,
      align: 'center',
      render: (time: string) => (time ? dayjs(time).format('MM/DD HH:mm') : '-'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      align: 'center',
      render: (status: number) => {
        const config = APPLICATION_STATUS_CONFIG[status]

        return config ? (
          <Badge status={config.color} text={config.label}></Badge>
        ) : (
          <Badge status="default" text={'未知'} />
        )
      },
    },

    {
      title: '操作',
      key: 'actions',
      align: 'center',
      width: 120,
      render: (_: any, record: IPersonApplicationItem) => (
        <Space size={0} split={<Divider type="vertical" />}>
          <Typography.Link onClick={() => handleViewDetail(record)}>详情</Typography.Link>
          {record.status === 1 && !project?.feNoEdit && (
            <Typography.Link onClick={() => handleReview(record)}>审核</Typography.Link>
          )}
        </Space>
      ),
    },
  ]

  return (
    <Flex vertical>
      <ListHeader title="招募审批" total={pagination.total} />
      <Table
        dataSource={applications}
        columns={columns}
        rowKey="id"
        loading={loading || loadingData}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          onChange: handlePaginationChange,
        }}
        locale={{ emptyText: <Empty /> }}
      />
      {/* 详情抽屉 */}
      {detailDrawerOpen ? (
        <PersonApplicationDetailDrawer
          open={detailDrawerOpen}
          application={currentApplication}
          project={project}
          onClose={() => setDetailDrawerOpen(false)}
        />
      ) : null}

      {/* 审核模态框 */}
      {reviewModalOpen ? (
        <PersonApplicationReviewModal
          open={reviewModalOpen}
          application={currentApplication}
          loading={reviewing}
          onClose={() => setReviewModalOpen(false)}
          onSubmit={handleSubmitReview}
        />
      ) : null}
      {/* 工作人员详情 */}
      {isPersonDetailOpen && selectedPersonId ? (
        <PersonDetail
          open={isPersonDetailOpen}
          personId={selectedPersonId}
          onCancel={() => {
            setIsPersonDetailOpen(false)
            setSelectedPersonId(null)
          }}
        />
      ) : null}

      {/* 演员详情 */}
      {isActorDetailOpen && selectedActorId ? (
        <ActorDetail
          visible={isActorDetailOpen}
          actorId={selectedActorId}
          onClose={() => {
            setIsActorDetailOpen(false)
            setSelectedActorId(null)
          }}
        />
      ) : null}
    </Flex>
  )
}

export default ProductionPersonApplicationReview
