.videoCard {
  position: relative;
  overflow: visible;
  width: 120px;
  height: 160px;

  video {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

// 文档卡片样式
.documentCard {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  width: 120px;
  height: 160px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  flex-direction: column;
  cursor: pointer;
}

// 文档文件名样式
.documentFileName {
  display: -webkit-box;
  overflow: hidden;
  margin-top: 8px;
  font-size: 12px;
  text-align: center;
  color: #666;
  word-break: break-all;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 下载图标样式
.downloadIcon {
  margin-top: 4px;
  color: #1890ff;
}

// 文档类型图标样式
.documentIcon {
  font-size: 32px;

  &.word {
    color: #1890ff;
  }

  &.excel {
    color: #52c41a;
  }

  &.pdf {
    color: #f5222d;
  }

  &.default {
    color: #666;
  }
}

// Tabs 容器样式
.tabsContainer {
  min-height: 60vh;
}

.readonlyInput {
  background-color: #f5f5f5;
}

.numberInput {
  width: 100%;
}

.formFooter {
  margin-bottom: 0;
  text-align: right;
}