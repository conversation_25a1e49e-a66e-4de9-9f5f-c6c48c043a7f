import useIndexStore from '@/store'
import { FolderAddOutlined, MoreOutlined, PlusOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Button, Card, Divider, Dropdown, Empty, Flex, List, message, Popconfirm, Space, Tag, Typography } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import { ACTOR_ROLE_TYPE_CONFIG, EXPENSE_TYPE_CONFIG, ExpenseType, ROLE_TYPE_CONFIG } from '../../../../../consts'
import useProjectListStore, { IProductionExtraExpensesItem, IProductionListItem } from '../store'
import AddExtraExpense from './AddExtraExpense'
import AddPackageBudget from './AddPackageBudget'
import EditExtraExpense from './EditExtraExpense'

const { Text } = Typography

interface IProductionExtraExpensesProps {
  productionId: number
  loading?: boolean
  project?: IProductionListItem
}

const ProductionExtraExpenses: React.FC<IProductionExtraExpensesProps> = ({
  productionId,
  loading = false,
  project,
}) => {
  const [expenseList, setExpenseList] = useState<IProductionExtraExpensesItem[]>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isPackageBudgetOpen, setIsPackageBudgetOpen] = useState(false)
  const [editingExpense, setEditingExpense] = useState<IProductionExtraExpensesItem | null>(null)
  const [loadingData, setLoadingData] = useState(false)
  const { authorBtn } = useIndexStore()

  const { getProductionExtraExpensesList, saveProductionExtraExpenses, deleteProductionExtraExpenses } =
    useProjectListStore()

  useEffect(() => {
    if (productionId) {
      loadExpenseList()
    }
  }, [productionId])

  // 加载费用列表
  const loadExpenseList = async () => {
    setLoadingData(true)
    try {
      const result = await getProductionExtraExpensesList(productionId)

      setExpenseList(result || [])
    } catch (error) {
      message.error('加载费用列表失败')
    } finally {
      setLoadingData(false)
    }
  }

  // 获取费用类型信息
  const getExpenseTypeInfo = (expenseType: number) =>
    EXPENSE_TYPE_CONFIG[expenseType as keyof typeof EXPENSE_TYPE_CONFIG] || { label: '未知', color: 'default' }

  // 处理新增费用
  const handleAddExpense = () => {
    setIsModalOpen(true)
  }

  // 处理新增打包费用
  const handleAddPackageBudget = () => {
    setIsPackageBudgetOpen(true)
  }

  // 处理打包费用添加成功
  const handlePackageBudgetSuccess = async (packageData: any) => {
    try {
      const newExpense = {
        ...packageData,
        productionId,
        sort: expenseList.length + 1,
      }

      const success = await saveProductionExtraExpenses({
        productionId,
        extraExpenses: [...expenseList, newExpense],
      })

      if (success) {
        message.success('打包费用添加成功')
        setIsPackageBudgetOpen(false)
        await loadExpenseList()
      }
    } catch (error) {
      console.error('添加打包费用失败:', error)
      message.error('添加失败')
    }
  }

  // 处理批量添加费用成功
  const handleAddExpensesSuccess = async (newExpenseList: any[]) => {
    try {
      // 检查新添加的费用类型是否与现有费用重复
      const existingExpenseTypes = expenseList.map(item => item.expenseType)
      const newExpenseTypes = newExpenseList.map(item => item.expenseType)

      const duplicateTypes = newExpenseTypes.filter(type => existingExpenseTypes.includes(type))

      if (duplicateTypes.length > 0) {
        const duplicateTypeNames = duplicateTypes.map(
          type => EXPENSE_TYPE_CONFIG[type as keyof typeof EXPENSE_TYPE_CONFIG]?.label || '未知类型'
        )

        message.warning(`以下费用类型已存在：${duplicateTypeNames.join('、')}，请检查后重新添加`)

        return
      }

      // 为每个费用添加项目ID和排序
      const expensesWithProjectId = newExpenseList.map((expense, index) => ({
        ...expense,
        productionId,
        sort: expenseList.length + index + 1,
      }))

      const success = await saveProductionExtraExpenses({
        productionId,
        extraExpenses: [...expenseList, ...expensesWithProjectId],
      })

      if (success) {
        message.success(`成功添加 ${newExpenseList.length} 项费用`)
        setIsModalOpen(false)
        await loadExpenseList()
      }
    } catch (error) {
      console.error('添加费用失败:', error)
      message.error('添加失败')
    }
  }

  // 处理编辑费用
  const handleEditExpense = (expense: IProductionExtraExpensesItem) => {
    setEditingExpense(expense)

    // 如果是打包费用，使用打包费用编辑组件
    if (expense.expenseType === ExpenseType.PackageBudget) {
      setIsPackageBudgetOpen(true)
    } else {
      setIsEditModalOpen(true)
    }
  }

  // 处理保存编辑费用
  const handleSaveEditExpense = async (values: any) => {
    if (!editingExpense?.id) {
      message.error('无法编辑该费用')

      return
    }

    try {
      const newExpenseList = [...expenseList]
      const index = newExpenseList.findIndex(item => item.id === editingExpense.id)

      if (index !== -1) {
        newExpenseList[index] = { ...editingExpense, ...values }

        const success = await saveProductionExtraExpenses({
          productionId,
          extraExpenses: newExpenseList,
        })

        if (success) {
          message.success('编辑成功')
          setIsEditModalOpen(false)
          setEditingExpense(null)
          await loadExpenseList()
        }
      }
    } catch (error) {
      console.error('编辑费用失败:', error)
      message.error('编辑失败')
    }
  }

  // 处理保存编辑打包费用
  const handleSaveEditPackageBudget = async (packageData: any) => {
    if (!editingExpense?.id) {
      message.error('无法编辑该费用')

      return
    }

    try {
      const newExpenseList = [...expenseList]
      const index = newExpenseList.findIndex(item => item.id === editingExpense.id)

      if (index !== -1) {
        newExpenseList[index] = { ...editingExpense, ...packageData }

        const success = await saveProductionExtraExpenses({
          productionId,
          extraExpenses: newExpenseList,
        })

        if (success) {
          message.success('编辑成功')
          setIsPackageBudgetOpen(false)
          setEditingExpense(null)
          await loadExpenseList()
        }
      }
    } catch (error) {
      console.error('编辑打包费用失败:', error)
      message.error('编辑失败')
    }
  }

  // 处理删除费用
  const handleDeleteExpense = async (expense: IProductionExtraExpensesItem) => {
    if (!expense.id) {
      message.error('无法删除该费用')

      return
    }

    try {
      const success = await deleteProductionExtraExpenses(expense.id)

      if (success) {
        message.success('删除成功')
        await loadExpenseList()
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  // 计算总价
  const calculateTotalPrice = (quotedPrice?: number, personCount?: number, dayCount?: number) => {
    if (!quotedPrice || !personCount || !dayCount) {
      return 0
    }

    return Number((quotedPrice * personCount * dayCount).toFixed(2))
  }

  // 渲染打包费用的内容
  const renderPackageContent = (expense: IProductionExtraExpensesItem) => {
    const isPackageBudget = expense.expenseType === ExpenseType.PackageBudget

    if (!isPackageBudget || !expense.expensesInfo?.length) {
      return null
    }

    return (
      <Space wrap size={[8, 8]}>
        {expense.expensesInfo.map((info, index) => {
          let label = '未知'
          let color = 'default'

          if (info.parentType === 1) {
            // 人员类型
            const config = ROLE_TYPE_CONFIG[info.roleType as unknown as keyof typeof ROLE_TYPE_CONFIG]

            if (config) {
              label = config.label
              color = config.color
            }
          } else if (info.parentType === 2) {
            // 演员类型
            const config = ACTOR_ROLE_TYPE_CONFIG[info.roleType as unknown as keyof typeof ACTOR_ROLE_TYPE_CONFIG]

            if (config) {
              label = config.label
              color = config.color
            }
          } else if (info.parentType === 3) {
            // 费用类型
            const config = EXPENSE_TYPE_CONFIG[info.roleType as unknown as keyof typeof EXPENSE_TYPE_CONFIG]

            if (config) {
              label = config.label
              color = config.color
            }
          }

          return (
            <Tag key={`${info.parentType}-${info.roleType}-${index}`}>
              {label} * {info.personCount || 1}
            </Tag>
          )
        })}
      </Space>
    )
  }

  // 渲染费用卡片
  const renderExpenseCard = (expense: IProductionExtraExpensesItem) => {
    const typeInfo = getExpenseTypeInfo(expense.expenseType)
    // 优先使用手动输入的总价，如果没有则使用计算的总价
    const calculatedTotalPrice = calculateTotalPrice(expense.quotedPrice, expense.personCount, expense.dayCount)
    const totalPrice = expense.totalPrice ?? calculatedTotalPrice
    const isPackageBudget = expense.expenseType === ExpenseType.PackageBudget

    const items = []
    items.push({
      key: 'edit',
      label: '编辑',
      onClick: () => handleEditExpense(expense),
    })

    // 只有在不是只读模式时才显示编辑/删除按钮
    if (!project?.feNoEdit) {
      items.push({
        key: 'delete',
        label: (
          <Popconfirm
            title="警告"
            description="确定要删除该费用吗？"
            onConfirm={() => handleDeleteExpense(expense)}
            okText="确定删除"
            cancelText="取消">
            <Typography.Text type="danger" style={{ width: '100%', display: 'inline-block' }}>
              删除
            </Typography.Text>
          </Popconfirm>
        ),
      })
    }

    return (
      <List.Item>
        <Card size="small" className="full-h hover-move">
          <Flex vertical gap={6}>
            <Flex justify="space-between">
              <Space size={0} split={<Divider type="vertical" />}>
                <Text strong>{expense?.expenseName || typeInfo.label}</Text>
                {expense.hasInvoice ? <Text>带发票</Text> : <Text type="secondary">无发票</Text>}
                {!isPackageBudget && expense.quotedPrice != null && (
                  <Dict
                    title="单价"
                    value={<Text>{`${project?.currencySymbol || '¥'}${expense.quotedPrice.toLocaleString()}`}</Text>}
                  />
                )}
                {!isPackageBudget && expense.personCount && expense.personCount > 0 ? (
                  <Dict title="数量" value={expense.personCount} />
                ) : null}
                {!isPackageBudget && expense?.dayCount && expense.dayCount > 0 ? (
                  <Dict title="天数" value={expense.dayCount || 0} />
                ) : null}
                {totalPrice > 0 ? (
                  <Dict
                    title="总价"
                    value={
                      <Text type="danger">{`${project?.currencySymbol || '¥'}${totalPrice.toLocaleString()}`}</Text>
                    }
                  />
                ) : null}
              </Space>
              {items.length > 0 && (
                <Dropdown menu={{ items }} trigger={['click']} placement="bottomRight">
                  <Button type="text" icon={<MoreOutlined />} />
                </Dropdown>
              )}
            </Flex>
            {renderPackageContent(expense)}
            {expense.description && <Typography.Text type="secondary">{expense.description}</Typography.Text>}
          </Flex>
        </Card>
      </List.Item>
    )
  }

  // 计算总费用
  const totalExpense = useMemo(
    () =>
      Number(
        expenseList
          .reduce((total, expense) => {
            // 优先使用手动输入的总价，如果没有则使用计算的总价
            const calculatedTotalPrice = calculateTotalPrice(expense.quotedPrice, expense.personCount, expense.dayCount)
            const itemTotalPrice = expense.totalPrice ?? calculatedTotalPrice

            return total + itemTotalPrice
          }, 0)
          .toFixed(2)
      ),
    [expenseList]
  )

  return (
    <Flex vertical>
      <ListHeader
        title={
          <Space>
            <span>费用总计</span>
            <Text type="danger" strong className="fs-lg">
              {`${project?.currencySymbol || '¥'}`}
              {totalExpense.toLocaleString()}
            </Text>
          </Space>
        }>
        <Space>
          <Button
            color="primary"
            variant="filled"
            shape="round"
            icon={<FolderAddOutlined />}
            className="text-primary"
            onClick={handleAddPackageBudget}>
            添加打包费用
          </Button>
          <Button type="primary" ghost shape="round" icon={<PlusOutlined />} onClick={handleAddExpense}>
            添加费用
          </Button>
        </Space>
      </ListHeader>

      {expenseList.length > 0 ? (
        <List
          loading={loading || loadingData}
          dataSource={expenseList}
          renderItem={renderExpenseCard}
          className="list-sm"
          split={false}
        />
      ) : (
        <Empty />
      )}

      {isModalOpen ? (
        <AddExtraExpense
          open={isModalOpen}
          onCancel={() => {
            setIsModalOpen(false)
          }}
          onSuccess={handleAddExpensesSuccess}
          loading={false}
          productionId={productionId}
        />
      ) : null}

      {isEditModalOpen ? (
        <EditExtraExpense
          open={isEditModalOpen}
          expense={editingExpense}
          onCancel={() => {
            setIsEditModalOpen(false)
            setEditingExpense(null)
          }}
          onOk={handleSaveEditExpense}
          loading={false}
        />
      ) : null}

      {isPackageBudgetOpen ? (
        <AddPackageBudget
          open={isPackageBudgetOpen}
          onCancel={() => {
            setIsPackageBudgetOpen(false)
            setEditingExpense(null)
          }}
          onSuccess={editingExpense ? handleSaveEditPackageBudget : handlePackageBudgetSuccess}
          loading={false}
          productionId={productionId}
          expense={editingExpense}
          project={project}
        />
      ) : null}
    </Flex>
  )
}

export default ProductionExtraExpenses
