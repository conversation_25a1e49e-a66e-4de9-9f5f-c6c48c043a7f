import QRCodeModal from '@/components/QRCodeModal'
import { IS_INTERNAL_CONFIG, ROLE_TYPE_CONFIG, RoleType } from '@/consts'
import AddContract from '@/pages/production/contract/list/components/Add'
import PersonDetail from '@/pages/production/person/components/PersonDetail'
import { MoreOutlined, PlusOutlined, QrcodeOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Button, Card, Divider, Dropdown, Empty, Flex, List, message, Popconfirm, Rate, Space, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import useProjectListStore, { IProductionListItem, IProductionPersonBasicItem } from '../store'
import AddPersonDrawer from './AddPersonDrawer'
import ContractStatus from './ContractStatus'
import EditPersonModal from './EditPersonModal'
import PersonEvaluationModal from './PersonEvaluationModal'

interface IProductionPersonBasicProps {
  productionId: number
  loading?: boolean
  projectName?: string
  project?: IProductionListItem
}

const ProductionPersonBasic: React.FC<IProductionPersonBasicProps> = ({
  productionId,
  loading = false,
  projectName,
  project,
}) => {
  const [persons, setPersons] = useState<IProductionPersonBasicItem[]>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isPersonDetailOpen, setIsPersonDetailOpen] = useState(false)
  const [selectedPersonId, setSelectedPersonId] = useState<number>()
  const [editingPerson, setEditingPerson] = useState<IProductionPersonBasicItem | null>(null)
  const [loadingData, setLoadingData] = useState(false)
  const [saving, setSaving] = useState(false)
  const [isContractModalOpen, setIsContractModalOpen] = useState(false)
  const [selectedPerson, setSelectedPerson] = useState<IProductionPersonBasicItem | null>(null)
  const [contractType, setContractType] = useState<number>(101) // 合同类型：101保密合同，103劳务合同
  const [isEvaluationModalOpen, setIsEvaluationModalOpen] = useState(false)
  const [evaluationPerson, setEvaluationPerson] = useState<IProductionPersonBasicItem | null>(null)
  const [qrCodeModalOpen, setQrCodeModalOpen] = useState(false)

  const { getProductionPersonBasicList, saveProductionPersonBasic, deleteProductionPersonBasic } = useProjectListStore()

  useEffect(() => {
    if (productionId) {
      loadPersons()
    }
  }, [productionId])

  // 加载人员列表
  const loadPersons = async () => {
    setLoadingData(true)
    try {
      const result = await getProductionPersonBasicList(productionId)

      setPersons(result || [])
    } catch (error) {
      console.error('加载人员失败:', error)
      message.error('加载人员失败')
    } finally {
      setLoadingData(false)
    }
  }

  // 获取角色类型信息
  const getRoleTypeInfo = (roleType: number) =>
    ROLE_TYPE_CONFIG[roleType as RoleType] || { label: '未知', color: 'default' }

  // 创建人员记录
  const createPersonRecords = (personList: any[]): IProductionPersonBasicItem[] =>
    personList.map((item: any, index: number) => ({
      productionId,
      ...item,
      dayCount: item.dayCount || 1,
      sort: persons.length + index + 1,
    }))

  // 处理编辑人员
  const handleEditPerson = (person: IProductionPersonBasicItem) => {
    setEditingPerson(person)
    setIsEditModalOpen(true)
  }

  // 处理批量添加人员成功
  const handleAddPersonsSuccess = async (flattenedPersonList: any[]) => {
    setSaving(true)
    try {
      // 验证人员重复性
      const newPersonIds = flattenedPersonList.filter(item => !!item.personId).map(item => item.personId)
      const existingPersonIds = persons.filter(item => !!item.personId).map(person => person.personId)
      const duplicatePersons = newPersonIds.filter(personId => existingPersonIds.includes(personId))

      if (duplicatePersons.length > 0) {
        message.warning('选择的人员中有已存在的，请检查后重新选择')

        return
      }

      const newPersons = createPersonRecords(flattenedPersonList)
      const success = await saveProductionPersonBasic({
        productionId,
        personBasic: [...persons, ...newPersons],
      })

      if (success) {
        message.success(`成功添加 ${newPersons.length} 位人员`)
        setIsModalOpen(false)
        await loadPersons()
      }
    } catch (error) {
      console.error('添加人员失败:', error)
    } finally {
      setSaving(false)
    }
  }

  // 处理删除人员
  const handleDeletePerson = async (person: IProductionPersonBasicItem) => {
    try {
      if (!person.id) {
        message.error('删除失败')

        return
      }
      const success = await deleteProductionPersonBasic(person.id)

      if (success) {
        message.success('删除成功')
        await loadPersons()
      }
    } catch (error) {
      console.error('删除人员失败:', error)
    }
  }

  // 保存编辑
  const handleSaveEdit = async (values: any) => {
    if (!editingPerson) {
      return
    }

    setSaving(true)
    try {
      if (values.personId !== editingPerson.personId) {
        // 验证人员重复性
        const existingPersonIds = persons
          .filter(person => person.id && person.id !== editingPerson.id)
          .map(person => person.personId)

        if (existingPersonIds.includes(values.personId)) {
          message.warning('选择的人员已存在，请重新选择')

          return
        }
      }

      const updatedPerson: IProductionPersonBasicItem = {
        ...editingPerson,
        personId: values.personId || 0,
        personName: values.personName,
        quotedPrice: values.quotedPrice,
        isInternal: values.isInternal,
        personCount: values.personCount || 1,
        hasInvoice: values.hasInvoice,
        description: values.description,
        dayCount: values.dayCount,
        totalPrice: values.totalPrice,
      }

      const updatedPersons = persons.map(person => (person.id === editingPerson.id ? updatedPerson : person))

      const success = await saveProductionPersonBasic({
        productionId,
        personBasic: updatedPersons,
      })

      if (success) {
        message.success('人员信息更新成功')
        setIsEditModalOpen(false)
        setEditingPerson(null)
        await loadPersons()
      }
    } catch (error) {
      console.error('更新人员信息失败:', error)
      message.error('更新失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  // 打开人员详情弹窗
  const handlePersonNameClick = (personId: number, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!personId) {
      return
    }
    setSelectedPersonId(personId)
    setIsPersonDetailOpen(true)
  }

  // 处理签约
  const handleSignContract = (person: IProductionPersonBasicItem, type = 101) => {
    setSelectedPerson(person)
    setContractType(type)
    setIsContractModalOpen(true)
  }

  // 处理签订劳务合同
  const handleSignLaborContract = (person: IProductionPersonBasicItem) => {
    handleSignContract(person, 103)
  }

  // 处理评价人员
  const handleEvaluatePerson = (person: IProductionPersonBasicItem) => {
    setEvaluationPerson(person)
    setIsEvaluationModalOpen(true)
  }

  // 评价成功回调
  const handleEvaluationSuccess = async () => {
    setIsEvaluationModalOpen(false)
    setEvaluationPerson(null)
    // 可以在这里重新加载数据或更新UI
    await loadPersons()
  }

  // 关闭评价弹窗
  const handleEvaluationCancel = () => {
    setIsEvaluationModalOpen(false)
    setEvaluationPerson(null)
  }

  const handleOpenModal = () => {
    if (isModalOpen) {
      return
    }
    setIsModalOpen(true)
  }

  // 渲染人员卡片
  const renderPersonCard = (person: IProductionPersonBasicItem) => {
    const typeInfo = getRoleTypeInfo(person.roleType)

    const items = []
    items.push({
      key: 'edit',
      label: '编辑',
      onClick: () => handleEditPerson(person),
    })
    // 只有在不是只读模式时才显示编辑/删除按钮
    if (!project?.feNoEdit) {
      items.push({
        key: 'delete',
        label: (
          <Popconfirm
            title="警告"
            description={`确定要删除【${person?.personName || ''}】吗？`}
            onConfirm={() => handleDeletePerson(person)}
            okText="确定删除"
            cancelText="取消">
            <Typography.Text type="danger" style={{ width: '100%', display: 'inline-block' }}>
              删除
            </Typography.Text>
          </Popconfirm>
        ),
      })
    }

    // 检查是否已签订指定类型的合同
    const hasContract = (contractType: number) =>
      person.commContractInfo?.some(item => item.verify === contractType) || false

    // 检查是否可以签约
    const canSign = person.personId && !person.isInternal

    // 保密合同按钮 - 只有在非只读模式下才显示
    if (!project?.feNoEdit && !hasContract(101) && canSign) {
      items.splice(1, 0, {
        key: 'sign-nda',
        label: '签订保密合同',
        onClick: () => handleSignContract(person, 101),
      })
    }

    // 劳务合同按钮 - 只有在非只读模式下才显示
    if (!project?.feNoEdit && !hasContract(103) && canSign) {
      items.splice(1, 0, {
        key: 'sign-labor',
        label: '签订劳务合同',
        onClick: () => handleSignLaborContract(person),
      })
    }

    // 评价按钮 - 只要有人员ID就可以评价
    if (person.personId) {
      items.splice(1, 0, {
        key: 'evaluate',
        label: person.personEvaluation ? '编辑评价' : '评价',
        onClick: () => handleEvaluatePerson(person),
      })
    }

    return (
      <List.Item>
        <Card size="small" className="full-h hover-move">
          <Flex justify="space-between">
            <Space
              size={0}
              split={<Divider type="vertical" />}
              onClick={e => handlePersonNameClick(person?.personId, e)}>
              <Dict title={typeInfo.label} value={<Typography.Text strong>{person?.personName}</Typography.Text>} />
              {typeof person.isInternal === 'number'
                ? `${IS_INTERNAL_CONFIG[person.isInternal]?.label}${person.isInternal ? '员工' : '人员'}` || '未知'
                : null}

              {person.personCount && person.personCount > 1 ? (
                <Dict title="人数" value={person.personCount || 0} />
              ) : null}
              {person.dayCount && (person.quotedPrice || person.totalPrice) ? (
                <Dict title="天数" value={person.dayCount} />
              ) : null}
              {!!person.quotedPrice && (
                <Dict
                  title="单价"
                  value={
                    <Typography.Text type="danger">{`${
                      project?.currencySymbol || '¥'
                    }${person.quotedPrice.toLocaleString()}`}</Typography.Text>
                  }
                />
              )}
              {!!person.totalPrice && (
                <Dict
                  title="总价"
                  value={
                    <Typography.Text type="danger">{`${
                      project?.currencySymbol || '¥'
                    }${person.totalPrice.toLocaleString()}`}</Typography.Text>
                  }
                />
              )}
              {person.isInternal ? null : <Typography.Text>{person.hasInvoice ? '带发票' : '无发票'}</Typography.Text>}
              {person?.personId && !person?.isInternal ? <ContractStatus contracts={person.commContractInfo} /> : null}
            </Space>
            <Space>
              {person.personEvaluation && <Rate count={10} value={person.personEvaluation.score} />}
              {items.length > 0 && (
                <Dropdown menu={{ items }} trigger={['click']} placement="bottomRight">
                  <Button type="text" icon={<MoreOutlined />} />
                </Dropdown>
              )}
            </Space>
          </Flex>
          {person.description && (
            <div style={{ marginTop: 8 }}>
              <Typography.Text type="secondary">{person.description}</Typography.Text>
            </div>
          )}
          {person.personEvaluation && (
            <div style={{ marginTop: 8 }}>
              {person.personEvaluation.comment && <Dict title="评价内容" value={person.personEvaluation.comment} />}
            </div>
          )}
        </Card>
      </List.Item>
    )
  }

  return (
    <Flex vertical>
      <ListHeader title="拍摄人员" total={persons.length}>
        <Space>
          <Button
            color="primary"
            shape="round"
            variant="filled"
            icon={<QrcodeOutlined />}
            onClick={() => setQrCodeModalOpen(true)}
            title="二维码">
            二维码
          </Button>
          <Button type="primary" shape="round" ghost icon={<PlusOutlined />} onClick={handleOpenModal}>
            添加人员
          </Button>
        </Space>
      </ListHeader>
      {persons.length > 0 ? (
        <List
          loading={loading || loadingData}
          dataSource={persons}
          renderItem={renderPersonCard}
          className="list-sm"
          split={false}
        />
      ) : (
        <Empty />
      )}
      {/* 批量添加侧边栏 */}
      {isModalOpen ? (
        <AddPersonDrawer
          open={isModalOpen}
          onCancel={() => setIsModalOpen(false)}
          onSuccess={handleAddPersonsSuccess}
          loading={saving}
          productionId={productionId}
          project={project}
        />
      ) : null}
      {/* 编辑人员模态框 */}
      {isEditModalOpen ? (
        <EditPersonModal
          open={isEditModalOpen}
          onCancel={() => {
            setIsEditModalOpen(false)
            setEditingPerson(null)
          }}
          onSuccess={handleSaveEdit}
          loading={saving}
          editingPerson={editingPerson}
          project={project}
        />
      ) : null}
      {/* 人员详情弹窗 */}
      {isPersonDetailOpen ? (
        <PersonDetail
          open={isPersonDetailOpen}
          personId={selectedPersonId}
          onCancel={() => {
            setIsPersonDetailOpen(false)
            setSelectedPersonId(void 0)
          }}
        />
      ) : null}
      {/* 合同签约模态框 */}
      {isContractModalOpen ? (
        <AddContract
          visible={isContractModalOpen}
          editData={
            selectedPerson
              ? {
                  verify: contractType,
                  novelName: projectName,
                  authorId: selectedPerson.personId,
                  penNames: selectedPerson.personName,
                  bookId: String(productionId),
                  greementDate: dayjs().format('YYYY-MM-DD'),
                  // 劳务合同特有字段
                  ...(contractType === 103 && {
                    ext1: ROLE_TYPE_CONFIG[selectedPerson.roleType].label, // 担任角色
                    ext2: String(selectedPerson.quotedPrice || 0), // 日薪
                    greementDate2: project?.endDate
                      ? dayjs(project.endDate).format('YYYY-MM-DD')
                      : dayjs().format('YYYY-MM-DD'), // 结束日期
                    partyADate: dayjs().format('YYYY-MM-DD'), // 甲方签署日期
                  }),
                }
              : null
          }
          onCancel={() => {
            setIsContractModalOpen(false)
            setSelectedPerson(null)
            setContractType(101)
          }}
          onSuccess={async () => {
            setIsContractModalOpen(false)
            setSelectedPerson(null)
            setContractType(101)
            await loadPersons() // 刷新列表
          }}
          loading={false}
        />
      ) : null}

      {/* 评价弹窗 */}
      {evaluationPerson && (
        <PersonEvaluationModal
          open={isEvaluationModalOpen}
          onCancel={handleEvaluationCancel}
          onSuccess={handleEvaluationSuccess}
          productionId={productionId}
          personId={evaluationPerson.personId}
          personName={evaluationPerson.personName || ''}
          parentType={1} // 人员类型
          roleType={evaluationPerson.roleType}
          initialData={evaluationPerson.personEvaluation}
        />
      )}

      {/* 二维码弹窗 */}
      {qrCodeModalOpen && (
        <QRCodeModal
          open={qrCodeModalOpen}
          onCancel={() => setQrCodeModalOpen(false)}
          url={`https://mpr.cdreader.com/#/login?joinProjectId=${productionId}&role=staff`}
          title="二维码"
          description="扫描二维码或分享链接进行加入"
          fileName={`项目${projectName}_二维码`}
        />
      )}
    </Flex>
  )
}

export default ProductionPersonBasic
