import { ACTOR_ROLE_TYPE_CONFIG, ACTOR_ROLE_TYPE_OPTIONS } from '@/consts'
import ActorSelector from '@/pages/production/actor/components/ActorSelector'
import { Button, Flex, Form, Input, InputNumber, Modal, Select, type FormInstance } from 'antd'
import React from 'react'
import { IProductionActorItem, IProductionListItem } from '../store'

const { TextArea } = Input

interface EditActorProps {
  form: FormInstance
  open?: boolean
  loading?: boolean
  editingActor?: IProductionActorItem | null
  project?: IProductionListItem
  onOk: (values: any) => void
  onCancel: () => void
}

const EditActor: React.FC<EditActorProps> = ({ form, open, loading, editingActor, project, onOk, onCancel }) => {
  // 监听角色类型的变化
  const roleType = Form.useWatch('roleType', form)
  const isAbstract = !!ACTOR_ROLE_TYPE_CONFIG?.[roleType]?.isAbstract

  // 处理演员选择
  const handleActorSelect = (actorId: number, option: any) => {
    // 自动填充演员信息
    if (actorId) {
      form.setFieldValue('headUrl', option.headUrl)
      form.setFieldValue('personName', option.personName)
      form.setFieldValue('stageName', option.stageName)
      form.setFieldValue('isInternal', option.isInternal)
      form.setFieldValue('personCount', 1)
    }
  }

  // 处理角色类型选择
  const handleRoleTypeSelect = (val: number) => {
    if (ACTOR_ROLE_TYPE_CONFIG?.[val]?.isAbstract) {
      form.setFieldValue('headUrl', '')
      form.setFieldValue('personName', ACTOR_ROLE_TYPE_CONFIG?.[val]?.label)
      form.setFieldValue('stageName', ACTOR_ROLE_TYPE_CONFIG?.[val]?.label)
      form.setFieldValue('actorId', 0)
    } else {
      const actorId = form.getFieldValue('actorId')

      if (actorId === 0) {
        form.setFieldValue('actorId', null)
      }
    }
  }

  return (
    <Modal title="编辑演员" open={open} onCancel={onCancel} footer={null} width={750} destroyOnHidden>
      <Form
        form={form}
        layout="horizontal"
        colon={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        onFinish={onOk}>
        <Form.Item name="roleType" label="角色类型" rules={[{ required: true, message: '请选择角色类型' }]}>
          <Select placeholder="请选择角色类型" options={ACTOR_ROLE_TYPE_OPTIONS} onSelect={handleRoleTypeSelect} />
        </Form.Item>
        <Form.Item
          name="actorId"
          label="选择演员"
        // rules={[{ required: true, message: '请选择演员' }]}
        // hidden={isAbstract}
        >
          <ActorSelector
            onSelect={handleActorSelect}
            // disabled={isAbstract}
            initOption={
              editingActor && editingActor.personId && !isAbstract
                ? {
                  id: editingActor.actorId,
                  personName: editingActor.personName,
                  stageName: editingActor.stageName,
                  headUrl: editingActor.headUrl,
                  isInternal: editingActor.isInternal,
                }
                : null
            }
          />
        </Form.Item>

        {/* 隐藏字段存储演员信息 */}
        <Form.Item name="headUrl" hidden></Form.Item>
        <Form.Item name="personName" hidden></Form.Item>
        <Form.Item name="stageName" hidden></Form.Item>
        <Form.Item name="isInternal" hidden></Form.Item>
        <Form.Item name="priceCurrency" initialValue={0} hidden>
          <Select
            options={[
              { value: 0, label: '人民币 (¥)' },
              { value: 1, label: '美元 ($)' },
            ]}
          />
        </Form.Item>
        <Form.Item name="playRole" label="饰演角色" hidden={isAbstract}>
          <Input placeholder="请输入饰演角色" />
        </Form.Item>
        <Form.Item name="personCount" label="人数" hidden={!isAbstract} initialValue={1}>
          <InputNumber className="full-h" placeholder="请输入人数" min={1} precision={0} />
        </Form.Item>

        <Form.Item name="dayCount" label="天数">
          <InputNumber className="full-h" placeholder="请输入天数" min={1} precision={0} />
        </Form.Item>

        <Form.Item name="quotedPrice" label="单价">
          <InputNumber className="full-h" placeholder="请输入单价" prefix={project?.currencySymbol} />
        </Form.Item>
        <Form.Item name="totalPrice" label="总价">
          <InputNumber
            className="full-h"
            placeholder="请输入总价"
            min={0}
            precision={2}
            prefix={project?.currencySymbol}
          />
        </Form.Item>
        <Form.Item name="hasInvoice" label="是否正规发票">
          <Select
            placeholder="请选择"
            options={[
              { value: true, label: '是' },
              { value: false, label: '否' },
            ]}
          />
        </Form.Item>
        {/*
        <Form.Item name="cooperationEvaluation" label="合作评价">
          <TextArea rows={2} placeholder="请输入合作评价" />
        </Form.Item> */}

        <Form.Item name="description" label="备注">
          <TextArea rows={5} placeholder="请输入备注信息" />
        </Form.Item>

        <Form.Item className="no-margin" label=" ">
          <Flex justify="end" gap={12}>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              立即保存
            </Button>
          </Flex>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default EditActor
