import EnvImage from '@/components/EnvImage'
import VideoView from '@/components/VideoView'
import {
  ACTOR_ROLE_TYPE_CONFIG,
  ActorRoleType,
  APPLICATION_STATUS_CONFIG,
  RECRUITMENT_PARENT_TYPE_CONFIG,
  ROLE_TYPE_CONFIG,
  RoleType,
} from '@/consts'
import { envUrl } from '@/utils/request'
import { FileOutlined } from '@ant-design/icons'
import { Button, Card, Col, Descriptions, Drawer, Flex, Row, Space, Tag, Typography } from 'antd'
import dayjs from 'dayjs'
import React from 'react'
import { IPersonApplicationItem, IProductionListItem } from '../store'

interface PersonApplicationDetailDrawerProps {
  open: boolean
  application: IPersonApplicationItem | null
  project?: IProductionListItem
  onClose: () => void
}

const PersonApplicationDetailDrawer: React.FC<PersonApplicationDetailDrawerProps> = ({
  open,
  application,
  project,
  onClose,
}) => {
  // 获取角色类型信息
  const getRoleTypeInfo = (parentType: number, roleType: number) => {
    if (parentType === 1) {
      // 人员
      return ROLE_TYPE_CONFIG[roleType as RoleType] || { label: '未知', color: 'default' }
    } else if (parentType === 2) {
      // 演员
      return ACTOR_ROLE_TYPE_CONFIG[roleType as ActorRoleType] || { label: '未知', color: 'default' }
    }

    return { label: '未知', color: 'default' }
  }

  // 判断文件类型
  const getFileType = (url: string): 'image' | 'video' | 'other' => {
    const extension = url.toLowerCase().split('.').pop() || ''

    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp', 'svg']
    const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v']

    if (imageExts.includes(extension)) {
      return 'image'
    }
    if (videoExts.includes(extension)) {
      return 'video'
    }

    return 'other'
  }

  // 渲染附件
  const renderAttachment = (url: string, index: number) => {
    const fileType = getFileType(url)

    switch (fileType) {
      case 'image':
        return (
          <Col key={index} span={8}>
            <EnvImage
              src={url}
              style={{ width: '100%', height: 120, objectFit: 'cover' }}
              placeholder={
                <div
                  style={{
                    width: '100%',
                    height: 120,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: '#f5f5f5',
                  }}>
                  加载中...
                </div>
              }
            />
          </Col>
        )
      case 'video':
        return (
          <Col key={index} span={8}>
            <VideoView videoUrl={url} width="100%" height={120} showPlayIcon={true} playIconSize={24} />
          </Col>
        )
      default:
        return (
          <Button
            key={index}
            type="link"
            icon={<FileOutlined />}
            href={envUrl + url}
            target="_blank"
            rel="noopener noreferrer">
            文件{index + 1}
          </Button>
        )
    }
  }

  return (
    <Drawer title="审核详情" width={700} open={open} onClose={onClose} destroyOnClose>
      {application && (
        <Flex vertical gap={16}>
          <Descriptions title="基本信息" column={2} size="small" bordered colon={false}>
            <Descriptions.Item label="姓名">
              <Space>{application.personName || '未知'}</Space>
            </Descriptions.Item>

            <Descriptions.Item label="申请类型">
              {RECRUITMENT_PARENT_TYPE_CONFIG[application.parentType]?.label || '未知'}
            </Descriptions.Item>
            <Descriptions.Item label="角色类型">
              {getRoleTypeInfo(application.parentType, application.roleType).label}
            </Descriptions.Item>
            <Descriptions.Item label="期望薪资">
              {application.expectedSalary
                ? `${project?.currencySymbol || '¥'}${application.expectedSalary.toLocaleString()}`
                : '面议'}
            </Descriptions.Item>
            <Descriptions.Item label="申请时间">
              {application.createTime ? dayjs(application.createTime).format('YYYY-MM-DD HH:mm') : '-'}
            </Descriptions.Item>
          </Descriptions>

          {application.introduction && (
            <Flex vertical gap={4}>
              <Typography.Text strong>自我介绍</Typography.Text>
              <Card>
                <Typography.Paragraph>{application.introduction}</Typography.Paragraph>
              </Card>
            </Flex>
          )}

          {application.introductionUrls && (
            <Flex vertical gap={4}>
              <Typography.Text strong>额外附件</Typography.Text>
              <Card>
                {(() => {
                  const urls = application.introductionUrls.split(',')
                  const mediaUrls = urls.filter(url => ['image', 'video'].includes(getFileType(url)))
                  const fileUrls = urls.filter(url => getFileType(url) === 'other')

                  return (
                    <Flex vertical gap={12}>
                      {/* 图片和视频展示 */}
                      {mediaUrls.length > 0 && (
                        <Row gutter={[8, 8]}>{mediaUrls.map((url, index) => renderAttachment(url, index))}</Row>
                      )}

                      {/* 其他文件链接 */}
                      {fileUrls.length > 0 && (
                        <Space wrap>{fileUrls.map((url, index) => renderAttachment(url, urls.indexOf(url)))}</Space>
                      )}
                    </Flex>
                  )
                })()}
              </Card>
            </Flex>
          )}

          {(application.status !== 1 || application.reviewComment) && (
            <Descriptions title="审核信息" column={1} size="small" bordered colon={false}>
              <Descriptions.Item label="审核状态">
                <Tag color={APPLICATION_STATUS_CONFIG[application.status]?.color}>
                  {APPLICATION_STATUS_CONFIG[application.status]?.label || '未知'}
                </Tag>
              </Descriptions.Item>
              {application.reviewComment && (
                <Descriptions.Item label="审核意见">{application.reviewComment}</Descriptions.Item>
              )}
              {application.reviewerNickName && (
                <Descriptions.Item label="审核人">{application.reviewerNickName}</Descriptions.Item>
              )}
              {application.reviewTime && (
                <Descriptions.Item label="审核时间">
                  {dayjs(application.reviewTime).format('YYYY-MM-DD HH:mm')}
                </Descriptions.Item>
              )}
            </Descriptions>
          )}
        </Flex>
      )}
    </Drawer>
  )
}

export default PersonApplicationDetailDrawer
