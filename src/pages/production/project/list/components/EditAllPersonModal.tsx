import { ACTOR_ROLE_TYPE_OPTIONS, RECRUITMENT_PARENT_TYPE_OPTIONS, ROLE_TYPE_OPTIONS_SELECT } from '@/consts'
import PersonSelector from '@/pages/production/person/components/PersonSelector'
import { Col, Form, Modal, Row, Select, Switch, message } from 'antd'
import React, { useEffect, useState } from 'react'
import useProjectListStore, { IPrProductionAllPerson } from '../store'

interface IEditAllPersonModalProps {
  open: boolean
  productionId: number
  person: IPrProductionAllPerson | null
  onCancel: () => void
  onSuccess: () => void
}

const EditAllPersonModal: React.FC<IEditAllPersonModalProps> = ({
  open,
  productionId,
  person,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm()
  const [saving, setSaving] = useState(false)
  const { saveProductionAllPerson, getAllPersonList } = useProjectListStore()
  const formParentType = Form.useWatch('parentType', form)
  const formRoleType = Form.useWatch('roleType', form)

  // 当弹窗打开且有人员数据时，设置表单值
  useEffect(() => {
    if (open && person) {
      form.setFieldsValue({
        personId: person.personId,
        parentType: person.parentType,
        roleType: person.roleType,
        isMeal: person.isMeal || false,
        isAccommodation: person.isAccommodation || false,
      })
    }
  }, [open, person, form])

  // 处理保存
  const handleSave = async () => {
    if (!person) {
      return
    }

    try {
      const values = await form.validateFields()

      setSaving(true)

      // 先获取当前所有人员列表
      const currentPersons = await getAllPersonList(productionId)

      if (!currentPersons) {
        message.error('获取人员列表失败')

        return
      }

      // 更新当前编辑的人员
      const updatedPerson: IPrProductionAllPerson = {
        ...person,
        personId: values.personId,
        parentType: values.parentType,
        roleType: values.roleType,
        isMeal: values.isMeal || false,
        isAccommodation: values.isAccommodation || false,
      }

      // 替换列表中的对应人员
      const updatedPersons = currentPersons.map(p => (p.id === person.id ? updatedPerson : p))

      const success = await saveProductionAllPerson({
        productionId,
        allPerson: updatedPersons,
      })

      if (success) {
        message.success('更新成功')
        form.resetFields()
        onSuccess()
      } else {
        message.error('更新失败')
      }
    } catch (error) {
      console.error('更新人员失败:', error)
      message.error('更新失败')
    } finally {
      setSaving(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title="编辑"
      open={open}
      onCancel={handleCancel}
      onOk={handleSave}
      okText="立即保存"
      confirmLoading={saving}
      width={800}
      destroyOnHidden>
      <Form form={form} layout="vertical">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="personId" label="人员" rules={[{ required: true, message: '请选择人员' }]}>
              <PersonSelector
                disabled={!formRoleType || !formParentType}
                roleType={formRoleType}
                initOption={
                  person && person.personId
                    ? {
                        ...person,
                        id: person.personId,
                        val: person.personId,
                        personName: person.personName,
                        label: person.personName,
                      }
                    : null
                }
                // onSelect={(val, opt) => handlePersonSelect(val, opt, name)}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="parentType" label="人员类型" rules={[{ required: true, message: '请选择人员类型' }]}>
              <Select
                placeholder="请选择人员类型"
                options={RECRUITMENT_PARENT_TYPE_OPTIONS}
                onChange={() => {
                  form.setFieldValue('roleType', null)
                }}></Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="roleType" label="角色类型" rules={[{ required: true, message: '请输入角色类型' }]}>
              <Select
                disabled={!formParentType}
                showSearch
                allowClear
                options={formParentType === 2 ? ACTOR_ROLE_TYPE_OPTIONS : (ROLE_TYPE_OPTIONS_SELECT as any)}></Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="isMeal" label="提供餐食" valuePropName="checked">
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="isAccommodation" label="提供住宿" valuePropName="checked">
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  )
}

export default EditAllPersonModal
