import { PROJECT_STATUS } from '@/consts/project'
import { PRODUCTION_TYPE_OPTIONS } from '@/consts'
import { useDebounceFn } from 'ahooks'
import { Button, Form, Input, Select, Space } from 'antd'
import React from 'react'

const { Option } = Select

const ProjectSearch: React.FC<ISearchProps> = ({ loading = false, onSearch, form, onReset }) => {
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(1), { wait: 500 })

  return (
    <Form form={form} onValuesChange={onSearchDebounce} colon={false}>
      <Space size={24}>
        <Form.Item name="productionName" label="项目">
          <Input className="w300" placeholder="支持项目名、代号模糊搜索" allowClear />
        </Form.Item>
        <Form.Item name="status" label="状态">
          <Select
            mode="multiple"
            maxTagCount={2}
            placeholder="默认全选"
            className="w200"
            allowClear
            options={PROJECT_STATUS}
          />
        </Form.Item>
        <Form.Item name="productionType" label="项目类型">
          <Select
            placeholder="选择项目类型"
            className="w200"
            allowClear
            options={PRODUCTION_TYPE_OPTIONS}
          />
        </Form.Item>
        <Form.Item>
          <Button type="primary" onClick={onSearchDebounce} loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Space>
    </Form>
  )
}

export default ProjectSearch
