import QRCodeModal from '@/components/QRCodeModal'
import { ACTOR_ROLE_TYPE_CONFIG, ACTOR_ROLE_TYPE_OPTIONS, ActorRoleType, IS_INTERNAL_CONFIG } from '@/consts'
import ActorDetail from '@/pages/production/actor/components/ActorDetail'
import ContractAdd from '@/pages/production/contract/list/components/Add'
import { envUrl } from '@/utils/request'
import { MoreOutlined, PlusOutlined, QrcodeOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import {
  Avatar,
  Button,
  Card,
  Divider,
  Dropdown,
  Empty,
  Flex,
  Form,
  Input,
  List,
  Popconfirm,
  Rate,
  Space,
  Typography,
  message,
} from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useMemo, useState } from 'react'
import useProjectListStore, { IProductionActorItem, IProductionListItem } from '../store'
import AddActor from './AddActor'
import ContractStatus from './ContractStatus'
import EditActor from './EditActor'
import PersonEvaluationModal from './PersonEvaluationModal'

const { TextArea } = Input

interface IProductionActorsProps {
  productionId: number
  loading?: boolean
  projectName?: string
  project?: IProductionListItem
}

const ProductionActors: React.FC<IProductionActorsProps> = ({
  productionId,
  loading = false,
  projectName,
  project,
}) => {
  const [actors, setActors] = useState<IProductionActorItem[]>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isActorDetailOpen, setIsActorDetailOpen] = useState(false)
  const [selectedActorId, setSelectedActorId] = useState<number | null>(null)
  const [editingActor, setEditingActor] = useState<IProductionActorItem | null>(null)
  const [loadingData, setLoadingData] = useState(false)
  const [saving, setSaving] = useState(false)
  const [form] = Form.useForm()
  const [editForm] = Form.useForm()
  const [isContractModalOpen, setIsContractModalOpen] = useState(false)
  const [selectedActor, setSelectedActor] = useState<IProductionActorItem | null>(null)
  const [contractType, setContractType] = useState<number>(101) // 合同类型：101保密合同，102演员合同
  const [isEvaluationModalOpen, setIsEvaluationModalOpen] = useState(false)
  const [evaluationActor, setEvaluationActor] = useState<IProductionActorItem | null>(null)
  const [qrCodeModalOpen, setQrCodeModalOpen] = useState(false)

  const { getProductionActorList, saveProductionActor, deleteProductionActor } = useProjectListStore()

  useEffect(() => {
    if (productionId) {
      loadActors()
    }
  }, [productionId])

  // 加载演员列表
  const loadActors = async () => {
    setLoadingData(true)
    try {
      const result = await getProductionActorList(productionId)

      setActors(result || [])
    } catch (error) {
      console.error('加载演员列表失败:', error)
    } finally {
      setLoadingData(false)
    }
  }

  // 获取角色类型信息
  const getRoleTypeInfo = (roleType: number) =>
    ACTOR_ROLE_TYPE_CONFIG[roleType as ActorRoleType] || { label: '未知', color: 'default' }

  // 验证演员重复性
  const validateActorDuplication = (newActorIds: number[], excludeActorId?: number) => {
    const existingActorIds = actors
      .filter(actor => (excludeActorId ? actor.id !== excludeActorId : true))
      .map(actor => actor.actorId)

    const duplicateActors = newActorIds.filter(actorId => existingActorIds.includes(actorId) && actorId)

    if (duplicateActors.length > 0) {
      message.warning('选择的演员中有已存在的，请检查后重新选择')

      return false
    }

    const uniqueActorIds = [...new Set(newActorIds)]

    if (uniqueActorIds.length !== newActorIds.length) {
      message.warning('不能重复选择同一位演员')

      return false
    }

    return true
  }
  // 创建演员记录
  const createActorRecords = (actorList: any[]): IProductionActorItem[] =>
    actorList.map((item: any, index: number) => ({
      productionId,
      actorId: item.actorId,
      headUrl: item.headUrl,
      personName: item.personName,
      stageName: item.stageName,
      roleType: item.roleType || ActorRoleType.Extra,
      playRole: item.playRole,
      cooperationEvaluation: item.cooperationEvaluation,
      quotedPrice: item.quotedPrice,
      priceCurrency: item.priceCurrency || 0,
      personCount: item.personCount || 1,
      hasInvoice: item.hasInvoice,
      dayCount: item.dayCount,
      totalPrice: item.totalPrice,
      sort: index + actors.length,
      description: item.description,
      isInternal: item.isInternal,
      gender: item.gender,
    }))

  // 处理批量新增演员
  const handleAddActors = async (values: any) => {
    setSaving(true)
    try {
      const { actorList } = values

      if (!actorList || actorList.length === 0) {
        message.warning('请至少添加一位演员')

        return
      }

      const newActorIds = actorList.map((item: any) => item.actorId)

      if (!validateActorDuplication(newActorIds)) {
        return
      }

      const newActors = createActorRecords(actorList)
      const success = await saveProductionActor({
        productionId,
        actors: [...actors, ...newActors].map(item => {
          item.actorId = item.actorId || 0

          return item
        }),
      })

      if (success) {
        message.success(`成功添加 ${newActors.length} 位演员`)
        setIsModalOpen(false)
        form.resetFields()
        await loadActors()
      }
    } catch (error) {
      console.error('添加演员失败:', error)
    } finally {
      setSaving(false)
    }
  }

  // 处理编辑演员
  const handleEditActor = (actor: IProductionActorItem) => {
    setEditingActor(actor)
    setIsEditModalOpen(true)

    editForm.setFieldsValue({
      actorId: actor.actorId ? actor.actorId : null,
      headUrl: actor.headUrl,
      personName: actor.personName,
      stageName: actor.stageName,
      roleType: actor.roleType,
      playRole: actor.playRole,
      cooperationEvaluation: actor.cooperationEvaluation,
      quotedPrice: actor.quotedPrice,
      priceCurrency: actor.priceCurrency,
      personCount: actor.personCount || 1,
      hasInvoice: actor.hasInvoice,
      dayCount: actor.dayCount,
      totalPrice: actor.totalPrice,
      description: actor.description,
    })
  }

  // 保存编辑
  const handleSaveEdit = async (values: any) => {
    if (!editingActor) {
      return
    }

    setSaving(true)
    try {
      if (values.actorId !== editingActor.actorId) {
        if (!validateActorDuplication([values.actorId], editingActor.id)) {
          return
        }
      }

      const updatedActor: IProductionActorItem = {
        ...editingActor,
        actorId: values.actorId ? values.actorId : 0,
        headUrl: values.headUrl,
        personName: values.personName,
        stageName: values.stageName,
        roleType: values.roleType,
        playRole: values.playRole,
        cooperationEvaluation: values.cooperationEvaluation,
        quotedPrice: values.quotedPrice,
        priceCurrency: values.priceCurrency,
        personCount: values.personCount || 1,
        hasInvoice: values.hasInvoice,
        dayCount: values.dayCount,
        totalPrice: values.totalPrice,
        description: values.description,
        isInternal: values.isInternal,
      }

      const updatedActors = actors.map(actor => {
        actor.actorId = actor.actorId || 0

        return actor.id === editingActor.id ? updatedActor : actor
      })

      const success = await saveProductionActor({
        productionId,
        actors: updatedActors,
      })

      if (success) {
        message.success('演员信息更新成功')
        setIsEditModalOpen(false)
        setEditingActor(null)
        editForm.resetFields()
        await loadActors()
      }
    } catch (error) {
      console.error('更新演员信息失败:', error)
    } finally {
      setSaving(false)
    }
  }

  // 处理删除演员
  const handleDeleteActor = async (actor: IProductionActorItem) => {
    try {
      if (!actor.id) {
        message.error('删除失败')

        return
      }

      const success = await deleteProductionActor(actor.id)

      if (success) {
        message.success('删除成功')
        await loadActors()
      }
    } catch (error) {
      console.error('删除演员失败:', error)
    }
  }

  // 打开演员详情侧边栏
  const handleAvatarClick = (actorId: number, e: React.MouseEvent) => {
    if (!actorId) {
      return
    }
    e.stopPropagation()
    setSelectedActorId(actorId)
    setIsActorDetailOpen(true)
  }

  // 处理签约
  const handleSignContract = (actor: IProductionActorItem, type = 101) => {
    setSelectedActor(actor)
    setContractType(type)
    setIsContractModalOpen(true)
  }

  // 处理签订演员合同
  const handleSignActorContract = (actor: IProductionActorItem) => {
    handleSignContract(actor, 102)
  }

  // 处理评价演员
  const handleEvaluateActor = (actor: IProductionActorItem) => {
    setEvaluationActor(actor)
    setIsEvaluationModalOpen(true)
  }

  // 评价成功回调
  const handleEvaluationSuccess = async () => {
    setIsEvaluationModalOpen(false)
    setEvaluationActor(null)
    await loadActors()
  }

  // 关闭评价弹窗
  const handleEvaluationCancel = () => {
    setIsEvaluationModalOpen(false)
    setEvaluationActor(null)
  }

  // 渲染演员卡片
  const renderActorCard = (actor: IProductionActorItem) => {
    const selectedActor = actors.find(item => item.actorId === actor.actorId)
    const actorName =
      selectedActor?.personName || actor.personName || `${actor.actorId ? `演员ID : ${actor.actorId}` : ''}`
    const headUrl = selectedActor?.headUrl || actor.headUrl
    const roleInfo = getRoleTypeInfo(actor.roleType)

    // 检查是否已签订指定类型的合同
    const hasContract = (contractType: number) =>
      actor.commContractInfo?.some(item => item.verify === contractType) || false

    // 检查是否可以签约
    const canSign = actor.actorId && actor.personId && !actor.isInternal

    const items = []
    items.push({
      key: 'edit',
      label: '编辑',
      onClick: () => handleEditActor(actor),
    })
    // 只有在不是只读模式时才显示编辑/删除按钮
    if (!project?.feNoEdit) {
      items.push({
        key: 'delete',
        label: (
          <Popconfirm
            title="警告"
            description={`确定要删除【${actor?.personName}】吗？`}
            onConfirm={() => handleDeleteActor?.(actor)}
            okText="确定删除"
            cancelText="取消">
            <Typography.Text type="danger" style={{ width: '100%', display: 'inline-block' }}>
              删除
            </Typography.Text>
          </Popconfirm>
        ),
      })
    }

    // 保密合同按钮 - 只有在非只读模式下才显示
    if (!project?.feNoEdit && !hasContract(101) && canSign) {
      items.splice(1, 0, {
        key: 'sign-nda',
        label: '签订保密合同',
        onClick: () => handleSignContract(actor, 101),
      })
    }

    // 演员合同按钮 - 只有在非只读模式下才显示
    if (!project?.feNoEdit && !hasContract(102) && canSign) {
      items.splice(1, 0, {
        key: 'sign-actor',
        label: '签订演员合同',
        onClick: () => handleSignActorContract(actor),
      })
    }
    // 评价按钮 - 只要有演员ID就可以评价
    if (actor.actorId && actor.personId) {
      items.splice(1, 0, {
        key: 'evaluate',
        label: actor.personEvaluation ? '编辑评价' : '评价',
        onClick: () => handleEvaluateActor(actor),
      })
    }

    return (
      <List.Item>
        <Card size="small" className="full-h hover-move">
          <Flex vertical gap={8}>
            <Flex justify="space-between" onClick={e => handleAvatarClick(actor.actorId, e)}>
              <Space size={16}>
                {headUrl ? (
                  <Avatar src={envUrl + headUrl} size={48} className="img-top" />
                ) : (
                  <Avatar size={48}>{actorName ? actorName.split('')[0] : roleInfo.label.split('')[0]}</Avatar>
                )}
                <Space direction="vertical" size={2}>
                  <Space size={0} split={<Divider type="vertical" />}>
                    {!!actorName && <Typography.Text strong>{actorName}</Typography.Text>}
                    <Typography.Text strong={!actorName}>{roleInfo.label}</Typography.Text>
                    {actor.playRole ? <Dict title="饰演" value={actor.playRole} /> : null}
                    {typeof actor.isInternal === 'number'
                      ? `${IS_INTERNAL_CONFIG[actor.isInternal]?.label}演员` || '未知'
                      : null}
                    {actor?.personCount && actor?.personCount > 1 ? (
                      <Dict title="人数" value={actor.personCount} />
                    ) : null}
                    {actor.dayCount && (actor.quotedPrice || actor.totalPrice) ? (
                      <Dict title="天数" value={actor.dayCount} />
                    ) : null}
                    {!!actor.quotedPrice && (
                      <Dict
                        title="单价"
                        value={
                          <Typography.Text type="danger">
                            {project?.currencySymbol || '¥'}
                            {actor.quotedPrice}
                          </Typography.Text>
                        }></Dict>
                    )}
                    {!!actor.totalPrice && (
                      <Dict
                        title="总价"
                        value={
                          <Typography.Text type="danger">
                            {project?.currencySymbol || '¥'}
                            {actor.totalPrice}
                          </Typography.Text>
                        }></Dict>
                    )}
                    {actor.isInternal ? null : (
                      <Typography.Text>{actor.hasInvoice ? '带发票' : '无发票'}</Typography.Text>
                    )}
                    {ACTOR_ROLE_TYPE_CONFIG?.[actor?.roleType]?.isAbstract || actor.isInternal ? null : (
                      <ContractStatus contracts={actor.commContractInfo} />
                    )}
                  </Space>
                </Space>
              </Space>
              <Space>
                {actor.personEvaluation && <Rate count={10} value={actor.personEvaluation.score} />}
                {items.length > 0 && (
                  <Dropdown menu={{ items }} trigger={['click']} placement="bottomRight">
                    <Button type="text" icon={<MoreOutlined />} />
                  </Dropdown>
                )}
              </Space>
            </Flex>
            {actor.description && <Typography.Text type="secondary"> {actor.description}</Typography.Text>}
            {actor.personEvaluation && <Dict title="评价内容" value={actor.personEvaluation.comment} />}
          </Flex>
        </Card>
      </List.Item>
    )
  }

  // 角色类型选项
  const roleOptions = useMemo(() => ACTOR_ROLE_TYPE_OPTIONS, [])

  // 货币类型选项
  const currencyOptions = useMemo(
    () => [
      { value: 0, label: '人民币 (¥)' },
      { value: 1, label: '美元 ($)' },
    ],
    []
  )

  return (
    <Flex vertical>
      <ListHeader title="演员列表" total={actors.length}>
        <Space>
          <Button
            color="primary"
            variant="filled"
            shape="round"
            icon={<QrcodeOutlined />}
            onClick={() => setQrCodeModalOpen(true)}
            title="二维码">
            二维码
          </Button>
          <Button type="primary" ghost shape="round" icon={<PlusOutlined />} onClick={() => setIsModalOpen(true)}>
            添加演员
          </Button>
        </Space>
      </ListHeader>
      {actors.length > 0 ? (
        <List
          loading={loading || loadingData}
          dataSource={actors}
          split={false}
          className="list-sm"
          renderItem={renderActorCard}
        />
      ) : (
        <Empty />
      )}

      {/* 批量添加模态框 */}
      {isModalOpen ? (
        <AddActor
          open={isModalOpen}
          form={form}
          multiple={false}
          loading={saving}
          project={project}
          onCancel={() => {
            setIsModalOpen(false)
            form.resetFields()
          }}
          onOk={handleAddActors}
        />
      ) : null}

      {/* 编辑演员模态框 */}
      {isEditModalOpen ? (
        <EditActor
          open={isEditModalOpen}
          form={editForm}
          loading={saving}
          editingActor={editingActor}
          project={project}
          onCancel={() => {
            setIsEditModalOpen(false)
            setEditingActor(null)
            editForm.resetFields()
          }}
          onOk={handleSaveEdit}
        />
      ) : null}

      {/* 演员详情侧边栏 */}
      {isActorDetailOpen ? (
        <ActorDetail
          visible={isActorDetailOpen}
          actorId={selectedActorId || void 0}
          onClose={() => {
            setIsActorDetailOpen(false)
            setSelectedActorId(null)
          }}
        />
      ) : null}

      {/* 合同签约模态框 */}
      {isContractModalOpen ? (
        <ContractAdd
          visible={isContractModalOpen}
          editData={
            selectedActor
              ? {
                  verify: contractType,
                  novelName: projectName,
                  authorId: selectedActor.personId,
                  penNames: selectedActor.personName,
                  bookId: String(productionId),
                  greementDate: dayjs().format('YYYY-MM-DD'),
                  // 演员合同特有字段
                  ...(contractType === 102 && {
                    ext: '自制', // 制作方式
                    ext1: selectedActor.playRole || ACTOR_ROLE_TYPE_CONFIG[selectedActor.roleType]?.label || '演员', // 担任角色
                    ext2: String(selectedActor.dayCount || 1), // 拍摄天数
                    ext3: String(selectedActor.quotedPrice || 0), // 日薪
                    greementDate2: dayjs(project?.startDate || undefined).format('YYYY-MM-DD'), // 拍摄开始日期
                    greementDate3: dayjs(project?.endDate || undefined).format('YYYY-MM-DD'), // 拍摄结束日期
                    greementDate4: dayjs(project?.startDate || undefined).format('YYYY-MM-DD'), // 定妆日期
                    greementDate5: dayjs(project?.startDate || undefined).format('YYYY-MM-DD'), // 开机日期
                    partyADate: dayjs().format('YYYY-MM-DD'), // 甲方签署日期
                    partyBDate: dayjs(project?.startDate || undefined).format('YYYY-MM-DD'), // 乙方报到日期
                  }),
                }
              : null
          }
          onCancel={() => {
            setIsContractModalOpen(false)
            setSelectedActor(null)
            setContractType(101)
          }}
          onSuccess={async () => {
            setIsContractModalOpen(false)
            setSelectedActor(null)
            setContractType(101)
            await loadActors() // 刷新列表
          }}
          loading={false}
        />
      ) : null}

      {/* 评价弹窗 */}
      {evaluationActor && (
        <PersonEvaluationModal
          open={isEvaluationModalOpen}
          onCancel={handleEvaluationCancel}
          onSuccess={handleEvaluationSuccess}
          productionId={productionId}
          personId={evaluationActor.personId || evaluationActor.actorId}
          personName={evaluationActor.personName || evaluationActor.stageName || ''}
          parentType={2} // 演员类型
          roleType={evaluationActor.roleType}
          initialData={evaluationActor.personEvaluation}
        />
      )}

      {/* 二维码弹窗 */}
      {qrCodeModalOpen && (
        <QRCodeModal
          open={qrCodeModalOpen}
          onCancel={() => setQrCodeModalOpen(false)}
          url={`https://mpr.cdreader.com/#/login?joinProjectId=${productionId}&role=actor`}
          title="二维码"
          description="扫描二维码或分享链接进行加入"
          fileName={`项目${projectName}_二维码`}
        />
      )}
    </Flex>
  )
}

export default ProductionActors
