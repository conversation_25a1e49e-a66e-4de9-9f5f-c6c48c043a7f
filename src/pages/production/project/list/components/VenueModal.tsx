import VenueSelector from '@/pages/production/venue/components/VenueSelector'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Card, Col, Flex, Form, Input, InputNumber, Modal, Row, Select, type FormInstance } from 'antd'
import React, { useEffect, useState, useMemo } from 'react'
import useVenueStore from '../../../venue/list/store'
import useProjectStore, { type ISceneVenueInfoDto } from '../../store'
import type { IProductionListItem, IProductionVenueItem } from '../store'

interface VenueModalProps {
  open?: boolean
  loading?: boolean
  form: FormInstance
  mode: 'add' | 'edit' // 模式：添加或编辑
  editingVenue?: IProductionVenueItem | null // 编辑的场地数据（仅编辑模式有效）
  project?: IProductionListItem
  onOk: (values: any) => void
  onCancel: () => void
}

// 通用场地模态框
const VenueModal: React.FC<VenueModalProps> = ({
  form,
  open,
  loading,
  mode,
  editingVenue,
  project,
  onOk,
  onCancel
}) => {
  const { getVenueMediaByVenueId, venueClassMap } = useVenueStore()
  const { getScenePlanVenue } = useProjectStore()

  // 场景数据状态
  const [sceneVenueData, setSceneVenueData] = useState<ISceneVenueInfoDto[]>([])
  const formVenueInfo = Form.useWatch('venueInfo', form)
  const currentVenueId = Form.useWatch('venueId', form)

  const subclassOptions = useMemo(() => {
    return currentVenueId ? (venueClassMap?.[currentVenueId] || []).map(item => ({
      label: item,
      value: item,
    })) : []
  }, [venueClassMap, currentVenueId])

  const isEditMode = mode === 'edit'
  const title = isEditMode ? '编辑场地' : '添加场地'
  const submitText = isEditMode ? '立即保存' : '立即添加'

  // 处理一维场景数据转二维数据
  const processedSceneData = useMemo(() => {
    if (!sceneVenueData.length) return { mainSceneOptions: [], subSceneOptions: {} }

    // 获取所有主场景
    const mainScenes = [...new Set(sceneVenueData.map(item => item.mainVenue).filter(Boolean))]
    const mainSceneOptions = mainScenes.map(scene => ({ label: scene, value: scene }))

    // 按主场景分组获取分场景
    const subSceneOptions: { [key: string]: Array<{ label: string; value: string }> } = {}
    mainScenes.forEach(mainScene => {
      if (mainScene) {
        const subScenes = sceneVenueData
          .filter(item => item.mainVenue === mainScene && item.venue)
          .map(item => item.venue!)
        const uniqueSubScenes = [...new Set(subScenes)]
        subSceneOptions[mainScene] = uniqueSubScenes.map(scene => ({ label: scene, value: scene }))
      }
    })

    return { mainSceneOptions, subSceneOptions }
  }, [sceneVenueData])
  // 获取场景数据
  const loadSceneData = async () => {
    if (!project?.id) return

    try {
      const result = await getScenePlanVenue(project.id)
      setSceneVenueData(result || [])
    } catch (error) {
      console.error('获取场景数据失败:', error)
    }
  }

  // 当模态框打开时加载场景数据
  useEffect(() => {
    if (open && project?.id) {
      loadSceneData()
    }
  }, [open, project?.id])

  // 处理场地选择
  const handleVenueSelect = (venueId: number, option: any) => {
    if (venueId) {
      form.setFieldValue('venueName', option.venueName)
      form.setFieldValue('cost', option.cost)
      form.setFieldValue('suitableType', option.suitableType)
      form.setFieldValue('address', option.address)
      form.setFieldValue('quotedPrice', option.cost)
      form.setFieldValue('isInternal', option.isInternal)
      form.setFieldValue('videos', option.videos)
      form.setFieldValue('photos', option.photos)
      getVenueMediaByVenueId(venueId)
    }
  }

  // 编辑模式：设置表单初始值
  useEffect(() => {
    if (open && isEditMode && editingVenue) {
      const formData = {
        ...editingVenue,
        venueInfo: editingVenue.venueInfo && editingVenue.venueInfo.length > 0 ? editingVenue.venueInfo : [{ selectionType: 1 }]
      }
      form.setFieldsValue(formData)
      getVenueMediaByVenueId(editingVenue.venueId)
    }
  }, [open, isEditMode, editingVenue])



  return (
    <Modal title={title} open={open} onCancel={onCancel} footer={null} width={800} destroyOnHidden>
      <Form
        form={form}
        layout="horizontal"
        colon={false}
        labelCol={{ span: 4 }}
        wrapperCol={isEditMode ? { span: 18 } : undefined}
        onFinish={onOk}
        initialValues={isEditMode ? undefined : { venueInfo: [{}] }}
        style={{ maxHeight: '70vh', overflow: 'auto' }}
      >

        <Form.Item name="venueId" label="选择场地" rules={[{ required: true, message: '请选择场地' }]}>
          <VenueSelector
            onSelect={handleVenueSelect}
            initOption={
              isEditMode && editingVenue
                ? {
                  id: editingVenue.venueId,
                  venueName: editingVenue.venueName,
                  label: editingVenue.venueName,
                  address: editingVenue.address,
                  suitableType: editingVenue.suitableType,
                  cost: editingVenue.cost,
                  isInternal: editingVenue.isInternal,
                }
                : null
            }
          />
        </Form.Item>

        <Form.Item name="dayCount" label="天数">
          <InputNumber className="full-h" placeholder="请输入拍摄天数" min={1} precision={0} suffix="天" />
        </Form.Item>

        <Form.Item name="quotedPrice" label="单价">
          <InputNumber
            className="full-h"
            placeholder="请输入单价"
            min={0}
            precision={2}
            prefix={project?.currencySymbol || '¥'}
          />
        </Form.Item>

        <Form.Item name="totalPrice" label="总价">
          <InputNumber
            className="full-h"
            placeholder="请输入总价"
            min={0}
            precision={2}
            prefix={project?.currencySymbol || '¥'}
          />
        </Form.Item>

        <Form.Item name="hasInvoice" label="是否正规发票" initialValue={false}>
          <Select
            className="full-h"
            placeholder="请选择是否正规发票"
            options={[
              { value: true, label: '是' },
              { value: false, label: '否' },
            ]}
          />
        </Form.Item>

        <Form.Item name="description" label="备注">
          <Input.TextArea
            className="full-h"
            placeholder="请输入备注信息"
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>

        {/* 场地场景信息 */}
        <Form.Item label="场地场景信息">
          <Form.List name="venueInfo">
            {(venueInfoFields, { add: addVenueInfo, remove: removeVenueInfo }) => (
              <>
                {venueInfoFields.map(({ key: venueInfoKey, name: venueInfoName, ...venueInfoRestField }) => {


                  return (
                    <Card
                      key={venueInfoKey}
                      size="small"
                      style={{ marginBottom: 8 }}
                      title={`场景信息 ${venueInfoName + 1}`}
                      extra={
                        venueInfoFields.length > 1 && (
                          <Button
                            type="text"
                            danger
                            size="small"
                            icon={<MinusCircleOutlined />}
                            onClick={() => removeVenueInfo(venueInfoName)}
                          >
                            删除
                          </Button>
                        )
                      }
                    >
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            {...venueInfoRestField}
                            name={[venueInfoName, 'selectionType']}
                            label="场地选择"
                            rules={[{ required: true, message: '请选择场地选择类型' }]}
                            initialValue={1}
                          >
                            <Select
                              placeholder="请选择场地选择类型"
                              options={[
                                // { value: 0, label: '备选' },
                                { value: 1, label: '主选' },
                              ]}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            {...venueInfoRestField}
                            name={[venueInfoName, 'subVenueName']}
                            label="子场地"
                            rules={[{ required: true, message: '请选择子场地' }]}
                          >
                            <Select
                              placeholder="请选择子场地"
                              options={subclassOptions}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Form.Item
                            {...venueInfoRestField}
                            name={[venueInfoName, 'mainSceneName']}
                            label="主场景"
                            rules={[{ required: true, message: '请选择主场景' }]}
                          >
                            <Select
                              placeholder="请选择主场景"
                              options={processedSceneData.mainSceneOptions}
                              onSelect={() => {
                                // 清空子场景选择
                                form.setFieldValue(['venueInfo', venueInfoName, 'subSceneName'], undefined)
                              }}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            {...venueInfoRestField}
                            name={[venueInfoName, 'subSceneName']}
                            label="子场景"
                            rules={[{ required: true, message: '请选择子场景' }]}
                            dependencies={[['venueInfo', venueInfoName, 'mainSceneName']]}
                          >
                            <Select
                              placeholder="请选择子场景"
                              options={formVenueInfo?.[venueInfoName]?.mainSceneName ? processedSceneData.subSceneOptions[formVenueInfo[venueInfoName]?.mainSceneName] : []}
                              disabled={!formVenueInfo?.[venueInfoName]?.mainSceneName}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Card>
                  )
                })}
                <Button
                  type="dashed"
                  onClick={() => addVenueInfo()}
                  block
                  icon={<PlusOutlined />}
                  size="small"
                >
                  添加场景信息
                </Button>
              </>
            )}
          </Form.List>
        </Form.Item>

        {/* 隐藏字段存储场地信息 */}
        <Form.Item name="venueName" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="address" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="suitableType" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="cost" hidden>
          <InputNumber />
        </Form.Item>
        <Form.Item name="isInternal" hidden />
        <Form.Item name="photos" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="videos" hidden>
          <Input />
        </Form.Item>
        <Form.Item name="customCost" hidden />

        <Form.Item className="no-margin" label={isEditMode ? " " : undefined}>
          <Flex justify="end" gap={12}>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              {submitText}
            </Button>
          </Flex>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default VenueModal
