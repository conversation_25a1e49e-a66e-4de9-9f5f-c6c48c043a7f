import filterMatch from '@/utils/filterMatch'
import { Fieldset } from '@fe/rockrose'
import { <PERSON><PERSON>, Di<PERSON>r, Drawer, Flex, Form, Input, InputNumber, message, Select, Space, Typography } from 'antd'
import React, { useMemo, useState } from 'react'
import { ACTOR_ROLE_TYPE_CONFIG, EXPENSE_TYPE_CONFIG, ExpenseType, ROLE_TYPE_CONFIG } from '../../../../../consts'
import { IPrExtraExpensesInfo } from '../store'

const { TextArea } = Input
const { Text } = Typography

interface IAddPackageBudgetProps {
  open: boolean
  onCancel: () => void
  onSuccess: (packageData: any) => void
  loading?: boolean
  productionId: number
  expense?: any // 编辑模式时传入的费用数据
  project?: any // 项目信息，用于获取货币符号
}

const AddPackageBudget: React.FC<IAddPackageBudgetProps> = ({
  open,
  onCancel,
  onSuccess,
  loading = false,
  productionId,
  expense,
  project,
}) => {
  const [form] = Form.useForm()
  const [selectedKeys, setSelectedKeys] = useState<string[]>([])
  const [expenseDetails, setExpenseDetails] = useState<Record<string, { personCount: number }>>({})

  // 判断是否为编辑模式
  const isEdit = !!expense?.id

  // 当打开模态框时，如果是编辑模式，预填充数据
  React.useEffect(() => {
    if (open) {
      if (isEdit && expense) {
        // 编辑模式：填充现有数据
        form.setFieldsValue({
          expenseName: expense.expenseName || '',
          quotedPrice: expense.quotedPrice,
          personCount: expense.personCount || 1,
          hasInvoice: expense.hasInvoice || false,
          dayCount: expense.dayCount || 1,
          description: expense.description || '',
          totalPrice: expense.totalPrice,
        })

        // 从 expensesInfo 构建 selectedKeys 和 expenseDetails
        if (expense.expensesInfo?.length) {
          const keys = expense.expensesInfo.map((info: IPrExtraExpensesInfo) => `${info.parentType}-${info.roleType}`)
          const details: Record<string, { personCount: number }> = {}

          expense.expensesInfo.forEach((info: IPrExtraExpensesInfo) => {
            const key = `${info.parentType}-${info.roleType}`

            details[key] = { personCount: info.personCount || 1 }
          })

          setSelectedKeys(keys)
          setExpenseDetails(details)
        }
      } else {
        // 新增模式：重置表单
        form.resetFields()
        setSelectedKeys([])
        setExpenseDetails({})
      }
    }
  }, [open, isEdit, expense, form])

  // 当选中的keys变化时，更新expenseDetails
  const handleSelectChange = (keys: string[]) => {
    setSelectedKeys(keys)

    // 更新 expenseDetails，保留已存在的人数设置
    const newDetails: Record<string, { personCount: number }> = {}

    keys.forEach(key => {
      // 如果已经存在，保留原来的值；否则设置默认值1
      newDetails[key] = expenseDetails[key] || { personCount: 1 }
    })
    setExpenseDetails(newDetails)
  }

  // 构建下拉选项数据
  const selectOptions = useMemo(() => {
    const options: Array<{
      label: string
      title?: string
      options?: Array<{ value: string; label: string }>
    }> = []

    // 人员类型 (parentType = 1)
    const roleTypeOptions = Object.entries(ROLE_TYPE_CONFIG)
      .filter(([, config]) => !config.disable) // 过滤掉禁用的角色
      .map(([value, config]) => ({
        value: `1-${value}`,
        label: config.label,
      }))

    options.push({
      label: '人员类型',
      title: '人员类型',
      options: roleTypeOptions,
    })

    // 演员类型 (parentType = 2)
    const actorRoleOptions = Object.entries(ACTOR_ROLE_TYPE_CONFIG).map(([value, config]) => ({
      value: `2-${value}`,
      label: config.label,
    }))

    options.push({
      label: '演员类型',
      title: '演员类型',
      options: actorRoleOptions,
    })

    // 费用类型 (parentType = 3)
    const expenseTypeOptions = Object.entries(EXPENSE_TYPE_CONFIG)
      .filter(([value]) => Number(value) !== ExpenseType.PackageBudget) // 排除打包费用本身
      .map(([value, config]) => ({
        value: `3-${value}`,
        label: config.label,
      }))

    options.push({
      label: '费用类型',
      title: '费用类型',
      options: expenseTypeOptions,
    })

    return options
  }, [])

  const handleOk = async () => {
    try {
      const values = await form.validateFields()

      if (selectedKeys.length === 0) {
        message.warning('请至少选择一项费用内容')

        return
      }

      // 构建 expensesInfo 数据
      const expensesInfo: IPrExtraExpensesInfo[] = selectedKeys.map((key, index) => {
        const [parentType, roleType] = key.split('-').map(Number)

        return {
          expenseId: expense?.id || productionId || null, // 关联的费用ID，新增时为null，编辑时为当前费用ID
          parentType,
          roleType,
          personCount: expenseDetails[key]?.personCount || 1,
          sort: index + 1,
        }
      })

      const packageData = {
        expenseType: ExpenseType.PackageBudget, // 48
        expenseName: values.expenseName || '',
        quotedPrice: values.quotedPrice,
        personCount: values.personCount || 1,
        hasInvoice: values.hasInvoice || false,
        description: values.description || '',
        dayCount: values.dayCount || 1,
        totalPrice: values.totalPrice,
        expensesInfo,
      }

      onSuccess(packageData)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  // 获取选中项目的显示名称
  const getItemLabel = (key: string) => {
    const [parentType, roleType] = key.split('-').map(Number)

    if (parentType === 1) {
      // 人员类型
      const config = ROLE_TYPE_CONFIG[roleType as unknown as keyof typeof ROLE_TYPE_CONFIG]

      return config?.label || '未知'
    } else if (parentType === 2) {
      // 演员类型
      const config = ACTOR_ROLE_TYPE_CONFIG[roleType as unknown as keyof typeof ACTOR_ROLE_TYPE_CONFIG]

      return config?.label || '未知'
    } else if (parentType === 3) {
      // 费用类型
      const config = EXPENSE_TYPE_CONFIG[roleType as unknown as keyof typeof EXPENSE_TYPE_CONFIG]

      return config?.label || '未知'
    }

    return '未知'
  }

  // 更新某个项目的人数
  const updatePersonCount = (key: string, personCount: number) => {
    setExpenseDetails(prev => ({
      ...prev,
      [key]: { personCount },
    }))
  }

  const handleCancel = () => {
    form.resetFields()
    setSelectedKeys([])
    setExpenseDetails({})
    onCancel()
  }

  return (
    <Drawer
      title={isEdit ? '编辑打包费用' : '添加打包费用'}
      open={open}
      onClose={handleCancel}
      width={800}
      destroyOnHidden
      footer={
        <Flex justify="flex-end" gap={12}>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={handleOk} loading={loading}>
            立即保存
          </Button>
        </Flex>
      }>
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          expenseName: '',
          personCount: 1,
          hasInvoice: false,
          dayCount: 1,
        }}>
        {/* 基础信息 */}
        <Space wrap>
          <Form.Item name="expenseName" label="打包名" rules={[{ required: true, message: '请输入名称' }]}>
            <Input placeholder="例如：摄影+灯光" className="w300" />
          </Form.Item>

          <Form.Item name="quotedPrice" label="打包价(元)" rules={[{ required: true, message: '请输入打包单价' }]}>
            <InputNumber
              className="w200"
              placeholder="请输入打包价"
              min={0}
              precision={2}
              prefix={project?.currencySymbol || '¥'}
            />
          </Form.Item>

          <Form.Item name="personCount" label="数量" rules={[{ required: true, message: '请输入数量' }]} hidden>
            <InputNumber placeholder="数量" min={1} precision={0} />
          </Form.Item>

          <Form.Item name="dayCount" label="天数" rules={[{ required: true, message: '请输入天数' }]} hidden>
            <InputNumber placeholder="天数" min={1} precision={0} />
          </Form.Item>

          <Form.Item name="hasInvoice" label="是否正规发票">
            <Select
              className="w200"
              placeholder="请选择"
              options={[
                { value: true, label: '是' },
                { value: false, label: '否' },
              ]}
            />
          </Form.Item>

          <Form.Item name="totalPrice" label="总价(元)" hidden>
            <InputNumber
              className="w200"
              placeholder="请输入总价"
              min={0}
              precision={2}
              prefix={project?.currencySymbol || '¥'}
            />
          </Form.Item>
        </Space>
        {/* 备注信息 */}
        <Form.Item name="description" label="备注">
          <TextArea rows={3} placeholder="请输入备注信息" maxLength={500} showCount />
        </Form.Item>

        {/* 费用构成选择 */}
        <Form.Item label="打包价包含费用细项" required rules={[{ required: true, message: '请选择费用构成' }]}>
          <Select
            mode="multiple"
            placeholder="支持多选"
            value={selectedKeys}
            onChange={handleSelectChange}
            options={selectOptions}
            maxTagCount="responsive"
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.label || '')

              return Boolean(result)
            }}
          />
        </Form.Item>

        {/* 选中项目的人数设置 */}
        {selectedKeys.length > 0 && (
          <Fieldset title="包含明细" direction="vertical">
            <Space direction="vertical" split={<Divider className="no-margin" variant="dashed" />} className="full-h">
              {selectedKeys.map(key => (
                <Flex key={key} justify="space-between" align="center">
                  <Flex flex={1}>{getItemLabel(key)}</Flex>
                  <Space>
                    <Text type="secondary">数量</Text>
                    <InputNumber
                      min={1}
                      precision={0}
                      value={expenseDetails[key]?.personCount || 1}
                      onChange={value => updatePersonCount(key, value || 1)}
                      style={{ width: 80 }}
                    />
                  </Space>
                </Flex>
              ))}
            </Space>
          </Fieldset>
        )}
      </Form>
    </Drawer>
  )
}

export default AddPackageBudget
