import { PRODUCTION_TYPE_CONFIG, ProductionType, ROLE_TYPE_CONFIG } from '@/consts'
import { DATE_FORMAT_BASE, DATE_FORMAT_DAY } from '@/consts/date'
import { PROJECT_STATUS_MAP } from '@/consts/project'
import { PlusOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Badge, Button, Card, Col, Descriptions, Divider, Flex, List, Row, Space, Tag, Typography } from 'antd'
import dayjs from 'dayjs'
import React from 'react'
import { IProductionListItem } from '../store'
import styles from './List.scss'

// 项目列表
const ProjectList: React.FC<any> = ({ data, pagination, loading, onChange, onOperate }) => {
  // 渲染项目卡片
  const renderProjectCard = (project: IProductionListItem) => {
    return (
      <List.Item>
        <Card
          className="full-h hover-move"
          size="small"
          hoverable
          onClick={() => onOperate && onOperate('view', project)}>
          <div style={{ position: 'relative' }}>
            <Row gutter={24}>
              <Col span={14}>
                <Flex vertical gap={12}>
                  <Space size={2} split={<Divider type="vertical" />}>
                    <Typography.Text strong className="fs-lg">
                      {project.productionName}
                    </Typography.Text>
                    {typeof project.productionType === 'number' && (
                      <Tag
                        className={`no-margin ${
                          project.productionType === ProductionType.COMMISSIONED ? 'text-primary' : ''
                        }`}>
                        {PRODUCTION_TYPE_CONFIG[project.productionType as ProductionType]?.label}
                      </Tag>
                    )}
                    {project.productionCode ? <Dict title="剧本代号" value={project.productionCode} /> : null}
                    {project.secondProductionCode ? (
                      <Dict title="项目代号" value={project.secondProductionCode} />
                    ) : null}
                    {/* {!!project.scriptBookId && <Dict title="剧本ID" value={project.scriptBookId} />} */}
                  </Space>
                  {project.description && <Typography.Text type="secondary">{project.description}</Typography.Text>}
                  <Space size={2} split={<Divider type="vertical" />}>
                    <Badge
                      status={PROJECT_STATUS_MAP[project.status].status}
                      text={PROJECT_STATUS_MAP[project.status].label}
                    />
                    {!project.updateTime ? (
                      <Space>
                        {project.creator && <Typography.Text>{project.creator}</Typography.Text>}
                        <Dict title="添加于" value={dayjs(project.createTime).format(DATE_FORMAT_BASE)} />
                      </Space>
                    ) : (
                      <Space>
                        {project.lastModifier && <Typography.Text>{project.lastModifier}</Typography.Text>}
                        <Dict title="最近更新于" value={dayjs(project.updateTime).format(DATE_FORMAT_BASE)} />
                      </Space>
                    )}
                  </Space>
                </Flex>
              </Col>
              <Col span={6}>
                {project.personBasicInfo && project.personBasicInfo?.length > 0 && (
                  <Descriptions column={1} colon={false} size="small" className={styles.descriptions}>
                    {project.personBasicInfo
                      .filter(person => [1, 3, 11, 12].includes(person.roleType))
                      .map(person => (
                        <Descriptions.Item label={ROLE_TYPE_CONFIG[person.roleType]?.label}>
                          {person.personName}
                        </Descriptions.Item>
                      ))}
                  </Descriptions>
                )}
              </Col>
              <Col span={4}>
                <Space direction="vertical" size={12} style={{ paddingRight: '40px' }}>
                  {project.startDate && (
                    <Dict title="开机日期" value={dayjs(project.startDate).format(DATE_FORMAT_DAY)}></Dict>
                  )}
                  {(project.startDate || project.endDate) && (
                    <Dict title="杀青日期" value={dayjs(project.endDate).format(DATE_FORMAT_DAY)}></Dict>
                  )}
                </Space>
              </Col>
            </Row>
          </div>
        </Card>
      </List.Item>
    )
  }

  return (
    <Flex vertical>
      <ListHeader title="项目列表" total={pagination?.total}>
        <Button type="primary" ghost icon={<PlusOutlined />} onClick={() => onOperate && onOperate('create')}>
          添加项目
        </Button>
      </ListHeader>
      <List
        loading={loading}
        dataSource={data}
        renderItem={renderProjectCard}
        rowKey="id"
        className="list-sm"
        split={false}
        pagination={{
          ...pagination,
          onChange,
          onShowSizeChange: onChange,
        }}
      />
    </Flex>
  )
}

export default ProjectList
