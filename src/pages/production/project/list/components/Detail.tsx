import { PRICE_CURRENCY_CONFIG } from '@/consts'
import { DATE_FORMAT_DAY } from '@/consts/date'
import useIndexStore from '@/store'
import { exportProductionInsurance } from '@/utils/export'
import {
  AccountBookOutlined,
  CloudDownloadOutlined,
  DeleteOutlined,
  DiffOutlined,
  EditOutlined,
  FileTextOutlined,
  ScheduleOutlined,
} from '@ant-design/icons'
import { ListHeader } from '@fe/rockrose'
import { Button, Descriptions, Divider, Drawer, Flex, Popconfirm, Space, Tabs, Typography, message } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useMemo, useState } from 'react'
import { ScenePlanSidebar } from '../../components'
import useProjectListStore, { IProductionListItem } from '../store'
import ProductionActors from './ProductionActors'
// import ProductionAllPerson from './ProductionAllPerson'
import ProductionBudgetOverview from './ProductionBudgetOverview'
import ProductionExtraExpenses from './ProductionExtraExpenses'
import ProductionMedia from './ProductionMedia'
import ProductionPersonApplicationReview from './ProductionPersonApplicationReview'
import ProductionPersonBasic from './ProductionPersonBasic'
import ProductionPersonRecruitment from './ProductionPersonRecruitment'
import ProductionPersonRoom from './ProductionPersonRoom'
import ProductionVenue from './ProductionVenue'

interface IProjectDetailProps {
  open: boolean
  project?: IProductionListItem
  onClose: () => void
  onEdit: (project: IProductionListItem) => void
  onDelete: (project: IProductionListItem) => void
  onOpenComparison: (currentProjectId: number) => void
  onOpenAnnotation: (productionId: number, productionName?: string) => void
}

const ProjectDetail: React.FC<IProjectDetailProps> = ({
  open,
  project,
  onClose,
  onEdit,
  onDelete,
  onOpenComparison,
  onOpenAnnotation,
}) => {
  const { authorBtn } = useIndexStore()
  const { buildProductionBudget, exportBudgetById, getProductionById } = useProjectListStore()
  const [activeKey, setActiveKey] = useState('actors')
  const [buildingBudget, setBuildingBudget] = useState(false)
  const [currentProject, setCurrentProject] = useState<IProductionListItem | undefined>(project)
  const [scenePlanVisible, setScenePlanVisible] = useState(false)

  // 当传入的project变化时，更新currentProject
  useEffect(() => {
    setCurrentProject(project)
  }, [project])

  // 当弹窗打开时重置 activeKey
  useEffect(() => {
    if (open) {
      setActiveKey('actors')
    }
  }, [open])

  // 刷新项目数据
  const refreshProject = async () => {
    if (!currentProject?.id) {
      return
    }

    try {
      const updatedProject = await getProductionById(currentProject.id)

      if (updatedProject) {
        setCurrentProject(updatedProject)
      }
    } catch (error) {
      console.error('刷新项目数据失败:', error)
    }
  }

  // 生成预算
  const handleBuildBudget = async () => {
    if (!currentProject?.id) {
      return
    }

    setBuildingBudget(true)
    try {
      const success = await buildProductionBudget(currentProject.id)

      if (success) {
        message.success('预算生成成功')
        await refreshProject()
        if (activeKey === 'budgetOverview') {
          setActiveKey('budget')

          setTimeout(() => {
            setActiveKey('budgetOverview')
          }, 100)
        } else {
          setActiveKey('budgetOverview')
        }
      }
    } catch (error) {
      console.error('生成预算失败:', error)
      message.error('预算生成失败')
    } finally {
      setBuildingBudget(false)
    }
  }

  const tabItems = useMemo(
    () => [
      // {
      //   key: 'allUser',
      //   label: '项目组',
      //   children:
      //     project?.id && activeKey == 'allUser' ? (
      //       <ProductionAllPerson productionId={project.id} projectName={project.productionName} project={project} />
      //     ) : null,
      // },
      {
        key: 'actors',
        label: '演员',
        children:
          project?.id && activeKey == 'actors' ? (
            <ProductionActors productionId={project.id} projectName={project.productionName} project={project} />
          ) : null,
      },
      {
        key: 'personBasic',
        label: '拍摄人员',
        children:
          project?.id && activeKey == 'personBasic' ? (
            <ProductionPersonBasic productionId={project.id} projectName={project.productionName} project={project} />
          ) : null,
      },
      {
        key: 'venue',
        label: '拍摄场地',
        children:
          project?.id && activeKey == 'venue' ? <ProductionVenue productionId={project.id} project={project} /> : null,
      },
      {
        key: 'extraExpenses',
        label: '登记费用',
        children:
          project?.id && activeKey == 'extraExpenses' ? (
            <ProductionExtraExpenses productionId={project.id} project={project} />
          ) : null,
      },

      // 预算总览（仅当已生成预算时显示）
      ...(currentProject?.hasBudget && activeKey == 'budgetOverview'
        ? [
            {
              key: 'budgetOverview',
              label: '预算总览',
              children: currentProject?.id ? (
                <ProductionBudgetOverview
                  productionId={currentProject.id}
                  isActive={activeKey === 'budgetOverview'}
                  project={currentProject}
                  isBudgetMode={true}
                />
              ) : null,
            },
          ]
        : []),
      {
        key: 'budget',
        label: '结算总览',
        children:
          currentProject?.id && activeKey == 'budget' ? (
            <ProductionBudgetOverview
              productionId={currentProject.id}
              isActive={activeKey === 'budget'}
              project={currentProject}
              isBudgetMode={false}
            />
          ) : null,
      },
      {
        key: 'media',
        label: '附件资料',
        children:
          project?.id && activeKey == 'media' ? <ProductionMedia productionId={project.id} project={project} /> : null,
      },
      {
        key: 'personRoom',
        label: '入住公寓',
        children:
          project?.id && activeKey == 'personRoom' ? (
            <ProductionPersonRoom productionId={project.id} projectName={project.productionName} project={project} />
          ) : null,
      },
      {
        key: 'personRecruitment',
        label: '招募人员',
        children:
          project?.id && activeKey == 'personRecruitment' ? (
            <ProductionPersonRecruitment
              productionId={project.id}
              projectName={project.productionName}
              project={project}
            />
          ) : null,
      },
      {
        key: 'personApplicationReview',
        label: '招募审批',
        children:
          project?.id && activeKey == 'personApplicationReview' ? (
            <ProductionPersonApplicationReview
              productionId={project.id}
              projectName={project.productionName}
              project={project}
            />
          ) : null,
      },
    ],
    [project, currentProject, activeKey]
  )

  // 处理导出保险记录
  const handleExportInsurance = async () => {
    if (!currentProject?.id) {
      return
    }

    try {
      await exportProductionInsurance(currentProject.id, `${currentProject.productionName}-投保名单.xlsx`)
    } catch (error) {
      console.error('导出保险记录失败:', error)
    }
  }

  return (
    <Drawer
      title={`${currentProject?.productionName || '项目详情'}`}
      width={960}
      open={open}
      onClose={onClose}
      extra={
        currentProject && (
          <Space.Compact>
            <Button
              type="default"
              shape="round"
              className="text-primary"
              icon={<FileTextOutlined />}
              onClick={() => onOpenAnnotation(currentProject.id, currentProject.productionName)}>
              剧本标注
            </Button>

            <Button
              type="default"
              shape="round"
              className="text-primary"
              icon={<ScheduleOutlined />}
              onClick={() => setScenePlanVisible(true)}>
              大计划
            </Button>

            <Button
              type="default"
              shape="round"
              className="text-primary"
              icon={<DiffOutlined />}
              onClick={() => onOpenComparison(currentProject?.id)}>
              结算对比
            </Button>

            {/* 预算相关按钮 */}
            <Button
              type="default"
              className="text-primary"
              icon={<AccountBookOutlined />}
              loading={buildingBudget}
              onClick={handleBuildBudget}>
              生成预算
            </Button>

            <Button
              type="default"
              shape="round"
              className="text-primary"
              icon={<CloudDownloadOutlined />}
              onClick={handleExportInsurance}>
              导出保险名单
            </Button>
          </Space.Compact>
        )
      }>
      <Flex vertical gap={16}>
        <Flex vertical gap={4}>
          <ListHeader title="基本信息" className="no-margin">
            {currentProject && (
              <Space size={0} split={<Divider type="vertical" />}>
                {authorBtn.includes('删除') && !currentProject.feNoEdit && (
                  <Popconfirm
                    title="警告"
                    description={`确定要删除【${currentProject.productionName}】吗？`}
                    onConfirm={() => onDelete(currentProject)}
                    okText="确定删除"
                    cancelText="取消">
                    <Typography.Link>
                      <Space size={6}>
                        <DeleteOutlined />
                        删除
                      </Space>
                    </Typography.Link>
                  </Popconfirm>
                )}
                <Typography.Link onClick={() => onEdit(currentProject)}>
                  <Space size={6}>
                    <EditOutlined />
                    编辑
                  </Space>
                </Typography.Link>
              </Space>
            )}
          </ListHeader>
          <Descriptions size="small" bordered column={3}>
            <Descriptions.Item label="项目" span={3}>
              <Typography.Text strong>{currentProject?.productionName || '-'}</Typography.Text>
            </Descriptions.Item>
            <Descriptions.Item label="剧本代号">{currentProject?.productionCode || '-'}</Descriptions.Item>
            <Descriptions.Item label="项目代号">{currentProject?.secondProductionCode || '-'}</Descriptions.Item>
            <Descriptions.Item label="剧本ID">{currentProject?.scriptBookId || '-'}</Descriptions.Item>
            <Descriptions.Item label="结算货币">
              {currentProject?.priceCurrency ? PRICE_CURRENCY_CONFIG[currentProject.priceCurrency]?.label || '-' : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="总集数">{currentProject?.totalEpisodes || '-'}</Descriptions.Item>
            <Descriptions.Item label="总时长">
              {currentProject?.totalDuration ? `${currentProject.totalDuration}分钟` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="城市">{currentProject?.cityName || '-'}</Descriptions.Item>
            <Descriptions.Item label="开机日期">
              {currentProject?.startDate ? dayjs(currentProject?.startDate).format(DATE_FORMAT_DAY) : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="杀青日期">
              {currentProject?.endDate ? dayjs(currentProject.endDate).format(DATE_FORMAT_DAY) : '-'}
            </Descriptions.Item>
            {!!currentProject?.description && (
              <Descriptions.Item label="描述" span={3}>
                {currentProject?.description}
              </Descriptions.Item>
            )}
          </Descriptions>
        </Flex>
        <Tabs
          activeKey={activeKey}
          onChange={setActiveKey}
          indicator={{ size: 32 }}
          items={tabItems}
          className="full-v"
        />
      </Flex>

      {/* 大计划侧边栏 */}
      {currentProject?.id && scenePlanVisible ? (
        <ScenePlanSidebar
          productionId={currentProject.id}
          visible={scenePlanVisible}
          onClose={() => setScenePlanVisible(false)}
        />
      ) : null}
    </Drawer>
  )
}

export default ProjectDetail
