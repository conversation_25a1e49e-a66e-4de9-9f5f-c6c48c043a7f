import { ACTOR_ROLE_TYPE_CONFIG, ROLE_TYPE_CONFIG } from '@/consts'
import { Button, Form, Input, Modal, Rate, Space, Typography, message } from 'antd'
import React, { useEffect } from 'react'
import useProjectListStore, { IPrPersonEvaluation } from '../store'

const { TextArea } = Input

interface IPersonEvaluationModalProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  loading?: boolean
  productionId: number
  personId: number
  personName: string
  parentType: number // 1人员，2演员
  roleType: number
  initialData?: IPrPersonEvaluation | null
}

const PersonEvaluationModal: React.FC<IPersonEvaluationModalProps> = ({
  open,
  onCancel,
  onSuccess,
  loading = false,
  productionId,
  personId,
  personName,
  parentType,
  roleType,
  initialData,
}) => {
  const [form] = Form.useForm()
  const [saving, setSaving] = React.useState(false)
  const { savePersonEvaluation } = useProjectListStore()

  useEffect(() => {
    if (open) {
      if (initialData) {
        // 编辑模式：填充现有数据
        form.setFieldsValue({
          score: initialData.score,
          comment: initialData.comment,
        })
      } else {
        // 新增模式：重置表单
        form.resetFields()
      }
    }
  }, [open, initialData, form])

  // 获取角色类型显示名称
  const getRoleTypeName = () => {
    if (parentType === 1) {
      // 人员类型
      const config = ROLE_TYPE_CONFIG[roleType as keyof typeof ROLE_TYPE_CONFIG]

      return config?.label || '未知角色'
    } else if (parentType === 2) {
      // 演员类型
      const config = ACTOR_ROLE_TYPE_CONFIG[String(roleType) as keyof typeof ACTOR_ROLE_TYPE_CONFIG]

      return config?.label || '未知角色'
    }

    return '未知角色'
  }

  const handleSubmit = () => {
    form.submit()
  }

  const handleFinish = async (values: any) => {
    setSaving(true)
    try {
      const evaluationData: IPrPersonEvaluation = {
        id: initialData?.id,
        productionId,
        personId,
        parentType,
        roleType,
        score: values.score,
        comment: values.comment,
      }

      const success = await savePersonEvaluation(evaluationData)

      if (success) {
        message.success(initialData ? '评价更新成功' : '评价保存成功')
        onSuccess()
      } else {
        message.error(initialData ? '评价更新失败' : '评价保存失败')
      }
    } catch (error) {
      console.error('保存评价失败:', error)
      message.error('操作失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const handleClose = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={`${initialData ? '编辑' : '添加'}人员评价`}
      open={open}
      onCancel={handleClose}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={handleClose}>取消</Button>
            <Button type="primary" onClick={handleSubmit} loading={saving || loading}>
              {initialData ? '更新评价' : '保存评价'}
            </Button>
          </Space>
        </div>
      }
      width={600}
      maskClosable={false}>
      <div style={{ marginBottom: 16 }}>
        <Typography.Text strong>评价对象：</Typography.Text>
        <Typography.Text>{personName}</Typography.Text>
        <Typography.Text type="secondary" style={{ marginLeft: 8 }}>
          ({getRoleTypeName()})
        </Typography.Text>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleFinish}
        initialValues={{
          score: 5,
          comment: '',
        }}>
        <Form.Item name="score" label="评分" rules={[{ required: true, message: '请给出评分' }]}>
          <Rate allowHalf={false} count={10} style={{ fontSize: 20 }} />
        </Form.Item>

        <Form.Item
          name="comment"
          label="评价内容"
          rules={[
            { required: true, message: '请填写评价内容' },
            { min: 5, message: '评价内容至少5个字符' },
            { max: 500, message: '评价内容不能超过500个字符' },
          ]}>
          <TextArea
            rows={4}
            placeholder="请详细描述对该人员的工作表现、专业能力、合作态度等方面的评价..."
            showCount
            maxLength={500}
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default PersonEvaluationModal
