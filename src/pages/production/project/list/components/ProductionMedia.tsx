import EnvImage from '@/components/EnvImage'
import UploadComponent from '@/components/Upload'
import VideoView from '@/components/VideoView'
import { envUrl } from '@/utils/request'
import { CloseCircleOutlined, DownloadOutlined, FileOutlined, FilePdfOutlined, PlusOutlined } from '@ant-design/icons'
import { Badge, Button, Empty, Flex, Form, Image, Input, message, Modal, Popconfirm, Space, Tabs } from 'antd'
import React, { useEffect, useState } from 'react'
import useProjectListStore, { IProductionListItem, IProductionMediaItem, prProductionMediaUploadAPI } from '../store'
import styles from './ProductionMedia.scss'

const { TextArea } = Input

interface IProductionMediaProps {
  productionId: number
  loading?: boolean
  project?: IProductionListItem
}

// 媒体类型映射
const MEDIA_TYPE_MAP: { [key: number]: { text: string; color: string; maxCount?: number } } = {
  1: { text: '剧照', color: 'blue', maxCount: 30 },
  2: { text: '定妆照', color: 'green', maxCount: 20 },
  3: { text: '片花', color: 'purple', maxCount: 10 },
  4: { text: 'Word', color: 'cyan', maxCount: 5 },
  5: { text: 'Excel', color: 'orange', maxCount: 5 },
  6: { text: 'PDF', color: 'red', maxCount: 5 },
  7: { text: '合照', color: 'magenta', maxCount: 15 },
}

const ProductionMedia: React.FC<IProductionMediaProps> = ({ productionId, loading = false, project }) => {
  const [mediaList, setMediaList] = useState<IProductionMediaItem[]>([])
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [loadingData, setLoadingData] = useState(false)
  const [saving, setSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('1')
  const [form] = Form.useForm()

  const { getProductionMediaList, saveProductionMedia, deleteProductionMedia } = useProjectListStore()

  useEffect(() => {
    if (productionId) {
      loadMediaList()
    }
  }, [productionId])

  // 加载媒体列表
  const loadMediaList = async () => {
    setLoadingData(true)
    try {
      const result = await getProductionMediaList(productionId)

      setMediaList(result || [])
    } catch (error) {
      message.error('加载媒体列表失败')
    } finally {
      setLoadingData(false)
    }
  }

  // 根据媒体类型过滤数据
  const getMediaByType = (mediaType: number) => mediaList.filter(item => item.mediaType === mediaType)

  // 获取媒体类型信息
  const getMediaTypeInfo = (mediaType: number) => MEDIA_TYPE_MAP[mediaType] || { text: '未知', color: 'default' }

  // 获取上传组件类型
  const getUploadType = (mediaType: number): 'image' | 'video' | 'word' | 'excel' | 'pdf' => {
    switch (mediaType) {
      case 3:
        return 'video'
      case 4:
        return 'word'
      case 5:
        return 'excel'
      case 6:
        return 'pdf'
      case 1:
      case 2:
      case 7: // 合照
        return 'image'
      default:
        return 'image'
    }
  }

  // 获取文件接受类型
  const getAcceptType = (mediaType: number): string => {
    switch (mediaType) {
      case 3:
        return '.mp4,.avi,.mov,.wmv,.flv,.mkv'
      case 4:
        return '.doc,.docx'
      case 5:
        return '.xls,.xlsx'
      case 6:
        return '.pdf'
      case 1:
      case 2:
      case 7: // 合照
      default:
        return '.png,.jpg,.jpeg,.gif,.webp'
    }
  }

  // 根据媒体类型渲染对应的卡片列表
  const renderMediaCards = (mediaData: IProductionMediaItem[], mediaType: number) => {
    if (!mediaData.length) {
      return <Empty />
    }

    if (mediaType === 3) {
      return mediaData.map(item => renderVideoCard(item))
    }

    if ([4, 5, 6].includes(mediaType)) {
      return mediaData.map(item => renderDocumentCard(item))
    }

    // 图片类型：剧照(1)、定妆照(2)、合照(7)
    return (
      <Image.PreviewGroup
        items={mediaData.map(media =>
          media?.mediaUrl?.startsWith('http') ? media?.mediaUrl : envUrl + media?.mediaUrl
        )}>
        {mediaData.map(item => renderImageCard(item))}
      </Image.PreviewGroup>
    )
  }

  // 处理新增媒体
  const handleAddMedia = async (values: any) => {
    setSaving(true)
    try {
      const mediaType = parseInt(activeTab, 10)
      const mediaUrls = values.mediaUrl

      // 检查是否有上传的文件
      if (!mediaUrls || (Array.isArray(mediaUrls) && mediaUrls.length === 0)) {
        message.warning('请至少上传一个文件')

        return
      }

      // 将上传的文件拆分为多个媒体实体
      const urls = Array.isArray(mediaUrls) ? mediaUrls : [mediaUrls]
      const baseSort = getMediaByType(mediaType).length + 1

      const newMediaList = urls.map((url: string, index: number) => ({
        productionId,
        mediaType,
        mediaUrl: url,
        description: values.description || '',
        sort: baseSort + index, // 如果是多文件，排序依次递增
      }))

      const success = await saveProductionMedia({
        productionId,
        medias: [...mediaList, ...newMediaList],
      })

      if (success) {
        message.success(`成功添加 ${newMediaList.length} 个${getMediaTypeInfo(mediaType).text}`)
        setIsModalOpen(false)
        form.resetFields()
        await loadMediaList()
      }
    } catch (error) {
      console.error('添加媒体失败:', error)
    } finally {
      setSaving(false)
    }
  }

  // 处理删除媒体
  const handleDeleteMedia = async (media: IProductionMediaItem) => {
    if (!media.id) {
      message.error('无法删除该媒体')

      return
    }

    try {
      const success = await deleteProductionMedia(media.id!)

      if (success) {
        message.success('删除成功')
        await loadMediaList()
      }
    } catch (error) {
      console.error('删除失败:', error)
    }
  }

  // 渲染图片卡片
  const renderImageCard = (mediaItem: IProductionMediaItem) => (
    <div key={mediaItem.id} className="hover-move hover-show-remove">
      <EnvImage
        src={mediaItem?.mediaUrl || ''}
        alt="图片"
        className="radius img-cover"
        width={120}
        height={160}
        preview={{ src: mediaItem?.mediaUrl?.startsWith('http') ? mediaItem?.mediaUrl : envUrl + mediaItem?.mediaUrl }}
        placeholder={true}
        scaleSize={20}
      />
      {!project?.feNoEdit && (
        <Popconfirm
          key="delete"
          title="警告"
          description="确定要删除该图片吗？"
          onConfirm={() => handleDeleteMedia(mediaItem)}
          okText="确定删除"
          cancelText="取消">
          <Button
            type="text"
            icon={<CloseCircleOutlined className="fs-xlg text-secondary" />}
            className="remove"></Button>
        </Popconfirm>
      )}
    </div>
  )

  // 渲染片花卡片
  const renderVideoCard = (mediaItem: IProductionMediaItem) => (
    <div key={mediaItem.id} className={`${styles.videoCard} hover-move hover-show-remove`}>
      {/* 视频 */}
      <VideoView playIconSize={32} videoUrl={mediaItem.mediaUrl || ''} width={'100%'} height={'100%'} />
      {!project?.feNoEdit && (
        <Popconfirm
          key="delete"
          title="警告"
          description="确定要删除该视频吗？"
          onConfirm={() => handleDeleteMedia(mediaItem)}
          okText="确定删除"
          cancelText="取消">
          <Button
            type="text"
            icon={<CloseCircleOutlined className="fs-xlg text-secondary" />}
            className="remove"></Button>
        </Popconfirm>
      )}
    </div>
  )

  // 获取文档类型图标
  const getDocumentIcon = (mediaType: number) => {
    switch (mediaType) {
      case 4: // Word
        return <FileOutlined className={`${styles.documentIcon} ${styles.word}`} />
      case 5: // Excel
        return <FileOutlined className={`${styles.documentIcon} ${styles.excel}`} />
      case 6: // PDF
        return <FilePdfOutlined className={`${styles.documentIcon} ${styles.pdf}`} />
      default:
        return <FileOutlined className={`${styles.documentIcon} ${styles.default}`} />
    }
  }
  const handleDownload = (url: string) => {
    window.open(url.startsWith('http') ? url : envUrl + url, '_blank')
  }
  // 渲染文档卡片
  const renderDocumentCard = (mediaItem: IProductionMediaItem) => (
    <div
      key={mediaItem.id}
      className={`${styles.documentCard} hover-move hover-show-remove`}
      onClick={() => mediaItem.mediaUrl && handleDownload(mediaItem.mediaUrl)}>
      {getDocumentIcon(mediaItem.mediaType)}
      <div className={styles.documentFileName}>
        {mediaItem.description ? mediaItem.description : mediaItem.mediaUrl?.split('/').pop() || '文档'}
      </div>
      <DownloadOutlined className={styles.downloadIcon} />
      {!project?.feNoEdit && (
        <Popconfirm
          key="delete"
          title="警告"
          description="确定要删除该文档吗？"
          onConfirm={e => {
            e?.stopPropagation()
            handleDeleteMedia(mediaItem)
          }}
          okText="确定删除"
          cancelText="取消">
          <Button
            type="text"
            icon={<CloseCircleOutlined className="fs-xlg text-secondary" />}
            className="remove"
            onClick={e => e.stopPropagation()}></Button>
        </Popconfirm>
      )}
    </div>
  )

  // 渲染媒体列表
  const renderMediaList = (mediaType: number) => {
    const LABELS = ['', '剧照', '定妆照', '片花', 'Word', 'Excel', 'PDF', '合照']
    const mediaData = getMediaByType(mediaType)
    const mediaTypeInfo = getMediaTypeInfo(mediaType)

    return {
      label: (
        <Space>
          {LABELS[mediaType]}
          {!!mediaData.length && <Badge count={mediaData.length} color="#ccc"></Badge>}
        </Space>
      ),
      children: (
        <Flex vertical gap={12}>
          <Flex justify="flex-end">
            <Button
              type="primary"
              ghost
              shape="round"
              icon={<PlusOutlined />}
              onClick={() => {
                setActiveTab(mediaType.toString())
                setIsModalOpen(true)
              }}>
              添加{mediaTypeInfo.text}
            </Button>
          </Flex>
          {/* 所有媒体类型都使用卡片布局 */}
          <Flex gap={10} wrap align="center" justify={mediaData.length > 0 ? 'flex-start' : 'center'}>
            {renderMediaCards(mediaData, mediaType)}
          </Flex>
        </Flex>
      ),
    }
  }

  // Tab配置
  const items = [
    renderMediaList(1),
    renderMediaList(2),
    renderMediaList(3),
    renderMediaList(7),
    renderMediaList(4),
    renderMediaList(5),
    renderMediaList(6),
  ]
  const tabItems = [
    {
      key: '1',
      label: items[0].label,
      children: items[0].children,
    },
    {
      key: '2',
      label: items[1].label,
      children: items[1].children,
    },
    {
      key: '3',
      label: items[2].label,
      children: items[2].children,
    },
    {
      key: '7',
      label: items[3].label,
      children: items[3].children,
    },
    {
      key: '4',
      label: items[4].label,
      children: items[4].children,
    },
    {
      key: '5',
      label: items[5].label,
      children: items[5].children,
    },
    {
      key: '6',
      label: items[6].label,
      children: items[6].children,
    },
  ]

  return (
    <>
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        tabPosition="left"
        className={styles.tabsContainer}
        indicator={{ size: 24 }}
      />

      {/* 新增媒体模态框 */}
      <Modal
        title={`添加${getMediaTypeInfo(parseInt(activeTab, 10)).text}`}
        open={isModalOpen}
        onCancel={() => {
          setIsModalOpen(false)
          form.resetFields()
        }}
        footer={null}
        width={600}>
        <Form form={form} layout="vertical" onFinish={handleAddMedia}>
          <Form.Item label="媒体类型" hidden>
            <Input value={getMediaTypeInfo(parseInt(activeTab, 10)).text} readOnly className={styles.readonlyInput} />
          </Form.Item>

          <Form.Item
            name="mediaUrl"
            label={`上传${getMediaTypeInfo(parseInt(activeTab, 10)).text}`}
            rules={[{ required: true, message: `请上传${getMediaTypeInfo(parseInt(activeTab, 10)).text}文件` }]}>
            <UploadComponent
              action={prProductionMediaUploadAPI}
              type={getUploadType(parseInt(activeTab, 10))}
              accept={getAcceptType(parseInt(activeTab, 10))}
              multiple={true}
              maxCount={getMediaTypeInfo(parseInt(activeTab, 10)).maxCount}
            />
          </Form.Item>

          <Form.Item name="description" label="描述" hidden={!['4', '5', '6'].includes(activeTab)}>
            <TextArea rows={3} placeholder="请输入描述" />
          </Form.Item>

          <Form.Item className={styles.formFooter}>
            <Space>
              <Button onClick={() => setIsModalOpen(false)}>取消</Button>
              <Button type="primary" htmlType="submit" loading={saving}>
                立即添加
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
}

export default ProductionMedia
