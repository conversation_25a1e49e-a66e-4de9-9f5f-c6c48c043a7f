import { ACTOR_ROLE_TYPE_CONFIG, RECRUITMENT_PARENT_TYPE_CONFIG, ROLE_TYPE_CONFIG, ROOM_TYPE_CONFIG } from '@/consts'
import { exportProductionRoomPerson } from '@/utils/export'
import { CloudDownloadOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Button, Card, Divider, Empty, Flex, List, message, Space, Tag, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'

// import AutoRoomAllocationSidebar from '../../components/AutoRoomAllocationSidebar'
import useProjectListStore, { IProductionListItem, IPrProductionAllPerson } from '../store'

interface IProductionPersonRoomProps {
  productionId: number
  loading?: boolean
  projectName?: string
  project?: IProductionListItem
}
// 计算入住时间范围显示
const getDateRangeDisplay = (dates: string[]) => {
  if (!dates || dates.length === 0) return ''
  const sortedDates = dates.sort((a, b) => dayjs(a).valueOf() - dayjs(b).valueOf())
  const startDate = dayjs(sortedDates[0]).format('MM-DD')
  const endDate = dayjs(sortedDates[sortedDates.length - 1]).format('MM-DD')
  return startDate === endDate ? startDate : `${startDate} ~ ${endDate}`
}

const ProductionPersonRoom: React.FC<IProductionPersonRoomProps> = ({ productionId, loading = false, project }) => {
  const [allPersons, setAllPersons] = useState<IPrProductionAllPerson[]>([])
  const [loadingData, setLoadingData] = useState(false)
  const [autoAllocationOpen, setAutoAllocationOpen] = useState(false)

  const { getAllPersonRoomList } = useProjectListStore()

  useEffect(() => {
    if (productionId) {
      loadAllPersonRooms()
    }
  }, [productionId])

  // 加载项目所有宿舍人员列表
  const loadAllPersonRooms = async () => {
    setLoadingData(true)
    try {
      const result = await getAllPersonRoomList(productionId)
      setAllPersons(result || [])
    } catch (error) {
      console.error('加载项目宿舍人员失败:', error)
      message.error('加载项目宿舍人员失败')
    } finally {
      setLoadingData(false)
    }
  }

  // 获取角色类型名称
  const getRoleTypeName = (parentType?: number, roleType?: number) => {
    if (!parentType || !roleType) {
      return '-'
    }

    if (parentType === 2) {
      // 演员类型
      const config = ACTOR_ROLE_TYPE_CONFIG[roleType as unknown as keyof typeof ACTOR_ROLE_TYPE_CONFIG]
      return config?.label || '-'
    }
    // 人员类型
    const config = ROLE_TYPE_CONFIG[roleType as unknown as keyof typeof ROLE_TYPE_CONFIG]
    return config?.label || '-'
  }

  // 渲染宿舍信息卡片
  const renderPersonRoomCard = (person: IPrProductionAllPerson) => {
    const menuItems = [
      {
        key: 'view',
        label: '查看详情',
        onClick: () => {
          // TODO: 实现查看详情功能
          console.log('查看详情:', person)
        },
      },
    ]

    return (
      <List.Item key={`${person.personId}-${person.productionId}`}>
        <Card size="small" className="w-full">
          <Flex justify="space-between">
            <Flex vertical gap={8} flex={1}>
              <Space size={0} split={<Divider type="vertical" />}>
                <Typography.Text strong>{person.personName}</Typography.Text>
                <Typography.Text>
                  {person.parentType ? RECRUITMENT_PARENT_TYPE_CONFIG[person.parentType].label : '-'}
                </Typography.Text>
                <Typography.Text>{getRoleTypeName(person.parentType, person.roleType)}</Typography.Text>
              </Space>

              {/* 宿舍信息 */}
              {person.personRoom && (
                <Space direction="vertical" size={4}>
                  {/* 入住时间 */}
                  {person.personRoomDates && person.personRoomDates.length > 0 && (
                    <Dict title="入住时间" value={getDateRangeDisplay(person.personRoomDates)} />
                  )}
                </Space>
              )}

              {/* 无宿舍信息提示 */}
              {!person.personRoom && <Typography.Text type="secondary">暂无宿舍信息</Typography.Text>}
            </Flex>
            <Space size={0} split={<Divider type="vertical" />}>
              <span>
                {person.personRoom?.roomLevel && (
                  <Tag color={person.personRoom.roomLevel === 1 ? 'volcano' : ''}>
                    {person.personRoom.roomLevel === 1 ? '优' : person.personRoom.roomLevel === 2 ? '良' : '一般'}
                  </Tag>
                )}
                {person.personRoom?.roomType && (
                  <Tag className="no-margin">
                    {ROOM_TYPE_CONFIG[person.personRoom.roomType as keyof typeof ROOM_TYPE_CONFIG]?.label || ''}
                  </Tag>
                )}
              </span>
              {person.personRoom?.communityName && <Typography.Text>{person.personRoom.communityName}</Typography.Text>}
              {/* 房间信息 */}
              <Typography.Text strong>
                {person.personRoom?.buildingNumber}#{person.personRoom?.roomNumber}号
                {person.personRoom?.roomPosition && `${person.personRoom.roomPosition}`}
              </Typography.Text>
            </Space>
          </Flex>
        </Card>
      </List.Item>
    )
  }

  // 处理自动分配房间
  const handleAutoAllocation = () => {
    setAutoAllocationOpen(true)
  }

  // 处理自动分配房间成功
  const handleAutoAllocationSuccess = () => {
    setAutoAllocationOpen(false)
    loadAllPersonRooms() // 重新加载数据
  }

  // 处理导出保险记录
  const handleExport = async () => {
    if (!project?.id) {
      return
    }

    try {
      await exportProductionRoomPerson(project.id, `${project.productionName}-入住信息.xlsx`)
    } catch (error) {
      console.error('导出保险记录失败:', error)
    }
  }

  // 过滤有宿舍信息的人员
  const personsWithRoom = allPersons.filter(person => person.personRoom)

  return (
    <Flex vertical>
      <ListHeader title="入住安排" total={personsWithRoom.length}>
        <Space size={0} split={<Divider type="vertical" />}>
          {/* productionType 为 1 时，不显示导出保险名单按钮 */}
          <Button
            color="primary"
            variant="filled"
            shape="round"
            icon={<CloudDownloadOutlined />}
            onClick={handleExport}>
            导出入住信息
          </Button>
        </Space>
      </ListHeader>

      {personsWithRoom.length > 0 ? (
        <List
          loading={loading || loadingData}
          dataSource={personsWithRoom}
          split={false}
          className="list-sm"
          renderItem={renderPersonRoomCard}
        />
      ) : (
        <Empty />
      )}

      {/* 自动分配房间侧边栏 */}
      {/* <AutoRoomAllocationSidebar
        open={autoAllocationOpen}
        productionId={productionId}
        project={project}
        onClose={() => setAutoAllocationOpen(false)}
        onSuccess={handleAutoAllocationSuccess}
      /> */}
    </Flex>
  )
}

export default ProductionPersonRoom
