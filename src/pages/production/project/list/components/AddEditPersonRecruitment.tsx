import { Form, Input, InputNumber, Modal, Select, Switch } from 'antd'
import React, { useEffect, useMemo } from 'react'
import {
  ACTOR_ROLE_TYPE_OPTIONS,
  GENDERS,
  PRICE_CURRENCY_OPTIONS,
  RECRUITMENT_PARENT_TYPE_OPTIONS,
  ROLE_TYPE_OPTIONS_SELECT,
  SALARY_TYPE_OPTIONS,
} from '../../../../../consts'
import { IProductionPersonRecruitmentItem } from '../store'

interface IAddEditPersonRecruitmentProps {
  open: boolean
  recruitment?: IProductionPersonRecruitmentItem | null
  onCancel: () => void
  onOk: (values: any) => void
  loading?: boolean
  priceCurrency: any
}

const AddEditPersonRecruitment: React.FC<IAddEditPersonRecruitmentProps> = ({
  open,
  recruitment,
  onCancel,
  onOk,
  loading = false,
  priceCurrency,
}) => {
  const [form] = Form.useForm()
  const isEdit = !!recruitment?.id

  // 监听表单中parentType字段的变化
  const formParentType = Form.useWatch('parentType', form)
  const formSalaryMin = Form.useWatch('salaryMin', form)

  useEffect(() => {
    if (open) {
      if (isEdit && recruitment) {
        form.setFieldsValue({
          parentType: recruitment.parentType,
          roleType: recruitment.roleType,
          personCount: recruitment.personCount || 1,
          gender: recruitment.gender,
          description: recruitment.description,
          salaryMin: recruitment.salaryMin,
          salaryMax: recruitment.salaryMax,
          salaryType: recruitment.salaryType,
          currency: priceCurrency || 1,
          isAccommodation: recruitment.isAccommodation || false,
          isMeal: recruitment.isMeal || false,
          specialRequirements: recruitment.specialRequirements,
        })
      } else {
        form.resetFields()
        form.setFieldsValue({
          personCount: 1,
          currency: priceCurrency || 1,
          isAccommodation: false,
          isMeal: false,
          gender: 1,
        })
      }
    }
  }, [open, recruitment, form, isEdit])

  // 使用useMemo缓存角色类型选项，依赖于formParentType
  const roleTypeOptions: any[] = useMemo(() => {
    if (formParentType === 1) {
      // 人员 - 使用 RoleType 枚举，排除演员类型
      return ROLE_TYPE_OPTIONS_SELECT
    } else if (formParentType === 2) {
      // 演员 - 使用 ActorRoleType 枚举
      return ACTOR_ROLE_TYPE_OPTIONS
    }

    return []
  }, [formParentType])

  const handleOk = async () => {
    try {
      const values = await form.validateFields()

      onOk(values)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={isEdit ? '编辑招募人员' : '添加招募人员'}
      open={open}
      onCancel={handleCancel}
      onOk={handleOk}
      width={600}
      okText="立即保存"
      cancelText="取消"
      confirmLoading={loading}
      centered>
      <Form
        form={form}
        layout="horizontal"
        colon={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        style={{ marginTop: 20 }}>
        <Form.Item name="parentType" label="招募类型" rules={[{ required: true, message: '请选择招募类型' }]}>
          <Select
            placeholder="请选择招募类型"
            options={RECRUITMENT_PARENT_TYPE_OPTIONS}
            onChange={() => {
              form.setFieldValue('roleType', null)
            }}
          />
        </Form.Item>

        <Form.Item
          name="roleType"
          label="角色类型"
          rules={[{ required: true, message: '请选择角色类型' }]}
          dependencies={['parentType']}>
          <Select placeholder="请选择角色类型" options={roleTypeOptions} disabled={!formParentType} />
        </Form.Item>

        <Form.Item name="personCount" label="人数" rules={[{ required: true, message: '请输入人数' }]}>
          <InputNumber min={1} placeholder="请输入人数" style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item name="gender" label="性别要求" initialValue={1}>
          <Select placeholder="请选择性别要求" options={GENDERS} allowClear />
        </Form.Item>
        <Form.Item name="specialRequirements" label="特殊要求">
          <Input.TextArea rows={3} placeholder="请输入特殊要求" />
        </Form.Item>
        <Form.Item name="description" label="角色介绍">
          <Input.TextArea rows={3} placeholder="请输入角色介绍" />
        </Form.Item>

        <Form.Item name="salaryType" label="薪资类型">
          <Select placeholder="请选择薪资类型" options={SALARY_TYPE_OPTIONS} />
        </Form.Item>
        <Form.Item label="薪资范围">
          <Input.Group compact>
            <Form.Item name="salaryMin" noStyle>
              <InputNumber placeholder="最低薪资" style={{ width: '40%' }} min={0} />
            </Form.Item>
            <Input style={{ width: '20%', textAlign: 'center', pointerEvents: 'none' }} placeholder="至" disabled />
            <Form.Item name="salaryMax" noStyle>
              <InputNumber placeholder="最高薪资" style={{ width: '40%' }} min={formSalaryMin || 0} />
            </Form.Item>
          </Input.Group>
        </Form.Item>

        <Form.Item name="currency" label="货币类型" hidden>
          <Select placeholder="请选择货币类型" options={PRICE_CURRENCY_OPTIONS} />
        </Form.Item>

        <Form.Item name="isAccommodation" label="提供住宿" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item name="isMeal" label="提供餐食" valuePropName="checked">
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default AddEditPersonRecruitment
