import QRCodeModal from '@/components/QRCodeModal'
import { ACTOR_ROLE_TYPE_CONFIG, RECRUITMENT_PARENT_TYPE_CONFIG, ROLE_TYPE_CONFIG, ROOM_TYPE_CONFIG } from '@/consts'
import { MoreOutlined, PlusOutlined, QrcodeOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Button, Card, Divider, Dropdown, Empty, Flex, List, message, Popconfirm, Space, Tag, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import PersonRoomModal from '../../../../room/components/PersonRoomModal'
import useProjectListStore, { IProductionListItem, IPrProductionAllPerson } from '../store'
import AddAllPersonModal from './AddAllPersonModal'
import EditAllPersonModal from './EditAllPersonModal'

interface IProductionAllPersonProps {
  productionId: number
  loading?: boolean
  projectName?: string
  project?: IProductionListItem
}

const ProductionAllPerson: React.FC<IProductionAllPersonProps> = ({ productionId, loading = false, project }) => {
  const [allPersons, setAllPersons] = useState<IPrProductionAllPerson[]>([])
  const [isAddModalOpen, setIsAddModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingPerson, setEditingPerson] = useState<IPrProductionAllPerson | null>(null)
  const [loadingData, setLoadingData] = useState(false)
  const [qrCodeModalOpen, setQrCodeModalOpen] = useState(false)
  const [personRoomModalOpen, setPersonRoomModalOpen] = useState(false)
  const [selectedPersonForRoom, setSelectedPersonForRoom] = useState<IPrProductionAllPerson | null>(null)

  const { getAllPersonList, deleteProductionAllPerson } = useProjectListStore()

  useEffect(() => {
    if (productionId) {
      loadAllPersons()
    }
  }, [productionId])

  // 加载项目所有人员列表
  const loadAllPersons = async () => {
    setLoadingData(true)
    try {
      const result = await getAllPersonList(productionId)

      setAllPersons(result || [])
    } catch (error) {
      console.error('加载项目人员失败:', error)
      message.error('加载项目人员失败')
    } finally {
      setLoadingData(false)
    }
  }

  // 处理添加人员成功
  const handleAddSuccess = async () => {
    setIsAddModalOpen(false)
    await loadAllPersons()
  }

  // 处理编辑人员
  const handleEditPerson = (person: IPrProductionAllPerson) => {
    setEditingPerson(person)
    setIsEditModalOpen(true)
  }

  // 处理编辑成功
  const handleEditSuccess = async () => {
    setIsEditModalOpen(false)
    setEditingPerson(null)
    await loadAllPersons()
  }

  // 处理删除人员
  const handleDeletePerson = async (person: IPrProductionAllPerson) => {
    if (!person.id) {
      message.error('删除失败：缺少人员ID')

      return
    }

    try {
      const success = await deleteProductionAllPerson(person.id)

      if (success) {
        message.success('删除成功')
        await loadAllPersons()
      } else {
        message.error('删除失败')
      }
    } catch (error) {
      console.error('删除人员失败:', error)
      message.error('删除失败')
    }
  }

  // 处理添加住宿信息
  const handleAddPersonRoom = (person: IPrProductionAllPerson) => {
    setSelectedPersonForRoom(person)
    setPersonRoomModalOpen(true)
  }

  // 处理住宿信息成功
  const handlePersonRoomSuccess = async () => {
    setPersonRoomModalOpen(false)
    setSelectedPersonForRoom(null)
    await loadAllPersons() // 重新加载数据
  }

  // 获取人员类型标签
  const getPersonTypeTag = (parentType?: number) => {
    const config = RECRUITMENT_PARENT_TYPE_CONFIG[parentType as keyof typeof RECRUITMENT_PARENT_TYPE_CONFIG]

    if (config) {
      return <Tag className="no-margin">{config.label}</Tag>
    }

    return null
  }

  // 获取角色类型名称
  const getRoleTypeName = (parentType?: number, roleType?: number) => {
    if (!parentType || !roleType) {
      return '-'
    }

    if (parentType === 2) {
      // 演员类型
      const config = ACTOR_ROLE_TYPE_CONFIG[roleType as unknown as keyof typeof ACTOR_ROLE_TYPE_CONFIG]

      return config?.label || '-'
    }
    // 人员类型
    const config = ROLE_TYPE_CONFIG[roleType as unknown as keyof typeof ROLE_TYPE_CONFIG]

    return config?.label || '-'
  }

  // 渲染人员卡片
  const renderPersonCard = (person: IPrProductionAllPerson) => {
    const menuItems = [
      {
        key: 'edit',
        label: '编辑',
        onClick: () => handleEditPerson(person),
      },
      {
        key: 'lodging',
        label: '安排住宿',
        onClick: () => handleAddPersonRoom(person),
      },
      {
        key: 'delete',
        label: (
          <Popconfirm
            title="确认删除"
            description="确定要删除这个人员吗？"
            onConfirm={() => handleDeletePerson(person)}
            okText="确定"
            cancelText="取消">
            <span style={{ color: '#ff4d4f' }}>删除</span>
          </Popconfirm>
        ),
      },
    ]

    return (
      <List.Item key={person.id}>
        <Card size="small" className="w-full">
          <Flex justify="space-between" align="flex-start">
            <Flex vertical gap={8} className="flex-1">
              <Space size={0} wrap split={<Divider type="vertical" />}>
                <Typography.Text strong>{person.personName}</Typography.Text>
                <Typography.Text>{RECRUITMENT_PARENT_TYPE_CONFIG[person.parentType].label}</Typography.Text>
                <Typography.Text>{getRoleTypeName(person.parentType, person.roleType)}</Typography.Text>
                <Space>
                  {person.isAccommodation && <Tag className="text-primary">包住宿</Tag>}
                  {person.isMeal && <Tag className="text-primary">包餐食</Tag>}
                </Space>
              </Space>

              {!!person.personRoom && (
                <Space direction="vertical" size={4}>
                  <Space size={0} split={<Divider type="vertical" />} wrap>
                    <Space>
                      {!!person.personRoom.community && <span>{person.personRoom.community}</span>}
                      <Space size={0} split="#">
                        <span>{person.personRoom.buildingNumber}</span>
                        <span>{person.personRoom.roomNumber}</span>
                      </Space>
                    </Space>
                    {person.personRoom.roomType && (
                      <span>{ROOM_TYPE_CONFIG[person.personRoom.roomType as keyof typeof ROOM_TYPE_CONFIG]?.label || ''}</span>
                    )}
                    {person.personRoomDates && person.personRoomDates.length > 0 && (
                      <Dict
                        title="入住时间"
                        value={person.personRoomDates.map(date => dayjs(date).format('MM-DD')).join('、')}
                      />
                    )}
                  </Space>
                </Space>
              )}
            </Flex>

            {!project?.feNoEdit && (
              <Dropdown menu={{ items: menuItems }} trigger={['click']} placement="bottomRight">
                <Button type="text" icon={<MoreOutlined />} />
              </Dropdown>
            )}
          </Flex>
        </Card>
      </List.Item>
    )
  }

  return (
    <Flex vertical>
      <ListHeader title="项目组" total={allPersons.length}>
        <Space>
          <Button type="link" icon={<QrcodeOutlined />} onClick={() => setQrCodeModalOpen(true)} title="招募二维码">
            二维码
          </Button>
          <Button type="primary" ghost icon={<PlusOutlined />} onClick={() => setIsAddModalOpen(true)}>
            添加人员
          </Button>
        </Space>
      </ListHeader>

      {allPersons.length > 0 ? (
        <List
          loading={loading || loadingData}
          dataSource={allPersons}
          split={false}
          className="list-sm"
          renderItem={renderPersonCard}
        />
      ) : (
        <Empty description="暂无项目组人员" />
      )}

      {/* 添加人员弹窗 */}
      <AddAllPersonModal
        open={isAddModalOpen}
        productionId={productionId}
        onCancel={() => setIsAddModalOpen(false)}
        onSuccess={handleAddSuccess}
      />

      {/* 编辑人员弹窗 */}
      <EditAllPersonModal
        open={isEditModalOpen}
        productionId={productionId}
        person={editingPerson}
        onCancel={() => {
          setIsEditModalOpen(false)
          setEditingPerson(null)
        }}
        onSuccess={handleEditSuccess}
      />

      {/* 二维码弹窗 */}
      {qrCodeModalOpen && (
        <QRCodeModal
          open={qrCodeModalOpen}
          onCancel={() => setQrCodeModalOpen(false)}
          url={`https://mpr.cdreader.com/#/login?joinProjectId=${productionId}`}
          title="二维码"
          description="扫描二维码或分享链接进行加入"
          fileName={`项目${productionId}_二维码`}
        />
      )}

      {/* 住宿信息弹窗 */}
      {personRoomModalOpen && selectedPersonForRoom && (
        <PersonRoomModal
          open={personRoomModalOpen}
          productionId={productionId}
          personId={selectedPersonForRoom.personId}
          onCancel={() => {
            setPersonRoomModalOpen(false)
            setSelectedPersonForRoom(null)
          }}
          onSuccess={handlePersonRoomSuccess}
        />
      )}
    </Flex>
  )
}

export default ProductionAllPerson
