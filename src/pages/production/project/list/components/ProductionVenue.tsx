import EnvImage from '@/components/EnvImage'
import { IS_INTERNAL_CONFIG } from '@/consts'
import VenueDetail from '@/pages/production/venue/list/components/Detail'
import useVenueStore from '@/pages/production/venue/list/store'
import { EnvironmentOutlined, MoreOutlined, PayCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import {
  Avatar,
  Button,
  Card,
  Divider,
  Dropdown,
  Empty,
  Flex,
  Form,
  InputNumber,
  List,
  Modal,
  Popconfirm,
  Space,
  Typography,
  message,
} from 'antd'
import React, { useEffect, useState } from 'react'
import useProjectListStore, { IProductionListItem, IProductionVenueItem } from '../store'
import VenueModal from './VenueModal'

interface IProductionVenueProps {
  productionId: number
  loading?: boolean
  project?: IProductionListItem
}

// 场地类型选项
const VENUE_TYPE_OPTIONS = [
  { value: '古装', label: '古装', color: 'red' },
  { value: '现代', label: '现代', color: 'blue' },
  { value: '武侠', label: '武侠', color: 'orange' },
  { value: '科幻', label: '科幻', color: 'purple' },
  { value: '民国', label: '民国', color: 'green' },
  { value: '乡村', label: '乡村', color: 'cyan' },
]

const ProductionVenue: React.FC<IProductionVenueProps> = ({ productionId, loading = false, project }) => {
  const [venues, setVenues] = useState<IProductionVenueItem[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [venueOptions, setVenueOptions] = useState<any[]>([])

  // 统一的场地模态框状态
  const [isVenueModalOpen, setIsVenueModalOpen] = useState(false)
  const [venueModalMode, setVenueModalMode] = useState<'add' | 'edit'>('add')
  const [editingVenue, setEditingVenue] = useState<IProductionVenueItem | null>(null)
  const [venueForm] = Form.useForm()

  // 场地预算相关状态
  const [isBudgetModalOpen, setIsBudgetModalOpen] = useState(false)
  const [budgetForm] = Form.useForm()
  const [venueBudget, setVenueBudget] = useState<number>(0)

  // 场地详情相关状态
  const [venueDetailOpen, setVenueDetailOpen] = useState(false)
  const [selectedVenueId, setSelectedVenueId] = useState<number | undefined>()

  const { fetchVenueList } = useVenueStore()
  const { getProductionVenueList, saveProductionVenue, deleteProductionVenue, saveProduction } = useProjectListStore()

  // 优化：只在组件挂载时加载一次场地选项
  useEffect(() => {
    loadAllVenues()
  }, [])

  useEffect(() => {
    if (productionId) {
      loadVenues()
    }
  }, [productionId])

  // 初始化场地预算值
  useEffect(() => {
    setVenueBudget(project?.venueBudget || 0)
  }, [project?.venueBudget])

  // 加载所有场地选项
  const loadAllVenues = async () => {
    try {
      const result = await fetchVenueList({
        pageIndex: 1,
        pageSize: 1000,
      })

      if (result?.list) {
        setVenueOptions(result.list)
      } else {
        setVenueOptions([])
      }
    } catch (error) {
      console.error('加载场地选项失败:', error)
      setVenueOptions([])
    }
  }

  // 加载场地列表
  const loadVenues = async () => {
    setIsLoading(true)
    try {
      const result = await getProductionVenueList(productionId)

      setVenues(result || [])
    } catch (error) {
      console.error('加载场地列表失败:', error)
      message.error('加载场地列表失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 获取场地信息（用于显示）
  const getVenueInfo = async (venueId: number) => {
    if (venueOptions.some(venue => venue.id === venueId)) {
      return venueOptions.find(venue => venue.id === venueId)
    }

    try {
      const result = await fetchVenueList({
        pageIndex: 1,
        pageSize: 1000,
      })

      if (result?.list && result.list.length > 0) {
        const venue = result.list.find(item => item.id === venueId)

        return venue || null
      }
    } catch (error) {
      console.error('获取场地信息失败:', error)
    }

    return null
  }

  // 获取适合类型标签
  const getSuitableTypeTags = (suitableType?: string) => {
    if (!suitableType) {
      return []
    }

    return suitableType.split(',').map(type => {
      const option = VENUE_TYPE_OPTIONS.find(opt => opt.value === type.trim())

      return {
        text: type.trim(),
        color: option?.color || 'default',
      }
    })
  }

  // 验证场地是否重复
  const validateVenueDuplication = (venueId: number, excludeVenueId?: number) => {
    const existingVenueIds = venues
      .filter(venue => (excludeVenueId ? venue.id !== excludeVenueId : true))
      .map(venue => venue.venueId)

    if (existingVenueIds.includes(venueId)) {
      message.warning('该场地已存在，请选择其他场地')
      return false
    }

    return true
  }

  // 保存新增场地
  const handleSaveAdd = async (values: any) => {
    setIsLoading(true)
    try {
      if (!values.venueId) {
        message.warning('请选择场地')
        return
      }

      // 验证场地是否重复
      if (!validateVenueDuplication(values.venueId)) {
        return
      }

      const newVenue: IProductionVenueItem = {
        productionId,
        venueId: values.venueId,
        quotedPrice: values.quotedPrice,
        totalPrice: values.totalPrice,
        dayCount: values.dayCount,
        description: values.description, // 备注
        hasInvoice: values.hasInvoice,
        sort: venues.length + 1,
        venueName: values.venueName,
        address: values.address,
        suitableType: values.suitableType,
        cost: values.cost,
        isInternal: values.isInternal,
        photos: values.photos,
        videos: values.videos,
        suitableTypeList: values.suitableType ? values.suitableType.split(',') : [],
        venueInfo: (values.venueInfo || []).map(item => {
          return {
            ...item,
            productionId: productionId,
            venueId: values.venueId,
          }
        }), // 场地场景表
      }

      const success = await saveProductionVenue({
        productionId,
        venues: [newVenue],
      })

      if (success) {
        message.success('添加成功')
        setIsVenueModalOpen(false)
        venueForm.resetFields()
        await loadVenues()
      }
    } catch (error) {
      console.error('添加场地失败:', error)
      message.error('添加失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 处理删除场地
  const handleDeleteVenue = async (venue: IProductionVenueItem) => {
    if (!venue.id) {
      message.error('场地信息不完整')

      return
    }

    try {
      const success = await deleteProductionVenue(venue.id)

      if (success) {
        message.success('删除成功')
        await loadVenues()
      }
    } catch (error) {
      console.error('删除场地失败:', error)
    }
  }

  // 打开场地预算编辑
  const handleOpenBudgetModal = () => {
    budgetForm.setFieldsValue({ venueBudget })
    setIsBudgetModalOpen(true)
  }

  // 保存场地预算
  const handleSaveBudget = async (values: any) => {
    if (!project) {
      message.error('缺少参数')

      return
    }

    setIsLoading(true)
    try {
      const params = {
        ...project,
        venueBudget: values.venueBudget,
      }

      delete params.createTime
      delete params.updateTime
      delete params.isDelete

      const success = await saveProduction(params)

      if (success) {
        message.success('保存成功')
        setIsBudgetModalOpen(false)
        // 更新本地状态
        setVenueBudget(values.venueBudget)
      }
    } catch (error) {
      console.error('保存场地预算失败:', error)
      message.error('保存失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 处理添加场地
  const handleAddVenue = () => {
    setVenueModalMode('add')
    setEditingVenue(null)
    setIsVenueModalOpen(true)
    venueForm.resetFields()
  }

  // 处理场地名称点击
  const handleVenueClick = (venue: IProductionVenueItem) => {
    if (venue.venueId) {
      setSelectedVenueId(venue.venueId)
      setVenueDetailOpen(true)
    }
  }

  // 关闭场地详情
  const handleVenueDetailClose = () => {
    setVenueDetailOpen(false)
    setSelectedVenueId(undefined)
  }

  // 处理编辑场地
  const handleEditVenue = (venue: IProductionVenueItem) => {
    setEditingVenue(venue)
    setVenueModalMode('edit')
    setIsVenueModalOpen(true)
    venueForm.setFieldsValue({
      venueId: venue.venueId,
      quotedPrice: venue.quotedPrice,
      totalPrice: venue.totalPrice,
      dayCount: venue.dayCount,
      description: venue.description, // 备注
      hasInvoice: venue.hasInvoice,
      venueName: venue.venueName,
      address: venue.address,
      suitableType: venue.suitableType,
      cost: venue.cost,
      isInternal: venue.isInternal,
      photos: venue.photos,
      videos: venue.videos,
      venueInfo: venue.venueInfo,
    })
  }

  // 保存编辑
  const handleSaveEdit = async (values: any) => {
    if (!editingVenue) {
      return
    }

    setIsLoading(true)
    try {
      const updatedVenue: IProductionVenueItem = {
        ...editingVenue,
        venueId: values.venueId,
        quotedPrice: values.quotedPrice,
        totalPrice: values.totalPrice,
        dayCount: values.dayCount,
        description: values.description, // 备注
        hasInvoice: values.hasInvoice,
        venueName: values.venueName,
        address: values.address,
        suitableType: values.suitableType,
        cost: values.cost,
        isInternal: values.isInternal,
        photos: values.photos,
        videos: values.videos,
        suitableTypeList: values.suitableType ? values.suitableType.split(',') : [],
        venueInfo: (values.venueInfo || []).map(item => {
          return {
            ...item,
            productionId: productionId,
            venueId: editingVenue.venueId,
          }
        }), // 场地场景表
      }

      const updatedVenues = [updatedVenue]

      const success = await saveProductionVenue({
        productionId,
        venues: updatedVenues,
      })

      if (success) {
        message.success('场地信息更新成功')
        setIsVenueModalOpen(false)
        setEditingVenue(null)
        venueForm.resetFields()
        await loadVenues()
      }
    } catch (error) {
      console.error('更新场地信息失败:', error)
      message.error('更新失败，请重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 渲染场地卡片
  const renderVenueCard = (venue: IProductionVenueItem) => {
    const selectedVenue = venueOptions.find(option => option.id === venue.venueId)
    const venueName = selectedVenue?.venueName || venue.venueName || `场地ID: ${venue.venueId}`
    const suitableTypeTags = getSuitableTypeTags(venue.suitableType)
    const firstPhoto = venue.photos ? venue.photos?.split(',')[0] : ''

    const items = []
    items.push({
      key: 'edit',
      label: '编辑',
      onClick: () => handleEditVenue(venue),
    })
    // 只有在不是只读模式时才显示编辑/删除按钮
    if (!project?.feNoEdit) {
      items.push({
        key: 'delete',
        label: (
          <Popconfirm
            title="警告"
            description="确定要删除该场地吗？"
            onConfirm={() => handleDeleteVenue(venue)}
            okText="确定删除"
            cancelText="取消">
            <Typography.Text type="danger" style={{ width: '100%', display: 'inline-block' }}>
              删除
            </Typography.Text>
          </Popconfirm>
        ),
      })
    }

    return (
      <List.Item>
        <Card size="small" className="full-h hover-move">
          <Flex justify="space-between" gap={12}>
            <Flex flex={1} gap={12} onClick={() => handleVenueClick(venue)}>
              {firstPhoto ? (
                <EnvImage
                  src={firstPhoto}
                  width={50}
                  height={75}
                  preview={false}
                  className="radius img-cover"
                  fallback="data:image/gif;base64,R0lGODlhAQABAIAAAMLCwgAAACH5BAAAAAAALAAAAAABAAEAAAICRAEAOw=="
                />
              ) : (
                <Avatar icon={<EnvironmentOutlined />} size={50} />
              )}
              <Flex vertical gap={6}>
                <Space size={0} split={<Divider type="vertical" />}>
                  <Typography.Text strong>{venueName}</Typography.Text>

                  {typeof venue.isInternal === 'number'
                    ? `${IS_INTERNAL_CONFIG[venue.isInternal]?.label}场地` || '未知'
                    : null}
                  {suitableTypeTags?.length ? (
                    <Dict title="适合" value={suitableTypeTags.map(tag => tag.text).join('、')} />
                  ) : null}
                  {venue.dayCount && (venue.quotedPrice || venue.totalPrice) ? (
                    <Typography.Text>{`${venue.dayCount}天`}</Typography.Text>
                  ) : null}
                  {!!venue.quotedPrice && (
                    <Dict
                      title="单价"
                      value={
                        <Typography.Text type="danger">{`${
                          project?.currencySymbol || '¥'
                        }${venue.quotedPrice.toLocaleString()}`}</Typography.Text>
                      }
                    />
                  )}
                  {!!venue.totalPrice && (
                    <Dict
                      title="总价"
                      value={
                        <Typography.Text type="danger">{`${
                          project?.currencySymbol || '¥'
                        }${venue.totalPrice.toLocaleString()}`}</Typography.Text>
                      }
                    />
                  )}
                  <Typography.Text>{venue.hasInvoice ? '有发票' : '无发票'}</Typography.Text>
                </Space>

                {venue.address && <Typography.Text>{venue.address}</Typography.Text>}
                {venue.description && <Dict title="备注" value={venue.description} />}

                {/* 场景信息展示 */}
                {venue.venueInfo && venue.venueInfo.length > 0 && (
                  <Dict
                    title="场景信息"
                    value={
                      <div style={{ lineHeight: '1.4' }}>
                        {venue.venueInfo.map((info, index) => {
                          const parts = []

                          // 选择类型
                          if (info.selectionType !== undefined) {
                            parts.push(
                              <span
                                key="type"
                                style={{
                                  color: '#666',
                                  fontWeight: info.selectionType === 1 ? 'bold' : 'normal',
                                }}>
                                {info.selectionType === 1 ? '主选' : '备选'}
                              </span>
                            )
                          }

                          // 子场地
                          if (info.subVenueName) {
                            parts.push(<span key="subVenue">{info.subVenueName}</span>)
                          }

                          // 主场景/子场景
                          if (info.mainSceneName || info.subSceneName) {
                            const sceneText = [info.mainSceneName, info.subSceneName].filter(Boolean).join('/')
                            parts.push(
                              <span key="scene" style={{ color: '#666' }}>
                                ({sceneText})
                              </span>
                            )
                          }

                          return (
                            <div
                              key={index}
                              style={{ marginBottom: index < (venue.venueInfo?.length || 0) - 1 ? 2 : 0 }}>
                              {parts.map((part, partIndex) => (
                                <span key={partIndex}>
                                  {part}
                                  {partIndex < parts.length - 1 && ' '}
                                </span>
                              ))}
                            </div>
                          )
                        })}
                      </div>
                    }
                  />
                )}
              </Flex>
            </Flex>
            {items.length > 0 && (
              <Dropdown menu={{ items }} trigger={['click']} placement="bottomRight">
                <Button type="text" icon={<MoreOutlined />} />
              </Dropdown>
            )}
          </Flex>
        </Card>
      </List.Item>
    )
  }

  return (
    <>
      <ListHeader title="拍摄场地" total={venues.length}>
        <Space>
          <Button
            color="primary"
            variant={venueBudget ? 'link' : 'filled'}
            icon={!venueBudget ? <PayCircleOutlined /> : undefined}
            shape="round"
            className="text-primary"
            onClick={handleOpenBudgetModal}>
            {venueBudget ? '场地预算：' : '添加预算'}
            {venueBudget ? `${project?.currencySymbol || '¥'}${venueBudget.toLocaleString()}` : ''}
          </Button>
          <Button type="primary" ghost shape="round" icon={<PlusOutlined />} onClick={handleAddVenue}>
            添加场地
          </Button>
        </Space>
      </ListHeader>
      {venues.length > 0 ? (
        <List
          loading={loading || isLoading}
          dataSource={venues}
          renderItem={renderVenueCard}
          split={false}
          className="list-sm"
        />
      ) : (
        <Empty />
      )}

      {/* 统一的场地模态框 */}
      {isVenueModalOpen ? (
        <VenueModal
          form={venueForm}
          mode={venueModalMode}
          open={isVenueModalOpen}
          loading={isLoading}
          editingVenue={editingVenue}
          project={project}
          onOk={venueModalMode === 'add' ? handleSaveAdd : handleSaveEdit}
          onCancel={() => {
            setIsVenueModalOpen(false)
            setEditingVenue(null)
            venueForm.resetFields()
          }}
        />
      ) : null}

      {/* 场地预算编辑Modal */}
      {isBudgetModalOpen ? (
        <Modal
          title="添加场地预算"
          open={isBudgetModalOpen}
          onCancel={() => setIsBudgetModalOpen(false)}
          footer={null}
          width={500}
          destroyOnHidden>
          <Form form={budgetForm} layout="vertical" onFinish={handleSaveBudget} style={{ marginTop: 20 }}>
            <Form.Item name="venueBudget" label="场地预算" rules={[{ required: true, message: '请输入场地预算' }]}>
              <InputNumber
                className="full-h"
                placeholder="请输入场地预算"
                min={0}
                precision={0}
                prefix={project?.currencySymbol || '¥'}
              />
            </Form.Item>
            <Form.Item className="no-margin">
              <Flex justify="flex-end" gap={12}>
                <Button onClick={() => setIsBudgetModalOpen(false)}>取消</Button>
                <Button type="primary" htmlType="submit" loading={isLoading}>
                  立即保存
                </Button>
              </Flex>
            </Form.Item>
          </Form>
        </Modal>
      ) : null}

      {/* 场地详情抽屉 */}
      {venueDetailOpen ? (
        <VenueDetail open={venueDetailOpen} venueId={selectedVenueId} isShow={true} onClose={handleVenueDetailClose} />
      ) : null}
    </>
  )
}

export default ProductionVenue
