import { CHINA_CITY_OPTIONS } from '@/consts/city'
import { DATE_FORMAT_DAY } from '@/consts/date'
import { PRICE_CURRENCY_OPTIONS, PRODUCTION_TYPE_OPTIONS } from '@/consts/index'
import { PROJECT_STATUS } from '@/consts/project'
import filterMatch from '@/utils/filterMatch'
import { AutoComplete, DatePicker, Form, Input, InputNumber, Modal, Select, Space } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import useCompanyStore from '../../../../system/company/store'
import useProjectListStore, { IProductionListItem } from '../store'

const { TextArea } = Input

interface IProjectFormProps {
  open: boolean
  project?: IProductionListItem
  onCancel: () => void
  onSubmit: (values: IProductionListItem) => void
}

const ProjectForm: React.FC<IProjectFormProps> = ({ open, project, onCancel, onSubmit }) => {
  const [form] = Form.useForm()
  const isEdit = !!project?.id
  const [scriptBookOptions, setScriptBookOptions] = useState<any[]>([])
  const { getAllScriptBookNames } = useProjectListStore()
  const { getAllCompanyNames, allCompanyOptions } = useCompanyStore()

  const formProductionType = Form.useWatch('productionType', form)

  const loadScriptBooks = async () => {
    try {
      const result = await getAllScriptBookNames()

      setScriptBookOptions((result || []).map(item => ({ ...item, label: `${item.bookName}(${item.bookCode})` })))
    } catch (error) {
      console.error('加载剧本选项失败:', error)
    }
  }

  const handleBookIdChange = (scriptBookId: number) => {
    if (scriptBookId) {
      // 找到选中的剧本
      const selectedBook = scriptBookOptions.find(book => book.id === scriptBookId)

      if (selectedBook) {
        // 自动设置项目名称和代号
        form.setFieldValue('productionName', selectedBook.bookName)
        form.setFieldValue('productionCode', selectedBook.bookCode)
        form.setFieldValue('totalEpisodes', selectedBook.totalEpisodes)
      }
    } else {
      // 如果清空选择，也清空自动填充的字段
      form.setFieldValue('productionName', '')
      form.setFieldValue('productionCode', '')
    }
  }

  // 加载剧本选项
  useEffect(() => {
    if (open) {
      loadScriptBooks()
      getAllCompanyNames()
    }
  }, [open])

  useEffect(() => {
    if (open) {
      if (project) {
        const formData = {
          ...project,
          scriptBookId: project?.scriptBookId || null,
          startDate: project.startDate ? dayjs(project.startDate) : null,
          endDate: project.endDate ? dayjs(project.endDate) : null,
          companyId: project.companyId ? project.companyId : null
        }

        form.setFieldsValue(formData)
      } else {
        form.resetFields()
      }
    }
  }, [open, project, form])

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()

      const submitData: IProductionListItem = {
        ...project, // 保留原有数据
        id: project?.id || 0,
        productionName: values.productionName || null,
        productionCode: values.productionCode || null,
        productionType: values.productionType || 0,
        companyId: values.companyId || 0,
        secondProductionCode: values.secondProductionCode || null,
        scriptBookId: values.scriptBookId || 0,
        startDate: values.startDate ? dayjs(values.startDate).format(DATE_FORMAT_DAY) : null,
        endDate: values.endDate ? dayjs(values.endDate).format(DATE_FORMAT_DAY) : null,
        description: values.description,
        priceCurrency: values.priceCurrency ?? 1,
        totalEpisodes: values.totalEpisodes || 0,
        totalDuration: values.totalDuration || 0,
        cityName: values.cityName || null,
        status: values.status || 0,
      }
      delete submitData.community
      onSubmit(submitData)
    } catch (error) { }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={isEdit ? '编辑项目' : '添加项目'}
      open={open}
      onOk={handleSubmit}
      onCancel={handleCancel}
      width={600}
      destroyOnHidden
      okText="立即保存"
      cancelText="取消">
      <Form
        form={form}
        colon={false}
        layout="horizontal"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        style={{ marginTop: 16 }}>
        <Form.Item name="productionCode" label="代号" hidden></Form.Item>

        <Form.Item name="scriptBookId" label="剧本">
          <Select
            placeholder="选择剧本"
            allowClear
            showSearch
            className="full-h"
            options={scriptBookOptions}
            fieldNames={{ label: 'label', value: 'id' }}
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.label || '')

              return Boolean(result)
            }}
            onChange={handleBookIdChange}
          />
        </Form.Item>
        <Form.Item name="productionName" label="项目名" rules={[{ required: true, message: '请输入项目名称' }]}>
          <Input placeholder="输入项目名称" maxLength={100} />
        </Form.Item>

        <Form.Item name="secondProductionCode" label="项目代号">
          <Input placeholder="输入项目代号" maxLength={50} />
        </Form.Item>

        <Form.Item name="priceCurrency" label="结算货币" initialValue={1}>
          <Select placeholder="选择结算货币" allowClear className="full-h" options={PRICE_CURRENCY_OPTIONS} />
        </Form.Item>
        <Form.Item name="productionType" label="项目类型" initialValue={0}>
          <Select placeholder="选择项目类型" allowClear className="full-h" options={PRODUCTION_TYPE_OPTIONS} />
        </Form.Item>
        {formProductionType == 1 ? <Form.Item name="companyId" label="公司" >
          <Select placeholder="选择公司" allowClear className="full-h"
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.companyName?.toString() || '')

              return Boolean(result)
            }}
            fieldNames={{ label: 'companyName', value: 'id' }}
            options={allCompanyOptions}
          />
        </Form.Item> : null}

        <Form.Item name="totalEpisodes" label="总集数">
          <InputNumber className="full-h" placeholder="输入总集数" min={1} suffix="集" precision={0} />
        </Form.Item>
        <Form.Item name="totalDuration" label="总时长">
          <InputNumber className="full-h" placeholder="输入总时长" min={1} precision={0} suffix="分钟" />
        </Form.Item>
        <Form.Item name="cityName" label="城市">
          <AutoComplete placeholder="选择主要拍摄城市" options={[...CHINA_CITY_OPTIONS]} showSearch allowClear />
        </Form.Item>
        <Form.Item name="status" label="状态" rules={[{ required: true }]}>
          <Select placeholder="选择项目状态" allowClear className="full-h" options={PROJECT_STATUS} />
        </Form.Item>
        <Form.Item label="拍摄日期" className="no-margin">
          <Space.Compact>
            <Form.Item name="startDate">
              <DatePicker placeholder="开机日期" className="w200" />
            </Form.Item>
            <Form.Item name="endDate">
              <DatePicker placeholder="结束日期" className="w200" />
            </Form.Item>
          </Space.Compact>
        </Form.Item>
        <Form.Item name="description" label="描述" rules={[{ max: 500, message: '项目描述不能超过500个字符' }]}>
          <TextArea placeholder="输入项目描述" rows={4} showCount maxLength={500} />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default ProjectForm
