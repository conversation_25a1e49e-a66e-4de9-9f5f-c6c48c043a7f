import {
  ACTOR_ROLE_TYPE_CONFIG,
  ActorRoleType,
  RECRUITMENT_PARENT_TYPE_CONFIG,
  ROLE_TYPE_CONFIG,
  RoleType,
} from '@/consts'
import { CheckOutlined, CloseOutlined } from '@ant-design/icons'
import { Button, Form, Input, Modal, Radio, Space, Typography } from 'antd'
import React from 'react'
import { IPersonApplicationItem } from '../store'

interface PersonApplicationReviewModalProps {
  open: boolean
  application: IPersonApplicationItem | null
  loading?: boolean
  onClose: () => void
  onSubmit: (values: { status: number; reviewComment: string }) => Promise<void>
}

const PersonApplicationReviewModal: React.FC<PersonApplicationReviewModalProps> = ({
  open,
  application,
  loading = false,
  onClose,
  onSubmit,
}) => {
  const [form] = Form.useForm()

  // 获取角色类型信息
  const getRoleTypeInfo = (parentType: number, roleType: number) => {
    if (parentType === 1) {
      // 人员
      return ROLE_TYPE_CONFIG[roleType as RoleType] || { label: '未知', color: 'default' }
    } else if (parentType === 2) {
      // 演员
      return ACTOR_ROLE_TYPE_CONFIG[roleType as ActorRoleType] || { label: '未知', color: 'default' }
    }

    return { label: '未知', color: 'default' }
  }

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    await onSubmit(values)
    form.resetFields()
  }

  // 处理关闭
  const handleClose = () => {
    form.resetFields()
    onClose()
  }

  return (
    <Modal title="审核申请" open={open} onCancel={handleClose} footer={null} destroyOnClose>
      {application && (
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item label="申请人信息">
            <Space>
              <div>
                <Typography.Text strong>{application.personName || '未知'}</Typography.Text>
                <br />
                <Typography.Text type="secondary">
                  {RECRUITMENT_PARENT_TYPE_CONFIG[application.parentType]?.label || '未知'} -{' '}
                  {getRoleTypeInfo(application.parentType, application.roleType).label}
                </Typography.Text>
              </div>
            </Space>
          </Form.Item>

          <Form.Item name="status" label="审核结果" rules={[{ required: true, message: '请选择审核结果' }]}>
            <Radio.Group>
              <Space direction="vertical">
                <Radio value={2}>
                  <Space>
                    <CheckOutlined style={{ color: '#52c41a' }} />
                    通过
                  </Space>
                </Radio>
                <Radio value={3}>
                  <Space>
                    <CloseOutlined style={{ color: '#ff4d4f' }} />
                    拒绝
                  </Space>
                </Radio>
              </Space>
            </Radio.Group>
          </Form.Item>

          <Form.Item name="reviewComment" label="审核意见" rules={[{ required: true, message: '请填写审核意见' }]}>
            <Input.TextArea rows={4} placeholder="请填写审核意见..." />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={handleClose}>取消</Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                提交审核
              </Button>
            </Space>
          </Form.Item>
        </Form>
      )}
    </Modal>
  )
}

export default PersonApplicationReviewModal
