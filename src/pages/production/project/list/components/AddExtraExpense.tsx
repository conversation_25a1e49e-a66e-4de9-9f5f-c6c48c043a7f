import { EXPENSE_TYPE_CONFIG, EXPENSE_TYPE_OPTIONS } from '@/consts'
import { Button, Col, Drawer, Form, InputNumber, Row, Select, Space, message } from 'antd'
import React, { useEffect } from 'react'
// 动态生成费用列表初始值
const generateInitExpenseList = () =>
  EXPENSE_TYPE_OPTIONS.filter(item => item.value != 48).map(expenseType => ({
    expenseType: expenseType.value,
    quotedPrice: null,
    personCount: 1,
    hasInvoice: false,
    dayCount: 1,
  }))

// 使用useMemo缓存初始列表，避免重复计算
const initExpenseList = generateInitExpenseList()

interface IAddExtraExpenseProps {
  open: boolean
  onCancel: () => void
  onSuccess: (expenses: any[]) => void
  loading?: boolean
  productionId: number
}

const AddExtraExpense: React.FC<IAddExtraExpenseProps> = ({
  open,
  onCancel,
  onSuccess,
  loading = false,
  productionId,
}) => {
  const [form] = Form.useForm()

  useEffect(() => {
    if (open) {
      form.resetFields()
      setTimeout(() => {
        form.setFieldsValue({ expenseList: initExpenseList })
      }, 50)
    } else {
      form.setFieldsValue({ expenseList: [] })
    }
  }, [open, form])

  // 处理批量新增费用
  const handleAddExpenses = (values: any) => {
    try {
      const { expenseList } = values

      // 过滤出有效的费用记录
      const validExpenseList: any[] = []

      expenseList.forEach((expenseItem: any) => {
        const { expenseType, quotedPrice, personCount, hasInvoice, dayCount, totalPrice } = expenseItem

        if (expenseType && ((quotedPrice && quotedPrice > 0) || (totalPrice && totalPrice > 0))) {
          validExpenseList.push({
            expenseType,
            quotedPrice,
            personCount: personCount || 1,
            hasInvoice: hasInvoice || false,
            dayCount: dayCount || 1,
            totalPrice,
          })
        }
      })

      if (validExpenseList.length === 0) {
        // 如果没有有效数据，检查是否有填写了费用类型但没填单价的
        const hasIncompleteData = expenseList.some(
          (item: any) =>
            item.expenseType &&
            (!item.quotedPrice || item.quotedPrice <= 0) &&
            (!item.totalPrice || item.totalPrice <= 0)
        )

        if (hasIncompleteData) {
          message.warning('请填写完整的费用信息（费用类型和单价）')

          return
        }

        message.warning('请至少添加一项费用')

        return
      }

      onSuccess(validExpenseList)
    } catch (error) {
      console.error('添加费用失败:', error)
      message.error('添加费用失败')
    }
  }

  return (
    <Drawer
      title="添加费用"
      open={open}
      onClose={onCancel}
      width={1100}
      destroyOnHidden
      footer={
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" onClick={() => form.submit()} loading={loading}>
              立即添加
            </Button>
          </Space>
        </div>
      }>
      <Form
        form={form}
        colon={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        layout="horizontal"
        onFinish={handleAddExpenses}
        initialValues={{ expenseList: [] }}>
        <Form.List name="expenseList">
          {(fields, { add, remove }) => {
            const formExpenseList = form.getFieldValue('expenseList') || []

            return (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <div key={key}>
                    <Row>
                      <Form.Item
                        {...restField}
                        name={[name, 'expenseType']}
                        label="费用类型"
                        labelCol={{ span: 12 }}
                        wrapperCol={{ span: 16 }}
                        hidden>
                        <Select
                          placeholder="请选择费用类型"
                          options={EXPENSE_TYPE_OPTIONS}
                          showSearch
                          filterOption={(input, option) =>
                            (option?.label?.toString() ?? '').toLowerCase().includes(input.toLowerCase())
                          }
                        />
                      </Form.Item>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'quotedPrice']}
                          label={
                            (EXPENSE_TYPE_CONFIG as any)?.[formExpenseList?.[name]?.expenseType || '-1']?.label || ''
                          }
                          labelCol={{ span: 12 }}
                          wrapperCol={{ span: 16 }}>
                          <InputNumber placeholder="单价" min={0} precision={2} prefix="¥" className="full-h" />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item
                          {...restField}
                          name={[name, 'personCount']}
                          label="数量"
                          labelCol={{ span: 8 }}
                          wrapperCol={{ span: 16 }}
                          initialValue={1}>
                          <InputNumber placeholder="数量" min={1} precision={0} className="full-h" />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item
                          {...restField}
                          name={[name, 'dayCount']}
                          label="天数"
                          labelCol={{ span: 8 }}
                          wrapperCol={{ span: 16 }}
                          initialValue={1}>
                          <InputNumber placeholder="天数" min={1} precision={0} className="full-h" />
                        </Form.Item>
                      </Col>
                      <Col span={5}>
                        <Form.Item
                          {...restField}
                          name={[name, 'totalPrice']}
                          label="总价"
                          labelCol={{ span: 10 }}
                          wrapperCol={{ span: 14 }}>
                          <InputNumber placeholder="总价" min={0} precision={2} prefix="¥" className="full-h" />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item
                          {...restField}
                          name={[name, 'hasInvoice']}
                          label="发票"
                          labelCol={{ span: 10 }}
                          wrapperCol={{ span: 14 }}
                          initialValue={false}>
                          <Select
                            placeholder="请选择"
                            options={[
                              { value: true, label: '是' },
                              { value: false, label: '否' },
                            ]}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </div>
                ))}
              </>
            )
          }}
        </Form.List>
      </Form>
    </Drawer>
  )
}

export default AddExtraExpense
