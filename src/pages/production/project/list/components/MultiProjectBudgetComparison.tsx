import { ArrowDownOutlined, ArrowUpOutlined, BookOutlined, CaretRightOutlined } from '@ant-design/icons'
import { Card, Checkbox, Collapse, Empty, Flex, Space, Spin, Table, Tag, Typography } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import useProjectListStore, { IProductionBudgetData } from '../store'
import styles from './MultiProjectBudgetComparison.scss'

const { Text } = Typography
const { Panel } = Collapse

// 常量定义
const DEPARTMENTS = ['制片', '导演', '摄影', '收音', '后期', '灯光', '服化', '美术', '器材类', '演员']

// 处理后的数据类型
interface IProcessedData {
  projectNames: string[]
  projectShortNames: string[]
  totalComparisonData: any[]
  venueComparisonData: {
    tableData: any[]
    totals: number[]
  }
  personComparisonData: Record<
    string,
    {
      tableData: any[]
      totals: number[]
      hasData: boolean
    }
  >
  expenseComparisonData: {
    tableData: any[]
    totals: number[]
  }
  currencySymbols: string[]
}

interface IMultiProjectBudgetComparisonProps {
  baseProjectId: number
  compareProjectIds: number[]
  loading?: boolean
}

const MultiProjectBudgetComparison: React.FC<IMultiProjectBudgetComparisonProps> = ({
  baseProjectId,
  compareProjectIds,
  loading = false,
}) => {
  // 存储所有项目的原始结算数据
  const [allBudgetData, setAllBudgetData] = useState<IProductionBudgetData[]>([])
  // 存储哪些项目被选中参与对比（第一个基准项目始终选中）
  const [selectedProjectIndexes, setSelectedProjectIndexes] = useState<number[]>([0])
  const [loadingData, setLoadingData] = useState(false)

  const { getProductionBudgetData } = useProjectListStore()

  // 根据货币类型获取货币符号
  const getCurrencySymbol = (priceCurrency?: number) => {
    if (priceCurrency === 2) {
      return '$' // 美元
    } else if (priceCurrency === 3) {
      return 'Ұ' // 日元
    }

    return '¥' // 人民币（默认）
  }

  // 格式化金额显示
  const formatAmount = (amount: string | number, currencySymbol = '¥') => {
    let num = typeof amount === 'string' ? parseFloat(amount) : amount

    num = Math.abs(num)

    return num ? `${currencySymbol}${num.toLocaleString()}` : '-'
  }

  // 获取项目名称
  const getProjectName = (budgetData: IProductionBudgetData | null) => {
    const project = budgetData?.productions?.[0]

    return project ? `${project.productionCode}：${project.productionName}` : '未命名项目'
  }

  // 获取项目简称（用于表头）
  const getProjectShortName = (budgetData: IProductionBudgetData | null) => {
    const fullName = getProjectName(budgetData)

    return fullName.length > 8 ? `${fullName.substring(0, 6)}...` : fullName
  }

  // 找到最低金额的项目索引
  const findBestProjectIndex = (amounts: number[]) => {
    const validAmounts = amounts.filter(amount => amount > 0)

    if (validAmounts.length === 0) {
      return -1
    }
    const minAmount = Math.min(...validAmounts)

    return amounts.findIndex(amount => amount === minAmount && amount > 0)
  }

  // 获取各部门小计数据
  const getDepartmentSubtotal = (budgetData: IProductionBudgetData | null, department: string) => {
    const subtotalItem = budgetData?.person?.find(
      (item: any) => item.maxRoleType === department && item.minRoleType === '小计'
    )

    return subtotalItem ? parseFloat(subtotalItem.allPriceStr || '0') : 0
  }

  // 按部门分组人员数据（排除小计行）
  const getGroupedPersonData = (budgetData: IProductionBudgetData | null) => {
    if (!budgetData?.person?.length) {
      return {}
    }

    return budgetData.person.reduce((groups: Record<string, any[]>, person: any) => {
      if (person.minRoleType === '小计') {
        return groups
      }
      const department = person.maxRoleType || '其他'

      if (!groups[department]) {
        groups[department] = []
      }
      groups[department].push(person)

      return groups
    }, {})
  }

  // 补齐部门子类数据
  const completeSubTypesForDepartment = (dept: string, allPersonData: IProductionBudgetData[]) => {
    const allSubTypes = new Set<string>()

    allPersonData.forEach(data => {
      const deptData = getGroupedPersonData(data)[dept] || []

      deptData.forEach(item => allSubTypes.add(item.minRoleType))
    })

    return allPersonData.map(data => {
      const deptData = getGroupedPersonData(data)[dept] || []
      const completeItems: any[] = []

      allSubTypes.forEach(subType => {
        const item = deptData.find(item => item.minRoleType === subType)

        completeItems.push(
          item || {
            maxRoleType: dept,
            minRoleType: subType,
            minRoleTypeStr: subType,
            personCountStr: '-',
            priceStr: '-',
            dayCountStr: '-',
            allPriceStr: '0.00',
            hasInvoiceStr: '-',
          }
        )
      })

      return completeItems
    })
  }

  // 补齐场地数据
  const getMergedVenueData = (allBudgetData: IProductionBudgetData[]) => {
    const allVenueTypes = new Set<string>()

    allBudgetData.forEach(data => {
      data?.venue?.forEach((item: any) => allVenueTypes.add(item.minRoleType))
    })

    const completeVenueData = allBudgetData.map(data => {
      const venues = data?.venue || []
      const completeVenues: any[] = []

      allVenueTypes.forEach(venueType => {
        const venue = venues.find((item: any) => item.minRoleType === venueType)

        completeVenues.push(
          venue || {
            minRoleType: venueType,
            minRoleTypeStr: venueType,
            personCountStr: '-',
            priceStr: '-',
            dayCountStr: '-',
            allPriceStr: '0.00',
            hasInvoiceStr: '-',
          }
        )
      })

      return {
        venues: completeVenues,
        total: parseFloat(data?.venueTotal || '0'),
      }
    })

    return completeVenueData
  }

  // 补齐制片费用数据
  const getMergedExpenseData = (allBudgetData: IProductionBudgetData[]) => {
    const allExpenseTypes = new Set<string>()

    allBudgetData.forEach(data => {
      data?.expense?.forEach((item: any) => allExpenseTypes.add(item.minRoleType))
    })

    const completeExpenseData = allBudgetData.map(data => {
      const expenses = data?.expense || []
      const completeExpenses: any[] = []

      allExpenseTypes.forEach(expenseType => {
        const expense = expenses.find((item: any) => item.minRoleType === expenseType)

        completeExpenses.push(
          expense || {
            minRoleType: expenseType,
            minRoleTypeStr: expenseType,
            personCountStr: '-',
            priceStr: '-',
            dayCountStr: '-',
            allPriceStr: '0.00',
            hasInvoiceStr: '-',
          }
        )
      })

      return {
        expenses: completeExpenses,
        total: parseFloat(data?.allExpensesPersonTotal || '0'),
      }
    })

    return completeExpenseData
  }

  // 准备详细对比数据
  const prepareDetailComparisonData = (items: any[][], projectCount: number) => {
    if (items.length === 0 || items[0].length === 0) {
      return []
    }

    return items[0].map((_, index) => {
      const amounts: number[] = []
      const rowData: any = {
        key: index,
        itemName: items[0][index]?.minRoleTypeStr || '-',
        baseCount: items[0][index]?.personCountStr || '-',
        basePrice: items[0][index]?.priceStr || '-',
        baseAmount: items[0][index]?.allPriceStr || '0.00',
      }

      amounts.push(parseFloat(rowData.baseAmount))

      items.slice(1).forEach((projectItems, projectIndex) => {
        const projectKey = `compare_${projectIndex}`
        const amount = projectItems[index]?.allPriceStr || '0.00'

        rowData[`${projectKey}_count`] = projectItems[index]?.personCountStr || '-'
        rowData[`${projectKey}_price`] = projectItems[index]?.priceStr || '-'
        rowData[`${projectKey}_amount`] = amount
        amounts.push(parseFloat(amount))
      })

      // 计算最优项目索引
      rowData.bestIndex = findBestProjectIndex(amounts)

      return rowData
    })
  }

  useEffect(() => {
    if (baseProjectId && compareProjectIds.length > 0) {
      const newProjectIds = [baseProjectId, ...compareProjectIds]
      // 重置选中状态，默认全选所有项目
      const allIndexes = newProjectIds.map((_, index) => index)

      setSelectedProjectIndexes(allIndexes)
      loadBudgetData(newProjectIds)
    }
  }, [baseProjectId, compareProjectIds])

  // 根据选中状态计算处理后的数据
  const processedData: IProcessedData | null = useMemo(() => {
    if (allBudgetData.length === 0 || selectedProjectIndexes.length < 2) {
      return null
    }

    const selectedBudgetData = selectedProjectIndexes.map(index => allBudgetData[index])
    const baseData = selectedBudgetData[0]
    const compareDataList = selectedBudgetData.slice(1)

    // 将processAllData的逻辑内联到这里，避免初始化顺序问题
    const allBudgetDataInner = [baseData, ...compareDataList]

    // 项目名称
    const projectNames = allBudgetDataInner.map(data => getProjectName(data))

    // 获取所有项目的货币符号
    const currencySymbols = allBudgetDataInner.map(data => getCurrencySymbol(data?.productions?.[0]?.priceCurrency))

    // 总计对比数据
    const baseNoTaxTotal = parseFloat(baseData?.allTotal || '0')
    const baseTaxTotal = parseFloat(baseData?.secondAllTotal || '0')

    const totalData: any[] = [
      {
        key: 'total_notax',
        type: '总计(不含税)',
        baseAmount: baseNoTaxTotal,
        bestIndex: 0,
      },
      {
        key: 'total_tax',
        type: '总计(含税点6.72)',
        baseAmount: baseTaxTotal,
        bestIndex: 0,
      },
    ]

    compareDataList.forEach((compareData, index) => {
      const compareNoTaxTotal = parseFloat(compareData?.allTotal || '0')
      const compareTaxTotal = parseFloat(compareData?.secondAllTotal || '0')

      totalData[0][`compare_${index}_amount`] = compareNoTaxTotal
      totalData[1][`compare_${index}_amount`] = compareTaxTotal
    })

    // 为每行计算最优项目
    totalData.forEach(row => {
      const amounts = [row.baseAmount]

      compareDataList.forEach((_, index) => {
        amounts.push(row[`compare_${index}_amount`] || 0)
      })
      row.bestIndex = findBestProjectIndex(amounts)
    })

    // 场地对比数据
    const venueData = getMergedVenueData(allBudgetDataInner)
    const venueTableData = prepareDetailComparisonData(
      venueData.map(item => item.venues),
      compareDataList.length
    )
    const venueTotals = venueData.map(item => item.total)

    // 制片费用对比数据
    const expenseData = getMergedExpenseData(allBudgetDataInner)
    const expenseTableData = prepareDetailComparisonData(
      expenseData.map(item => item.expenses),
      compareDataList.length
    )
    const expenseTotals = expenseData.map(item => item.total)

    // 人工费用对比数据
    const personData: Record<string, any> = {}

    // 动态获取所有实际存在的部门
    const allDepartments = new Set<string>()

    allBudgetDataInner.forEach(data => {
      const groupedData = getGroupedPersonData(data)

      Object.keys(groupedData).forEach(dept => allDepartments.add(dept))

      // 也要从小计数据中获取部门
      data?.person?.forEach(item => {
        if (item.minRoleType === '小计' && item.maxRoleType) {
          allDepartments.add(item.maxRoleType)
        }
      })
    })

    // 合并基础部门列表
    DEPARTMENTS.forEach(dept => allDepartments.add(dept))

    allDepartments.forEach(department => {
      const departmentItems = completeSubTypesForDepartment(department, allBudgetDataInner)
      const departmentTotals = allBudgetDataInner.map(data => getDepartmentSubtotal(data, department))
      const hasData = departmentTotals.some(total => total > 0) || departmentItems.some(items => items.length > 0)

      if (hasData) {
        personData[department] = {
          tableData: prepareDetailComparisonData(departmentItems, compareDataList.length),
          totals: departmentTotals,
          hasData: true,
        }
      } else {
        personData[department] = {
          tableData: [],
          totals: [],
          hasData: false,
        }
      }
    })

    return {
      projectNames,
      projectShortNames: projectNames,
      totalComparisonData: totalData,
      venueComparisonData: {
        tableData: venueTableData,
        totals: venueTotals,
      },
      personComparisonData: personData,
      expenseComparisonData: {
        tableData: expenseTableData,
        totals: expenseTotals,
      },
      currencySymbols,
    }
  }, [allBudgetData, selectedProjectIndexes])

  // 处理checkbox选中状态变化
  const handleProjectSelect = (projectIndex: number, checked: boolean) => {
    if (projectIndex === 0) {
      return
    } // 基准项目不可取消选中

    setSelectedProjectIndexes(prev => {
      if (checked) {
        return [...prev, projectIndex].sort((a, b) => a - b)
      }

      return prev.filter(index => index !== projectIndex)
    })
  }

  // 加载所有项目的结算数据
  const loadBudgetData = async (projectIds: number[]) => {
    setLoadingData(true)
    try {
      const results = await Promise.all([...projectIds.map(id => getProductionBudgetData(id))])

      const validResults = results.filter((result): result is IProductionBudgetData => result !== null)

      setAllBudgetData(validResults)
    } catch (error) {
      console.error('加载结算数据失败:', error)
    } finally {
      setLoadingData(false)
    }
  }

  // 动态生成表格列配置
  const generateComparisonColumns = () => {
    if (!processedData) {
      return []
    }

    const columns: any[] = [
      {
        title: '项目',
        dataIndex: 'itemName',
        key: 'itemName',
        width: 120,
        align: 'center',
        fixed: 'left',
      },
      {
        title: `${processedData.projectShortNames[0]}`,
        fixed: 'left',
        children: [
          {
            title: '数量',
            dataIndex: 'baseCount',
            key: 'baseCount',
            width: 60,
            align: 'center',
            fixed: 'left',
            render: (text: string) => text || '-',
          },
          {
            title: '单价',
            dataIndex: 'basePrice',
            key: 'basePrice',
            width: 80,
            align: 'center',
            fixed: 'left',
            render: (text: string) => formatAmount(text, processedData?.currencySymbols[0]),
          },
          {
            title: '金额',
            dataIndex: 'baseAmount',
            key: 'baseAmount',
            width: 100,
            align: 'center',
            fixed: 'left',
            render: (text: string, record: any) => {
              const isLowest = record.bestIndex === 0

              return (
                <Text className={isLowest ? 'text-primary' : ''}>
                  {formatAmount(text, processedData?.currencySymbols[0])}
                </Text>
              )
            },
          },
        ],
      },
    ]

    processedData.projectShortNames.slice(1).forEach((projectName, index) => {
      const projectKey = `compare_${index}`

      columns.push({
        title: projectName,
        children: [
          {
            title: '数量',
            dataIndex: `${projectKey}_count`,
            key: `${projectKey}_count`,
            width: 60,
            align: 'center',
            render: (text: string) => text || '-',
          },
          {
            title: '单价',
            dataIndex: `${projectKey}_price`,
            key: `${projectKey}_price`,
            width: 80,
            align: 'center',
            render: (text: string) => formatAmount(text, processedData?.currencySymbols[index + 1]),
          },
          {
            title: '金额',
            dataIndex: `${projectKey}_amount`,
            key: `${projectKey}_amount`,
            width: 100,
            align: 'center',
            render: (text: string, record: any) => {
              const isLowest = record.bestIndex === index + 1

              return (
                <Text className={isLowest ? 'text-primary' : ''}>
                  {formatAmount(text, processedData?.currencySymbols[index + 1])}
                </Text>
              )
            },
          },
          {
            title: '差额',
            key: `${projectKey}_diff`,
            width: 100,
            align: 'center',
            render: (_: any, record: any) => {
              const baseAmount = parseFloat(record.baseAmount || '0')
              const compareAmount = parseFloat(record[`${projectKey}_amount`] || '0')
              const diff = baseAmount - compareAmount

              if (diff === 0) {
                return <Text type="secondary">-</Text>
              }

              const textType = diff === 0 ? 'secondary' : diff > 0 ? 'danger' : 'success'
              const percentage = baseAmount > 0 ? Math.abs((diff / baseAmount) * 100).toFixed(1) : '∞'

              return (
                <Space size={12}>
                  <Typography.Text type={textType} className="fs-lg">
                    {diff > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                  </Typography.Text>
                  <Space direction="vertical" size={4}>
                    <Text type={textType}>{formatAmount(diff, processedData?.currencySymbols[index + 1])}</Text>
                    {baseAmount > 0 && <Text type={textType}>{`${percentage}%`}</Text>}
                  </Space>
                </Space>
              )
            },
          },
        ],
      })
    })

    return columns
  }

  // 生成详细对比表格的汇总行
  const generateSummaryRow = (totals: number[]) => {
    if (!processedData) {
      return null
    }

    const bestIndex = findBestProjectIndex(totals)

    const summaryRowCells = [
      <Table.Summary.Cell key="label" index={0} align="center" className="bg-secondary">
        <Text strong>小计</Text>
      </Table.Summary.Cell>,
      <Table.Summary.Cell key="base-count" index={1} align="center" className="bg-secondary"></Table.Summary.Cell>,
      <Table.Summary.Cell key="base-price" index={2} align="center" className="bg-secondary"></Table.Summary.Cell>,
      <Table.Summary.Cell key="base-amount" index={3} align="center" className="bg-secondary">
        <Text className={bestIndex === 0 ? 'text-primary' : ''}>
          {formatAmount(totals[0], processedData?.currencySymbols[0])}
        </Text>
      </Table.Summary.Cell>,
    ]

    totals.slice(1).forEach((total, index) => {
      const cellIndex = 4 + index * 4
      const isLowest = bestIndex === index + 1

      summaryRowCells.push(
        <Table.Summary.Cell key={`compare-${index}-count`} index={cellIndex} align="center"></Table.Summary.Cell>,
        <Table.Summary.Cell key={`compare-${index}-price`} index={cellIndex + 1} align="center"></Table.Summary.Cell>,
        <Table.Summary.Cell key={`compare-${index}-amount`} index={cellIndex + 2} align="center">
          <Text className={isLowest ? 'text-primary' : ''}>
            {formatAmount(total, processedData?.currencySymbols[index + 1])}
          </Text>
        </Table.Summary.Cell>,
        <Table.Summary.Cell key={`compare-${index}-diff`} index={cellIndex + 3} align="center">
          {(() => {
            const diff = total - totals[0]

            if (diff === 0) {
              return <Text>-</Text>
            }

            const textType = diff === 0 ? 'secondary' : diff > 0 ? 'danger' : 'success'
            const percentage = totals[0] > 0 ? Math.abs((diff / totals[0]) * 100).toFixed(1) : '∞'

            return (
              <Space size={12}>
                <Typography.Text type={textType} className="fs-lg">
                  {diff > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />}
                </Typography.Text>
                <Space direction="vertical" size={4}>
                  <Text type={textType}>{formatAmount(diff, processedData?.currencySymbols[index + 1])}</Text>
                  {totals[0] > 0 && <Text type={textType}>{`${percentage}%`}</Text>}
                </Space>
              </Space>
            )
          })()}
        </Table.Summary.Cell>
      )
    })

    return <Table.Summary.Row className="bg-secondary">{summaryRowCells}</Table.Summary.Row>
  }

  // 生成总计表格列
  const generateTotalColumns = () => {
    if (!processedData) {
      return []
    }

    const columns: any[] = [
      { title: '项目', dataIndex: 'type', key: 'type', align: 'center', width: 200, fixed: 'left' },
      {
        title: `${processedData.projectShortNames[0]}`,
        dataIndex: 'baseAmount',
        key: 'baseAmount',
        align: 'center',
        fixed: 'left',
        render: (text: number, record: any) => {
          const isLowest = record.bestIndex === 0

          return (
            <Text className={isLowest ? 'text-primary' : ''}>
              {formatAmount(text, processedData?.currencySymbols[0])}
            </Text>
          )
        },
      },
    ]

    processedData.projectShortNames.slice(1).forEach((projectName, index) => {
      columns.push({
        title: projectName,
        dataIndex: `compare_${index}_amount`,
        key: `compare_${index}_amount`,
        align: 'center',
        ellipsis: true,
        render: (text: number, record: any) => {
          const isLowest = record.bestIndex === index + 1

          return (
            <Text className={isLowest ? 'text-primary' : ''}>
              {formatAmount(text, processedData?.currencySymbols[index + 1])}
            </Text>
          )
        },
      })
    })

    return columns
  }

  if (loadingData || loading) {
    return (
      <Flex justify="center" align="center" style={{ minHeight: 200 }}>
        <Spin size="large" />
      </Flex>
    )
  }

  if (allBudgetData.length === 0) {
    return <Empty description="暂无结算对比数据" />
  }

  return (
    <Flex vertical gap={24}>
      {/* 项目信息卡片 */}
      <Card size="small">
        <Flex align="center">
          <Space wrap>
            {allBudgetData.map((budgetData, index) => {
              console.log('budgetData', budgetData)
              const projectName = getProjectName(budgetData)
              const isSelected = selectedProjectIndexes.includes(index)
              const isBase = index === 0

              return (
                <Tag key={index} style={{ maxWidth: '300px' }}>
                  <Checkbox
                    checked={isSelected}
                    disabled={isBase}
                    onChange={e => handleProjectSelect(index, e.target.checked)}>
                    <Text ellipsis={{ tooltip: projectName }} strong={isSelected}>
                      {projectName}
                      {isBase ? '(基准)' : ''}
                    </Text>
                  </Checkbox>
                </Tag>
              )
            })}
          </Space>
        </Flex>
      </Card>

      {/* 对比说明 */}
      {selectedProjectIndexes.length < 2 && (
        <Card size="small">
          <Flex justify="center" align="center">
            <Text type="secondary">请至少选择一个项目与基准项目进行对比</Text>
          </Flex>
        </Card>
      )}

      {/* 总计对比 */}
      {processedData && (
        <Flex vertical gap={6}>
          <Typography.Text strong className="fs-lg">
            结算总计
          </Typography.Text>
          <Table
            columns={generateTotalColumns()}
            dataSource={processedData.totalComparisonData}
            pagination={false}
            size="small"
            bordered
            scroll={{ x: 400 + (selectedProjectIndexes.length - 1) * 200 }}
          />
        </Flex>
      )}

      {/* 费用明细对比 */}
      {processedData && (
        <Collapse
          defaultActiveKey={['venue', 'person', 'expense']}
          ghost
          className={styles.collapse}
          expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}>
          {/* 场地费用对比 */}
          <Panel header={<Text strong>场地费用对比</Text>} key="venue">
            <Table
              columns={generateComparisonColumns()}
              dataSource={processedData.venueComparisonData.tableData}
              pagination={false}
              size="small"
              rowKey="key"
              bordered
              scroll={{ x: 400 + (selectedProjectIndexes.length - 1) * 400 }}
              summary={() => generateSummaryRow(processedData.venueComparisonData.totals)}
            />
          </Panel>

          {/* 人工费用对比 */}
          <Panel header={<Text strong>人工费用对比</Text>} key="person">
            <Flex vertical gap={16}>
              {Object.keys(processedData.personComparisonData).map(department => {
                const deptData = processedData.personComparisonData[department]

                if (!deptData.hasData) {
                  return null
                }

                return (
                  <Table
                    key={department}
                    title={() => (
                      <Space>
                        <BookOutlined />
                        <Text strong>{department}</Text>
                      </Space>
                    )}
                    columns={generateComparisonColumns()}
                    dataSource={deptData.tableData}
                    pagination={false}
                    size="small"
                    rowKey="key"
                    bordered
                    scroll={{ x: 400 + (selectedProjectIndexes.length - 1) * 400 }}
                    summary={() => generateSummaryRow(deptData.totals)}
                  />
                )
              })}
            </Flex>
          </Panel>

          {/* 制片费用对比 */}
          <Panel header={<Text strong>制片费用对比</Text>} key="expense">
            <Table
              columns={generateComparisonColumns()}
              dataSource={processedData.expenseComparisonData.tableData}
              pagination={false}
              size="small"
              rowKey="key"
              bordered
              scroll={{ x: 400 + (selectedProjectIndexes.length - 1) * 400 }}
              summary={() => generateSummaryRow(processedData.expenseComparisonData.totals)}
            />
          </Panel>
        </Collapse>
      )}
    </Flex>
  )
}

export default MultiProjectBudgetComparison
