import { Card, Collapse, Empty, Flex, Space, Spin, Table, Tag, Typography } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import useProjectListStore, { IProductionBudgetData } from '../store'

const { Text, Title } = Typography
const { Panel } = Collapse

// 常量定义
const DEPARTMENTS = ['制片', '导演', '摄影', '收音', '后期', '灯光', '服化', '美术', '器材类', '演员'] as const
const COLORS = {
  PROJECT_A: '#1890ff',
  PROJECT_B: '#52c41a',
  INCREASE: '#ff4d4f',
  DECREASE: '#52c41a',
  NEUTRAL: '#666',
  BACKGROUND: '#fafafa',
} as const

interface IProductionBudgetComparisonProps {
  projectId1: number
  projectId2: number
  loading?: boolean
}

// 定义所有可能的部门类型
const ALL_DEPARTMENTS = DEPARTMENTS

const ProductionBudgetComparison: React.FC<IProductionBudgetComparisonProps> = ({
  projectId1,
  projectId2,
  loading = false,
}) => {
  const [budgetData1, setBudgetData1] = useState<IProductionBudgetData | null>(null)
  const [budgetData2, setBudgetData2] = useState<IProductionBudgetData | null>(null)
  const [loadingData, setLoadingData] = useState(false)

  const { getProductionBudgetData } = useProjectListStore()

  useEffect(() => {
    if (projectId1 && projectId2) {
      loadBudgetData()
    }
  }, [projectId1, projectId2])

  // 加载两个项目的结算数据
  const loadBudgetData = async () => {
    setLoadingData(true)
    try {
      const [result1, result2] = await Promise.all([
        getProductionBudgetData(projectId1),
        getProductionBudgetData(projectId2),
      ])

      setBudgetData1(result1)
      setBudgetData2(result2)
    } catch (error) {
      console.error('加载结算数据失败:', error)
    } finally {
      setLoadingData(false)
    }
  }

  // 获取项目名称
  const getProjectName = (budgetData: any) => budgetData?.productions?.[0]?.productionName || '未命名项目'

  // 获取项目简称（用于表头）
  const getProjectShortName = (budgetData: any) => {
    const fullName = getProjectName(budgetData)

    return fullName.length > 8 ? `${fullName.substring(0, 6)}...` : fullName
  }

  // 获取各部门小计数据
  const getDepartmentSubtotal = (budgetData: any, department: string) => {
    const subtotalItem = budgetData?.person?.find(
      (item: any) => item.maxRoleType === department && item.minRoleType === '小计'
    )

    return subtotalItem ? parseFloat(subtotalItem.allPriceStr || '0') : 0
  }

  // 按部门分组人员数据（排除小计行）
  const getGroupedPersonData = (budgetData: any) => {
    if (!budgetData?.person?.length) {
      return {}
    }

    return budgetData.person.reduce((groups: Record<string, any[]>, person: any) => {
      // 跳过小计行，只处理具体人员
      if (person.minRoleType === '小计') {
        return groups
      }

      const department = person.maxRoleType || '其他'

      if (!groups[department]) {
        groups[department] = []
      }
      groups[department].push(person)

      return groups
    }, {})
  }

  // 补齐部门子类数据的辅助函数
  const completeSubTypesForDepartment = (dept: string, items1: any[], items2: any[]) => {
    // 获取所有唯一的子类名称
    const allSubTypes = new Set([...items1.map(item => item.minRoleType), ...items2.map(item => item.minRoleType)])

    const completeItems1: any[] = []
    const completeItems2: any[] = []

    allSubTypes.forEach(subType => {
      const item1 = items1.find(item => item.minRoleType === subType)
      const item2 = items2.find(item => item.minRoleType === subType)

      // 项目1的数据
      completeItems1.push(
        item1 || {
          maxRoleType: dept,
          minRoleType: subType,
          minRoleTypeStr: subType,
          personCountStr: '-',
          priceStr: '-',
          dayCountStr: '-',
          allPriceStr: '0.00',
          hasInvoiceStr: '-',
        }
      )

      // 项目2的数据
      completeItems2.push(
        item2 || {
          maxRoleType: dept,
          minRoleType: subType,
          minRoleTypeStr: subType,
          personCountStr: '-',
          priceStr: '-',
          dayCountStr: '-',
          allPriceStr: '0.00',
          hasInvoiceStr: '-',
        }
      )
    })

    return { completeItems1, completeItems2 }
  }

  // 补齐并合并两个项目的人员数据
  const mergedPersonData = useMemo(() => {
    const data1 = getGroupedPersonData(budgetData1)
    const data2 = getGroupedPersonData(budgetData2)

    // 动态获取所有实际存在的部门
    const allDepartments = new Set([
      ...ALL_DEPARTMENTS, // 包含基础部门
      ...Object.keys(data1), // 项目1的部门
      ...Object.keys(data2), // 项目2的部门
    ])

    const result: Record<
      string,
      {
        items1: any[]
        items2: any[]
        subtotal1: number
        subtotal2: number
      }
    > = {}

    // 为所有部门初始化数据
    allDepartments.forEach(dept => {
      result[dept] = {
        items1: data1[dept] || [],
        items2: data2[dept] || [],
        subtotal1: getDepartmentSubtotal(budgetData1, dept),
        subtotal2: getDepartmentSubtotal(budgetData2, dept),
      }
    })

    // 补齐子类数据 - 收集所有唯一的子类
    allDepartments.forEach(dept => {
      const items1 = result[dept].items1
      const items2 = result[dept].items2

      const { completeItems1, completeItems2 } = completeSubTypesForDepartment(dept, items1, items2)

      result[dept].items1 = completeItems1
      result[dept].items2 = completeItems2
    })

    return result
  }, [budgetData1, budgetData2])

  // 补齐场地数据
  const mergedVenueData = useMemo(() => {
    const venues1 = budgetData1?.venue || []
    const venues2 = budgetData2?.venue || []

    // 获取所有唯一的场地类型
    const allVenueTypes = new Set([
      ...venues1.map((item: any) => item.minRoleType),
      ...venues2.map((item: any) => item.minRoleType),
    ])

    const completeVenues1: any[] = []
    const completeVenues2: any[] = []

    allVenueTypes.forEach(venueType => {
      const venue1 = venues1.find((item: any) => item.minRoleType === venueType)
      const venue2 = venues2.find((item: any) => item.minRoleType === venueType)

      completeVenues1.push(
        venue1 || {
          minRoleType: venueType,
          minRoleTypeStr: venueType,
          personCountStr: '-',
          priceStr: '-',
          dayCountStr: '-',
          allPriceStr: '0.00',
          hasInvoiceStr: '-',
        }
      )

      completeVenues2.push(
        venue2 || {
          minRoleType: venueType,
          minRoleTypeStr: venueType,
          personCountStr: '-',
          priceStr: '-',
          dayCountStr: '-',
          allPriceStr: '0.00',
          hasInvoiceStr: '-',
        }
      )
    })

    return {
      venues1: completeVenues1,
      venues2: completeVenues2,
      total1: parseFloat(budgetData1?.venueTotal || '0'),
      total2: parseFloat(budgetData2?.venueTotal || '0'),
    }
  }, [budgetData1, budgetData2])

  // 补齐制片费用数据
  const mergedExpenseData = useMemo(() => {
    const expenses1 = budgetData1?.expense || []
    const expenses2 = budgetData2?.expense || []

    // 获取所有唯一的费用类型
    const allExpenseTypes = new Set([
      ...expenses1.map((item: any) => item.minRoleType),
      ...expenses2.map((item: any) => item.minRoleType),
    ])

    const completeExpenses1: any[] = []
    const completeExpenses2: any[] = []

    allExpenseTypes.forEach(expenseType => {
      const expense1 = expenses1.find((item: any) => item.minRoleType === expenseType)
      const expense2 = expenses2.find((item: any) => item.minRoleType === expenseType)

      completeExpenses1.push(
        expense1 || {
          minRoleType: expenseType,
          minRoleTypeStr: expenseType,
          personCountStr: '-',
          priceStr: '-',
          dayCountStr: '-',
          allPriceStr: '0.00',
          hasInvoiceStr: '-',
        }
      )

      completeExpenses2.push(
        expense2 || {
          minRoleType: expenseType,
          minRoleTypeStr: expenseType,
          personCountStr: '-',
          priceStr: '-',
          dayCountStr: '-',
          allPriceStr: '0.00',
          hasInvoiceStr: '-',
        }
      )
    })

    return {
      expenses1: completeExpenses1,
      expenses2: completeExpenses2,
      total1: parseFloat(budgetData1?.allExpensesPersonTotal || '0'),
      total2: parseFloat(budgetData2?.allExpensesPersonTotal || '0'),
    }
  }, [budgetData1, budgetData2])

  // 格式化金额显示
  const formatAmount = (amount: string | number) => {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount

    return num ? `¥${num.toLocaleString()}` : '-'
  }

  // 优化后的对比表格列配置
  const comparisonColumns = [
    {
      title: '项目',
      dataIndex: 'minRoleTypeStr',
      key: 'minRoleTypeStr',
      width: 120,
      align: 'center' as const,
      fixed: 'left' as const,
    },
    {
      title: getProjectShortName(budgetData1),
      children: [
        {
          title: '数量',
          dataIndex: 'personCountStr1',
          key: 'personCountStr1',
          width: 60,
          align: 'center' as const,
          render: (text: string) => text || '-',
        },
        {
          title: '单价',
          dataIndex: 'priceStr1',
          key: 'priceStr1',
          width: 80,
          align: 'center' as const,
          render: (text: string) => formatAmount(text),
        },
        {
          title: '金额',
          dataIndex: 'allPriceStr1',
          key: 'allPriceStr1',
          width: 100,
          align: 'center' as const,
          render: (text: string) => <Text style={{ color: '#1890ff', fontWeight: 'bold' }}>{formatAmount(text)}</Text>,
        },
      ],
    },
    {
      title: getProjectShortName(budgetData2),
      children: [
        {
          title: '数量',
          dataIndex: 'personCountStr2',
          key: 'personCountStr2',
          width: 60,
          align: 'center' as const,
          render: (text: string) => text || '-',
        },
        {
          title: '单价',
          dataIndex: 'priceStr2',
          key: 'priceStr2',
          width: 80,
          align: 'center' as const,
          render: (text: string) => formatAmount(text),
        },
        {
          title: '金额',
          dataIndex: 'allPriceStr2',
          key: 'allPriceStr2',
          width: 100,
          align: 'center' as const,
          render: (text: string) => <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>{formatAmount(text)}</Text>,
        },
      ],
    },
    {
      title: '差额分析',
      key: 'difference',
      width: 120,
      align: 'center' as const,
      render: (_: any, record: any) => {
        const amount1 = parseFloat(record.allPriceStr1 || '0')
        const amount2 = parseFloat(record.allPriceStr2 || '0')
        const diff = amount2 - amount1

        if (diff === 0) {
          return <Text style={{ color: '#666' }}>-</Text>
        }

        const percentage = amount1 > 0 ? ((diff / amount1) * 100).toFixed(1) : '∞'
        const color = diff > 0 ? '#ff4d4f' : '#52c41a'

        return (
          <Space direction="vertical" size={2} style={{ textAlign: 'center' }}>
            <Text style={{ color, fontWeight: 'bold', fontSize: '12px' }}>
              {diff > 0 ? '+' : ''}
              {formatAmount(diff)}
            </Text>
            {amount1 > 0 && (
              <Text style={{ color, fontSize: '10px' }}>
                {diff > 0 ? '+' : ''}
                {percentage}%
              </Text>
            )}
          </Space>
        )
      },
    },
  ]

  // 简化的大类对比列配置
  const categoryColumns = [
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      width: 120,
      align: 'center' as const,
    },
    {
      title: getProjectShortName(budgetData1),
      dataIndex: 'amount1',
      key: 'amount1',
      width: 150,
      align: 'center' as const,
      render: (text: number) => (
        <Text style={{ color: '#1890ff', fontWeight: 'bold', fontSize: '14px' }}>{formatAmount(text)}</Text>
      ),
    },
    {
      title: getProjectShortName(budgetData2),
      dataIndex: 'amount2',
      key: 'amount2',
      width: 150,
      align: 'center' as const,
      render: (text: number) => (
        <Text style={{ color: '#52c41a', fontWeight: 'bold', fontSize: '14px' }}>{formatAmount(text)}</Text>
      ),
    },
    {
      title: '差额分析',
      key: 'difference',
      width: 120,
      align: 'center' as const,
      render: (_: any, record: any) => {
        const diff = record.amount2 - record.amount1

        if (diff === 0) {
          return <Text style={{ color: '#666' }}>-</Text>
        }

        const percentage = record.amount1 > 0 ? ((diff / record.amount1) * 100).toFixed(1) : '∞'
        const color = diff > 0 ? '#ff4d4f' : '#52c41a'

        return (
          <Space direction="vertical" size={2} style={{ textAlign: 'center' }}>
            <Text style={{ color, fontWeight: 'bold' }}>
              {diff > 0 ? '+' : ''}
              {formatAmount(diff)}
            </Text>
            {record.amount1 > 0 && (
              <Text style={{ color, fontSize: '12px' }}>
                {diff > 0 ? '+' : ''}
                {percentage}%
              </Text>
            )}
          </Space>
        )
      },
    },
  ]

  // 准备对比数据
  const prepareComparisonData = (items1: any[], items2: any[]) =>
    items1.map((item1, index) => {
      const item2 = items2[index]

      return {
        minRoleTypeStr: item1.minRoleTypeStr,
        personCountStr1: item1.personCountStr,
        priceStr1: item1.priceStr,
        allPriceStr1: item1.allPriceStr,
        personCountStr2: item2.personCountStr,
        priceStr2: item2.priceStr,
        allPriceStr2: item2.allPriceStr,
      }
    })

  if (loadingData || loading) {
    return (
      <Flex justify="center" align="center" style={{ minHeight: 200 }}>
        <Spin size="large" />
      </Flex>
    )
  }

  if (!budgetData1 || !budgetData2) {
    return <Empty description="暂无结算对比数据" />
  }

  return (
    <Flex vertical gap={24}>
      {/* 项目信息卡片 */}
      <Card size="small">
        <Flex justify="center" align="center">
          <Space size="large" align="center">
            <Tag color="blue" style={{ margin: 0, fontSize: '14px', padding: '6px 16px', maxWidth: '300px' }}>
              <Text ellipsis={{ tooltip: getProjectName(budgetData1) }} style={{ color: 'inherit' }}>
                {getProjectName(budgetData1)}
              </Text>
            </Tag>
            <Text style={{ fontSize: '20px', color: '#d9d9d9', fontWeight: 'bold' }}>VS</Text>
            <Tag color="green" style={{ margin: 0, fontSize: '14px', padding: '6px 16px', maxWidth: '300px' }}>
              <Text ellipsis={{ tooltip: getProjectName(budgetData2) }} style={{ color: 'inherit' }}>
                {getProjectName(budgetData2)}
              </Text>
            </Tag>
          </Space>
        </Flex>
      </Card>

      {/* 总计对比 */}
      <Card
        size="small"
        title={
          <Text strong style={{ fontSize: '16px' }}>
            结算总计对比
          </Text>
        }>
        <Table
          columns={[
            { title: '项目', dataIndex: 'type', key: 'type', align: 'center' as const, width: 200 },
            {
              title: getProjectShortName(budgetData1),
              dataIndex: 'amount1',
              key: 'amount1',
              align: 'center' as const,
              render: (text: number) => (
                <Text style={{ color: '#1890ff', fontWeight: 'bold', fontSize: '16px' }}>{formatAmount(text)}</Text>
              ),
            },
            {
              title: getProjectShortName(budgetData2),
              dataIndex: 'amount2',
              key: 'amount2',
              align: 'center' as const,
              render: (text: number) => (
                <Text style={{ color: '#52c41a', fontWeight: 'bold', fontSize: '16px' }}>{formatAmount(text)}</Text>
              ),
            },
            {
              title: '差额分析',
              key: 'difference',
              align: 'center' as const,
              render: (_: any, record: any) => {
                const diff = record.amount2 - record.amount1

                if (diff === 0) {
                  return <Text style={{ color: '#666' }}>-</Text>
                }

                const percentage = record.amount1 > 0 ? ((diff / record.amount1) * 100).toFixed(1) : '∞'
                const color = diff > 0 ? '#ff4d4f' : '#52c41a'

                return (
                  <Space direction="vertical" size={4} style={{ textAlign: 'center' }}>
                    <Text style={{ color, fontWeight: 'bold', fontSize: '16px' }}>
                      {diff > 0 ? '+' : ''}
                      {formatAmount(diff)}
                    </Text>
                    {record.amount1 > 0 && (
                      <Text style={{ color, fontSize: '14px' }}>
                        {diff > 0 ? '+' : ''}
                        {percentage}%
                      </Text>
                    )}
                  </Space>
                )
              },
            },
          ]}
          dataSource={[
            {
              key: 'total_notax',
              type: '总计（不含税）',
              amount1: parseFloat(budgetData1?.allTotal || '0'),
              amount2: parseFloat(budgetData2?.allTotal || '0'),
            },
            {
              key: 'total_tax',
              type: '总计（含税6.72%）',
              amount1: parseFloat(budgetData1?.secondAllTotal || '0'),
              amount2: parseFloat(budgetData2?.secondAllTotal || '0'),
            },
          ]}
          pagination={false}
          size="small"
          bordered
        />
      </Card>

      {/* 费用明细对比 */}
      <Collapse defaultActiveKey={['venue', 'person', 'expense']} ghost>
        {/* 场地费用对比 */}
        <Panel header={<Text strong>场地费用对比</Text>} key="venue">
          <Card size="small">
            <Table
              columns={comparisonColumns}
              dataSource={prepareComparisonData(mergedVenueData.venues1, mergedVenueData.venues2)}
              pagination={false}
              size="small"
              rowKey="minRoleTypeStr"
              bordered
              scroll={{ x: 800 }}
              summary={() => (
                <Table.Summary.Row style={{ backgroundColor: '#fafafa' }}>
                  <Table.Summary.Cell index={0} align="center">
                    <Text strong>小计</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1} align="center">
                    -
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2} align="center">
                    -
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3} align="center">
                    <Text style={{ color: '#1890ff', fontWeight: 'bold' }}>{formatAmount(mergedVenueData.total1)}</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={4} align="center">
                    -
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={5} align="center">
                    -
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={6} align="center">
                    <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>{formatAmount(mergedVenueData.total2)}</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={7} align="center">
                    {(() => {
                      const diff = mergedVenueData.total2 - mergedVenueData.total1

                      if (diff === 0) {
                        return <Text style={{ color: '#666' }}>-</Text>
                      }

                      const percentage =
                        mergedVenueData.total1 > 0 ? ((diff / mergedVenueData.total1) * 100).toFixed(1) : '∞'
                      const color = diff > 0 ? '#ff4d4f' : '#52c41a'

                      return (
                        <Space direction="vertical" size={2} style={{ textAlign: 'center' }}>
                          <Text style={{ color, fontWeight: 'bold' }}>
                            {diff > 0 ? '+' : ''}
                            {formatAmount(diff)}
                          </Text>
                          {mergedVenueData.total1 > 0 && (
                            <Text style={{ color, fontSize: '12px' }}>
                              {diff > 0 ? '+' : ''}
                              {percentage}%
                            </Text>
                          )}
                        </Space>
                      )
                    })()}
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              )}
            />
          </Card>
        </Panel>

        {/* 人工费用对比 */}
        <Panel header={<Text strong>人工费用对比</Text>} key="person">
          <Flex vertical gap={16}>
            {Object.keys(mergedPersonData).map(department => {
              const deptData = mergedPersonData[department]
              const hasData =
                deptData.subtotal1 > 0 ||
                deptData.subtotal2 > 0 ||
                deptData.items1.length > 0 ||
                deptData.items2.length > 0

              if (!hasData) {
                return null
              }

              return (
                <Card key={department} size="small" title={<Text strong>{department}部门对比</Text>}>
                  <Table
                    columns={comparisonColumns}
                    dataSource={prepareComparisonData(deptData.items1, deptData.items2)}
                    pagination={false}
                    size="small"
                    rowKey="minRoleTypeStr"
                    bordered
                    scroll={{ x: 800 }}
                    summary={() => (
                      <Table.Summary.Row style={{ backgroundColor: '#fafafa' }}>
                        <Table.Summary.Cell index={0} align="center">
                          <Text strong>小计</Text>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={1} align="center">
                          -
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={2} align="center">
                          -
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={3} align="center">
                          <Text style={{ color: '#1890ff', fontWeight: 'bold' }}>
                            {formatAmount(deptData.subtotal1)}
                          </Text>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={4} align="center">
                          -
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={5} align="center">
                          -
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={6} align="center">
                          <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>
                            {formatAmount(deptData.subtotal2)}
                          </Text>
                        </Table.Summary.Cell>
                        <Table.Summary.Cell index={7} align="center">
                          {(() => {
                            const diff = deptData.subtotal2 - deptData.subtotal1

                            if (diff === 0) {
                              return <Text style={{ color: '#666' }}>-</Text>
                            }

                            const percentage =
                              deptData.subtotal1 > 0 ? ((diff / deptData.subtotal1) * 100).toFixed(1) : '∞'
                            const color = diff > 0 ? '#ff4d4f' : '#52c41a'

                            return (
                              <Space direction="vertical" size={2} style={{ textAlign: 'center' }}>
                                <Text style={{ color, fontWeight: 'bold' }}>
                                  {diff > 0 ? '+' : ''}
                                  {formatAmount(diff)}
                                </Text>
                                {deptData.subtotal1 > 0 && (
                                  <Text style={{ color, fontSize: '12px' }}>
                                    {diff > 0 ? '+' : ''}
                                    {percentage}%
                                  </Text>
                                )}
                              </Space>
                            )
                          })()}
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    )}
                  />
                </Card>
              )
            })}
          </Flex>
        </Panel>

        {/* 制片费用对比 */}
        <Panel header={<Text strong>制片费用对比</Text>} key="expense">
          <Card size="small">
            <Table
              columns={comparisonColumns}
              dataSource={prepareComparisonData(mergedExpenseData.expenses1, mergedExpenseData.expenses2)}
              pagination={false}
              size="small"
              rowKey="minRoleTypeStr"
              bordered
              scroll={{ x: 800 }}
              summary={() => (
                <Table.Summary.Row style={{ backgroundColor: '#fafafa' }}>
                  <Table.Summary.Cell index={0} align="center">
                    <Text strong>小计</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1} align="center">
                    -
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2} align="center">
                    -
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3} align="center">
                    <Text style={{ color: '#1890ff', fontWeight: 'bold' }}>
                      {formatAmount(mergedExpenseData.total1)}
                    </Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={4} align="center">
                    -
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={5} align="center">
                    -
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={6} align="center">
                    <Text style={{ color: '#52c41a', fontWeight: 'bold' }}>
                      {formatAmount(mergedExpenseData.total2)}
                    </Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={7} align="center">
                    {(() => {
                      const diff = mergedExpenseData.total2 - mergedExpenseData.total1

                      if (diff === 0) {
                        return <Text style={{ color: '#666' }}>-</Text>
                      }

                      const percentage =
                        mergedExpenseData.total1 > 0 ? ((diff / mergedExpenseData.total1) * 100).toFixed(1) : '∞'
                      const color = diff > 0 ? '#ff4d4f' : '#52c41a'

                      return (
                        <Space direction="vertical" size={2} style={{ textAlign: 'center' }}>
                          <Text style={{ color, fontWeight: 'bold' }}>
                            {diff > 0 ? '+' : ''}
                            {formatAmount(diff)}
                          </Text>
                          {mergedExpenseData.total1 > 0 && (
                            <Text style={{ color, fontSize: '12px' }}>
                              {diff > 0 ? '+' : ''}
                              {percentage}%
                            </Text>
                          )}
                        </Space>
                      )
                    })()}
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              )}
            />
          </Card>
        </Panel>
      </Collapse>
    </Flex>
  )
}

export default ProductionBudgetComparison
