import { ACTOR_ROLE_TYPE_OPTIONS, RECRUITMENT_PARENT_TYPE_OPTIONS, ROLE_TYPE_OPTIONS_SELECT } from '@/consts'
import PersonSelector from '@/pages/production/person/components/PersonSelector'
import { Col, Form, Modal, Row, Select, Switch, message } from 'antd'
import React, { useState } from 'react'
import useProjectListStore, { IPrProductionAllPerson } from '../store'

interface IAddAllPersonModalProps {
  open: boolean
  productionId: number
  onCancel: () => void
  onSuccess: () => void
}

const AddAllPersonModal: React.FC<IAddAllPersonModalProps> = ({ open, productionId, onCancel, onSuccess }) => {
  const [form] = Form.useForm()
  const [saving, setSaving] = useState(false)
  const { saveProductionAllPerson } = useProjectListStore()
  const formParentType = Form.useWatch('parentType', form)
  const formRoleType = Form.useWatch('roleType', form)

  // 处理保存
  const handleSave = async () => {
    try {
      const values = await form.validateFields()

      setSaving(true)

      const newPerson: IPrProductionAllPerson = {
        productionId,
        personId: values.personId,
        parentType: values.parentType,
        roleType: values.roleType,
        isMeal: values.isMeal || false,
        isAccommodation: values.isAccommodation || false,
      }

      const success = await saveProductionAllPerson({
        productionId,
        allPerson: [newPerson],
      })

      if (success) {
        message.success('成功添加人员')
        form.resetFields()
        onSuccess()
      } else {
        message.error('添加失败')
      }
    } catch (error) {
      console.error('添加人员失败:', error)
      message.error('添加失败')
    } finally {
      setSaving(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title="添加项目组人员"
      open={open}
      onCancel={handleCancel}
      onOk={handleSave}
      confirmLoading={saving}
      width={800}
      destroyOnHidden>
      <Form form={form} layout="vertical">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="parentType" label="人员类型" rules={[{ required: true, message: '请选择人员类型' }]}>
              <Select
                placeholder="请选择人员类型"
                options={RECRUITMENT_PARENT_TYPE_OPTIONS}
                onChange={() => {
                  form.setFieldValue('roleType', undefined)
                  form.setFieldValue('personId', undefined)
                }}></Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name="roleType" label="角色类型" rules={[{ required: true, message: '请输入角色类型' }]}>
              <Select
                disabled={!formParentType}
                showSearch
                allowClear
                options={formParentType == 2 ? ACTOR_ROLE_TYPE_OPTIONS : ROLE_TYPE_OPTIONS_SELECT}></Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="personId" label="人员" rules={[{ required: true, message: '请选择人员' }]}>
              <PersonSelector
                disabled={!formRoleType || !formParentType}
                // onSelect={(val, opt) => handlePersonSelect(val, opt, name)}
              />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="isMeal" label="提供餐食" valuePropName="checked">
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="isAccommodation" label="提供住宿" valuePropName="checked">
              <Switch checkedChildren="是" unCheckedChildren="否" />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  )
}

export default AddAllPersonModal
