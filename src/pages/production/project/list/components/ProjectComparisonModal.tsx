import filterMatch from '@/utils/filterMatch'
import { But<PERSON>, Drawer, Flex, Form, message, Select, Typography } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import useProjectListStore from '../store'
import MultiProjectBudgetComparison from './MultiProjectBudgetComparison'

const { Title } = Typography

interface IProjectComparisonModalProps {
  open: boolean
  onClose: () => void
  defaultBaseProjectId?: number // 基准项目ID
}

const ProjectComparisonModal: React.FC<IProjectComparisonModalProps> = ({ open, onClose, defaultBaseProjectId }) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [comparing, setComparing] = useState(false)
  const [baseProjectId, setBaseProjectId] = useState<number | null>(defaultBaseProjectId || null)
  const [compareProjectIds, setCompareProjectIds] = useState<number[]>([])

  const { allProductionOptions, getAllProductionNames } = useProjectListStore()

  // 监听基准项目变化
  const baseProject = Form.useWatch('baseProject', form)

  // 计算对比项目的选项（排除基准项目）
  const compareProjectOptions = useMemo(
    () => allProductionOptions.filter(option => option.val !== baseProject),
    [allProductionOptions, baseProject]
  )

  // 加载项目选项数据
  const loadProjectOptions = async () => {
    if (allProductionOptions.length === 0) {
      setLoading(true)
      try {
        await getAllProductionNames()
      } catch (error) {
        console.error('加载项目选项失败:', error)
        message.error('加载项目选项失败')
      } finally {
        setLoading(false)
      }
    }
  }

  // 开始对比
  const handleStartComparison = () => {
    form
      .validateFields()
      .then(values => {
        const { baseProject, compareProjects = [] } = values

        if (!baseProject) {
          message.error('请选择基准项目')

          return
        }

        if (compareProjects.length === 0) {
          message.error('请至少选择一个对比项目')

          return
        }

        // 检查是否有重复项目
        const allProjects = [baseProject, ...compareProjects]
        const uniqueProjects = [...new Set(allProjects)]

        if (allProjects.length !== uniqueProjects.length) {
          message.warning('请确保基准项目与对比项目不重复')

          return
        }

        setBaseProjectId(parseInt(baseProject, 10))
        setCompareProjectIds(compareProjects.map((id: string) => parseInt(id, 10)))
        setComparing(true)
      })
      .catch(() => {
        message.error('请完整填写项目信息')
      })
  }

  // 重新选择
  const handleReselect = () => {
    setComparing(false)
    setBaseProjectId(null)
    setCompareProjectIds([])
    form.resetFields()
  }

  // 关闭侧边栏
  const handleClose = () => {
    setComparing(false)
    setBaseProjectId(null)
    setCompareProjectIds([])
    form.resetFields()
    onClose()
  }

  useEffect(() => {
    if (open) {
      loadProjectOptions()

      // 设置默认值
      if (defaultBaseProjectId) {
        form.setFieldsValue({
          baseProject: `${defaultBaseProjectId}`,
          compareProjects: [],
        })
      }
    }
  }, [open, defaultBaseProjectId])

  return (
    <Drawer
      title="结算对比"
      open={open}
      onClose={handleClose}
      width="64vw"
      destroyOnHidden
      styles={{ body: { padding: '24px' } }}
      extra={
        comparing && (
          <Button className="text-primary" onClick={handleReselect}>
            重新选择
          </Button>
        )
      }>
      {comparing === false ? (
        // 项目选择阶段
        <Flex vertical gap={24} style={{ maxWidth: 800, margin: '0 auto' }}>
          <Title level={4}>选择要对比的项目</Title>

          <Form
            form={form}
            layout="vertical"
            initialValues={{
              baseProject: defaultBaseProjectId ? `${defaultBaseProjectId}` : null,
              compareProjects: [],
            }}>
            {/* 基准项目选择 */}
            <Form.Item label="当前项目" name="baseProject" rules={[{ required: true, message: '请选择基准项目' }]}>
              <Select
                placeholder="请选择基准项目"
                options={allProductionOptions}
                loading={loading}
                showSearch
                fieldNames={{ label: 'labelStr', value: 'val' }}
                filterOption={(inputValue, option) => {
                  const result = filterMatch(inputValue, option?.labelStr || '')

                  return Boolean(result)
                }}
                size="large"
              />
            </Form.Item>

            {/* 对比项目选择 */}
            <Form.Item
              label={'对比项目'}
              name="compareProjects"
              rules={[{ required: true, message: '请至少选择一个对比项目' }]}>
              <Select
                mode="multiple"
                placeholder="请选择对比项目（可多选）"
                options={compareProjectOptions}
                loading={loading}
                showSearch
                maxCount={10}
                fieldNames={{ label: 'labelStr', value: 'val' }}
                filterOption={(inputValue, option) => {
                  const result = filterMatch(inputValue, option?.labelStr || '')

                  return Boolean(result)
                }}
                size="large"
                maxTagCount="responsive"
              />
            </Form.Item>
          </Form>

          <Flex justify="end" gap={12}>
            <Button onClick={handleClose}>取消</Button>
            <Button type="primary" onClick={handleStartComparison} loading={loading}>
              开始对比
            </Button>
          </Flex>
        </Flex>
      ) : (
        // 对比结果展示阶段
        <Flex vertical gap={16}>
          {baseProjectId && (
            <MultiProjectBudgetComparison baseProjectId={baseProjectId} compareProjectIds={compareProjectIds} />
          )}
        </Flex>
      )}
    </Drawer>
  )
}

export default ProjectComparisonModal
