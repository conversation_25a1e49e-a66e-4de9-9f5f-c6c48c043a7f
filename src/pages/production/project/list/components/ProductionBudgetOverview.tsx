import { Fieldset } from '@fe/rockrose'
import { Card, Descriptions, Empty, Flex, Spin, Table, Typography } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'

import useProjectListStore, { IProductionBudgetData, IProductionListItem } from '../store'

const { Text } = Typography

interface IProductionBudgetOverviewProps {
  productionId: number
  loading?: boolean
  project?: IProductionListItem
  isBudgetMode?: boolean // 是否为预算模式（true为预算，false为结算）
}

// 人员类型枚举映射
const ROLE_TYPE_MAP: Record<string, string> = {
  制片: 'production',
  导演: 'director',
  摄影: 'photography',
  收音: 'sound',
  后期: 'postproduction',
  灯光: 'lighting',
  服化: 'costume',
  美术: 'art',
  器材类: 'equipment',
  演员: 'actor',
}

const ProductionBudgetOverview: React.FC<IProductionBudgetOverviewProps> = ({
  productionId,
  loading = false,
  project,
  isBudgetMode = false,
}) => {
  const [budgetData, setBudgetData] = useState<IProductionBudgetData | null>(null)
  const [loadingData, setLoadingData] = useState(false)

  const { getProductionBudgetData, getBudgetByProductionId } = useProjectListStore()

  useEffect(() => {
    if (productionId) {
      loadBudgetData()
    }
  }, [productionId])

  // 加载预算/结算数据
  const loadBudgetData = async () => {
    setLoadingData(true)
    try {
      const result = isBudgetMode
        ? await getBudgetByProductionId(productionId)
        : await getProductionBudgetData(productionId)

      setBudgetData(result)
    } catch (error) {
      console.error(`加载${isBudgetMode ? '预算' : '结算'}数据失败:`, error)
    } finally {
      setLoadingData(false)
    }
  }

  // 按部门分组人员数据（排除小计行）
  const groupedPersonData = useMemo(() => {
    if (!budgetData?.person?.length) {
      return {}
    }

    return budgetData.person.reduce((groups: Record<string, any[]>, person: any) => {
      // 跳过小计行，只处理具体人员
      if (person.minRoleType === '小计') {
        return groups
      }

      const department = person.maxRoleType || '其他'

      if (!groups[department]) {
        groups[department] = []
      }
      groups[department].push(person)

      return groups
    }, {})
  }, [budgetData])

  // 获取各部门小计数据
  const getDepartmentSubtotal = (department: string) => {
    const subtotalItem = budgetData?.person?.find(
      (item: any) => item.maxRoleType === department && item.minRoleType === '小计'
    )

    return subtotalItem ? parseFloat(subtotalItem.allPriceStr || '0') : 0
  }

  // 计算总额
  const totals = useMemo(() => {
    const venueTotal = parseFloat(budgetData?.venueTotal || '0')
    const personTotal = parseFloat(budgetData?.personTotal || '0')
    const expenseTotal = parseFloat(budgetData?.allExpensesPersonTotal || '0')
    const allTotal = parseFloat(budgetData?.allTotal || '0')
    const taxTotal = parseFloat(budgetData?.secondAllTotal || '0')

    return {
      venueTotal,
      personTotal,
      expenseTotal,
      allTotal,
      taxTotal,
    }
  }, [budgetData])

  // 场地费用表格列配置
  const venueColumns = [
    {
      title: '项目',
      dataIndex: 'minRoleTypeStr',
      key: 'minRoleTypeStr',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '数量',
      dataIndex: 'personCountStr',
      key: 'personCountStr',
      width: 80,
      align: 'center' as const,
      render: (text: string) => text || '1',
    },
    {
      title: '拍摄天数',
      dataIndex: 'dayCountStr',
      key: 'dayCountStr',
      width: 150,
      align: 'center' as const,
    },
    {
      title: '单价',
      dataIndex: 'priceStr',
      key: 'priceStr',
      width: 100,
      align: 'center' as const,
      render: (text: string) => (text ? `${project?.currencySymbol || '¥'}${parseFloat(text).toLocaleString()}` : '-'),
    },

    {
      title: '总金额',
      dataIndex: 'allPriceStr',
      key: 'allPriceStr',
      width: 120,
      align: 'center' as const,
      render: (text: string) => (
        <Text>{text ? `${project?.currencySymbol || '¥'}${parseFloat(text).toLocaleString()}` : '-'}</Text>
      ),
    },
    {
      title: '有无发票',
      dataIndex: 'hasInvoiceStr',
      key: 'hasInvoiceStr',
      width: 120,
      align: 'center' as const,
      render: (text: string) => text || '-',
    },
  ]

  // 人员表格列配置
  const personColumns = [
    {
      title: '项目',
      dataIndex: 'minRoleTypeStr',
      key: 'minRoleTypeStr',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '数量',
      dataIndex: 'personCountStr',
      key: 'personCountStr',
      width: 80,
      align: 'center' as const,
      render: (text: string) => text || '1',
    },
    {
      title: '拍摄天数',
      dataIndex: 'dayCountStr',
      key: 'dayCountStr',
      width: 150,
      align: 'center' as const,
    },
    {
      title: '单价',
      dataIndex: 'priceStr',
      key: 'priceStr',
      width: 100,
      align: 'center' as const,
      render: (text: string) => (text ? `${project?.currencySymbol || '¥'}${parseFloat(text).toLocaleString()}` : '-'),
    },

    {
      title: '总金额',
      dataIndex: 'allPriceStr',
      key: 'allPriceStr',
      width: 120,
      align: 'center' as const,
      render: (text: string) => (
        <Text>{text ? `${project?.currencySymbol || '¥'}${parseFloat(text).toLocaleString()}` : '-'}</Text>
      ),
    },
    {
      title: '有无发票',
      dataIndex: 'hasInvoiceStr',
      key: 'hasInvoiceStr',
      width: 120,
      align: 'center' as const,
      render: (text: string) => text || '-',
    },
  ]

  // 制片费用表格列配置
  const expenseColumns = [
    {
      title: '项目',
      dataIndex: 'minRoleTypeStr',
      key: 'minRoleTypeStr',
      width: 120,
      align: 'center' as const,
    },
    {
      title: '数量',
      dataIndex: 'personCountStr',
      key: 'personCountStr',
      width: 80,
      align: 'center' as const,
      render: (text: string) => text || '1',
    },
    {
      title: '拍摄天数',
      dataIndex: 'dayCountStr',
      key: 'dayCountStr',
      width: 150,
      align: 'center' as const,
    },
    {
      title: '单价',
      dataIndex: 'priceStr',
      key: 'priceStr',
      width: 100,
      align: 'center' as const,
      render: (text: string) => (text ? `${project?.currencySymbol || '¥'}${parseFloat(text).toLocaleString()}` : '-'),
    },

    {
      title: '总金额',
      dataIndex: 'allPriceStr',
      key: 'allPriceStr',
      width: 120,
      align: 'center' as const,
      render: (text: string) => (
        <Text>{text ? `${project?.currencySymbol || '¥'}${parseFloat(text).toLocaleString()}` : '-'}</Text>
      ),
    },
    {
      title: '有无发票',
      dataIndex: 'hasInvoiceStr',
      key: 'hasInvoiceStr',
      width: 120,
      align: 'center' as const,
      render: (text: string) => text || '-',
    },
  ]

  if (loadingData || loading) {
    return (
      <Flex justify="center" align="center" style={{ minHeight: 200 }}>
        <Spin size="large" />
      </Flex>
    )
  }

  if (!budgetData) {
    return <Empty description="暂无结算数据" />
  }

  return (
    <Flex vertical gap={24}>
      <Flex vertical gap={8}>
        <Flex justify="space-between" align="center">
          <Typography.Text strong className="fs-lg">
            总计
          </Typography.Text>
        </Flex>

        <Descriptions size="small" bordered column={1} className="full-h">
          <Descriptions.Item label="总计（不含税）">
            <Flex justify="space-between" align="center">
              <Text>{budgetData?.allTotalStr || '-'}</Text>
              <Text type="danger" strong className="fs-lg">
                {project?.currencySymbol || '¥'}
                {totals.allTotal.toLocaleString()}
              </Text>
            </Flex>
          </Descriptions.Item>
          <Descriptions.Item label="总计（含税点6.72）">
            <Flex justify="space-between" align="center">
              <Text>{budgetData?.secondAllTotalStr || '-'}</Text>
              <Text type="danger" strong className="fs-lg">
                {project?.currencySymbol || '¥'}
                {totals.taxTotal.toLocaleString()}
              </Text>
            </Flex>
          </Descriptions.Item>
        </Descriptions>
      </Flex>
      <Flex vertical gap={8}>
        <Flex justify="space-between" align="center">
          <Typography.Text strong>场地费用</Typography.Text>
          <Typography.Text type="danger" strong className="fs-lg">
            {project?.currencySymbol || '¥'}
            {totals.venueTotal.toLocaleString()}
          </Typography.Text>
        </Flex>
        <Card size="small">
          <Table
            columns={venueColumns}
            dataSource={budgetData?.venue || []}
            pagination={false}
            size="small"
            rowKey={(record, index) => `venue-${index}`}
            summary={() => (
              <Table.Summary.Row className="bg-secondary">
                <Table.Summary.Cell index={0} align="center">
                  <Text strong>小计</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1}></Table.Summary.Cell>
                <Table.Summary.Cell index={2}></Table.Summary.Cell>
                <Table.Summary.Cell index={3}></Table.Summary.Cell>
                <Table.Summary.Cell index={4} align="center">
                  <Text strong>
                    {project?.currencySymbol || '¥'}
                    {totals.venueTotal.toLocaleString()}
                  </Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5}></Table.Summary.Cell>
              </Table.Summary.Row>
            )}
          />
        </Card>
      </Flex>
      <Flex vertical gap={18}>
        <Flex justify="space-between" align="center">
          <Typography.Text strong>人工费用</Typography.Text>
          <Typography.Text type="danger" strong className="fs-lg">
            {project?.currencySymbol || '¥'}
            {totals.personTotal.toLocaleString()}
          </Typography.Text>
        </Flex>
        <Flex vertical gap={24}>
          {Object.entries(groupedPersonData).map(([department, persons]) => {
            const personsArray = persons as any[]
            const departmentTotal = getDepartmentSubtotal(department)

            return (
              <Fieldset key={department} title={department}>
                <Table
                  columns={personColumns}
                  dataSource={personsArray}
                  pagination={false}
                  size="small"
                  className="full-h"
                  rowKey={(record, index) => `${department}-${index}`}
                  summary={() => (
                    <Table.Summary.Row className="bg-secondary">
                      <Table.Summary.Cell index={0} align="center">
                        <Text strong>小计</Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={1} align="center"></Table.Summary.Cell>
                      <Table.Summary.Cell index={2} align="center"></Table.Summary.Cell>
                      <Table.Summary.Cell index={3} align="center"></Table.Summary.Cell>
                      <Table.Summary.Cell index={4} align="center">
                        <Text strong>
                          {project?.currencySymbol || '¥'}
                          {departmentTotal.toLocaleString()}
                        </Text>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={5} align="center"></Table.Summary.Cell>
                    </Table.Summary.Row>
                  )}
                />
              </Fieldset>
            )
          })}
        </Flex>
      </Flex>
      <Flex vertical gap={8}>
        <Flex justify="space-between" align="center">
          <Typography.Text strong>制片费用</Typography.Text>
          <Typography.Text type="danger" strong className="fs-lg">
            {project?.currencySymbol || '¥'}
            {totals.expenseTotal.toLocaleString()}
          </Typography.Text>
        </Flex>
        <Card size="small">
          <Table
            columns={expenseColumns}
            dataSource={budgetData?.expense || []}
            pagination={false}
            size="small"
            rowKey={(record, index) => `expense-${index}`}
            summary={() => (
              <Table.Summary.Row className="bg-secondary">
                <Table.Summary.Cell index={0} align="center">
                  <Text strong>小计</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1} align="center"></Table.Summary.Cell>
                <Table.Summary.Cell index={2} align="center"></Table.Summary.Cell>
                <Table.Summary.Cell index={3} align="center"></Table.Summary.Cell>
                <Table.Summary.Cell index={4} align="center">
                  <Text strong>
                    {project?.currencySymbol || '¥'}
                    {totals.expenseTotal.toLocaleString()}
                  </Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5} align="center"></Table.Summary.Cell>
              </Table.Summary.Row>
            )}
          />
        </Card>
      </Flex>
    </Flex>
  )
}

export default ProductionBudgetOverview
