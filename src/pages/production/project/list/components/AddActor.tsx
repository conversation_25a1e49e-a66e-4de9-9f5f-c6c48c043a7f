import { ACTOR_ROLE_TYPE_CONFIG, ACTOR_ROLE_TYPE_OPTIONS, ActorRoleType, PRICE_CURRENCY_OPTIONS } from '@/consts'
import ActorSelector from '@/pages/production/actor/components/ActorSelector'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Card, Flex, Form, Input, InputNumber, Modal, Select, type FormInstance } from 'antd'
import React from 'react'
import { IProductionListItem } from '../store'

interface AddActorProps {
  form: FormInstance
  open?: boolean
  loading?: boolean
  multiple?: boolean
  project?: IProductionListItem
  onOk: (values: any) => void
  onCancel: () => void
}

// 添加演员
const AddActor: React.FC<AddActorProps> = ({ form, open, multiple = true, loading, project, onOk, onCancel }) => {
  // 监听整个actorList的变化
  const actorList = Form.useWatch('actorList', form) || []

  // 处理演员选择（批量添加）
  const handleActorSelect = (actorId: number, option: any, fieldName: number) => {
    // 自动填充演员信息
    if (actorId) {
      form.setFieldValue(['actorList', fieldName, 'headUrl'], option.headUrl)
      form.setFieldValue(['actorList', fieldName, 'personName'], option.personName)
      form.setFieldValue(['actorList', fieldName, 'stageName'], option.stageName)
      form.setFieldValue(['actorList', fieldName, 'isInternal'], option.isInternal)
      form.setFieldValue(['actorList', fieldName, 'gender'], option.gender)
    }
  }

  const handleRoleTypeSelect = (val: number, opt: any, fieldName: number) => {
    if (ACTOR_ROLE_TYPE_CONFIG?.[val]?.isAbstract) {
      form.setFieldValue(['actorList', fieldName, 'headUrl'], '')
      form.setFieldValue(['actorList', fieldName, 'personName'], ACTOR_ROLE_TYPE_CONFIG?.[val]?.label)
      form.setFieldValue(['actorList', fieldName, 'stageName'], ACTOR_ROLE_TYPE_CONFIG?.[val]?.label)
      form.setFieldValue(['actorList', fieldName, 'actorId'], 0)
    } else {
      const actorId = form.getFieldValue(['actorList', fieldName, 'actorId'])

      if (actorId == 0) {
        form.setFieldValue(['actorList', fieldName, 'actorId'], null)
      }
    }
  }

  return (
    <Modal title="添加演员" open={open} onCancel={onCancel} footer={null} width={750}>
      <Form
        form={form}
        colon={false}
        layout="horizontal"
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 18 }}
        onFinish={onOk}
        initialValues={{ actorList: [{ roleType: ActorRoleType.Extra, priceCurrency: 0, hasInvoice: false }] }}>
        <Form.List name="actorList">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => {
                // 从actorList中获取当前行的roleType
                const isAbstract = !!ACTOR_ROLE_TYPE_CONFIG?.[actorList[name]?.roleType]?.isAbstract

                const formItems = (
                  <>
                    <Form.Item
                      {...restField}
                      name={[name, 'roleType']}
                      label="角色类型"
                      rules={[{ required: true, message: '请选择角色类型' }]}
                      initialValue={ActorRoleType.Extra}>
                      <Select
                        placeholder="请选择角色类型"
                        options={ACTOR_ROLE_TYPE_OPTIONS}
                        onSelect={(val, opt) => handleRoleTypeSelect(val, opt, name)}
                      />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'actorId']}
                      label="演员"
                    // rules={isAbstract ? [] : [{ required: true, message: '请选择演员' }]}
                    // hidden={isAbstract}
                    >
                      <ActorSelector
                        onSelect={(actorId, option) => handleActorSelect(actorId, option, name)}
                      // disabled={isAbstract}
                      />
                    </Form.Item>
                    {/* 隐藏字段存储演员信息 */}
                    <Form.Item {...restField} name={[name, 'headUrl']} hidden></Form.Item>
                    <Form.Item {...restField} name={[name, 'personName']} hidden></Form.Item>
                    <Form.Item {...restField} name={[name, 'stageName']} hidden></Form.Item>
                    <Form.Item {...restField} name={[name, 'isInternal']} hidden></Form.Item>
                    <Form.Item {...restField} name={[name, 'gender']} hidden></Form.Item>
                    <Form.Item {...restField} name={[name, 'playRole']} label="饰演角色" hidden={isAbstract}>
                      <Input placeholder="请输入饰演角色" />
                    </Form.Item>
                    <Form.Item
                      {...restField}
                      name={[name, 'personCount']}
                      label="人数"
                      hidden={!isAbstract}
                      initialValue={1}>
                      <InputNumber className="full-h" placeholder="请输入人数" min={1} precision={0} />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'dayCount']} label="天数" initialValue={1}>
                      <InputNumber className="full-h" placeholder="请输入天数" min={1} precision={0} />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'quotedPrice']} label="单价">
                      <InputNumber className="full-h" placeholder="请输入单价" prefix={project?.currencySymbol} />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'totalPrice']} label="总价">
                      <InputNumber
                        className="full-h"
                        placeholder="请输入总价"
                        min={0}
                        precision={2}
                        prefix={project?.currencySymbol}
                      />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'hasInvoice']} label="是否正规发票" initialValue={false}>
                      <Select
                        placeholder="请选择"
                        options={[
                          { value: true, label: '是' },
                          { value: false, label: '否' },
                        ]}
                      />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'priceCurrency']} label="货币类型" initialValue={1} hidden>
                      <Select options={PRICE_CURRENCY_OPTIONS} />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'description']} label="备注">
                      <Input.TextArea rows={5} placeholder="请输入备注信息" />
                    </Form.Item>
                  </>
                )

                if (!multiple) {
                  return formItems
                }

                return (
                  <Card
                    key={key}
                    size="small"
                    style={{ marginBottom: 16 }}
                    title={`演员 ${name + 1}`}
                    className="no-padding-b"
                    extra={
                      fields.length > 1 && (
                        <Button type="text" icon={<DeleteOutlined />} onClick={() => remove(name)}></Button>
                      )
                    }>
                    {formItems}
                  </Card>
                )
              })}
              {multiple && (
                <Form.Item>
                  <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                    添加演员
                  </Button>
                </Form.Item>
              )}
            </>
          )}
        </Form.List>

        <Form.Item className="no-margin" label=" ">
          <Flex justify="flex-end" gap={12}>
            <Button onClick={onCancel}>取消</Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              立即添加
            </Button>
          </Flex>
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default AddActor
