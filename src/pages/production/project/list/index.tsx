import { PAGINATION } from '@/consts'
import useSyncParams, { initFormFromUrlState, parsePagination } from '@/hooks/useSyncParams'
import { Flex, Form, message } from 'antd'
import React, { useEffect, useState } from 'react'
import { SceneAnnotationSidebar } from '../components'
import ProjectForm from './components/Add'
import ProjectDetail from './components/Detail'
import List from './components/List'
import ProjectComparisonModal from './components/ProjectComparisonModal'
import Search from './components/Search'
import useProjectListStore, { IProductionListItem, IProductionListSearchParams } from './store'
import { useNavigate } from 'react-router-dom'

const ProjectInfo: React.FC = () => {
  const [urlState, setUrlState] = useSyncParams<IProductionListSearchParams>()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<IProductionListItem[]>([])
  const [pagination, setPagination] = useState(PAGINATION)

  const [show, setShow] = useState(false)
  const [operateData, setOperateData] = useState<any | IProductionListItem>()
  const [operateType, setOperateType] = useState<any | IOperateType>()

  // 结算对比相关状态
  const [showComparison, setShowComparison] = useState(false)
  const [defaultProjectId, setDefaultProjectId] = useState<number | null>(null)

  // 剧本标注相关状态
  const [showAnnotation, setShowAnnotation] = useState(false)
  const [annotationProductionId, setAnnotationProductionId] = useState<number | null>(null)
  const [annotationProductionName, setAnnotationProductionName] = useState<string | null>(null)

  const { fetchProductionList, saveProduction, deleteProduction } = useProjectListStore()

  const navigate = useNavigate()
  const handleDetail = (item: IProductionListItem) => {
    navigate(`/production/project/${item.id}`)
  }
  // 初始化url数据
  const initParams = () => {
    // 使用工具函数简化表单初始化
    initFormFromUrlState(urlState, form, { numberArrayFields: ['status'],numberFields:['productionType'] })

    // 使用工具函数处理分页
    const { current, pageSize } = parsePagination(urlState, {
      current: pagination.current,
      pageSize: pagination.pageSize,
    })

    handleSearch(current, pageSize)
  }
  const handleSearch = async (current = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true)
    try {
      const values = form.getFieldsValue()
      const searchParams: IProductionListSearchParams = {
        pageIndex: current,
        pageSize,
        verifyPermissions: true,
        ...values,
      }

      const result = await fetchProductionList(searchParams)

      if (result) {
        setDataSource(result.list)
        setPagination(prev => ({
          ...prev,
          total: result.total,
          current: result.pageIndex,
          pageSize: result.pageSize,
        }))
        setUrlState({
          productionName: values.productionName || '',
          status: values.status || [],
          productionType: values.productionType || [],
          pageSize: result.pageSize,
          pageIndex: result.pageIndex,
        })
      } else {
        setDataSource([])
      }
    } catch (error) {
      setDataSource([])
    } finally {
      setLoading(false)
    }
  }

  const handleOperate = (type: IOperateType, data?: IProductionListItem) => {
    if (type === 'delete') {
      data && handleDelete(data)
    } else if (type == 'view') {
      data && handleDetail(data)
    }
    else if (['create', 'edit'].includes(type)) {
      setOperateData(data || null)
      setOperateType(type)
      setShow(true)
    }
  }

  const handleOperateClose = () => {
    setOperateData(null)
    setOperateType('')
    setShow(false)
  }

  // 表单提交
  const handleFormSubmit = async (values: IProductionListItem) => {
    try {
      
      const result = await saveProduction(values)

      if (result) {
        message.success(operateType == 'edit' ? '更新成功' : '新增成功')
        handleOperateClose()
        await handleSearch(1)
      } else {
        message.error('操作失败')
      }
    } catch (error) {
      message.error('操作失败')
    }
  }

  // 删除项目
  const handleDelete = async (project: IProductionListItem) => {
    if (!project.id) {
      message.error('项目信息不完整')

      return
    }

    try {
      const result = await deleteProduction(project.id)

      if (result) {
        message.success('删除成功')
        handleOperateClose() // 关闭详情抽屉
        await handleSearch(1) // 刷新列表，回到第一页
      } else {
        message.error('删除失败，请重试')
      }
    } catch (error) {
      console.error('删除项目失败:', error)
      message.error('删除失败，请重试')
    }
  }

  // 从详情页面编辑
  const handleEditFromDetail = (project: IProductionListItem) => {
    setOperateData(project)
    setOperateType('edit')
    // 保持抽屉打开状态，但切换到编辑模式
  }

  // 从详情页面删除
  const handleDeleteFromDetail = (project: IProductionListItem) => {
    handleDelete(project)
  }

  // 打开结算对比弹窗
  const handleOpenComparison = (currentProjectId?: number) => {
    handleOperateClose()
    setDefaultProjectId(currentProjectId || null)
    setShowComparison(true)
  }

  // 关闭结算对比弹窗
  const handleCloseComparison = () => {
    setDefaultProjectId(null)
    setShowComparison(false)
  }

  // 打开剧本标注侧边栏
  const handleOpenAnnotation = (productionId: number, productionName?: string) => {
    handleOperateClose()
    setAnnotationProductionId(productionId)
    setAnnotationProductionName(productionName || null)
    setShowAnnotation(true)
  }

  // 关闭剧本标注侧边栏
  const handleCloseAnnotation = () => {
    setAnnotationProductionId(null)
    setAnnotationProductionName(null)
    setShowAnnotation(false)
  }

  useEffect(() => {
    initParams()
    // handleSearch()
  }, [])

  return (
    <Flex vertical>
      <Search form={form} onSearch={handleSearch} onReset={() => handleSearch(1)} />
      <List
        data={dataSource}
        loading={loading}
        onOperate={handleOperate}
        onChange={handleSearch}
        pagination={pagination}
      />

      {/* 项目详情抽屉 */}
      <ProjectDetail
        open={show && operateType === 'view'}
        project={operateData ?? void 0}
        onClose={handleOperateClose}
        onEdit={handleEditFromDetail}
        onDelete={handleDeleteFromDetail}
        onOpenComparison={handleOpenComparison}
        onOpenAnnotation={handleOpenAnnotation}
      />

      {/* 项目表单模态框 */}
      <ProjectForm
        open={show && ['edit', 'create'].includes(operateType)}
        project={operateData ?? void 0}
        onCancel={handleOperateClose}
        onSubmit={handleFormSubmit}
      />

      {/* 结算对比弹窗 */}
      <ProjectComparisonModal
        open={showComparison}
        onClose={handleCloseComparison}
        {...(defaultProjectId && { defaultBaseProjectId: defaultProjectId })}
      />

      {/* 剧本标注侧边栏 */}
      {annotationProductionId && (
        <SceneAnnotationSidebar
          productionId={annotationProductionId}
          {...(annotationProductionName && { productionName: annotationProductionName })}
          visible={showAnnotation}
          onClose={handleCloseAnnotation}
        />
      )}
    </Flex>
  )
}

export default ProjectInfo
