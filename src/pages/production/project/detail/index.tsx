import { Skeleton, Tabs, Typography, message } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import ProjectForm from '../list/components/Add'
import useProjectListStore, { IProductionListItem } from '../list/store'
import FinanceManagementTab from './components/FinanceManagementTab'
import NewOverviewTab from './components/NewOverviewTab'
import ProductionPersonTab from './components/ProductionPersonTab'
import RecruitmentManagementTab from './components/RecruitmentManagementTab'
import ShootingManagementTab from './components/ShootingManagementTab'
import VenueManagementTab from './components/VenueManagementTab'
import styles from './index.scss'

const ProjectDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { getProductionById, saveProduction, deleteProduction } = useProjectListStore()

  const [activeKey, setActiveKey] = useState('overview')
  const [currentProject, setCurrentProject] = useState<IProductionListItem | undefined>()

  // 编辑表单相关状态
  const [showEditForm, setShowEditForm] = useState(false)

  // 加载项目数据
  useEffect(() => {
    if (id) {
      loadProjectData(parseInt(id))
    }
  }, [id])

  const loadProjectData = async (projectId: number) => {
    try {
      const project = await getProductionById(projectId)
      if (project) {
        setCurrentProject(project)
      } else {
        message.error('项目不存在')
        navigate('/production/project')
      }
    } catch (error) {
      console.error('加载项目数据失败:', error)
      message.error('加载项目数据失败')
      navigate('/production/project')
    }
  }

  // 处理编辑
  const handleEdit = (project: IProductionListItem) => {
    setCurrentProject(project)
    setShowEditForm(true)
  }

  // 处理删除
  const handleDelete = async (project: IProductionListItem) => {
    if (!project.id) {
      message.error('项目信息不完整')
      return
    }

    try {
      const result = await deleteProduction(project.id)
      if (result) {
        message.success('删除成功')
        navigate('/production/project')
      }
    } catch (error) {
      console.error('删除项目失败:', error)
      message.error('删除失败，请重试')
    }
  }

  // 处理表单提交
  const handleFormSubmit = async (values: IProductionListItem) => {
    try {
      const result = await saveProduction(values)
      if (result) {
        message.success('更新成功')
        setShowEditForm(false)
        // 重新加载项目数据
        if (id) {
          await loadProjectData(parseInt(id))
        }
      } else {
        message.error('操作失败')
      }
    } catch (error) {
      message.error('操作失败')
    }
  }

  const tabItems = useMemo(() => {
    const allItems = [
      {
        key: 'overview',
        label: '项目概览',
        // icon: <ProjectOutlined />,
        children:
          activeKey === 'overview' ? (
            <div className={styles.scrollC}>
              <NewOverviewTab project={currentProject} onEdit={handleEdit} onDelete={handleDelete} />
            </div>
          ) : null,
      },
      {
        key: 'person',
        label: '项目人员',
        // icon: <TeamOutlined />,
        children:
          currentProject?.id && activeKey === 'person' ? (
            <div className={styles.scrollC}>
              <ProductionPersonTab
                productionId={currentProject.id}
                projectName={currentProject.productionName}
                project={currentProject}
              />
            </div>
          ) : null,
      },
      {
        key: 'venue',
        label: '拍摄场地',
        // icon: <EnvironmentOutlined />,
        children:
          currentProject?.id && activeKey === 'venue' ? (
            <div className={styles.scrollC}>
              <VenueManagementTab productionId={currentProject.id} project={currentProject} />
            </div>
          ) : null,
      },
      {
        key: 'finance',
        label: '财务管理',
        // icon: <DollarOutlined />,
        children:
          currentProject?.id && activeKey === 'finance' ? (
            <div className={styles.scrollC}>
              <FinanceManagementTab productionId={currentProject.id} project={currentProject} />
            </div>
          ) : null,
      },

      {
        key: 'recruitment',
        label: '招募管理',
        // icon: <SolutionOutlined />,
        children:
          currentProject?.id && activeKey === 'recruitment' ? (
            <div className={styles.scrollC}>
              <RecruitmentManagementTab
                productionId={currentProject.id}
                projectName={currentProject.productionName}
                project={currentProject}
              />
            </div>
          ) : null,
      },
      {
        key: 'shooting',
        label: '拍摄管理',
        // icon: <VideoCameraOutlined />,
        children:
          currentProject?.id && activeKey === 'shooting' ? (
            <div className={styles.scrollC}>
              <ShootingManagementTab
                productionId={currentProject.id}
                productionName={currentProject.productionName}
                project={currentProject}
              />
            </div>
          ) : null,
      },
    ]

    if (currentProject?.productionType === 1) {
      const excludedKeys = ['venue', 'recruitment', 'shooting']
      return allItems.filter(item => !excludedKeys.includes(item.key))
    }

    return allItems
  }, [currentProject, activeKey])

  if (!currentProject) {
    return <Skeleton active />
  }

  return (
    <div className="full-v">
      <div style={{ padding: '0 0 10px' }}>
        <Typography.Title level={4} className="no-margin">
          {currentProject.productionName}
          {currentProject.secondProductionCode ? `(${currentProject.secondProductionCode})` : ''}
        </Typography.Title>
      </div>
      <Tabs
        activeKey={activeKey}
        onChange={setActiveKey}
        indicator={{ size: 42 }}
        items={tabItems}
        tabBarStyle={{ flexShrink: 0 }}
      />

      {/* 项目编辑表单模态框 */}
      <ProjectForm
        open={showEditForm}
        project={currentProject}
        onCancel={() => setShowEditForm(false)}
        onSubmit={handleFormSubmit}
      />
    </div>
  )
}

export default ProjectDetailPage
