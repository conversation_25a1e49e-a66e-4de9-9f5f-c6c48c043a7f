import { ATMOSPHERE_TYPE_CONFIG, CAMERA_SHOT_CONFIG, CAMERA_STATUS_CONFIG, LOCATION_TYPE_CONFIG } from '@/consts'
import { CaretRightOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { useDebounceFn } from 'ahooks'
import {
  Collapse,
  Divider,
  Empty,
  Flex,
  Input,
  Skeleton,
  Space,
  Table,
  Tag,
  Typography,
  message,
  type TableProps,
} from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import { IProductionListItem } from '../../list/store'
import useProjectStore, { IPrSceneCallInfo, type IPrSceneFilmShotLog } from '../../store'
import styles from './FilmShotLogTab.scss'

interface FilmShotLogTabProps {
  productionId: number
  project: IProductionListItem
}

const FilmShotLogTab: React.FC<FilmShotLogTabProps> = ({ productionId, project }) => {
  const { getSceneAndFilmShotLogs } = useProjectStore()
  const [loading, setLoading] = useState(false)
  const [sceneData, setSceneData] = useState<IPrSceneCallInfo[]>([])
  const [searchText, setSearchText] = useState('')

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      const result = await getSceneAndFilmShotLogs(productionId)
      setSceneData(result)
    } catch (error) {
      console.error('获取场记数据失败:', error)
      message.error('获取场记数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [productionId])

  // 搜索防抖
  const { run: onSearchDebounce } = useDebounceFn(
    (value: string) => {
      setSearchText(value)
    },
    { wait: 300 }
  )

  // 过滤后的场次数据
  const filteredSceneData = useMemo(() => {
    if (!searchText.trim()) {
      return sceneData
    }

    const searchLower = searchText.toLowerCase()
    return sceneData.filter(
      scene =>
        scene.sceneNumber?.toLowerCase().includes(searchLower) ||
        scene.shootingLocation?.toLowerCase().includes(searchLower) ||
        scene.scene?.toLowerCase().includes(searchLower) ||
        scene.mainContent?.toLowerCase().includes(searchLower) ||
        scene.mainActors?.toLowerCase().includes(searchLower) ||
        scene.groupExtraActors?.toLowerCase().includes(searchLower) ||
        scene.specialActors?.toLowerCase().includes(searchLower) ||
        scene.remark?.toLowerCase().includes(searchLower)
    )
  }, [sceneData, searchText])

  // 场记日志表格列配置
  const filmShotLogColumns: TableProps<IPrSceneFilmShotLog>['columns'] = [
    {
      title: '场次',
      dataIndex: 'sceneNumber',
      key: 'sceneNumber',
      width: 100,
      align: 'center',
    },
    {
      title: '镜号',
      dataIndex: 'shotNumber',
      key: 'shotNumber',
      width: 80,
      align: 'center',
    },
    {
      title: '次数',
      dataIndex: 'takeNumber',
      key: 'takeNumber',
      width: 80,
      align: 'center',
    },
    {
      title: '氛围',
      dataIndex: 'atmosphere',
      key: 'atmosphere',
      width: 120,
      ellipsis: true,
      align: 'center',
    },
    {
      title: 'A机景别',
      dataIndex: 'cameraShotByA',
      key: 'cameraShotByA',
      width: 100,
      align: 'center',
      render: (value: string) => {
        const config = CAMERA_SHOT_CONFIG[value as keyof typeof CAMERA_SHOT_CONFIG]
        return config ? <Tag color={config.color}>{config.label}</Tag> : value || '-'
      },
    },
    {
      title: 'A机状态',
      dataIndex: 'cameraStatusByA',
      key: 'cameraStatusByA',
      width: 100,
      align: 'center',
      render: (value: number) => {
        const config = CAMERA_STATUS_CONFIG[value as keyof typeof CAMERA_STATUS_CONFIG]
        return config ? <Tag color={config.color}>{config.label}</Tag> : '-'
      },
    },
    {
      title: 'A机文件号',
      dataIndex: 'cameraFileByA',
      key: 'cameraFileByA',
      width: 100,
      align: 'center',
      render: (value: string) => value || '-',
    },
    {
      title: 'B机景别',
      dataIndex: 'cameraShotByB',
      key: 'cameraShotByB',
      width: 100,
      align: 'center',
      render: (value: string) => {
        const config = CAMERA_SHOT_CONFIG[value as keyof typeof CAMERA_SHOT_CONFIG]
        return config ? <Tag color={config.color}>{config.label}</Tag> : value || '-'
      },
    },

    {
      title: 'B机状态',
      dataIndex: 'cameraStatusByB',
      key: 'cameraStatusByB',
      width: 100,
      align: 'center',
      render: (value: number) => {
        const config = CAMERA_STATUS_CONFIG[value as keyof typeof CAMERA_STATUS_CONFIG]
        return config ? <Tag color={config.color}>{config.label}</Tag> : '-'
      },
    },

    {
      title: 'B机文件号',
      dataIndex: 'cameraFileByB',
      key: 'cameraFileByB',
      width: 100,
      align: 'center',
      render: (value: string) => value || '-',
    },
    {
      title: '内容',
      key: 'content',
      align: 'center',
      ellipsis: true,
      render: (item: IPrSceneFilmShotLog) => (
        <Flex gap={2} vertical className="text-left">
          <span>{item.content}</span>
          {!!item.remark && <Dict title="备注" value={item.remark} />}
        </Flex>
      ),
    },
    {
      title: '创建人',
      key: 'creator',
      width: 150,
      align: 'center',
      render: (item: IPrSceneFilmShotLog) => (
        <Space size={2} direction="vertical">
          <Typography.Text>{item.creator}</Typography.Text>
          <Typography.Text type="secondary">
            {item.createTime ? new Date(item.createTime).toLocaleString() : '-'}
          </Typography.Text>
        </Space>
      ),
    },
  ]

  if (loading) {
    return <Skeleton active />
  }

  if (!sceneData.length) {
    return <Empty />
  }

  // 构建 Collapse 面板数据
  const collapseItems = filteredSceneData.map(scene => {
    const filmShotLogs = scene.filmShotLogs || []

    return {
      key: scene.id?.toString() || '',
      label: (
        <Flex justify="space-between">
          <Space direction="vertical">
            <Space size={2} split={<Divider type="vertical" />}>
              <Space>
                <Typography.Text strong>{scene.sceneNumber}</Typography.Text>
                <Tag className="fw-bold no-margin">
                  {ATMOSPHERE_TYPE_CONFIG[scene.atmosphere as keyof typeof ATMOSPHERE_TYPE_CONFIG]?.label ||
                    scene.atmosphere}
                </Tag>
                <Tag className="fw-bold no-margin">
                  {LOCATION_TYPE_CONFIG[scene.locationType as keyof typeof LOCATION_TYPE_CONFIG]?.label ||
                    scene.locationType}
                </Tag>
                {scene.pageNumber ? <Typography.Text>{scene.pageNumber} 页</Typography.Text> : null}
              </Space>
              {scene.mainContent ? <Typography.Text>{scene.mainContent}</Typography.Text> : null}
            </Space>
            <Space size={24} className="fs-normal" wrap>
              {scene.shootingLocation ? <Dict title="场地" value={scene.shootingLocation} /> : null}
              {scene.scene ? <Dict title="场景" value={scene.scene} /> : null}
              {scene.mainActors ? <Dict title="主演" value={scene.mainActors.split(',').join('、')} /> : null}
              {scene.costumeMakeupTip ? <Dict title="服化道提示" value={scene.costumeMakeupTip} /> : null}
              {scene.groupExtraActors ? <Dict title="群演" value={scene.groupExtraActors} /> : null}
              {scene.specialActors ? (
                <Dict title="特约/群特" value={scene.specialActors.split('，').join('、')} />
              ) : null}
            </Space>
          </Space>
          <Typography.Text type="secondary">
            <Typography.Text>{filmShotLogs.length}</Typography.Text> 条数据
          </Typography.Text>
        </Flex>
      ),
      children:
        filmShotLogs.length > 0 ? (
          <Table
            columns={filmShotLogColumns}
            dataSource={filmShotLogs}
            rowKey="id"
            pagination={false}
            scroll={{ x: 1320 }}
            size="small"
          />
        ) : (
          <Empty />
        ),
    }
  })

  return (
    <Flex vertical>
      <ListHeader
        title={
          <Space>
            <Typography.Title level={5} className="no-margin">
              场记
            </Typography.Title>
            <Typography.Text type="secondary" className="fw-normal">
              (
              {searchText.trim() ? (
                <>
                  <Space size={3}>
                    <span>显示</span>
                    <Typography.Text strong>
                      {filteredSceneData.length}/{sceneData.length}
                    </Typography.Text>
                    <span>个场次，</span>
                  </Space>
                  <Space>
                    <Typography.Text strong>
                      {filteredSceneData.reduce((total, scene) => total + (scene.filmShotLogs?.length || 0), 0)}/
                      {sceneData.reduce((total, scene) => total + (scene.filmShotLogs?.length || 0), 0)}
                    </Typography.Text>
                    <span>条场记</span>
                  </Space>
                </>
              ) : (
                <>
                  共 <Typography.Text strong>{sceneData.length}</Typography.Text> 个场次，
                  <Typography.Text strong>
                    {sceneData.reduce((total, scene) => total + (scene.filmShotLogs?.length || 0), 0)}
                  </Typography.Text>{' '}
                  条场记
                </>
              )}
              )
            </Typography.Text>
          </Space>
        }>
        <Input.Search
          placeholder="支持场次、场景、内容、演员搜索"
          allowClear
          className="w300"
          onChange={e => onSearchDebounce(e.target.value)}
        />
      </ListHeader>

      {filteredSceneData.length === 0 && searchText.trim() ? (
        <Empty />
      ) : (
        <Collapse
          className={styles.collapse}
          expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
          items={collapseItems}
          size="large"
        />
      )}
    </Flex>
  )
}

export default FilmShotLogTab
