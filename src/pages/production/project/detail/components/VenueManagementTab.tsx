import { ProjectStatus } from '@/consts/project'
import { AimOutlined, EnvironmentOutlined } from '@ant-design/icons'
import { Tabs } from 'antd'
import React, { useState } from 'react'
import ProductionVenue from '../../list/components/ProductionVenue'
import { IProductionListItem } from '../../list/store'
import styles from '../index.scss'
import VenueExplorationTab from './VenueExplorationTab'

interface VenueManagementTabProps {
  productionId: number
  project: IProductionListItem
}

const VenueManagementTab: React.FC<VenueManagementTabProps> = ({ productionId, project }) => {
  const [activeKey, setActiveKey] = useState('venue')

  // 只有在项目筹备状态时才显示场景勘探标签页
  const isProjectPreparation = project.status <= ProjectStatus.SHOOTING

  const tabItems = [
    {
      key: 'venue',
      label: '拍摄场地',
      icon: <EnvironmentOutlined />,
      children:
        activeKey === 'venue' ? (
          <div className={styles.scrollC}>
            <ProductionVenue productionId={productionId} project={project} />
          </div>
        ) : null,
    },
    // 只有在项目筹备状态时才添加场景勘探标签页
    ...(isProjectPreparation
      ? [
          {
            key: 'exploration',
            label: '场景勘探',
            icon: <AimOutlined />,
            children:
              activeKey === 'exploration' ? (
                <div className={styles.scrollC}>
                  <VenueExplorationTab productionId={productionId} />
                </div>
              ) : null,
          },
        ]
      : []),
  ]

  return (
    <Tabs
      activeKey={activeKey}
      onChange={setActiveKey}
      items={tabItems}
      type="card"
      tabBarGutter={8}
      tabPosition="left"
      indicator={{ size: 24 }}
    />
  )
}

export default VenueManagementTab
