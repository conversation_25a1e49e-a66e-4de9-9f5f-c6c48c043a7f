import { MEAL_TYPE_OPTIONS } from '@/consts'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Drawer, Flex, Form, Input, InputNumber, Select, TimePicker } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect } from 'react'
import { IPrSceneCall } from '../../store'

interface ISceneCallEditModalProps {
  visible: boolean
  sceneCall: IPrSceneCall | null
  onOk: (values: Partial<IPrSceneCall>) => Promise<void>
  onCancel: () => void
}

const SceneCallEditModal: React.FC<ISceneCallEditModalProps> = ({ visible, sceneCall, onOk, onCancel }) => {
  const [form] = Form.useForm()

  // 当弹窗打开时，设置表单初始值
  useEffect(() => {
    if (visible && sceneCall) {
      form.setFieldsValue({
        dayNumber: sceneCall.dayNumber || 1,
        scheduleRemark: sceneCall.scheduleRemark || '',
        remark: sceneCall.remark || '',
        responsibleDept: sceneCall.responsibleDept || '',
        contact: sceneCall.contact || '',
        meals:
          sceneCall.meals?.map(meal => ({
            mealType: meal.mealType,
            mealTime: meal.mealTime ? dayjs(meal.mealTime, 'HH:mm') : null,
            location: meal.location,
          })) || [],
      })
    } else if (visible && !sceneCall) {
      // 创建新通告单时的默认值
      form.setFieldsValue({
        dayNumber: 1,
        scheduleRemark: `场务车出发时间：
灯光车出发时间：
服装车出发时间：
中巴车出发时间：
演员车出发时间：
导演车出发时间：
预计开机第一镜：`,
        remark: `特殊提示：
★拍摄顺序由导演随时调整，以现场通知为准
★摄影灯光组（请提前布置器材，及时与导演确定拍摄场地）
★美术道具组（请提前看场景，及时与导演确定场景内容）
★注意：
1.
2.`,
        responsibleDept: `出品公司：
制作公司：
总策划：
总监制：
监制：
导演：
执行制片：
执行导演：
统筹：
演员统筹：`,
        contact: `摄影指导：
灯光指导：
服化指导：
美术指导：
演员副导：`,
        meals: [],
      })
    }
  }, [visible, sceneCall, form])

  const handleOk = async () => {
    try {
      const values = await form.validateFields()

      // 处理 meals 数据格式转换
      const formattedValues = {
        ...values,
        meals:
          values.meals?.map((meal: any) => ({
            mealType: meal.mealType,
            mealTime: meal.mealTime ? meal.mealTime.format('HH:mm') : null,
            location: meal.location,
          })) || [],
      }

      await onOk(formattedValues)
      form.resetFields()
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Drawer
      title={sceneCall?.dayNumber ? `第 ${sceneCall?.dayNumber || 1} 天` : '创建通告单'}
      open={visible}
      onClose={handleCancel}
      width={800}
      extra={
        <Button type="primary" onClick={() => handleOk()}>
          立即保存
        </Button>
      }>
      <Form form={form} layout="horizontal" colon={false} labelCol={{ span: 3 }} preserve={false}>
        <Form.Item
          label="第几天"
          name="dayNumber"
          rules={[
            { required: true, message: '请输入第几天' },
            { type: 'number', min: 1, message: '天数必须大于0' },
          ]}
          hidden={!!sceneCall}>
          <InputNumber placeholder="请输入第几天" min={1} />
        </Form.Item>

        <Form.Item label="行程安排" name="scheduleRemark">
          <Input.TextArea placeholder="请输入行程安排" rows={7} autoSize maxLength={500} showCount />
        </Form.Item>

        <Form.Item label="责任部门" name="responsibleDept">
          <Input.TextArea placeholder="请输入责任部门" rows={10} autoSize maxLength={300} />
        </Form.Item>

        <Form.Item label="联系方式" name="contact">
          <Input.TextArea placeholder="请输入联系方式" rows={5} autoSize maxLength={300} />
        </Form.Item>

        <Form.Item label="特别注意" name="remark">
          <Input.TextArea placeholder="请输入备注" rows={6} autoSize maxLength={300} showCount />
        </Form.Item>

        {/* 用餐安排 */}
        <Form.Item label="用餐安排">
          <Form.List name="meals">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Flex key={key} gap={8} align="baseline">
                    <Form.Item
                      {...restField}
                      name={[name, 'mealType']}
                      rules={[{ required: true, message: '请选择用餐类型' }]}>
                      <Select placeholder="用餐类型" options={MEAL_TYPE_OPTIONS} style={{ width: 120 }} />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'mealTime']}>
                      <TimePicker placeholder="用餐时间" format="HH:mm" style={{ width: 120 }} />
                    </Form.Item>
                    <Form.Item {...restField} name={[name, 'location']}>
                      <Input placeholder="用餐地点" style={{ width: 150 }} maxLength={100} />
                    </Form.Item>
                    <Button icon={<DeleteOutlined />} onClick={() => remove(name)}></Button>
                  </Flex>
                ))}
                <Form.Item>
                  <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                    添加用餐
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form.Item>
      </Form>
    </Drawer>
  )
}

export default SceneCallEditModal
