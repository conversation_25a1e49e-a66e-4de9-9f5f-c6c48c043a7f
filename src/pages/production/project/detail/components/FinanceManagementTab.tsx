import { AccountBookOutlined, OneToOneOutlined, PayCircleOutlined, PlusCircleOutlined, PropertySafetyOutlined } from '@ant-design/icons'
import { Tabs } from 'antd'
import React, { useState } from 'react'
import ProductionBudgetOverview from '../../list/components/ProductionBudgetOverview'
import ProductionExtraExpenses from '../../list/components/ProductionExtraExpenses'
import { IProductionListItem } from '../../list/store'
import styles from '../index.scss'
import BudgetOverviewTab from './BudgetOverviewTab'
import NewBudgetTab from './NewBudgetTab'
import ProjectComparisonTab from './ProjectComparisonTab'

interface FinanceManagementTabProps {
  productionId: number
  project: IProductionListItem
}

const FinanceManagementTab: React.FC<FinanceManagementTabProps> = ({ productionId, project }) => {
  const [activeKey, setActiveKey] = useState('extraExpenses')

  const tabItems = [
    {
      key: 'newBudget',
      label: '新预算',
      icon: <PlusCircleOutlined />,
      children:
        activeKey === 'newBudget' ? (
          <div className={styles.scrollC}>
            <NewBudgetTab productionId={productionId} project={project} />
          </div>
        ) : null,
    },
    {
      key: 'extraExpenses',
      label: '登记费用(旧)',
      icon: <PayCircleOutlined />,
      children:
        activeKey === 'extraExpenses' ? (
          <div className={styles.scrollC}>
            <ProductionExtraExpenses productionId={productionId} project={project} />
          </div>
        ) : null,
    },

    {
      key: 'budgetOverview',
      label: '预算总览(旧)',
      icon: <AccountBookOutlined />,
      children:
        activeKey === 'budgetOverview' ? (
          <div className={styles.scrollC}>
            <BudgetOverviewTab project={project} isBudgetMode={true} />
          </div>
        ) : null,
    },
    {
      key: 'budget',
      label: '结算总览(旧)',
      icon: <PropertySafetyOutlined />,
      children:
        activeKey === 'budget' ? (
          <div className={styles.scrollC}>
            <ProductionBudgetOverview productionId={productionId} project={project} isBudgetMode={false} />
          </div>
        ) : null,
    },
    {
      key: 'projectComparison',
      label: '结算对比(旧)',
      icon: <OneToOneOutlined />,
      children:
        activeKey === 'projectComparison' ? (
          <div className={styles.scrollC}>
            <ProjectComparisonTab defaultBaseProjectId={project?.id} />
          </div>
        ) : null,
    },
  ]

  return (
    <Tabs
      activeKey={activeKey}
      onChange={setActiveKey}
      items={tabItems}
      type="card"
      tabBarGutter={8}
      tabPosition="left"
      indicator={{ size: 24 }}
    />
  )
}

export default FinanceManagementTab
