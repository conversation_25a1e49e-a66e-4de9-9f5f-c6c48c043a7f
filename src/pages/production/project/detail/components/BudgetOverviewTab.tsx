import { exportProductionBudget, exportProductionBudgetById } from '@/utils/export'
import { AccountBookOutlined, CloudDownloadOutlined } from '@ant-design/icons'
import { Button, Flex, message, Space, Typography } from 'antd'
import React, { useState } from 'react'
import ProductionBudgetOverview from '../../list/components/ProductionBudgetOverview'
import useProjectListStore, { IProductionListItem } from '../../list/store'

const { Title } = Typography

interface IBudgetOverviewTabProps {
  project?: IProductionListItem
  isBudgetMode?: boolean // true为预算模式，false为结算模式
}

const BudgetOverviewTab: React.FC<IBudgetOverviewTabProps> = ({ project, isBudgetMode = true }) => {
  const [buildingBudget, setBuildingBudget] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)
  const [hasBudget, setHas] = useState(false)

  const { buildProductionBudget } = useProjectListStore()

  // 生成预算
  const handleBuildBudget = async () => {
    if (!project?.id) {
      return
    }

    setBuildingBudget(true)
    try {
      const success = await buildProductionBudget(project.id)

      if (success) {
        message.success('预算生成成功')
        // 刷新预算数据
        setRefreshKey(prev => prev + 1)
        setHas(true)
      }
    } catch (error) {
      console.error('生成预算失败:', error)
      message.error('预算生成失败')
    } finally {
      setBuildingBudget(false)
    }
  }

  // 导出预算
  const handleExportBudgetById = async () => {
    if (!project?.id) {
      return
    }

    try {
      await exportProductionBudgetById(project.id, `${project?.productionName}-预算.xlsx`)
    } catch (error) {
      console.error('导出预算失败:', error)
    }
  }

  // 导出结算
  const handleExportBudget = async () => {
    if (!project?.id) {
      return
    }

    try {
      await exportProductionBudget(project.id, `${project?.productionName}-结算.xlsx`)
    } catch (error) {
      console.error('导出结算失败:', error)
    }
  }

  return (
    <div>
      {/* 顶部工具栏 */}
      <Flex justify="space-between" align="center" style={{ marginBottom: 16 }}>
        <Title level={4} className="no-margin">
          {isBudgetMode ? '预算总览' : '结算总览'}
        </Title>

        <Space size={12}>
          {isBudgetMode && (
            <Button
              color="primary"
              variant="filled"
              shape="round"
              icon={<AccountBookOutlined />}
              loading={buildingBudget}
              onClick={handleBuildBudget}>
              生成预算
            </Button>
          )}
          <Button
            type="primary"
            ghost
            shape="round"
            icon={<CloudDownloadOutlined />}
            onClick={() => (isBudgetMode ? handleExportBudgetById() : handleExportBudget())}>
            导出{isBudgetMode ? '预算' : '结算'}
          </Button>
        </Space>
      </Flex>

      {/* 预算/结算内容 */}
      <div style={{ height: 'calc(100% - 60px)', overflow: 'auto' }}>
        {project?.id && (project?.hasBudget || hasBudget) ? (
          <ProductionBudgetOverview
            key={refreshKey}
            productionId={project.id}
            project={project}
            isBudgetMode={isBudgetMode}
          />
        ) : null}
      </div>
    </div>
  )
}

export default BudgetOverviewTab
