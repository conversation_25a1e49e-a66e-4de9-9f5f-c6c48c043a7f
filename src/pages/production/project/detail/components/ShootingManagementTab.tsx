import {
  CalendarOutlined,
  EnvironmentOutlined,
  HighlightOutlined,
  ProfileOutlined,
  ScheduleOutlined,
} from '@ant-design/icons'
import { Tabs } from 'antd'
import React, { useState } from 'react'
import { IProductionListItem } from '../../list/store'
import styles from '../index.scss'
import FilmShotLogTab from './FilmShotLogTab'
import SceneAnnotationTab from './SceneAnnotationTab'
import SceneCallList from './SceneCallList'
import ScenePlanTab from './ScenePlanTab'
import SceneVenueTab from './SceneVenueTab'

interface ShootingManagementTabProps {
  productionId: number
  productionName?: string
  project: IProductionListItem
}

const ShootingManagementTab: React.FC<ShootingManagementTabProps> = ({
  productionId,
  productionName = '',
  project,
}) => {
  const [activeKey, setActiveKey] = useState('sceneAnnotation')

  const tabItems = [
    {
      key: 'sceneAnnotation',
      label: '剧本标注',
      icon: <HighlightOutlined />,
      children:
        activeKey === 'sceneAnnotation' ? (
          <div className={styles.scrollC}>
            <SceneAnnotationTab productionId={productionId} productionName={productionName} />
          </div>
        ) : null,
    },
    {
      key: 'scenePlan',
      label: '大计划',
      icon: <CalendarOutlined />,
      children:
        activeKey === 'scenePlan' ? (
          <div className={styles.scrollC}>
            <ScenePlanTab productionId={productionId} />
          </div>
        ) : null,
    },
    {
      key: 'sceneVenue',
      label: '场景表',
      icon: <EnvironmentOutlined />,
      children:
        activeKey === 'sceneVenue' ? (
          <div className={styles.scrollC}>
            <SceneVenueTab productionId={productionId} />
          </div>
        ) : null,
    },
    {
      key: 'sceneCall',
      label: '通告单',
      icon: <ScheduleOutlined />,
      children:
        activeKey === 'sceneCall' ? (
          <div className={styles.scrollC}>
            <SceneCallList productionId={productionId} productionName={productionName} project={project} />
          </div>
        ) : null,
    },
    {
      key: 'filmShotLog',
      label: '场记',
      icon: <ProfileOutlined />,
      children:
        activeKey === 'filmShotLog' ? (
          <div className={styles.scrollC}>
            <FilmShotLogTab productionId={productionId} project={project} />
          </div>
        ) : null,
    },
  ]

  return (
    <Tabs
      activeKey={activeKey}
      onChange={setActiveKey}
      items={tabItems}
      type="card"
      tabBarGutter={8}
      tabPosition="left"
      indicator={{ size: 24 }}
    />
  )
}

export default ShootingManagementTab
