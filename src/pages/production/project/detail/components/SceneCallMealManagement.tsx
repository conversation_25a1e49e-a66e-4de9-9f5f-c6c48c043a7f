import { MEAL_TYPE_CONFIG, MEAL_TYPE_OPTIONS, MealType } from '@/consts'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Flex, Form, Input, Modal, Select, Table, TimePicker, Typography } from 'antd'
import dayjs, { type Dayjs } from 'dayjs'
import React, { useState } from 'react'
import { IPrSceneCallMeal, ISaveSceneCallMealDto } from '../../store'

const { Text } = Typography

interface ISceneCallMealManagementProps {
  callId: number
  meals: IPrSceneCallMeal[]
  onSave: (params: ISaveSceneCallMealDto) => Promise<boolean>
  onRefresh: () => Promise<void>
  disabled?: boolean // 是否禁用操作按钮
}

interface IMealFormData {
  mealType: number
  mealTime?: Dayjs
  location?: string
}

const SceneCallMealManagement: React.FC<ISceneCallMealManagementProps> = ({
  callId,
  meals,
  onSave,
  onRefresh,
  disabled = false,
}) => {
  const [form] = Form.useForm()
  const [editingMeals, setEditingMeals] = useState<IPrSceneCallMeal[]>(meals)

  const [editModal, setEditModal] = useState<{
    visible: boolean
    meal: IPrSceneCallMeal | null
    isAdd: boolean
  }>({
    visible: false,
    meal: null,
    isAdd: false,
  })

  // 添加用餐
  const handleAdd = () => {
    setEditModal({
      visible: true,
      meal: null,
      isAdd: true,
    })
  }

  // 编辑用餐
  const handleEdit = (meal: IPrSceneCallMeal) => {
    setEditModal({
      visible: true,
      meal,
      isAdd: false,
    })
  }

  // 处理模态框提交
  const handleModalOk = async () => {
    try {
      const values: IMealFormData = await form.validateFields()

      const mealData: IPrSceneCallMeal = {
        callId,
        mealType: values.mealType,
        mealTime: values.mealTime ? values.mealTime.format('HH:mm') : undefined,
        location: values.location,
      }

      let newMeals: IPrSceneCallMeal[]
      if (editModal.isAdd) {
        // 添加新用餐
        newMeals = [...editingMeals, mealData]
      } else {
        // 编辑现有用餐
        const index = editingMeals.findIndex(m => m.id === editModal.meal?.id)
        if (index !== -1) {
          newMeals = [...editingMeals]
          newMeals[index] = { ...editModal.meal!, ...mealData }
        } else {
          newMeals = editingMeals
        }
      }

      // 直接调用API保存
      const success = await onSave({
        callId,
        meals: newMeals,
      })

      if (success) {
        await onRefresh()
        setEditModal({ visible: false, meal: null, isAdd: false })
        form.resetFields()
      }
    } catch (error) {
      console.error('保存失败:', error)
    }
  }

  // 处理模态框取消
  const handleModalCancel = () => {
    setEditModal({ visible: false, meal: null, isAdd: false })
    form.resetFields()
  }

  // 设置表单初始值
  React.useEffect(() => {
    if (editModal.visible && editModal.meal) {
      form.setFieldsValue({
        mealType: editModal.meal.mealType,
        mealTime: editModal.meal.mealTime ? dayjs(editModal.meal.mealTime, 'HH:mm') : undefined,
        location: editModal.meal.location,
      })
    } else if (editModal.visible && editModal.isAdd) {
      form.resetFields()
    }
  }, [editModal, form])

  // 同步外部数据
  React.useEffect(() => {
    setEditingMeals(meals)
  }, [meals])

  const columns = [
    {
      title: '类型',
      dataIndex: 'mealType',
      key: 'mealType',
      align: 'center',
      render: (mealType: number) => {
        const config = MEAL_TYPE_CONFIG[mealType as MealType]
        return config ? <Text strong>{config.label}</Text> : mealType
      },
    },
    {
      title: '时间',
      dataIndex: 'mealTime',
      key: 'mealTime',
      align: 'center',
      render: (time: string) => time || '-',
    },
    {
      title: '地点',
      dataIndex: 'location',
      key: 'location',
      align: 'center',
      render: (location: string) => location || '-',
    },
  ]

  if (!disabled) {
    columns.push({
      title: '操作',
      key: 'action',
      width: 120,
      align: 'center',
      render: (_: any, record: IPrSceneCallMeal) => (
        <Button type="link" size="small" onClick={() => handleEdit(record)}>
          编辑
        </Button>
      ),
    })
  }

  return (
    <>
      <Flex vertical gap={16}>
        {!disabled && (
          <Flex justify="flex-end">
            <Button type="primary" ghost icon={<PlusOutlined />} onClick={handleAdd}>
              添加用餐
            </Button>
          </Flex>
        )}
        <Table
          columns={columns}
          dataSource={editingMeals}
          rowKey={(record, index) => record.id || `temp-${index}`}
          pagination={false}
          size="small"
        />
      </Flex>

      {/* 编辑用餐模态框 */}
      {editModal.visible ? (
        <Modal
          title={editModal.isAdd ? '添加' : '编辑'}
          open={editModal.visible}
          onOk={handleModalOk}
          onCancel={handleModalCancel}
          okText="立即保存"
          width={500}>
          <Form form={form} layout="vertical" preserve={false}>
            <Form.Item label="类型" name="mealType" rules={[{ required: true, message: '请选择用餐类型' }]}>
              <Select placeholder="请选择用餐类型" options={MEAL_TYPE_OPTIONS} />
            </Form.Item>

            <Form.Item label="时间" name="mealTime">
              <TimePicker placeholder="请选择用餐时间" format="HH:mm" style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item label="地点" name="location">
              <Input placeholder="请输入用餐地点" maxLength={100} />
            </Form.Item>
          </Form>
        </Modal>
      ) : null}
    </>
  )
}

export default SceneCallMealManagement
