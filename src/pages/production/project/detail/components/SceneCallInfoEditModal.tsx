import {
  ATMOSPHERE_TYPE_OPTIONS,
  LOCATION_TYPE_OPTIONS,
  PLAN_TYPE_OPTIONS
} from '@/consts'
import { Form, Input, InputNumber, Modal, Select, Space } from 'antd'
import React from 'react'
import { IPrSceneCallInfo } from '../../store'

interface ISceneInfoFormData {
  sort?: number
  planType?: number
  sceneNumber?: string
  scriptLocation?: string
  atmosphere?: number
  locationType?: number
  pageNumber?: number
  scene?: string
  mainContent?: string
  costumeMakeupTip?: string
  groupExtraActors?: string
  specialActors?: string
  remark?: string
  consultation?: string
}

interface ISceneCallInfoEditModalProps {
  visible: boolean
  onCancel: () => void
  onOk: (values: ISceneInfoFormData) => Promise<void>
  editInfo: IPrSceneCallInfo | null
  isAdd: boolean
}

const SceneCallInfoEditModal: React.FC<ISceneCallInfoEditModalProps> = ({
  visible,
  onCancel,
  onOk,
  editInfo,
  isAdd
}) => {
  const [form] = Form.useForm()

  const handleOk = async () => {
    try {
      const values: ISceneInfoFormData = await form.validateFields()
      await onOk(values)
      form.resetFields()
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  // 设置表单初始值
  React.useEffect(() => {
    if (visible && editInfo) {
      form.setFieldsValue({
        sort: editInfo.sort,
        planType: editInfo.planType,
        sceneNumber: editInfo.sceneNumber,
        scriptLocation: editInfo.scriptLocation,
        atmosphere: editInfo.atmosphere,
        locationType: editInfo.locationType,
        pageNumber: editInfo.pageNumber,
        scene: editInfo.scene,
        mainContent: editInfo.mainContent,
        costumeMakeupTip: editInfo.costumeMakeupTip,
        groupExtraActors: editInfo.groupExtraActors,
        specialActors: editInfo.specialActors,
        remark: editInfo.remark,
        consultation: editInfo.consultation,
      })
    } else if (visible && isAdd) {
      form.resetFields()
    }
  }, [visible, editInfo, isAdd, form])

  return (
    <Modal
      title={isAdd ? '添加分割线' : '编辑场次信息'}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        preserve={false}
      >
        {isAdd ? (
          // 添加分割线 - 只显示备注字段
          <Form.Item
            label="分割线备注"
            name="remark"
            rules={[{ required: true, message: '请输入分割线备注' }]}
          >
            <Input.TextArea
              placeholder="请输入分割线备注，用于分组场次"
              rows={3}
              maxLength={300}
              showCount
            />
          </Form.Item>
        ) : (
          // 编辑场次信息 - 显示完整表单
          <Space direction="vertical" size={16} style={{ width: '100%' }}>
            <div style={{ display: 'flex', gap: 16 }}>
              <Form.Item
                label="序号"
                name="sort"
                style={{ flex: 1 }}
              >
                <InputNumber
                  placeholder="请输入序号"
                  style={{ width: '100%' }}
                  min={0}
                />
              </Form.Item>

              <Form.Item
                label="计划类型"
                name="planType"
                style={{ flex: 1 }}
              >
                <Select
                  placeholder="请选择计划类型"
                  options={PLAN_TYPE_OPTIONS}
                />
              </Form.Item>
            </div>

            <div style={{ display: 'flex', gap: 16 }}>
              <Form.Item
                label="场次编号"
                name="sceneNumber"
                style={{ flex: 1 }}
              >
                <Input
                  placeholder="请输入场次编号"
                  maxLength={50}
                />
              </Form.Item>

              <Form.Item
                label="剧本场景"
                name="scriptLocation"
                style={{ flex: 1 }}
              >
                <Input
                  placeholder="请输入剧本场景"
                  maxLength={100}
                />
              </Form.Item>
            </div>

            <div style={{ display: 'flex', gap: 16 }}>
              <Form.Item
                label="气氛"
                name="atmosphere"
                style={{ flex: 1 }}
              >
                <Select
                  placeholder="请选择气氛"
                  options={ATMOSPHERE_TYPE_OPTIONS}
                  allowClear
                />
              </Form.Item>

              <Form.Item
                label="内/外景"
                name="locationType"
                style={{ flex: 1 }}
              >
                <Select
                  placeholder="请选择内/外景"
                  options={LOCATION_TYPE_OPTIONS}
                  allowClear
                />
              </Form.Item>

              <Form.Item
                label="页数"
                name="pageNumber"
                style={{ flex: 1 }}
              >
                <InputNumber
                  placeholder="请输入页数"
                  style={{ width: '100%' }}
                  min={0}
                />
              </Form.Item>
            </div>

            <Form.Item
              label="场景"
              name="scene"
            >
              <Input
                placeholder="请输入场景"
                maxLength={100}
              />
            </Form.Item>

            <Form.Item
              label="主要内容"
              name="mainContent"
            >
              <Input.TextArea
                placeholder="请输入主要内容"
                rows={3}
                maxLength={500}
                showCount
              />
            </Form.Item>

            <Form.Item
              label="备注"
              name="remark"
            >
              <Input.TextArea
                placeholder="请输入备注"
                rows={2}
                maxLength={300}
                showCount
              />
            </Form.Item>
          </Space>
        )}
      </Form>
    </Modal>
  )
}

export default SceneCallInfoEditModal
export type { ISceneInfoFormData }
