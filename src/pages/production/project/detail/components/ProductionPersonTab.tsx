import { IdcardOutlined, TeamOutlined } from '@ant-design/icons'
import { Tabs } from 'antd'
import React, { useState } from 'react'
import ProductionActors from '../../list/components/ProductionActors'
import ProductionPersonBasic from '../../list/components/ProductionPersonBasic'
import { IProductionListItem } from '../../list/store'
import styles from '../index.scss'

interface ProductionPersonTabProps {
  productionId: number
  projectName?: string
  project: IProductionListItem
}

const ProductionPersonTab: React.FC<ProductionPersonTabProps> = ({ productionId, projectName = '', project }) => {
  const [activeKey, setActiveKey] = useState('actors')

  const tabItems = [
    {
      key: 'actors',
      label: '演员列表',
      icon: <IdcardOutlined />,
      children:
        activeKey === 'actors' ? (
          <div className={styles.scrollC}>
            <ProductionActors productionId={productionId} projectName={projectName} project={project} />
          </div>
        ) : null,
    },
    {
      key: 'personBasic',
      label: '工作人员',
      icon: <TeamOutlined />,
      children:
        activeKey === 'personBasic' ? (
          <div className={styles.scrollC}>
            <ProductionPersonBasic productionId={productionId} projectName={projectName} project={project} />
          </div>
        ) : null,
    },
  ]

  return (
    <Tabs
      activeKey={activeKey}
      onChange={setActiveKey}
      items={tabItems}
      type="card"
      tabBarGutter={8}
      tabPosition="left"
      indicator={{ size: 24 }}
    />
  )
}

export default ProductionPersonTab
