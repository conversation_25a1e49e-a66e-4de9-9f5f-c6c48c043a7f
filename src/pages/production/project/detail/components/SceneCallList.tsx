import { exportSingleCallTemplate } from '@/utils/export'
import { PlusOutlined, ReloadOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Button, Card, Divider, Empty, Flex, List, message, Modal, Space, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useCallback, useEffect, useState } from 'react'
import { IProductionListItem } from '../../list/store'
import useProjectStore, { type IPrSceneCall } from '../../store'
import SceneCallDetailModal from './SceneCallDetailModal'
import SceneCallEditModal from './SceneCallEditModal'

// 扩展 IPrSceneCall 接口，添加 canDelete 属性
interface ISceneCallWithCanDelete extends IPrSceneCall {
  canDelete?: boolean
}

interface ISceneCallListProps {
  productionId: number
  productionName?: string
  project?: IProductionListItem
}

export const SceneCallList: React.FC<ISceneCallListProps> = ({ productionId, productionName = '', project }) => {
  const { getSceneCall, createSceneCall, deleteSceneCall } = useProjectStore()

  // 状态管理
  const [sceneCallList, setSceneCallList] = useState<ISceneCallWithCanDelete[]>([])
  const [loading, setLoading] = useState(false)

  const [exportingItems, setExportingItems] = useState<Set<number>>(new Set())

  // 创建弹窗状态
  const [createModal, setCreateModal] = useState(false)

  // 详情弹窗状态
  const [detailModal, setDetailModal] = useState<{
    visible: boolean
    sceneCall: ISceneCallWithCanDelete | null
  }>({
    visible: false,
    sceneCall: null,
  })

  // 计算是否可以删除通告单
  const calculateCanDelete = (sceneCall: IPrSceneCall): boolean => {
    if (!project?.startDate || !sceneCall.dayNumber) {
      return true // 如果没有开机日期或天数信息，允许删除
    }

    const sceneCallDate = dayjs(project.startDate).add(sceneCall.dayNumber - 1, 'day')
    const today = dayjs()

    return !today.isAfter(sceneCallDate, 'day')
  }

  // 获取通告单列表
  const fetchData = useCallback(async () => {
    if (!productionId) {
      return
    }

    setLoading(true)
    try {
      const data = await getSceneCall(productionId)

      // 在设置数据时计算 canDelete 属性
      const dataWithCanDelete = (data || []).map(item => ({
        ...item,
        canDelete: calculateCanDelete(item),
      }))

      setSceneCallList(dataWithCanDelete)
    } catch (error) {
      message.error('获取通告单列表失败')
    } finally {
      setLoading(false)
    }
  }, [productionId, getSceneCall])

  // 创建通告单
  const handleCreate = () => {
    setCreateModal(true)
  }

  // 查看通告单详情
  const handleView = (sceneCall: ISceneCallWithCanDelete) => {
    setDetailModal({
      visible: true,
      sceneCall,
    })
  }

  // 删除通告单
  const handleDelete = async (sceneCall: ISceneCallWithCanDelete) => {
    if (!sceneCall.id) {
      message.error('无法删除：ID不存在')

      return
    }

    // 检查是否可以删除（使用预计算的属性）
    if (!sceneCall.canDelete) {
      message.error('该通告单对应的拍摄日期已过，无法删除')
      return
    }

    Modal.confirm({
      title: '确认删除',
      content: `确定要删除第 ${sceneCall.dayNumber || 1} 天的通告单吗？`,
      onOk: async () => {
        try {
          const success = await deleteSceneCall(sceneCall.id!)

          if (success) {
            message.success('删除成功')
            await fetchData()
          }
        } catch (error) {
          message.error('删除失败')
        }
      },
    })
  }

  // 导出单个通告单
  const handleExportSingle = async (sceneCall: ISceneCallWithCanDelete) => {
    if (!sceneCall.id) {
      message.error('无法导出：ID不存在')

      return
    }

    setExportingItems(prev => new Set(prev).add(sceneCall.id!))
    try {
      const dayItem = dayjs(project?.startDate).add(sceneCall.dayNumber ? sceneCall.dayNumber - 1 : 0, 'day')
      await exportSingleCallTemplate(
        sceneCall.id,
        `《${productionName}》-${dayItem.format('YYYYMMDD')}-第${sceneCall.dayNumber || 1}天`
      )
    } catch (error) {
      message.error('导出失败')
    } finally {
      setExportingItems(prev => {
        const newSet = new Set(prev)

        newSet.delete(sceneCall.id!)

        return newSet
      })
    }
  }

  // 处理创建弹窗提交
  const handleCreateSubmit = async (values: Partial<IPrSceneCall>) => {
    try {
      const success = await createSceneCall({
        productionId,
        ...values,
      })

      if (success) {
        message.success('创建成功')
        await fetchData()
      }
    } catch (error) {
      message.error('创建失败')
    }
    setCreateModal(false)
  }

  // 初始化数据
  useEffect(() => {
    if (productionId) {
      fetchData()
    }
  }, [productionId, fetchData])

  // 渲染列表项
  const renderListItem = (item: ISceneCallWithCanDelete) => (
    <List.Item>
      <Card size="small" className="full-h hover-move">
        <Flex justify="space-between" gap={12} align="center">
          <Flex flex={1} onClick={() => handleView(item)} style={{ cursor: 'pointer' }}>
            <Space direction="vertical" size={8} style={{ width: '100%' }}>
              <Space split={<Divider type="vertical" />} size={2}>
                <Typography.Title level={5} className="no-margin">
                  第 {item.dayNumber || 1} 天
                </Typography.Title>
                <Typography.Text>
                  <strong className="fs-lg">{item.sceneInfos?.length || 0}</strong> 个场次
                </Typography.Text>
              </Space>

              {/* {item.scheduleRemark && (
                <div>
                  <Text type="secondary">行程安排：</Text>
                  <Text>{item.scheduleRemark}</Text>
                </div>
              )} */}

              {!!item.actors?.length && (
                <Dict title="演员" value={item.actors?.map(actor => actor.actorName).join('、')} />
              )}
              {!!item.meals?.length && (
                <Space size="large">
                  {item.meals.map(meal => (
                    <Dict title={meal.mealTypeStr} value={meal.mealTime}></Dict>
                  ))}
                </Space>
              )}

              {/* {item.remark && (
                <div>
                  <Text type="secondary">备注：{item.remark}</Text>
                </div>
              )} */}
            </Space>
          </Flex>

          <Space size={0} split={<Divider type="vertical" />}>
            {project?.feNoEdit || !item.canDelete ? null : (
              <Button type="link" size="small" disabled={!item.canDelete} onClick={() => handleDelete(item)}>
                删除
              </Button>
            )}
            <Button
              type="link"
              size="small"
              loading={exportingItems.has(item.id!)}
              disabled={!item.id}
              onClick={() => handleExportSingle(item)}>
              导出
            </Button>
          </Space>
        </Flex>
      </Card>
    </List.Item>
  )

  return (
    <Flex vertical>
      <ListHeader title="通告单" total={sceneCallList.length} unitText="个">
        <Space>
          <Button
            color="primary"
            variant="filled"
            shape="round"
            icon={<ReloadOutlined />}
            loading={loading}
            onClick={fetchData}>
            刷新
          </Button>
          <Button type="primary" ghost shape="round" icon={<PlusOutlined />} onClick={handleCreate}>
            创建通告单
          </Button>
        </Space>
      </ListHeader>

      {sceneCallList.length > 0 ? (
        <List
          dataSource={sceneCallList}
          loading={loading}
          split={false}
          className="list-sm"
          renderItem={renderListItem}
        />
      ) : (
        <Empty />
      )}

      {/* 创建通告单弹窗 */}
      {createModal ? (
        <SceneCallEditModal
          visible={createModal}
          sceneCall={null}
          onOk={handleCreateSubmit}
          onCancel={() => setCreateModal(false)}
        />
      ) : null}

      {/* 通告单详情弹窗 */}
      {detailModal.visible ? (
        <SceneCallDetailModal
          visible={detailModal.visible}
          sceneCall={detailModal.sceneCall}
          onCancel={() => setDetailModal({ visible: false, sceneCall: null })}
          onRefresh={fetchData}
          productionId={productionId}
          disabled={!detailModal.sceneCall?.canDelete}
        />
      ) : null}
    </Flex>
  )
}

export default SceneCallList
