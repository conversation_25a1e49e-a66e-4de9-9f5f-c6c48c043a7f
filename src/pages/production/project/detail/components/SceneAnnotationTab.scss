// 主容器布局
.container {
  overflow: hidden;
  height: calc(100vh - 200px);

  :global {
    .ant-splitter-bar-dragger::before {
      width: 1px !important;
    }
  }
}

.toolbar {
  margin-bottom: 16px;
}

.contentArea {
  height: calc(100% - 60px);

  :global {
    .ant-card-body {
      height: 100%;
    }
  }
}

.textPanel {
  overflow: auto;
  padding: 16px;
  height: 100%;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  white-space: pre-wrap;
  line-height: 1.6;
  user-select: text;
  cursor: text;
}

.rightPanel {
  overflow: auto;
  padding: 16px;
  height: 100%;

  :global {
    .ant-collapse-content-box {
      padding-block: 0 !important;
    }
  }
}

.sceneInfo {
  margin-top: 16px;
}

// 标注文本样式
.annotatedText {
  padding: 3px 6px;
  font-weight: 500;
  border: 1px solid var(--annotation-color);
  border-radius: 4px;
  background-color: var(--annotation-color);
  box-shadow: 0 1px 3px rgb(0 0 0 / 10%);
  transition: all 0.2s ease;
  cursor: pointer;

  &:hover {
    transform: scale(1.02);
    box-shadow: 0 2px 6px rgb(0 0 0 / 15%);
  }
}
