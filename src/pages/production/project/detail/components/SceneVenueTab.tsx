import { exportScenePlanVenue } from '@/utils/export'
import { DownloadOutlined } from '@ant-design/icons'
import { Button, Flex, Spin, Table, message } from 'antd'
import React, { useEffect, useState } from 'react'
import useProjectStore, { ISceneVenueInfoDto } from '../../store'

interface SceneVenueTabProps {
  productionId: number
}

const SceneVenueTab: React.FC<SceneVenueTabProps> = ({ productionId }) => {
  const { getScenePlanVenue } = useProjectStore()
  const [loading, setLoading] = useState(false)
  const [venueData, setVenueData] = useState<ISceneVenueInfoDto[]>([])

  // 加载数据
  const loadData = async () => {
    setLoading(true)
    try {
      const result = await getScenePlanVenue(productionId)

      setVenueData(result)
    } catch (error) {
      console.error('获取场景表数据失败:', error)
      message.error('获取场景表数据失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadData()
  }, [productionId])

  // 导出场景表
  const handleExport = async () => {
    try {
      await exportScenePlanVenue(productionId, '场景表.xlsx')
    } catch (error) {
      console.error('导出场景表失败:', error)
    }
  }

  // 处理表格数据，合并相同的 mainVenue
  const processedData = React.useMemo(() => {
    if (!venueData.length) {
      return []
    }

    const result: any[] = []
    let currentMainVenue = ''
    let mainVenueRowSpan = 0
    let mainVenueStartIndex = 0

    // 按 sort 排序
    const sortedData = [...venueData].sort((a, b) => (a.sort || 0) - (b.sort || 0))

    sortedData.forEach((item, index) => {
      if (item?.mainVenueByPage !== currentMainVenue) {
        // 更新之前的 mainVenue 的 rowSpan
        if (currentMainVenue && mainVenueRowSpan > 0) {
          for (let i = mainVenueStartIndex; i < mainVenueStartIndex + mainVenueRowSpan; i++) {
            if (i === mainVenueStartIndex) {
              result[i].mainVenueRowSpan = mainVenueRowSpan
            } else {
              result[i].mainVenueRowSpan = 0
            }
          }
        }

        // 开始新的 mainVenue 组
        currentMainVenue = item?.mainVenueByPage || ''
        mainVenueStartIndex = result.length
        mainVenueRowSpan = 1
      } else {
        mainVenueRowSpan++
      }

      result.push({
        ...item,
        key: index,
        mainVenueRowSpan: 1, // 临时值，后面会更新
      })
    })

    // 处理最后一组
    if (currentMainVenue && mainVenueRowSpan > 0) {
      for (let i = mainVenueStartIndex; i < mainVenueStartIndex + mainVenueRowSpan; i++) {
        if (i === mainVenueStartIndex) {
          result[i].mainVenueRowSpan = mainVenueRowSpan
        } else {
          result[i].mainVenueRowSpan = 0
        }
      }
    }

    return result
  }, [venueData])

  const columns = [
    {
      title: '序号',
      dataIndex: 'sort',
      key: 'sort',
      width: 50,
      align: 'center' as const,
      render: (value: number, record: any) => ({
        children: value || 0,
        props: { rowSpan: record.mainVenueRowSpan },
      }),
    },
    {
      title: <div style={{ textAlign: 'center' }}>主场景</div>,
      dataIndex: 'mainVenueByPage',
      key: 'mainVenueByPage',
      width: 300,
      render: (text: string, record: any) => ({
        children: text || '-',
        props: { rowSpan: record.mainVenueRowSpan },
      }),
    },
    {
      title: <div style={{ textAlign: 'center' }}>分场景</div>,
      dataIndex: 'venue',
      key: 'venue',
      width: 300,
      render: (text: string) => text || '-',
    },
    {
      title: '页数',
      dataIndex: 'pageCount',
      key: 'pageCount',
      width: 100,
      align: 'center' as const,
      render: (value: number) => value || 0,
    },
    {
      title: '场次',
      dataIndex: 'count',
      key: 'count',
      width: 100,
      align: 'center' as const,
      render: (value: number) => value || 0,
    },
  ]

  return (
    <Spin spinning={loading}>
      <Flex vertical gap={16}>
        <Flex justify="flex-end">
          <Button
            color="primary"
            variant="filled"
            shape="round"
            icon={<DownloadOutlined />}
            onClick={handleExport}
            disabled={!venueData.length}>
            导出场景表
          </Button>
        </Flex>
        <Table
          columns={columns}
          dataSource={processedData}
          pagination={false}
          size="small"
          bordered
          scroll={{ y: 'calc(100vh - 300px)' }}
        />
      </Flex>
    </Spin>
  )
}

export default SceneVenueTab
