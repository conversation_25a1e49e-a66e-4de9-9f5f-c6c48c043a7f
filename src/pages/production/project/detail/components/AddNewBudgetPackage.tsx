import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, Card, Drawer, Flex, Form, Input, InputNumber, message, Select, Space, Switch, Typography } from 'antd'
import React, { useEffect, useMemo } from 'react'
import {
  BUDGET_CATEGORY_OPTIONS,
  BUDGET_SUBCATEGORY_OPTIONS,
  BudgetCategory,
  getSubcategoryOptionsByCategory,
} from '../../../../../consts/budget'
import { IProductionListItem } from '../../list/store'
import useProjectDetailStore, { IPrProductionBudgetItem } from '../store'

const { TextArea } = Input
const { Text } = Typography

interface IAddNewBudgetPackageProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  loading?: boolean
  productionId: number
  project?: IProductionListItem
  editingItem?: IPrProductionBudgetItem // 编辑模式时传入的数据
  addedSubcategories?: Set<number> // 已添加的subcategory集合，用于过滤重复项
}

const AddNewBudgetPackage: React.FC<IAddNewBudgetPackageProps> = ({
  open,
  onCancel,
  onSuccess,
  loading = false,
  productionId,
  project,
  editingItem,
  addedSubcategories = new Set(),
}) => {
  const [form] = Form.useForm()
  const { saveBudgetItems } = useProjectDetailStore()

  // 判断是否为编辑模式
  const isEdit = !!editingItem?.id

  // 监听表单中的 itemsDetail 变化
  const formItemsDetail = Form.useWatch('itemsDetail', form)

  // 使用useMemo计算过滤后的二级分类选项
  const getFilteredSubcategoryOptions = useMemo(() => {
    return (category: BudgetCategory) => {
      if (!category) return []
      return getSubcategoryOptionsByCategory(category)
        .filter(option => !addedSubcategories.has(option.value))
    }
  }, [addedSubcategories])

  // 监听一级分类变化，清空二级分类
  const handleCategoryChange = (name: number) => {
    form.setFieldValue(['itemsDetail', name, 'subcategory'], undefined)
  }



  useEffect(() => {
    if (open) {
      if (isEdit && editingItem) {
        // 编辑模式：填充现有数据
        form.setFieldsValue({
          itemName: editingItem.itemName || '',
          quotedPrice: editingItem.quotedPrice || undefined,
          personCount: editingItem.personCount || undefined,
          dayCount: editingItem.dayCount || undefined,
          totalPrice: editingItem.totalPrice,
          hasInvoice: editingItem.hasInvoice === 1,
          description: editingItem.description || '',
          // 填充itemsDetail列表
          itemsDetail: editingItem.itemsDetail?.map(detail => ({
            category: detail.category,
            subcategory: detail.subcategory,
            personCount: detail.personCount || undefined,
          })) || [{ category: undefined, subcategory: undefined, personCount: 1 }],
        })
      } else {
        // 新增模式：重置表单并设置默认值
        form.resetFields()
        form.setFieldsValue({
          personCount: null,
          dayCount: null,
          hasInvoice: false,
          // 默认添加一个空的预算子项
          itemsDetail: [{ category: undefined, subcategory: undefined, personCount: null }],
        })
      }
    }
  }, [open, isEdit, editingItem, form])

  // 处理保存
  const handleSave = async (values: any) => {
    try {
      // 验证itemsDetail
      if (!values.itemsDetail || values.itemsDetail.length === 0) {
        message.error('请至少添加一个预算子项')
        return
      }

      // 验证每个子项的必填字段
      const invalidItems = values.itemsDetail.filter((item: any) => !item.category || !item.subcategory)
      if (invalidItems.length > 0) {
        message.error('请完善所有预算子项的分类信息')
        return
      }

      const budgetItem: IPrProductionBudgetItem = {
        id: editingItem?.id,
        productionId,
        isPackage: true,
        itemName: values.itemName,
        quotedPrice: values.quotedPrice || 0,
        personCount: values.personCount || 0,
        dayCount: values.dayCount || 0,
        totalPrice: values.totalPrice,
        hasInvoice: values.hasInvoice ? 1 : 0,
        description: values.description,
        itemsDetail: values.itemsDetail.map((item: any) => ({
          itemId: editingItem?.id || 0, // 新增时为0，后端会处理
          category: item.category,
          subcategory: item.subcategory,
          personCount: item.personCount || 0, // 如果子项没有设置人数，使用总人数
        })),
      }

      const success = await saveBudgetItems({
        productionId,
        budgetItems: [budgetItem],
      })

      if (success) {
        message.success(isEdit ? '编辑打包项成功' : '添加打包项成功')
        onSuccess()
        onCancel()
      } else {
        message.error(isEdit ? '编辑打包项失败' : '添加打包项失败')
      }
    } catch (error) {
      console.error('保存打包项失败:', error)
      message.error(isEdit ? '编辑打包项失败' : '添加打包项失败')
    }
  }

  return (
    <Drawer
      title={isEdit ? '编辑打包项' : '添加打包项'}
      open={open}
      onClose={onCancel}
      width={800}
      extra={
        <Space>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" loading={loading} onClick={() => form.submit()}>
            {isEdit ? '保存' : '添加'}
          </Button>
        </Space>
      }>
      <Form form={form} layout="vertical" onFinish={handleSave}>
        <Form.Item
          label="打包名称"
          name="itemName"
          rules={[{ required: true, message: '请输入打包名称' }]}>
          <Input placeholder="请输入打包名称" />
        </Form.Item>

        <Form.Item label="预算子项">
          <Form.List name="itemsDetail">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card
                    key={key}
                    size="small"
                    title={`子项 ${name + 1}`}
                    extra={
                      fields.length > 1 ? (
                        <Button
                          type="link"
                          danger
                          icon={<MinusCircleOutlined />}
                          onClick={() => remove(name)}>
                          删除
                        </Button>
                      ) : null
                    }
                    style={{ marginBottom: 16 }}>
                    <Flex gap={16}>
                      <Form.Item
                        {...restField}
                        name={[name, 'category']}
                        label="一级分类"
                        rules={[{ required: true, message: '请选择一级分类' }]}
                        style={{ flex: 1 }}>
                        <Select
                          placeholder="请选择一级分类"
                          options={BUDGET_CATEGORY_OPTIONS}
                          onChange={() => handleCategoryChange(name)}
                        />
                      </Form.Item>

                      <Form.Item
                        {...restField}
                        name={[name, 'subcategory']}
                        label="二级分类"
                        rules={[{ required: true, message: '请选择二级分类' }]}
                        style={{ flex: 1 }}>
                        <Select
                          placeholder="请选择二级分类"
                          options={formItemsDetail?.[name]?.category ?
                            getSubcategoryOptionsByCategory(formItemsDetail[name].category)
                              .filter(option => !addedSubcategories.has(option.value)) : []}
                        />
                      </Form.Item>

                      <Form.Item
                        {...restField}
                        name={[name, 'personCount']}
                        label="人数"
                        style={{ flex: 1 }}>
                        <InputNumber min={1} placeholder="人数" style={{ width: '100%' }} />
                      </Form.Item>
                    </Flex>
                  </Card>
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add({ category: undefined, subcategory: undefined, personCount: undefined })}
                    block
                    icon={<PlusOutlined />}>
                    添加预算子项
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>
        </Form.Item>

        <Flex gap={16}>
          <Form.Item
            label={`单价 (${project?.currencySymbol || '¥'})`}
            name="quotedPrice"
            style={{ flex: 1 }}
          >
            <InputNumber
              placeholder="请输入单价"
              min={0}
              precision={2}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            label="人数"
            name="personCount"
            style={{ flex: 1 }}
          >
            <InputNumber
              placeholder="请输入人数"
              min={1}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            label="天数"
            name="dayCount"
            style={{ flex: 1 }}
          >
            <InputNumber
              placeholder="请输入天数"
              min={1}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Flex>

        <Form.Item
          label={`总价 (${project?.currencySymbol || '¥'})`}
          name="totalPrice"
          rules={[{ required: true, message: '请输入总价' }]}>
          <InputNumber
            placeholder="请输入总价"
            min={0}
            precision={2}
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item label="是否有发票" name="hasInvoice" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="备注说明" name="description">
          <TextArea
            placeholder="请输入备注说明"
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>
      </Form>
    </Drawer>
  )
}

export default AddNewBudgetPackage
