import { ATMOSPHERE_TYPE_CONFIG, AtmosphereType, LOCATION_TYPE_CONFIG, LocationType, PlanType } from '@/consts'
import { SearchOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { useDebounceFn } from 'ahooks'
import { Alert, Card, Divider, Empty, Flex, Input, Modal, Skeleton, Space, Tag, Typography } from 'antd'
import React, { useMemo, useState } from 'react'
import useProjectStore, { IPrScenePlan } from '../../store'

const { Text } = Typography

interface IScenePlanSelectorModalProps {
  visible: boolean
  onCancel: () => void
  onSelect: (scenePlans: IPrScenePlan[]) => void
  productionId: number
}

const ScenePlanSelectorModal: React.FC<IScenePlanSelectorModalProps> = ({
  visible,
  onCancel,
  onSelect,
  productionId,
}) => {
  const { getScenePlan } = useProjectStore()
  const [scenePlans, setScenePlans] = useState<IPrScenePlan[]>([])
  const [selectedPlans, setSelectedPlans] = useState<IPrScenePlan[]>([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')

  // 获取大计划数据
  React.useEffect(() => {
    if (visible && productionId) {
      fetchScenePlans()
    } else if (!visible) {
      // 关闭弹窗时重置搜索状态
      setSearchText('')
      setSelectedPlans([])
    }
  }, [visible, productionId])

  const fetchScenePlans = async () => {
    setLoading(true)
    try {
      const data = await getScenePlan(productionId)
      // 只显示场次明细类型的场景
      const detailPlans = data?.filter(plan => plan.planType === PlanType.SCENE_DETAIL && !plan.verification) || []
      setScenePlans(detailPlans)
    } catch (error) {
      console.error('获取大计划失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSelect = (plan: IPrScenePlan, checked: boolean) => {
    if (checked) {
      setSelectedPlans([...selectedPlans, plan])
    } else {
      setSelectedPlans(selectedPlans.filter(p => p.id !== plan.id))
    }
  }

  // 搜索防抖
  const { run: onSearchDebounce } = useDebounceFn(
    (value: string) => {
      setSearchText(value)
    },
    { wait: 300 }
  )

  // 过滤后的场景数据
  const filteredScenePlans = useMemo(() => {
    if (!searchText.trim()) {
      return scenePlans
    }

    const searchLower = searchText.toLowerCase()
    return scenePlans.filter(
      plan =>
        plan.sceneNumber?.toLowerCase().includes(searchLower) ||
        plan.scriptLocation?.toLowerCase().includes(searchLower) ||
        plan.scene?.toLowerCase().includes(searchLower) ||
        plan.mainContent?.toLowerCase().includes(searchLower) ||
        plan.remark?.toLowerCase().includes(searchLower)
    )
  }, [scenePlans, searchText])

  const handleConfirm = () => {
    onSelect(selectedPlans)
    setSelectedPlans([])
    onCancel()
  }

  return (
    <Modal
      title="添加场次"
      open={visible}
      onCancel={onCancel}
      onOk={handleConfirm}
      width={800}
      okText="确认添加"
      cancelText="取消"
      okButtonProps={{ disabled: selectedPlans.length === 0 }}>
      <Flex vertical gap={12}>
        <ListHeader
          className="no-margin"
          title={
            !loading &&
            scenePlans.length > 0 &&
            (searchText.trim()
              ? `找到 ${filteredScenePlans.length} 个场次（共 ${scenePlans.length} 个）`
              : `共 ${scenePlans.length} 个场次`)
          }>
          <Input
            placeholder="支持场次、场景、内容搜索"
            prefix={<SearchOutlined />}
            allowClear
            className="w300"
            onChange={e => onSearchDebounce(e.target.value)}
          />
        </ListHeader>

        <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
          {loading ? (
            <Skeleton active />
          ) : filteredScenePlans.length > 0 ? (
            <Flex vertical gap={8}>
              {filteredScenePlans.map(plan => (
                <Card
                  key={plan.id}
                  size="small"
                  className={`pointer ${selectedPlans.find(p => p.id === plan.id) ? 'border-success' : ''}`}
                  onClick={() => handleSelect(plan, !selectedPlans.find(p => p.id === plan.id))}>
                  <Flex vertical gap={6}>
                    <Space>
                      <Text strong>{plan.sceneNumber}</Text>
                      {plan.atmosphere && (
                        <Tag className="no-margin fw-bold">
                          {ATMOSPHERE_TYPE_CONFIG[plan.atmosphere as AtmosphereType]?.label}
                        </Tag>
                      )}
                      {plan.locationType && (
                        <Tag className="no-margin fw-bold">
                          {LOCATION_TYPE_CONFIG[plan.locationType as LocationType]?.label}
                        </Tag>
                      )}
                      <Space size={2} split={<Divider type="vertical" />}>
                        {plan.pageNumber && <Text> {plan.pageNumber} 页</Text>}
                        {plan.mainContent && <Text>{plan.mainContent}</Text>}
                      </Space>
                    </Space>
                    <Space wrap>
                      {plan.scriptLocation && <Dict title="主场景" value={plan.scriptLocation} />}
                      {plan.scene && <Dict title="子场景" value={plan.scene} />}
                    </Space>
                  </Flex>
                </Card>
              ))}
            </Flex>
          ) : (
            <Empty />
          )}
        </div>
        {selectedPlans.length > 0 && (
          <Alert
            message={
              <Text>
                已选择 <Text strong>{selectedPlans.length}</Text> 个场次
              </Text>
            }
            type="success"
          />
        )}
      </Flex>
    </Modal>
  )
}

export default ScenePlanSelectorModal
