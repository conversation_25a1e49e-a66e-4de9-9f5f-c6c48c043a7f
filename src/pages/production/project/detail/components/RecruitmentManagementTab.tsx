import { SafetyCertificateOutlined, UsergroupAddOutlined } from '@ant-design/icons'
import { Tabs } from 'antd'
import React, { useState } from 'react'
import ProductionPersonApplicationReview from '../../list/components/ProductionPersonApplicationReview'
import ProductionPersonRecruitment from '../../list/components/ProductionPersonRecruitment'
import { IProductionListItem } from '../../list/store'
import styles from '../index.scss'

interface RecruitmentManagementTabProps {
  productionId: number
  projectName: string
  project: IProductionListItem
}

const RecruitmentManagementTab: React.FC<RecruitmentManagementTabProps> = ({ productionId, projectName, project }) => {
  const [activeKey, setActiveKey] = useState('recruitment')

  const tabItems = [
    {
      key: 'recruitment',
      label: '招募人员',
      icon: <UsergroupAddOutlined />,
      children:
        activeKey === 'recruitment' ? (
          <div className={styles.scrollC}>
            <ProductionPersonRecruitment productionId={productionId} projectName={projectName} project={project} />
          </div>
        ) : null,
    },
    {
      key: 'applicationReview',
      label: '招募审批',
      icon: <SafetyCertificateOutlined />,
      children:
        activeKey === 'applicationReview' ? (
          <div className={styles.scrollC}>
            <ProductionPersonApplicationReview
              productionId={productionId}
              projectName={projectName}
              project={project}
            />
          </div>
        ) : null,
    },
  ]

  return (
    <Tabs
      activeKey={activeKey}
      onChange={setActiveKey}
      items={tabItems}
      type="card"
      tabBarGutter={8}
      tabPosition="left"
      indicator={{ size: 24 }}
    />
  )
}

export default RecruitmentManagementTab
