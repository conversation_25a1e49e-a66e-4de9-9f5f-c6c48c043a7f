import {
  CAMERA_SHOT_OPTIONS,
  CAMERA_STATUS_OPTIONS,
  CameraStatus
} from '@/consts'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Button,
  Card,
  Col,
  Drawer,
  Form,
  Input,
  InputNumber,
  message,
  Popconfirm,
  Row,
  Select,
  Space,
  Typography
} from 'antd'
import React, { useEffect, useState } from 'react'
import useProjectStore, { IPrSceneFilmShotLog } from '../../store'

const { Text } = Typography

interface IFilmShotLogDetailModalProps {
  visible: boolean
  onClose: () => void
  sceneNumber?: string
  callId?: number
  productionId: number
  atmosphere?: string // 场景氛围，用作新场记的默认值
  locationTypeStr?: string
  disabled?: boolean // 是否禁用编辑操作
}

const FilmShotLogDetailModal: React.FC<IFilmShotLogDetailModalProps> = ({
  visible,
  onClose,
  sceneNumber,
  callId,
  productionId,
  atmosphere,
  locationTypeStr = '',
  disabled = false
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const { getFilmShotLogsById, saveSceneFilmShotLog, deleteSceneFilmShotLog } = useProjectStore()

  // 获取场记列表数据
  const fetchFilmShotLogs = async () => {
    if (!visible || !productionId || !sceneNumber || !callId) return

    setLoading(true)
    try {
      const logs = await getFilmShotLogsById(callId, sceneNumber)

      // 初始化表单数据
      form.setFieldsValue({
        filmShotLogs: logs.map(log => ({
          ...log,
          cameraStatusByA: log.cameraStatusByA ?? CameraStatus.PENDING,
          cameraStatusByB: log.cameraStatusByB ?? CameraStatus.PENDING
        }))
      })
    } catch (error) {
      console.error('获取场记列表失败:', error)
      message.error('获取场记列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化和刷新数据
  useEffect(() => {
    fetchFilmShotLogs()
  }, [visible, productionId, sceneNumber])

  // 保存表单
  const handleSave = async () => {
    try {
      setLoading(true)
      const values = await form.validateFields()

      // 保存每个场记记录
      for (const filmShotLog of values.filmShotLogs || []) {
        const logData: IPrSceneFilmShotLog = {
          ...filmShotLog,
          productionId,
          sceneNumber,
          callId
        }
        await saveSceneFilmShotLog(logData)
      }

      message.success('保存场记成功')
      await fetchFilmShotLogs() // 刷新本地数据
    } catch (error) {
      console.error('保存场记失败:', error)
      message.error('保存场记失败')
    } finally {
      setLoading(false)
    }
  }

  // 删除场记
  const handleDelete = async (id: number, remove: () => void) => {
    if (id) {
      try {
        const success = await deleteSceneFilmShotLog(id)
        if (success) {
          message.success('删除场记成功')
          remove()
          await fetchFilmShotLogs() // 刷新本地数据
        } else {
          message.error('删除场记失败')
        }
      } catch (error) {
        console.error('删除场记失败:', error)
        message.error('删除场记失败')
      }
    } else {
      // 新添加的记录，直接移除
      remove()
    }
  }

  return (
    <Drawer
      title={`场记详情 - ${sceneNumber || '未知场次'}`}
      open={visible}
      onClose={onClose}
      width="90vw"
      placement="right"
      extra={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" loading={loading} onClick={handleSave}
          // disabled={disabled}
          >
            保存
          </Button>
        </Space>
      }
    >
      <div style={{ padding: '0 0 16px 0' }}>
        <Space direction="vertical" size={16} style={{ width: '100%' }}>
          <Card size="small">
            <Space>
              <Text strong>场次编号：</Text>
              <Text>{sceneNumber || '-'}</Text>
            </Space>
          </Card>

          <Form form={form} layout="vertical">
            <Form.List name="filmShotLogs">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <div
                      key={key}
                      style={{
                        border: '1px solid #d9d9d9',
                        borderRadius: '6px',
                        padding: '4px',
                        marginBottom: '4px',
                        backgroundColor: '#fafafa'
                      }}
                    >
                      <Row gutter={8} align="bottom">
                        {/* 基本信息 */}
                        <Col span={2}>
                          <Form.Item
                            {...restField}
                            name={[name, 'shotNumber']}
                            label="镜号"
                            rules={[{ required: true, message: '请输入镜号' }]}
                            style={{ marginBottom: 0 }}
                          >
                            <InputNumber
                              placeholder="镜号"
                              style={{ width: '100%' }}
                              min={1}
                              precision={0}
                              // disabled={disabled}
                              onChange={(value) => {
                                // 当镜号改动时，将同级的takeNumber重置为1
                                if (value !== null && value !== undefined) {
                                  form.setFieldValue(['filmShotLogs', name, 'takeNumber'], 1)
                                  form.setFieldValue(['filmShotLogs', name, 'content'], '')
                                }
                              }}
                            />
                          </Form.Item>
                        </Col>

                        <Col span={2}>
                          <Form.Item
                            {...restField}
                            name={[name, 'takeNumber']}
                            label="次数"
                            style={{ marginBottom: 0 }}
                            rules={[{ required: true, message: '请输入次数' }]}
                          >
                            <InputNumber placeholder="次数" style={{ width: '100%' }} min={1} precision={0}
                            // disabled={disabled}
                            />
                          </Form.Item>
                        </Col>

                        <Col span={2}>
                          <Form.Item
                            {...restField}
                            name={[name, 'atmosphere']}
                            label="氛围"
                            style={{ marginBottom: 0 }}
                          >
                            <Input placeholder="氛围" style={{ width: '100%' }}
                            //  disabled={disabled}
                            />
                          </Form.Item>
                        </Col>

                        {/* 拍摄内容 */}
                        <Col span={3}>
                          <Form.Item
                            {...restField}
                            name={[name, 'content']}
                            label="拍摄内容"
                            style={{ marginBottom: 0 }}
                          >
                            <Input placeholder="拍摄内容描述" style={{ width: '100%' }}
                            // disabled={disabled} 
                            />
                          </Form.Item>
                        </Col>

                        {/* A机信息 */}
                        <Col span={2}>
                          <Form.Item
                            {...restField}
                            name={[name, 'cameraShotByA']}
                            label="A机景别"
                            style={{ marginBottom: 0 }}
                          >
                            <Select placeholder="A机景别" options={[...CAMERA_SHOT_OPTIONS]} allowClear style={{ width: '100%' }}
                            // disabled={disabled}
                            />
                          </Form.Item>
                        </Col>

                        <Col span={2}>
                          <Form.Item
                            {...restField}
                            name={[name, 'cameraFileByA']}
                            label="A机文件"
                            style={{ marginBottom: 0 }}
                          >
                            <Input placeholder="A机文件" style={{ width: '100%' }}
                            // disabled={disabled}
                            />

                          </Form.Item>
                        </Col>

                        <Col span={2}>
                          <Form.Item
                            {...restField}
                            name={[name, 'cameraStatusByA']}
                            label="A机状态"
                            rules={[{ required: true, message: '请选择A机状态' }]}
                            style={{ marginBottom: 0 }}
                          >
                            <Select placeholder="A机状态" options={CAMERA_STATUS_OPTIONS} style={{ width: '100%' }}
                            // disabled={disabled} 
                            />
                          </Form.Item>
                        </Col>

                        {/* B机信息 */}
                        <Col span={2}>
                          <Form.Item
                            {...restField}
                            name={[name, 'cameraShotByB']}
                            label="B机景别"
                            style={{ marginBottom: 0 }}
                          >
                            <Select placeholder="B机景别" options={[...CAMERA_SHOT_OPTIONS]} allowClear style={{ width: '100%' }}
                            //  disabled={disabled} 
                            />
                          </Form.Item>
                        </Col>

                        <Col span={2}>
                          <Form.Item
                            {...restField}
                            name={[name, 'cameraFileByB']}
                            label="B机文件"
                            style={{ marginBottom: 0 }}
                          >
                            <Input placeholder="B机文件" style={{ width: '100%' }}
                            // disabled={disabled}
                            />
                          </Form.Item>
                        </Col>

                        <Col span={2}>
                          <Form.Item
                            {...restField}
                            name={[name, 'cameraStatusByB']}
                            label="B机状态"
                            rules={[{ required: true, message: '请选择B机状态' }]}
                            style={{ marginBottom: 0 }}
                          >
                            <Select placeholder="B机状态" options={CAMERA_STATUS_OPTIONS} style={{ width: '100%' }}
                            //  disabled={disabled}
                            />
                          </Form.Item>
                        </Col>


                        {/* 备注 */}
                        <Col span={2}>
                          <Form.Item
                            {...restField}
                            name={[name, 'remark']}
                            label="备注"
                            style={{ marginBottom: 0 }}
                          >
                            <Input placeholder="备注" style={{ width: '100%' }}
                            // disabled={disabled} 
                            />
                          </Form.Item>
                        </Col>

                        {/* 操作 */}
                        <Col span={1}>
                          <Form.Item label="操作" style={{ marginBottom: 0 }}>
                            <Popconfirm
                              title="确定要删除这条场记吗？"
                              onConfirm={() => {
                                const fieldValue = form.getFieldValue(['filmShotLogs', name])
                                handleDelete(fieldValue?.id, () => remove(name))
                              }}
                              okText="确定"
                              cancelText="取消"
                            >
                              <Button
                                type="text"
                                size="small"
                                icon={<DeleteOutlined />}
                                danger
                                disabled={disabled}
                              />
                            </Popconfirm>
                          </Form.Item>
                        </Col>
                      </Row>
                    </div>
                  ))}
                  <div style={{ marginBottom: 16 }}>
                    <Button
                      type="dashed"
                      onClick={() => {
                        const currentValues = form.getFieldValue('filmShotLogs') || []
                        const lastItem = currentValues[currentValues.length - 1]
                        const nextShotNumber = lastItem?.shotNumber ? lastItem.shotNumber : 1
                        const nextTakeNumber = lastItem?.takeNumber ? lastItem.takeNumber + 1 : 1
                        const nextAtmosphere = lastItem?.atmosphere || `${atmosphere}${locationTypeStr ? '/' + locationTypeStr : ''}` || ''
                        const nextContent = lastItem?.content || ''
                        let nextCameraFileByA = lastItem?.cameraFileByA || ''
                        let nextCameraFileByB = lastItem?.cameraFileByB || ''
                        const nextCameraShotByA = lastItem?.cameraShotByA || undefined
                        const nextCameraShotByB = lastItem?.cameraShotByB || undefined
                        try {
                          const parsedA = parseInt(nextCameraFileByA)
                          const parsedB = parseInt(nextCameraFileByB)
                          nextCameraFileByA = isNaN(parsedA) ? '' : (parsedA + 1).toString()
                          nextCameraFileByB = isNaN(parsedB) ? '' : (parsedB + 1).toString()
                        } catch (e) {
                          nextCameraFileByA = ''
                          nextCameraFileByB = ''
                        }
                        add({
                          shotNumber: nextShotNumber,
                          takeNumber: nextTakeNumber,
                          atmosphere: nextAtmosphere || '',
                          cameraShotByA: nextCameraShotByA,
                          cameraShotByB: nextCameraShotByB,
                          content: nextContent,
                          cameraFileByA: nextCameraFileByA,
                          cameraFileByB: nextCameraFileByB,
                          cameraStatusByA: CameraStatus.PENDING,
                          cameraStatusByB: CameraStatus.PENDING,
                          remark: ''
                        })
                      }}
                      block
                      icon={<PlusOutlined />}
                      // disabled={disabled}
                    >
                      添加场记
                    </Button>
                  </div>
                  {fields.length === 0 && (
                    <Card style={{ textAlign: 'center', padding: 40 }}>
                      <Text type="secondary">暂无场记记录，请点击上方"添加场记"按钮开始添加</Text>
                    </Card>
                  )}
                </>
              )}
            </Form.List>
          </Form>
        </Space>
      </div>
    </Drawer >
  )
}

export default FilmShotLogDetailModal
