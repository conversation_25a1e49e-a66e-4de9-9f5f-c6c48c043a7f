import { Form, Input, Modal } from 'antd'
import React, { useState } from 'react'

interface ISceneVerificationModalProps {
  visible: boolean
  onCancel: () => void
  onConfirm: (remark: string) => Promise<void>
  sceneNumber?: string
}

const SceneVerificationModal: React.FC<ISceneVerificationModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  sceneNumber
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const handleOk = async () => {
    try {
      const values = await form.validateFields()
      setLoading(true)
      await onConfirm(values.remark || '')
      form.resetFields()
      onCancel()
    } catch (error) {
      console.error('销场失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={`销场确认 - ${sceneNumber || '未知场次'}`}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      okText="确认销场"
      cancelText="取消"
      width={500}
    >
      <Form
        form={form}
        layout="vertical"
        preserve={false}
      >
        <Form.Item
          label="备注"
          name="remark"
          rules={[{ required: false }]}
        >
          <Input.TextArea
            placeholder="请输入销场备注（可选）"
            rows={4}
            maxLength={300}
            showCount
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default SceneVerificationModal
