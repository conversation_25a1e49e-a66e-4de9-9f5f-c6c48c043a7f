import {
  BookOutlined,
  CaretRightOutlined,
  CloudDownloadOutlined,
  DeleteOutlined,
  FileWordOutlined,
  FormatPainterOutlined,
  UnorderedListOutlined,
} from '@ant-design/icons'
import {
  Badge,
  Button,
  Card,
  Collapse,
  Divider,
  Empty,
  Flex,
  Form,
  Input,
  List,
  message,
  Pagination,
  Radio,
  Space,
  Splitter,
  Tooltip,
  Typography,
  Upload,
} from 'antd'
import dayjs from 'dayjs'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import {
  ATMOSPHERE_TYPE_OPTIONS,
  AtmosphereType,
  CONTENT_TYPE_CONFIG,
  ContentType,
  LOCATION_TYPE_OPTIONS,
  LocationType,
} from '../../../../../consts'
import { DATE_FORMAT_BASE } from '../../../../../consts/date'
import useStore from '../../../../../store'
import { exportProductionTemplate } from '../../../../../utils/export'
import { getUploadProps } from '../../../../../utils/file'
import AnnotationModal from '../../components/AnnotationModal'
import BatchAddDictionaryModal from '../../components/BatchAddDictionaryModal'
import DictionaryManagement from '../../components/DictionaryManagement'
import SceneDataSidebar from '../../components/SceneDataSidebar'
import useProjectStore, { type IPrSceneInfo } from '../../store'
import styles from './SceneAnnotationTab.scss'

// 标注的数据结构
export interface Annotation {
  start: number // 标注在纯文本中的全局起始偏移量
  end: number // 标注在纯文本中的全局结束偏移量
  color: string // 标注的背景颜色
  text: string // 实际标注的文本内容
  type: ContentType // 标注的内容类型
  description?: string // 标注描述
  firstText?: string // 一级内容
  secondText?: string // 二级内容
  mainText?: string // 别名
  creator: string // 创建者
  createTime: string // 创建时间
  createType: 'user' | 'robot'
}

export interface DeleteLog {
  text: string
  type: ContentType
  creator: string
  createTime: string
}

// 标注数据接口
export interface AnnotationData {
  annotations: Annotation[]
  sceneId: number
  atmosphere?: AtmosphereType // 气氛（日、夜、日转夜、夜转日）
  locationType?: LocationType // 内/外景（内、外）
  mainContent?: string // 主要内容
  remark?: string // 备注
  updater: string
  updateTime: string
  deleteLogs: Array<DeleteLog>
}

const mergeAnalysisData = (userData: AnnotationData, robotData: AnnotationData) => {
  // 合并 atmosphere
  const atmosphere = userData.atmosphere || robotData.atmosphere

  // 合并 locationType
  const locationType = userData.locationType || robotData.locationType

  // 合并 mainContent
  const mainContent = userData.mainContent || robotData.mainContent

  // 合并 remark
  const remark = userData.remark || robotData.remark

  // 合并annotations
  let robotDataAnnotations = [...(robotData.annotations || [])].map(item => {
    item.createType = 'robot'
    return item
  })
  const userDataAnnotations = [...(userData.annotations || [])].map(item => {
    item.createType = 'user'
    return item
  })

  // 过滤掉被删除的机器人标注
  robotDataAnnotations = robotDataAnnotations.filter(
    robotAnnotation =>
      !userData?.deleteLogs?.some(
        deleteLog => deleteLog.text === robotAnnotation.text && deleteLog.type === robotAnnotation.type
      )
  )

  // 先push userDataAnnotations，再push robotDataAnnotations，并去重
  const mergedAnnotations = [...userDataAnnotations]

  // 添加机器人标注，去重逻辑：如果用户标注中已存在相同text和type的标注，则不添加机器人标注
  robotDataAnnotations.forEach(robotAnnotation => {
    const isDuplicate = userDataAnnotations.some(userAnnotation => userAnnotation.text === robotAnnotation.text)

    if (!isDuplicate) {
      mergedAnnotations.push(robotAnnotation)
    }
  })
  const annotations = mergedAnnotations.filter(item => (item.type as number) !== 6) // 过滤掉类型6

  return {
    atmosphere,
    locationType,
    mainContent,
    remark,
    annotations,
  }
}

interface SceneAnnotationTabProps {
  productionId: number
  productionName?: string
}

export const SceneAnnotationTab: React.FC<SceneAnnotationTabProps> = ({ productionId, productionName }) => {
  const { getSceneInfoList, saveSceneInfo, refreshRobotAnalysisById } = useProjectStore()
  const { userInfo } = useStore()

  // 状态管理
  const [currentScene, setCurrentScene] = useState<IPrSceneInfo | null>(null)
  const [annotations, setAnnotations] = useState<Annotation[]>([])
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [importing, setImporting] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // 当前场次状态
  const [currentPageIndex, setCurrentPageIndex] = useState(1)
  const [totalScenes, setTotalScenes] = useState(0)

  // 新增的场次信息状态
  const [sceneFormData, setSceneFormData] = useState({
    atmosphere: null as AtmosphereType | null,
    locationType: null as LocationType | null,
    mainContent: '',
    remark: '',
  })
  const [deleteLogs, setDeleteLogs] = useState<Array<DeleteLog>>([])

  // 标注弹窗状态
  const [annotationModal, setAnnotationModal] = useState({
    visible: false,
    start: 0,
    end: 0,
  })

  // 标注表单
  const [annotationForm] = Form.useForm()

  // 字典管理状态
  const [dictionaryVisible, setDictionaryVisible] = useState(false)

  // 批量新增字典弹窗状态
  const [batchAddModalVisible, setBatchAddModalVisible] = useState(false)
  const [batchAddData, setBatchAddData] = useState<any[]>([])

  // 查看顺场表状态
  const [sceneDataVisible, setSceneDataVisible] = useState(false)

  // 文本选择相关
  const textContainerRef = useRef<HTMLDivElement>(null)
  const selectionTimeoutRef = useRef<NodeJS.Timeout>()

  const handleCloseDictionary = () => {
    setDictionaryVisible(false)
    fetchCurrentScene(currentPageIndex)
  }

  // 关闭批量新增字典弹窗
  const handleCloseBatchAddModal = () => {
    setBatchAddModalVisible(false)
    setBatchAddData([])
    fetchCurrentScene(currentPageIndex)
  }

  // 获取当前场次 - 使用 GetSceneInfoList，pageSize=1
  const fetchCurrentScene = async (pageIndex = 1) => {
    if (!productionId) {
      return
    }

    setLoading(true)
    try {
      const response = await getSceneInfoList({
        pageIndex,
        pageSize: 1, // 固定为1，每次只获取一个场次
        productionId,
      })

      if (response?.list && response.list.length > 0) {
        const scene = response.list[0] // 只有一个场次

        setCurrentScene(scene)
        setCurrentPageIndex(pageIndex)
        setTotalScenes(response.total)

        // 解析已有的标注数据
        if (scene.contentAnalysis || scene.robotContentAnalysis) {
          try {
            const contentAnalysisData: AnnotationData = JSON.parse(scene.contentAnalysis || '{}')
            const robotContentAnalysisData: AnnotationData = JSON.parse(scene.robotContentAnalysis || '{}')
            const annotationData = mergeAnalysisData(contentAnalysisData, robotContentAnalysisData)

            setAnnotations(annotationData.annotations || [])
            setSceneFormData({
              atmosphere: annotationData.atmosphere || null,
              locationType: annotationData.locationType || null,
              mainContent: annotationData.mainContent || '',
              remark: annotationData.remark || '',
            })
            setDeleteLogs(contentAnalysisData.deleteLogs || [])
          } catch {
            setAnnotations([])
            setSceneFormData({
              atmosphere: null,
              locationType: null,
              mainContent: '',
              remark: '',
            })
          }
        } else {
          setAnnotations([])
          setSceneFormData({
            atmosphere: null,
            locationType: null,
            mainContent: '',
            remark: '',
          })
        }
      }
    } catch (error) {
      message.error('获取场次失败')
    } finally {
      setLoading(false)
    }
  }

  // 文本选择处理（提取到外部，使用 useCallback）
  const handleTextSelection = useCallback(() => {
    // 如果没有场次内容或正在导入，直接返回
    if (!currentScene?.content?.trim() || loading || importing) {
      return
    }

    const selection = window.getSelection()

    if (!selection || selection.isCollapsed) {
      return
    }

    const selectedText = selection.toString().trim()

    if (!selectedText) {
      return
    }

    // 检查选择是否在文本容器内
    const range = selection.getRangeAt(0)

    if (!textContainerRef.current?.contains(range.commonAncestorContainer)) {
      return
    }

    // 清除之前的定时器
    if (selectionTimeoutRef.current) {
      clearTimeout(selectionTimeoutRef.current)
    }

    // 1秒后弹出标注弹窗
    selectionTimeoutRef.current = setTimeout(() => {
      const textContent = textContainerRef.current?.textContent || ''
      const start = textContent.indexOf(selectedText)
      const end = start + selectedText.length

      setAnnotationModal({
        visible: true,
        start,
        end,
      })

      // 设置表单初始值
      annotationForm.setFieldsValue({
        selectedText,
        type: ContentType.ACTOR,
        description: '',
        mainText: '',
        firstText: '',
        secondText: '',
      })

      // 清除选择
      selection.removeAllRanges()
    }, 600)
  }, [currentScene?.content])

  // 保存标注
  const handleSaveAnnotation = async () => {
    if (!currentScene?.id) {
      return
    }

    try {
      // 验证表单并获取值
      const formValues = await annotationForm.validateFields()

      const newAnnotation: Annotation = {
        start: annotationModal.start,
        end: annotationModal.end,
        text: formValues.selectedText,
        type: formValues.type,
        description: formValues.description || '',
        mainText: formValues.mainText || '',
        firstText: formValues.firstText || '',
        secondText: formValues.secondText || '',
        color: CONTENT_TYPE_CONFIG?.[formValues.type as ContentType]?.color,
        creator: userInfo?.nickName || '未知用户',
        createTime: dayjs().format(DATE_FORMAT_BASE),
        createType: 'user',
      }

      // 检查是否存在重复的标注文本（包含关系）
      const isDuplicate = annotations.some(annotation => annotation.text == newAnnotation.text)

      if (isDuplicate) {
        message.warning('该文本已被标注，不可重复添加')
        return
      }

      const updatedAnnotations = [...annotations, newAnnotation]
      setAnnotations(updatedAnnotations)

      const annotationData: AnnotationData = {
        annotations: updatedAnnotations.filter(item => item.createType == 'user'),
        sceneId: currentScene.id,
        updater: userInfo?.nickName || '未知用户',
        updateTime: dayjs().format(DATE_FORMAT_BASE),
        deleteLogs,
      }

      // 保留现有的场次信息
      if (sceneFormData.atmosphere) {
        annotationData.atmosphere = sceneFormData.atmosphere
      }
      if (sceneFormData.locationType) {
        annotationData.locationType = sceneFormData.locationType
      }
      if (sceneFormData.mainContent) {
        annotationData.mainContent = sceneFormData.mainContent
      }
      if (sceneFormData.remark) {
        annotationData.remark = sceneFormData.remark
      }

      setSaving(true)
      const success = await saveSceneInfo({
        id: currentScene.id,
        contentAnalysis: JSON.stringify(annotationData),
      })

      if (success) {
        message.success('标注保存成功')
        setAnnotationModal({
          visible: false,
          start: 0,
          end: 0,
        })
      }
    } catch (error: any) {
      if (error?.errorFields) {
        // 表单验证失败
        return
      }
      message.error('标注保存失败')
    } finally {
      setSaving(false)
    }
  }

  // 删除标注
  const handleDeleteAnnotation = async (targetAnnotation: Annotation) => {
    if (!currentScene?.id) {
      return
    }

    const updatedAnnotations = annotations.filter(
      ann =>
        !(
          ann.text === targetAnnotation.text &&
          ann.type === targetAnnotation.type &&
          ann.createType === targetAnnotation.createType
        )
    )

    setAnnotations(updatedAnnotations)
    const newDeleteLogs = [...deleteLogs]

    if (targetAnnotation.createType == 'robot') {
      newDeleteLogs.push({
        text: targetAnnotation.text,
        type: targetAnnotation.type,
        creator: userInfo?.nickName || '未知用户',
        createTime: dayjs().format(DATE_FORMAT_BASE),
      })
    }
    const annotationData: AnnotationData = {
      annotations: updatedAnnotations.filter(item => item.createType == 'user'),
      sceneId: currentScene.id,
      updater: userInfo?.nickName || '未知用户',
      updateTime: dayjs().format(DATE_FORMAT_BASE),
      deleteLogs: newDeleteLogs,
    }

    // 保留现有的场次信息
    if (sceneFormData.atmosphere) {
      annotationData.atmosphere = sceneFormData.atmosphere
    }
    if (sceneFormData.locationType) {
      annotationData.locationType = sceneFormData.locationType
    }
    if (sceneFormData.mainContent) {
      annotationData.mainContent = sceneFormData.mainContent
    }
    if (sceneFormData.remark) {
      annotationData.remark = sceneFormData.remark
    }

    try {
      await saveSceneInfo({
        id: currentScene.id,
        contentAnalysis: JSON.stringify(annotationData),
      })
      await fetchCurrentScene(currentPageIndex)
      message.success('标注删除成功')
    } catch (error) {
      message.error('标注删除失败')
    }
  }

  // 保存场次信息
  const handleSaveSceneInfo = async () => {
    if (!currentScene?.id) {
      return
    }

    const annotationData: AnnotationData = {
      annotations: annotations.filter(item => item.createType == 'user'),
      sceneId: currentScene.id,
      updater: userInfo?.nickName || '未知用户',
      updateTime: dayjs().format(DATE_FORMAT_BASE),
      deleteLogs,
    }

    // 只添加有值的字段
    if (sceneFormData.atmosphere) {
      annotationData.atmosphere = sceneFormData.atmosphere
    }
    if (sceneFormData.locationType) {
      annotationData.locationType = sceneFormData.locationType
    }
    if (sceneFormData.mainContent) {
      annotationData.mainContent = sceneFormData.mainContent
    }
    if (sceneFormData.remark) {
      annotationData.remark = sceneFormData.remark
    }

    setSaving(true)
    try {
      const success = await saveSceneInfo({
        id: currentScene.id,
        contentAnalysis: JSON.stringify(annotationData),
      })

      if (success) {
        message.success('保存成功')
      }
    } catch (error) {
      message.error('保存失败')
    } finally {
      setSaving(false)
    }
  }

  // 标注弹窗取消处理
  const handleAnnotationCancel = () => {
    setAnnotationModal({
      visible: false,
      start: 0,
      end: 0,
    })
  }

  // 刷新机器解析
  const handleRefreshRobotAnalysis = async () => {
    if (!currentScene?.id) {
      message.warning('请先选择场次')
      return
    }

    setRefreshing(true)
    try {
      const success = await refreshRobotAnalysisById(currentScene.id)

      if (success) {
        message.success('刷新机器解析成功')
        // 刷新当前页面数据
        await fetchCurrentScene(currentPageIndex)
      }
    } catch (error) {
      message.error('刷新机器解析失败')
    } finally {
      setRefreshing(false)
    }
  }

  // 导入前验证
  const beforeImportUpload = (file: any) => {
    const isWordFile =
      file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
      file.type === 'application/msword' ||
      file.name.endsWith('.doc') ||
      file.name.endsWith('.docx')

    if (!isWordFile) {
      message.error('只能上传Word文档(.doc/.docx)格式的文件')
      return false
    }

    const isLt50M = file.size / 1024 / 1024 < 50
    if (!isLt50M) {
      message.error('文件大小不能超过50MB')
      return false
    }

    return true
  }

  // 导入场次处理
  const handleImportScene = (info: any) => {
    if (info.file.status === 'uploading') {
      setImporting(true)
      return
    }

    if (info.file.status === 'done') {
      if (info.file?.response?.statusCode == -1) {
        setImporting(false)
        message.error('导入失败')
      } else {
        try {
          message.success('导入成功')
          const responseData = info.file?.response?.data

          if (responseData?.dataList && Array.isArray(responseData.dataList) && responseData.dataList.length > 0) {
            setBatchAddData(responseData.dataList)
            setBatchAddModalVisible(true)
          } else {
            fetchCurrentScene(1)
          }
        } catch (error) {
          message.error('刷新数据失败')
        } finally {
          setImporting(false)
        }
      }
    } else if (info.file.status === 'error') {
      setImporting(false)
      message.error('导入失败')
    }
  }

  // 导出顺场表
  const handleExportTemplate = async () => {
    if (!productionId) return
    try {
      await exportProductionTemplate(productionId, `${productionName || '项目'}-顺场表.xlsx`)
    } catch (error) {
      console.error('导出顺场表失败:', error)
    }
  }

  // 分页改变处理
  const handlePageChange = (page: number) => {
    fetchCurrentScene(page)
  }

  // 初始化数据
  useEffect(() => {
    if (productionId) {
      fetchCurrentScene(1)
    }
  }, [productionId])

  // 渲染带标注的文本 - 相同文本内容都会显示背景色
  const renderAnnotatedText = () => {
    if (!currentScene?.content) {
      return null
    }

    const text = currentScene.content

    // 收集所有标注的文本内容及其样式
    const textMap = new Map<string, { color: string; type: ContentType; descriptions: string[] }>()

    annotations.forEach(annotation => {
      if (textMap.has(annotation.text)) {
        const existing = textMap.get(annotation.text)!
        existing.descriptions.push(annotation.description || '')
      } else {
        textMap.set(annotation.text, {
          color: CONTENT_TYPE_CONFIG?.[annotation.type]?.color || annotation.color,
          type: annotation.type,
          descriptions: [annotation.description || ''].filter(Boolean),
        })
      }
    })

    // 如果没有标注，直接返回原文本
    if (textMap.size === 0) {
      return text
    }

    // 创建正则表达式，按长度倒序排列避免短文本匹配覆盖长文本
    const sortedTexts = Array.from(textMap.keys()).sort((a, b) => b.length - a.length)
    const regex = new RegExp(`(${sortedTexts.map(t => t.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')).join('|')})`, 'g')

    const elements: React.ReactNode[] = []
    let lastIndex = 0
    let matchIndex = 0

    text.replace(regex, (match, _p1, offset) => {
      // 添加匹配前的文本
      if (offset > lastIndex) {
        elements.push(<span key={`text-${matchIndex}`}>{text.slice(lastIndex, offset)}</span>)
      }

      // 添加匹配的标注文本
      const annotation = textMap.get(match)!
      const descriptions = annotation.descriptions.filter(Boolean)

      elements.push(
        <span
          key={`annotation-${match}-${offset}-${matchIndex}`}
          className={styles.annotatedText}
          style={{ '--annotation-color': annotation.color } as React.CSSProperties}
          title={`${CONTENT_TYPE_CONFIG?.[annotation.type]?.label}${
            descriptions.length > 0 ? `: ${descriptions.join('; ')}` : ''
          }`}>
          {match}
        </span>
      )

      lastIndex = offset + match.length
      matchIndex++

      return match
    })

    // 添加最后剩余的文本
    if (lastIndex < text.length) {
      elements.push(<span key="text-end">{text.slice(lastIndex)}</span>)
    }

    return elements
  }

  // 按类型分组渲染标注内容
  const renderGroupedAnnotations = () => {
    if (annotations.length === 0) {
      return <Empty />
    }

    // 按类型分组
    const groupedAnnotations = annotations.reduce((groups, annotation) => {
      const type = annotation.type

      if (!groups[type]) {
        groups[type] = []
      }
      groups[type].push(annotation)

      return groups
    }, {} as Record<ContentType, Annotation[]>)

    // 按时间排序每个组内的标注
    Object.keys(groupedAnnotations).forEach(type => {
      groupedAnnotations[type as unknown as ContentType].sort(
        (a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
      )
    })

    // 构建Collapse的items
    const collapseItems = Object.entries(groupedAnnotations).map(([type, typeAnnotations]) => {
      const contentType = type as unknown as ContentType
      const config = CONTENT_TYPE_CONFIG?.[contentType]

      return {
        key: type,
        label: (
          <Space>
            <Badge color={config?.color} text={<Typography.Text strong>{config?.label}</Typography.Text>} />
            <Badge color="#555" count={typeAnnotations.length} />
          </Space>
        ),
        children: (
          <List
            dataSource={typeAnnotations}
            renderItem={item => (
              <List.Item>
                <Flex justify="space-between" className="full-h hover-show-actions">
                  <Flex flex={1} vertical>
                    <Tooltip
                      placement="top"
                      title={
                        <Space>
                          {item.creator && <span>{item.creator}</span>}
                          <span>{dayjs(item.createTime).format('MM-DD HH:mm')}</span>
                        </Space>
                      }>
                      <Typography.Text>
                        &quot;
                        {item.type === ContentType.SCENE && item.firstText && item.secondText
                          ? `${item.firstText}/${item.secondText}`
                          : item.text}
                        &quot;
                      </Typography.Text>
                    </Tooltip>
                    {item.description && <Typography.Text type="secondary"> {item.description}</Typography.Text>}
                  </Flex>
                  <Button
                    type="text"
                    size="small"
                    className="actions"
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeleteAnnotation(item)}></Button>
                </Flex>
              </List.Item>
            )}></List>
        ),
      }
    })

    return (
      <Collapse
        items={collapseItems}
        defaultActiveKey={Object.keys(groupedAnnotations)}
        expandIconPosition="end"
        expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
        size="small"
      />
    )
  }

  // 文本选择监听
  useEffect(() => {
    document.addEventListener('selectionchange', handleTextSelection)

    return () => {
      document.removeEventListener('selectionchange', handleTextSelection)
      if (selectionTimeoutRef.current) {
        clearTimeout(selectionTimeoutRef.current)
      }
    }
  }, [handleTextSelection])

  return (
    <div className={styles.container}>
      {/* 顶部工具栏 */}
      <Flex justify="space-between" align="center" className={styles.toolbar}>
        <Space align="center">
          <Typography.Title level={4} className="no-margin">
            {currentScene?.sceneNumber}
          </Typography.Title>
          <Typography.Text type="secondary">(选中文本1秒后可标注)</Typography.Text>
          <Pagination
            current={currentPageIndex}
            total={totalScenes}
            pageSize={1}
            showSizeChanger={false}
            showQuickJumper
            onChange={handlePageChange}
            disabled={importing || loading}
            size="small"
          />
        </Space>

        <Space size={42}>
          <Space.Compact>
            <Button
              type="default"
              shape="round"
              size="small"
              icon={<BookOutlined />}
              className="text-primary"
              onClick={() => setDictionaryVisible(true)}>
              字典
            </Button>
            <Upload
              {...getUploadProps('/PrProductions/ImportScene')}
              accept=".doc,.docx"
              showUploadList={false}
              beforeUpload={beforeImportUpload}
              onChange={handleImportScene}
              data={{ productionId }}>
              <Button
                type="default"
                size="small"
                loading={importing}
                icon={<FileWordOutlined />}
                className="text-primary"
                disabled={!productionId || importing || loading}>
                导入剧本
              </Button>
            </Upload>
            <Button
              type="default"
              size="small"
              icon={<FormatPainterOutlined />}
              loading={refreshing}
              disabled={!currentScene?.id || refreshing || loading}
              className="text-primary"
              onClick={handleRefreshRobotAnalysis}>
              刷新机器标注
            </Button>
            <Button
              type="default"
              size="small"
              icon={<UnorderedListOutlined />}
              className="text-primary"
              onClick={() => setSceneDataVisible(true)}>
              查看顺场表
            </Button>
            <Button
              type="default"
              size="small"
              shape="round"
              icon={<CloudDownloadOutlined />}
              className="text-primary"
              onClick={handleExportTemplate}>
              导出顺场表
            </Button>
          </Space.Compact>
        </Space>
      </Flex>

      {/* 主要内容区域 */}
      <Card size="small" className={styles.contentArea}>
        <Splitter>
          <Splitter.Panel defaultSize="65%" min="50%" max="80%">
            <div ref={textContainerRef} className={styles.textPanel}>
              {currentScene?.content ? <div>{renderAnnotatedText()}</div> : <Empty />}
            </div>
          </Splitter.Panel>
          <Splitter.Panel>
            <div className={styles.rightPanel}>
              <Flex vertical gap={16}>
                <Flex vertical gap={4}>
                  <Flex justify="space-between" align="center">
                    <Typography.Text strong>1、场次信息</Typography.Text>
                    <Button
                      type="primary"
                      size="small"
                      loading={saving}
                      disabled={importing || loading}
                      onClick={handleSaveSceneInfo}>
                      立即保存
                    </Button>
                  </Flex>
                  <Divider variant="dotted" style={{ margin: 0 }} />
                </Flex>
                <Form layout="horizontal" colon={false} labelCol={{ span: 4 }} size="small">
                  <Form.Item label="气氛">
                    <Radio.Group
                      value={sceneFormData.atmosphere}
                      onChange={e => setSceneFormData({ ...sceneFormData, atmosphere: e.target.value })}
                      disabled={importing || loading}
                      options={ATMOSPHERE_TYPE_OPTIONS}
                    />
                  </Form.Item>

                  <Form.Item label="内/外景">
                    <Radio.Group
                      value={sceneFormData.locationType}
                      onChange={e => setSceneFormData({ ...sceneFormData, locationType: e.target.value })}
                      disabled={importing || loading}
                      options={LOCATION_TYPE_OPTIONS}
                    />
                  </Form.Item>

                  <Form.Item label="概要">
                    <Input.TextArea
                      value={sceneFormData.mainContent}
                      onChange={e => setSceneFormData({ ...sceneFormData, mainContent: e.target.value })}
                      placeholder="请输入场次概要"
                      rows={2}
                      disabled={importing || loading}
                    />
                  </Form.Item>

                  <Form.Item label="备注">
                    <Input.TextArea
                      value={sceneFormData.remark}
                      onChange={e => setSceneFormData({ ...sceneFormData, remark: e.target.value })}
                      placeholder="请输入备注"
                      rows={2}
                      disabled={importing || loading}
                    />
                  </Form.Item>
                </Form>
              </Flex>

              <Flex vertical gap={6} className={styles.sceneInfo}>
                <Space size={3}>
                  <Typography.Text strong>2、已标注</Typography.Text>
                  <Typography.Text type="secondary">({`共 ${annotations.length} 个`})</Typography.Text>
                </Space>
                {renderGroupedAnnotations()}
              </Flex>
            </div>
          </Splitter.Panel>
        </Splitter>
      </Card>

      {/* 字典管理 */}
      {dictionaryVisible && (
        <DictionaryManagement
          visible={dictionaryVisible}
          onClose={handleCloseDictionary}
          productionId={productionId}
          productionName={productionName}
        />
      )}

      {/* 批量新增字典弹窗 */}
      {batchAddModalVisible && (
        <BatchAddDictionaryModal
          visible={batchAddModalVisible}
          onClose={handleCloseBatchAddModal}
          productionId={productionId}
          batchData={batchAddData}
        />
      )}

      {/* 查看顺场表 */}
      {sceneDataVisible && (
        <SceneDataSidebar
          visible={sceneDataVisible}
          onClose={() => setSceneDataVisible(false)}
          productionId={productionId}
        />
      )}

      {/* 标注弹窗 */}
      <AnnotationModal
        visible={annotationModal.visible}
        form={annotationForm}
        onSave={handleSaveAnnotation}
        onCancel={handleAnnotationCancel}
        saving={saving}
        productionId={productionId}
      />
    </div>
  )
}

export default SceneAnnotationTab
