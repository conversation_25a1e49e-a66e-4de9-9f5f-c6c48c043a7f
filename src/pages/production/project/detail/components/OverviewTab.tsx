import { PRICE_CURRENCY_CONFIG, PRODUCTION_TYPE_CONFIG, ProductionType } from '@/consts'
import { DATE_FORMAT_DAY } from '@/consts/date'
import { PROJECT_STATUS_MAP } from '@/consts/project'
import useIndexStore from '@/store'
import { exportProductionInsurance } from '@/utils/export'
import { CloudDownloadOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { ListHeader } from '@fe/rockrose'
import { Button, Descriptions, Divider, Flex, Popconfirm, Space, Typography } from 'antd'
import dayjs from 'dayjs'
import React from 'react'
import { IProductionListItem } from '../../list/store'
import ProductionProgressCard from './ProductionProgressCard'

interface IOverviewTabProps {
  project?: IProductionListItem
  onEdit?: (project: IProductionListItem) => void
  onDelete?: (project: IProductionListItem) => void
}

const OverviewTab: React.FC<IOverviewTabProps> = ({ project, onEdit, onDelete }) => {
  const { authorBtn } = useIndexStore()
  // 处理导出保险记录
  const handleExportInsurance = async () => {
    if (!project?.id) {
      return
    }

    try {
      await exportProductionInsurance(project.id, `${project.productionName}-投保名单.xlsx`)
    } catch (error) {
      console.error('导出保险记录失败:', error)
    }
  }

  return (
    <Flex vertical gap={16}>

      <Flex vertical gap={4}>
        <ListHeader title="基本信息" className="no-margin">
          <Space size={0} split={<Divider type="vertical" />}>
            {project?.productionType !== 1 && project?.status <= 2  && authorBtn.includes('导出保险名单') && (
              <Button type="link" icon={<CloudDownloadOutlined />} onClick={handleExportInsurance}>
                导出保险名单
              </Button>
            )}
            {project && (
              <Space size={0} split={<Divider type="vertical" />}>
                {authorBtn.includes('删除') && !project.feNoEdit && project.status == 0 && (
                  <Popconfirm
                    title="警告"
                    description={`确定要删除【${project.productionName}】吗？`}
                    onConfirm={() => onDelete?.(project)}
                    okText="确定删除"
                    cancelText="取消">
                    <Button type="link" icon={<DeleteOutlined />}>
                      删除
                    </Button>
                  </Popconfirm>
                )}
                {authorBtn.includes('修改') && <Button type="link" icon={<EditOutlined />} onClick={() => onEdit?.(project)}>
                  编辑
                </Button>}
              </Space>
            )}
          </Space>
        </ListHeader>
        <Descriptions bordered column={3}>
          <Descriptions.Item label="项目">
            <Typography.Text strong>{project?.productionName || '-'}</Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="类型">
            {project?.productionType !== undefined
              ? PRODUCTION_TYPE_CONFIG[project.productionType as ProductionType]?.label || '-'
              : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="剧本代号">{project?.productionCode || '-'}</Descriptions.Item>
          <Descriptions.Item label="项目代号">{project?.secondProductionCode || '-'}</Descriptions.Item>
          <Descriptions.Item label="剧本ID">{project?.scriptBookId || '-'}</Descriptions.Item>
          <Descriptions.Item label="结算货币">
            {project?.priceCurrency ? PRICE_CURRENCY_CONFIG[project.priceCurrency]?.label || '-' : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="城市">{project?.cityName || '-'}</Descriptions.Item>
          {project?.productionType == 1 && project?.companyInfo?.companyName ? (
            <Descriptions.Item label="公司">{project?.companyInfo?.companyName}</Descriptions.Item>
          ) : null}

          <Descriptions.Item label="总集数">{project?.totalEpisodes || '-'}</Descriptions.Item>
          <Descriptions.Item label="总时长">
            {project?.totalDuration ? `${project.totalDuration}分钟` : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="开机日期">
            {project?.startDate ? dayjs(project?.startDate).format(DATE_FORMAT_DAY) : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="杀青日期">
            {project?.endDate ? dayjs(project.endDate).format(DATE_FORMAT_DAY) : '-'}
          </Descriptions.Item>
          <Descriptions.Item label="状态">{PROJECT_STATUS_MAP?.[project?.status]?.label || '-'}</Descriptions.Item>

          {!!project?.description && (
            <Descriptions.Item label="描述" span={3}>
              {project?.description}
            </Descriptions.Item>
          )}
        </Descriptions>
      </Flex>
      {/* 项目进度卡片 */}
      <ProductionProgressCard project={project} />
    </Flex>
  )
}

export default OverviewTab
