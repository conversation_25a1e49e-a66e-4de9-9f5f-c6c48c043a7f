import { ProjectStatus } from '@/consts/project'
import { getAdjustedCurrentTime } from '@/utils'
import { ListHeader } from '@fe/rockrose'
import { Card, Divider, Flex, Progress, ProgressProps, Space, Statistic, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useMemo, useState } from 'react'
import { IProductionListItem } from '../../list/store'
import useProjectStore, { IVerificationProgress } from '../../store'

const twoColors: ProgressProps['strokeColor'] = {
  '0%': '#108ee9',
  '100%': '#87d068',
}

interface IProductionProgressCardProps {
  project?: IProductionListItem
}

const ProductionProgressCard: React.FC<IProductionProgressCardProps> = ({ project }) => {
  const { getProductionTrackProgress } = useProjectStore()
  const [progress, setProgress] = useState<IVerificationProgress | null>(null)
  const [loading, setLoading] = useState(false)

  // 获取项目进度数据
  const fetchProgress = async () => {
    if (!project?.id) return

    setLoading(true)
    try {
      const result = await getProductionTrackProgress(project.id)
      setProgress(result)
    } catch (error) {
      console.error('获取项目进度失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchProgress()
  }, [project?.id])

  // 计算项目天数
  const projectDays = useMemo(() => {
    if (!project?.startDate) return 0

    const startDate = dayjs(project.startDate)

    // 如果项目已结束且有结束日期，计算总拍摄天数
    if (project?.endDate && project?.status >= ProjectStatus.SHOOTING) {
      const endDate = dayjs(project.endDate)
      return Math.max(1, endDate.diff(startDate, 'day') + 1)
    }

    // 如果项目正在拍摄中，计算当前天数（考虑5:00分界点）
    if (project?.status === ProjectStatus.SHOOTING) {
      const adjustedToday = getAdjustedCurrentTime()
      return Math.max(1, adjustedToday.diff(startDate, 'day') + 1)
    }

    return 0
  }, [project?.startDate, project?.endDate, project?.status])

  // 获取当前拍摄天数（用于查找当天数据，考虑5:00分界点）
  const currentDay = useMemo(() => {
    if (!project?.startDate || project?.status !== ProjectStatus.SHOOTING) return 0

    const startDate = dayjs(project.startDate)
    const adjustedToday = getAdjustedCurrentTime()

    return Math.max(1, adjustedToday.diff(startDate, 'day') + 1)
  }, [project?.startDate, project?.status])

  // 计算所有统计数据
  const statisticsData = useMemo(() => {
    // 获取当天数据
    const currentDayData = progress?.dayTotal?.find(day => day.day === currentDay) || null

    // 计算总体累计数据
    const totalVerificationCount = progress?.dayTotal?.reduce((sum, day) => sum + (day.verificationCount || 0), 0) || 0
    const totalVerificationPageCount =
      progress?.dayTotal?.reduce((sum, day) => sum + (day.verificationPageCount || 0), 0) || 0

    // 计算总进度百分比
    const totalVerificationProgress = progress?.totalCount
      ? Math.round((totalVerificationCount / progress.totalCount) * 100)
      : 0

    const totalPageProgress = progress?.totalPageNum
      ? Math.round((totalVerificationPageCount / progress.totalPageNum) * 100)
      : 0

    // 计算当天进度百分比
    const currentDayVerificationProgress = currentDayData?.totalCount
      ? Math.round(((currentDayData.verificationCount || 0) / currentDayData.totalCount) * 100)
      : 0

    const currentDayPageProgress = currentDayData?.totalPageNum
      ? Math.round(((currentDayData.verificationPageCount || 0) / currentDayData.totalPageNum) * 100)
      : 0

    return {
      // 当天数据
      currentDayData,

      // 总体数据 - 场次显示为整数
      totalVerificationCount: Math.round(totalVerificationCount),
      totalVerificationPageCount: Number(totalVerificationPageCount.toFixed(1)),
      totalVerificationProgress,
      totalPageProgress,
      totalCount: Math.round(progress?.totalCount || 0),
      totalPageNum: Number((progress?.totalPageNum || 0).toFixed(1)),

      // 当天数据 - 场次显示为整数，页数保留一位小数
      currentDayVerificationProgress,
      currentDayPageProgress,
      currentDayTotalCount: Math.round(currentDayData?.totalCount || 0),
      currentDayTotalPageNum: Number((currentDayData?.totalPageNum || 0).toFixed(1)),
      currentDayVerificationCount: Math.round(currentDayData?.verificationCount || 0),
      currentDayVerificationPageCount: Number((currentDayData?.verificationPageCount || 0).toFixed(1)),
    }
  }, [progress, currentDay])

  // 如果项目未开机，不显示进度统计
  if (!project?.startDate || projectDays === 0 || project.productionType != 0) {
    return null
  }

  return (
    <Flex vertical gap={4}>
      <ListHeader title="拍摄进度" className="no-margin"></ListHeader>

      <Card loading={loading}>
        <Flex gap={32}>
          <Flex flex={1}>
            <Flex vertical gap={8}>
              <Typography.Text strong>整体进度</Typography.Text>
              <Statistic title="拍摄计划" value={projectDays} suffix={<span className="fs-lg">天</span>} />
            </Flex>
          </Flex>
          <Flex flex={1} vertical gap={8}>
            <Typography.Text type="secondary">销场进度</Typography.Text>
            <Typography.Title level={4} className="no-margin fw-normal">
              <Space>
                <span>
                  {statisticsData.totalVerificationCount} / {statisticsData.totalCount}
                </span>
                <span className="fs-sm">场次</span>
              </Space>
            </Typography.Title>
            <Progress
              percent={statisticsData.totalVerificationProgress}
              showInfo
              percentPosition={{ align: 'center', type: 'inner' }}
              strokeColor={twoColors}
              size={['70%', 16]}
            />
          </Flex>
          <Flex flex={1} vertical gap={8}>
            <Typography.Text type="secondary">页数进度</Typography.Text>
            <Typography.Title level={4} className="no-margin fw-normal">
              <Space>
                <span>
                  {statisticsData.totalVerificationPageCount} / {statisticsData.totalPageNum}
                </span>
                <span className="fs-sm">页</span>
              </Space>
            </Typography.Title>
            <Progress
              percent={statisticsData.totalPageProgress}
              showInfo
              percentPosition={{ align: 'center', type: 'inner' }}
              strokeColor={twoColors}
              size={['70%', 16]}
            />
          </Flex>
        </Flex>

        {/* 当天数据统计 */}
        {currentDay > 0 && (
          <>
            <Divider dashed />
            <Flex gap={32}>
              <Flex flex={1}>
                <Flex vertical gap={8}>
                  <Typography.Title level={5} className="no-margin">
                    第 {currentDay} 天进度
                  </Typography.Title>
                  <Statistic
                    title="拍摄计划"
                    value={statisticsData.currentDayTotalCount}
                    suffix={<span className="fs-lg">场次</span>}
                  />
                </Flex>
              </Flex>
              <Flex flex={1} vertical gap={8}>
                <Typography.Text type="secondary">销场进度</Typography.Text>
                <Typography.Title level={4} className="no-margin fw-normal">
                  <Space>
                    <span>
                      {statisticsData.currentDayVerificationCount} / {statisticsData.currentDayTotalCount}
                    </span>
                    <span className="fs-sm">场次</span>
                  </Space>
                </Typography.Title>
                <Progress
                  percent={statisticsData.currentDayVerificationProgress}
                  showInfo
                  percentPosition={{ align: 'center', type: 'inner' }}
                  strokeColor={twoColors}
                  size={['70%', 16]}
                />
              </Flex>
              <Flex flex={1} vertical gap={8}>
                <Typography.Text type="secondary">页数进度</Typography.Text>
                <Typography.Title level={4} className="no-margin fw-normal">
                  <Space>
                    <span>
                      {statisticsData.currentDayVerificationPageCount} / {statisticsData.currentDayTotalPageNum}
                    </span>
                    <span className="fs-sm">页</span>
                  </Space>
                </Typography.Title>
                <Progress
                  percent={statisticsData.currentDayPageProgress}
                  showInfo
                  percentPosition={{ align: 'center', type: 'inner' }}
                  strokeColor={twoColors}
                  size={['70%', 16]}
                />
              </Flex>
            </Flex>
          </>
        )}
      </Card>
    </Flex>
  )
}

export default ProductionProgressCard
