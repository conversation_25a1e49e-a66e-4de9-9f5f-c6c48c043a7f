import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>sul<PERSON>, <PERSON> } from 'antd'
import React, { useEffect, useState } from 'react'
import ActorDetailContent from '../list/components/ActorDetailContent'
import useActorListStore, { IActorListItem } from '../list/store'

interface ActorDetailProps {
  visible: boolean
  actorId?: number
  onClose: () => void
}

const ActorDetail: React.FC<ActorDetailProps> = ({ visible, actorId, onClose }) => {
  const { getActorById } = useActorListStore()
  const [actor, setActor] = useState<IActorListItem | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')

  // 获取演员详情
  const fetchActorDetail = async () => {
    if (!actorId) {
      return
    }

    try {
      setLoading(true)
      setError('')
      const result = await getActorById(actorId)

      if (result) {
        setActor(result)
      } else {
        setError('未找到该演员信息')
      }
    } catch (err) {
      setError('获取演员详情失败')
      console.error('获取演员详情失败:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (visible && actorId) {
      fetchActorDetail()
    }
  }, [visible, actorId])

  // 处理演员信息更新
  const handleActorUpdate = (updatedActor: IActorListItem) => {
    setActor(updatedActor)
  }

  const handleClose = () => {
    setActor(null)
    setError('')
    onClose()
  }

  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center h-96">
          <Spin size="large" tip="加载中..." />
        </div>
      )
    }

    if (error) {
      return (
        <Result
          status="error"
          title="加载失败"
          subTitle={error}
          extra={
            <Button type="primary" onClick={handleClose}>
              关闭
            </Button>
          }
        />
      )
    }

    if (!actor) {
      return null
    }

    return <ActorDetailContent actor={actor} onActorUpdate={handleActorUpdate} />
  }

  return (
    <Drawer
      title={
        <div className="flex items-center gap-3">
          <span>演员详情 - {actor?.personName}</span>
          {actor?.stageName && <span className="text-gray-500 font-normal">({actor.stageName})</span>}
        </div>
      }
      placement="right"
      width="960px"
      open={visible}
      onClose={handleClose}
      destroyOnClose>
      {renderContent()}
    </Drawer>
  )
}

export default ActorDetail
