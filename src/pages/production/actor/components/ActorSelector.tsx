import useActorStore from '@/pages/production/actor/list/store'
import { useDebounceFn } from 'ahooks'
import { Select } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'

// 演员选择器
const ActorSelector: React.FC<{
  value?: number
  onChange?: (value: number) => void
  onSelect?: (actorId: number, option: any) => void
  placeholder?: string
  disabled?: boolean
  initOption?: any // 初始化选项，用于编辑时回填
}> = ({ value, onChange, onSelect, placeholder = '请选择演员', disabled = false, initOption }) => {
  const [options, setOptions] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [searchValue, setSearchValue] = useState('')
  const { fetchActorList } = useActorStore()
  const optionsFormat = useMemo(
    () =>
      options.map(item => {
        let labelStr = item.personName

        if (item.isInternal) {
          labelStr = item.jobNumber ? `${item.personName}(内部_${item.jobNumber})` : `${item.personName}(内部)`
        } else {
          labelStr = `${item.personName}(外部)`
        }

        return {
          ...item,
          labelStr,
          disabled: item.status == 2,
        }
      }),
    [options]
  )

  // 初始化选项
  useEffect(() => {
    if (initOption) {
      setOptions([initOption])
    } else {
      // 如果没有初始选项，加载默认的演员列表
      loadDefaultActors()
    }
  }, [initOption])

  // 加载默认演员列表
  const loadDefaultActors = async () => {
    setLoading(true)
    try {
      const result = await fetchActorList({
        pageIndex: 1,
        pageSize: 20, // 默认加载20个演员
        personName: '', // 空搜索，获取所有演员
      })

      if (result?.list) {
        setOptions(result.list)
      }
    } catch (error) {
      console.error('加载默认演员列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 防抖搜索
  const { run: debouncedSearch } = useDebounceFn(
    async (searchText: string) => {
      if (!searchText.trim()) {
        // 如果清空搜索，恢复初始选项或重新加载默认列表
        if (initOption) {
          setOptions([initOption])
        } else {
          await loadDefaultActors()
        }

        return
      }

      setLoading(true)
      try {
        const result = await fetchActorList({
          pageIndex: 1,
          pageSize: 50,
          personName: searchText.trim(),
        })

        if (result?.list) {
          // 如果有初始选项且当前值匹配，确保初始选项在结果中
          const newOptions = result.list

          if (initOption && value === initOption.id && !newOptions.find(opt => opt.id === initOption.id)) {
            newOptions.unshift(initOption)
          }
          setOptions(newOptions)
        } else {
          setOptions(initOption ? [initOption] : [])
        }
      } catch (error) {
        console.error('搜索演员失败:', error)
        setOptions(initOption ? [initOption] : [])
      } finally {
        setLoading(false)
      }
    },
    { wait: 300 }
  )

  const handleSearch = (searchText: string) => {
    setSearchValue(searchText)
    debouncedSearch(searchText)
  }

  const handleChange = (selectedValue: number) => {
    onChange?.(selectedValue)
  }

  const handleSelect = (selectedValue: number, option: any) => {
    onSelect?.(selectedValue, option)
  }

  const getNotFoundContent = () => {
    if (loading) {
      return '搜索中...'
    }
    if (searchValue) {
      return '暂无匹配结果'
    }
    if (options.length === 0) {
      return '暂无演员数据'
    }

    return '请输入搜索关键词以筛选演员'
  }

  return (
    <Select
      placeholder={placeholder}
      showSearch
      loading={loading}
      disabled={disabled}
      options={optionsFormat}
      fieldNames={{ label: 'labelStr', value: 'id' }}
      value={value}
      onChange={handleChange}
      onSelect={handleSelect}
      onSearch={handleSearch}
      searchValue={searchValue}
      filterOption={false} // 禁用本地过滤
      notFoundContent={getNotFoundContent()}
    />
  )
}

export default ActorSelector
