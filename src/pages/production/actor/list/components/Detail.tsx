import useIndexStore from '@/store'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, Drawer, DrawerProps, Popconfirm, Space, Typography } from 'antd'
import React from 'react'
import { IActorListItem } from '../store'
import ActorDetailContent from './ActorDetailContent'

interface IProps extends DrawerProps {
  actor?: IActorListItem
  onActorUpdate?: (updatedActor: IActorListItem) => void
  onEdit?: (actor: IActorListItem) => void
  onDelete?: (actor: IActorListItem) => void
}

const ActorDetail: React.FC<IProps> = ({ actor, onActorUpdate, onEdit, onDelete, ...props }) => {
  const { authorBtn } = useIndexStore()

  return (
    <Drawer
      width={960}
      title={
        <Space>
          <span>{actor?.personName}</span>
          {actor?.stageName && (
            <Typography.Text type="secondary" className="fw-normal">
              ({actor.stageName})
            </Typography.Text>
          )}
        </Space>
      }
      extra={
        actor && (
          <Space.Compact>
            {authorBtn.includes('删除') ? (
              <Popconfirm
                title="警告"
                description={`确定要删除演员 "${actor.personName}" 吗？删除后无法恢复！`}
                onConfirm={() => onDelete?.(actor)}
                okText="确定删除"
                cancelText="取消"
                okType="danger">
                <Button type="default" shape="round" className="text-primary" icon={<DeleteOutlined />}>
                  删除
                </Button>
              </Popconfirm>
            ) : null}
            <Button
              type="default"
              shape="round"
              className="text-primary"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(actor)}>
              编辑
            </Button>
          </Space.Compact>
        )
      }
      {...props}
      destroyOnHidden>
      <ActorDetailContent actor={actor} onActorUpdate={onActorUpdate} />
    </Drawer>
  )
}

export default ActorDetail
