import { MediaFormInput } from '@/components/Media'
import { Form, Input, Modal } from 'antd'
import type { FormInstance } from 'antd/lib'
import React from 'react'
import { prActorsUploadAPI } from '../store'

interface AddMediaProps {
  title: string
  open?: boolean
  loading?: boolean
  type?: number | null
  form: FormInstance
  onCancel: () => void
  onOk: () => void
}

const AddMedia: React.FC<AddMediaProps> = ({ title, open = false, type, form, loading, onOk, onCancel }) => {
  return (
    <Modal
      title={title}
      open={open}
      onCancel={onCancel}
      onOk={onOk}
      confirmLoading={loading}
      width={600}
      okText="立即保存">
      <Form form={form} layout="vertical" style={{ marginTop: 20 }}>
        {/* <Form.Item name="label" label="标签">
          <Input placeholder="请输入标签（可选）" />
        </Form.Item> */}
        <Form.Item name="mediaUrl" label={type == 4 ? '价格' : ''} rules={[{ required: true, message: '请选择文件' }]}>
          <Form.Item shouldUpdate noStyle>
            {() => {
              const currentValue = form.getFieldValue('mediaUrl')

              return (
                <MediaFormInput
                  mediaType={type || 1}
                  value={currentValue}
                  onChange={value => form.setFieldValue('mediaUrl', value)}
                  uploadAction={prActorsUploadAPI}
                />
              )
            }}
          </Form.Item>
        </Form.Item>
        {type === 4 && (
          <Form.Item name="description" label="短剧项目" rules={[{ required: false }]}>
            <Input placeholder="某个项目的报价，例如：缚苍龙" />
          </Form.Item>
        )}
      </Form>
    </Modal>
  )
}

export default AddMedia
