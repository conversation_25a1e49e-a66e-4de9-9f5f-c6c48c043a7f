import filterMatch from '@/utils/filterMatch'
import { useDebounceFn } from 'ahooks'
import { Button, Form, Input, Select, Space } from 'antd'
import React from 'react'
import { IS_INTERNAL_OPTIONS, IS_WX_USER_OPTIONS, PERSON_STATUS_OPTIONS } from '../../../../../consts'
import { CHINA_CITY_OPTIONS } from '../../../../../consts/city'

const { Option } = Select

const ActorSearch: React.FC<ISearchProps> = ({ loading = false, onSearch, form }) => {
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(1), { wait: 500 })

  return (
    <Form form={form} onValuesChange={onSearchDebounce} layout="vertical" className="search-form">
      <Space wrap size={24}>
        <Form.Item name="personName" label="姓名">
          <Input className="w200" placeholder="输入姓名" allowClear />
        </Form.Item>
        <Form.Item name="stageName" label="艺名">
          <Input className="w200" placeholder="输入艺名" allowClear />
        </Form.Item>
        <Form.Item name="gender" label="性别">
          <Select className="w200" placeholder="默认全选" allowClear>
            <Option value={1}>男</Option>
            <Option value={2}>女</Option>
            <Option value={3}>其他</Option>
          </Select>
        </Form.Item>
        <Form.Item name="isChildActor" label="儿童演员">
          <Select className="w200" placeholder="默认全选" allowClear>
            <Option value={true}>是</Option>
            <Option value={false}>否</Option>
          </Select>
        </Form.Item>
        <Form.Item name="isInternal" label="是否内部">
          <Select className="w200" placeholder="默认全选" allowClear options={IS_INTERNAL_OPTIONS} />
        </Form.Item>
        <Form.Item name="status" label="人员状态">
          <Select className="w200" placeholder="默认全选" allowClear options={PERSON_STATUS_OPTIONS} />
        </Form.Item>
        <Form.Item name="cityName" label="所在城市">
          <Select
            mode="multiple"
            className="w200"
            placeholder="默认全选"
            options={[...CHINA_CITY_OPTIONS]}
            showSearch
            allowClear
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.label || '')

              return Boolean(result)
            }}
          />
        </Form.Item>
        <Form.Item name="isWxUser" label="来源">
          <Select className="w200" placeholder="默认全选" allowClear options={IS_WX_USER_OPTIONS} />
        </Form.Item>

        <Form.Item name="wxNickName" label="微信昵称">
          <Input className="w200" placeholder="支持模糊查询" allowClear />
        </Form.Item>

        <Form.Item label=" ">
          <Button type="primary" onClick={onSearchDebounce} loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Space>
    </Form>
  )
}

export default ActorSearch
