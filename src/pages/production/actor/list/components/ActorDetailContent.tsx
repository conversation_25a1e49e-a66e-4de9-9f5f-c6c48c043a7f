import EvaluationHistory from '@/components/EvaluationHistory'
import {
  ACTOR_ROLE_TYPE_CONFIG,
  GENDER_OPTIONS,
  IS_WX_USER_CONFIG,
  MEDIA_TYPE_OPTIONS,
  PERSON_STATUS_CONFIG,
  PERSON_TYPE_CONFIG,
} from '@/consts'
import { DATE_FORMAT_BASE } from '@/consts/date'
import { envUrl } from '@/utils/request'
import {
  CloseCircleOutlined,
  FileOutlined,
  FireOutlined,
  PictureOutlined,
  PlusOutlined,
  VideoCameraOutlined,
} from '@ant-design/icons'
import { ListHeader } from '@fe/rockrose'
import {
  Badge,
  Button,
  Card,
  Descriptions,
  Divider,
  Empty,
  Flex,
  Form,
  Image,
  message,
  Popconfirm,
  Space,
  Tabs,
  TabsProps,
  Tag,
  Timeline,
  Typography,
} from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { MediaRenderer } from '../../../../../components/Media'
import WorkInfoForm from '../../../../../components/WorkInfoForm'
import useActorListStore, { IActorListItem } from '../store'
import AddMedia from './AddMedia'
import styles from './Detail.scss'

interface IActorDetailContentProps {
  actor?: IActorListItem
  onActorUpdate?: (updatedActor: IActorListItem) => void
}

const ActorDetailContent: React.FC<IActorDetailContentProps> = ({ actor, onActorUpdate }) => {
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [workModalVisible, setWorkModalVisible] = useState(false)
  const [selectedMediaType, setSelectedMediaType] = useState<number | null>(null)
  const [currentActor, setCurrentActor] = useState<IActorListItem | undefined>(actor) // 添加内部状态
  const [currentEditingWork, setCurrentEditingWork] = useState<any | null>(null) // 当前编辑的作品，null表示新增模式
  const [form] = Form.useForm()
  const [workForm] = Form.useForm()
  const { saveActor, getActorById, saveActorMedia, deleteActorMedia, saveActorWorks, deleteActorWorks } =
    useActorListStore()

  // 当外部传入的actor发生变化时，更新内部状态
  useEffect(() => {
    setCurrentActor(actor)
  }, [actor])

  // 获取性别文本
  const getGenderText = (gender: number) => GENDER_OPTIONS.find(item => item.value === gender)?.label || '未知'

  // 获取人员类型信息
  const personTypeInfo = currentActor?.personType ? PERSON_TYPE_CONFIG[currentActor.personType] : null

  // 获取媒体类型文本和图标
  const getMediaTypeInfo = (mediaType: number) => {
    const mediaConfig = MEDIA_TYPE_OPTIONS.find(option => option.value === mediaType)
    const label = mediaConfig?.label || '未知类型'
    const iconName = mediaConfig?.icon || 'FileOutlined'
    const color = mediaConfig?.color || 'default'

    // 根据图标名称返回对应的React组件
    let icon = <FileOutlined />

    switch (iconName) {
      case 'PictureOutlined':
        icon = <PictureOutlined />
        break
      case 'VideoCameraOutlined':
        icon = <VideoCameraOutlined />
        break
      case 'FileOutlined':
      default:
        icon = <FileOutlined />
        break
    }

    return { label, icon, color }
  }

  // 计算年龄
  const getAge = (dateOfBirth?: number) => {
    if (!dateOfBirth) {
      return null
    }
    const currentYear = new Date().getFullYear()

    return currentYear - dateOfBirth
  }

  // 按时间排序所有媒体信息
  const sortedMediaInfos = [...(currentActor?.mediaInfos || [])].sort((a, b) => {
    if (!a.createTime || !b.createTime) {
      return 0
    }

    return new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
  })

  // 根据媒体类型过滤数据
  const getMediaByTypes = (types: number[]) => sortedMediaInfos.filter(media => types.includes(media.mediaType))

  // 渲染媒体网格
  const renderMediaGrid = (mediaList: any[], emptyText: string, mediaType?: number) => {
    const typeName = MEDIA_TYPE_OPTIONS.find(opt => opt.value === mediaType)?.label

    // 获取媒体配置信息
    const isSpecialType = mediaType ? [4, 5].includes(mediaType) : false // 报价和简历类型

    // 计算总数：特殊类型不拆分URL，其他类型按URL数量计算
    const getTotalCount = () => {
      if (isSpecialType) {
        return mediaList.length
      }

      return mediaList.reduce((total, media) => {
        const urls = media.mediaUrl ? media.mediaUrl.split(',').filter((url: string) => url.trim()) : []

        return total + urls.length
      }, 0)
    }

    const totalCount = getTotalCount()

    return (
      <Flex vertical gap={2}>
        {mediaType && (
          <ListHeader title={typeName} total={totalCount}>
            <Button type="primary" ghost icon={<PlusOutlined />} onClick={() => handleAddMedia(mediaType)}>
              添加{typeName || '媒体文件'}
            </Button>
          </ListHeader>
        )}

        {mediaList.length === 0 ? (
          <Empty description={emptyText} />
        ) : (
          <div>
            {isSpecialType ? (
              // 报价和简历类型：使用卡片布局，不拆分URL
              <Flex vertical gap={12}>
                {mediaList.map(media => (
                  <Card size="small" key={media.id} className="hover-move hover-show-remove">
                    <Flex justify="space-between">
                      <MediaRenderer media={media} />
                      <Typography.Text type="secondary">
                        {dayjs(media.createTime).format(DATE_FORMAT_BASE)}
                      </Typography.Text>
                    </Flex>
                    <Popconfirm
                      title="警告"
                      description={`确定要删除该${mediaType === 4 ? '报价' : '简历'}吗？`}
                      onConfirm={() => handleDeleteMedia(media.id)}
                      okText="确定删除"
                      cancelText="取消">
                      <Button
                        type="text"
                        className="remove"
                        icon={<CloseCircleOutlined className="fs-xlg text-secondary" />}
                      />
                    </Popconfirm>
                  </Card>
                ))}
              </Flex>
            ) : (
              // 图片/视频类型：支持多URL显示，统一排列
              (() => {
                // 收集所有URL用于统一预览
                const allUrls: string[] = []

                mediaList.forEach(media => {
                  const urls = media.mediaUrl ? media.mediaUrl.split(',').filter((url: string) => url.trim()) : []

                  allUrls.push(...urls.map((url: string) => `${envUrl}${url.trim()}`))
                })

                return (
                  <Image.PreviewGroup items={allUrls}>
                    <Flex gap={18} wrap>
                      {mediaList.map(media => {
                        const urls = media.mediaUrl ? media.mediaUrl.split(',').filter((url: string) => url.trim()) : []

                        if (urls.length === 0) {
                          return null
                        }

                        return urls.map((url: string, urlIndex: number) => (
                          <div key={`${media.id}-${urlIndex}`} className="hover-move hover-show-remove">
                            <MediaRenderer media={{ ...media, mediaUrl: url.trim() }} width={135} height={180} />
                            {media.label && (
                              <Flex justify="end" gap={12}>
                                <Tag color="volcano" className="no-margin">
                                  {media.label}
                                </Tag>
                              </Flex>
                            )}
                            {/* 为每个URL添加删除按钮 */}
                            <Popconfirm
                              title="警告"
                              description={urls.length > 1 ? '确定要删除这个文件吗？' : '确定要删除该媒体记录吗？'}
                              onConfirm={() =>
                                urls.length > 1 ? handleDeleteSingleUrl(media, url.trim()) : handleDeleteMedia(media.id)
                              }
                              okText="确定删除"
                              cancelText="取消">
                              <Button
                                type="text"
                                className="remove"
                                icon={<CloseCircleOutlined className="fs-xlg text-secondary " />}
                              />
                            </Popconfirm>
                          </div>
                        ))
                      })}
                    </Flex>
                  </Image.PreviewGroup>
                )
              })()
            )}
          </div>
        )}
      </Flex>
    )
  }

  // 渲染作品信息
  const renderWorkInfos = () => {
    const workInfos = currentActor?.workInfos || []

    return (
      <Flex vertical gap={2}>
        <ListHeader title="代表作品" total={workInfos.length}>
          <Button
            type="primary"
            ghost
            icon={<PlusOutlined />}
            onClick={() => {
              setCurrentEditingWork(null) // 设为新增模式
              setWorkModalVisible(true)
              workForm.setFieldsValue({ workInfos: [{}] })
            }}>
            添加代表作品
          </Button>
        </ListHeader>

        {workInfos.length === 0 ? (
          <Empty description="暂无数据" />
        ) : (
          <Flex vertical gap={12}>
            {/* 按排序字段排序 */}
            {[...workInfos]
              .sort((a, b) => (a.sort || 0) - (b.sort || 0))
              .map(work => (
                <Card key={work.id} size="small" hoverable className="hover-move hover-show-actions">
                  <Flex>
                    <Flex flex={1} vertical gap={work.roleDescription ? 12 : 0}>
                      <Space size={2} split={<Divider type="vertical" />}>
                        <Space size={4}>
                          {work.isHit && (
                            <Typography.Text type="danger">
                              <FireOutlined />
                            </Typography.Text>
                          )}
                          <Typography.Text strong>《{work.workName}》</Typography.Text>
                        </Space>
                        {work.workType && <Tag className="no-margin">{work.workType}</Tag>}
                        {work.roleType && (
                          <Typography.Text>{ACTOR_ROLE_TYPE_CONFIG?.[work.roleType]?.label || ''}</Typography.Text>
                        )}
                      </Space>
                      <Typography.Text type="secondary" ellipsis>
                        {work.roleDescription}
                      </Typography.Text>
                    </Flex>
                    <Space split={<Divider type="vertical" />} size={0} className="actions">
                      <Popconfirm
                        key="delete"
                        title="警告"
                        description="确定要删除该代表作品吗？"
                        onConfirm={() => handleDeleteWork(work.id || 0)}
                        okText="确定删除"
                        cancelText="取消">
                        <Typography.Link>删除</Typography.Link>
                      </Popconfirm>
                      <Typography.Link onClick={() => handleEditWork(work)}>编辑</Typography.Link>
                    </Space>
                  </Flex>
                </Card>
              ))}
          </Flex>
        )}
      </Flex>
    )
  }

  // 打开新增媒体弹窗
  const handleAddMedia = (mediaType: number) => {
    setSelectedMediaType(mediaType)
    setModalVisible(true)
    form.resetFields()
  }

  // 保存新增媒体
  const handleSaveMedia = async () => {
    try {
      const values = await form.validateFields()

      if (!currentActor?.id) {
        message.error('演员信息不完整')

        return
      }

      setLoading(true)

      // 构建新的媒体信息
      const newMedia = {
        actorId: currentActor.id,
        mediaType: selectedMediaType!,
        mediaUrl: Array.isArray(values.mediaUrl) ? values.mediaUrl.join(',') : `${values.mediaUrl}`,
        description: values.description || '',
        label: values.label || '',
      }

      // 调用专门的媒体保存接口
      const result = await saveActorMedia({
        actorId: currentActor.id,
        actorMediaInfos: [newMedia],
      })

      if (result) {
        message.success('添加成功')
        setModalVisible(false)
        form.resetFields()
        // 刷新最新数据
        await refreshActorData()
      } else {
        message.error('添加失败，请重试')
      }
    } catch (error) {
      console.error('添加失败:', error)
      message.error('添加失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 保存作品信息（新增或编辑）
  const handleSaveWork = async (workInfos: any[]) => {
    try {
      if (!currentActor?.id) {
        message.error('演员信息不完整')

        return
      }

      setLoading(true)

      if (currentEditingWork) {
        // 编辑模式：更新现有作品
        if (workInfos.length === 0) {
          message.error('参数错误')

          return
        }

        const updatedWork = {
          id: currentEditingWork.id,
          actorId: currentActor.id,
          workName: workInfos[0].workName,
          isHit: workInfos[0].isHit || false,
          roleType: workInfos[0].roleType || '',
          roleDescription: workInfos[0].roleDescription || '',
          workType: workInfos[0].workType || '',
          sort: workInfos[0].sort || currentEditingWork.sort || 1,
        }

        // 调用专门的作品保存接口（编辑模式）
        const result = await saveActorWorks({
          actorId: currentActor.id,
          actorWorkInfos: [updatedWork],
        })

        if (result) {
          message.success('更新成功')
          setWorkModalVisible(false)
          setCurrentEditingWork(null)
          workForm.setFieldsValue({ workInfos: [{}] })
          // 刷新最新数据
          await refreshActorData()
        } else {
          message.error('更新失败，请重试')
        }
      } else {
        // 新增模式：添加新作品
        const newWorkInfos = workInfos.map((work, index) => ({
          actorId: currentActor.id!,
          workName: work.workName,
          isHit: work.isHit || false,
          roleType: work.roleType || '',
          roleDescription: work.roleDescription || '',
          workType: work.workType || '',
          sort: work.sort || (currentActor.workInfos?.length || 0) + index + 1,
        }))

        // 调用专门的作品保存接口（新增模式）
        const result = await saveActorWorks({
          actorId: currentActor.id,
          actorWorkInfos: newWorkInfos,
        })

        if (result) {
          message.success(`成功添加 ${newWorkInfos.length} 个作品`)
          setWorkModalVisible(false)
          workForm.setFieldsValue({ workInfos: [{}] })
          // 刷新最新数据
          await refreshActorData()
        } else {
          message.error('添加失败，请重试')
        }
      }
    } catch (error) {
      console.error('保存失败:', error)
      message.error('保存失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 编辑作品信息
  const handleEditWork = (work: any) => {
    setCurrentEditingWork(work)
    setWorkModalVisible(true)
    // 设置表单初始值
    workForm.setFieldsValue({
      workInfos: [
        {
          workName: work.workName,
          isHit: work.isHit || false,
          roleType: work.roleType || '',
          roleDescription: work.roleDescription || '',
          workType: work.workType || '',
          sort: work.sort || 1,
        },
      ],
    })
  }

  // 删除作品信息
  const handleDeleteWork = async (workId: number) => {
    try {
      if (!currentActor?.id || !workId || workId === 0) {
        message.error('参数错误')

        return
      }

      setLoading(true)

      // 调用专门的作品删除接口
      const result = await deleteActorWorks(workId)

      if (result) {
        message.success('删除成功')
        // 刷新最新数据
        await refreshActorData()
      } else {
        message.error('删除失败，请重试')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 删除单个URL（调用保存修改接口）
  const handleDeleteSingleUrl = async (media: any, urlToDelete: string) => {
    try {
      if (!currentActor?.id || !media.id) {
        message.error('参数错误')

        return
      }

      setLoading(true)

      // 获取当前所有URL，过滤掉要删除的URL
      const currentUrls = media.mediaUrl ? media.mediaUrl.split(',').map((url: string) => url.trim()) : []
      const newUrls = currentUrls.filter((url: string) => url !== urlToDelete)

      if (newUrls.length === 0) {
        // 如果删除后没有URL了，删除整个媒体记录
        await handleDeleteMedia(media.id)

        return
      }

      // 构建更新的媒体信息
      const updatedMedia = {
        id: media.id, // 确保包含ID以便后端识别为更新操作
        actorId: currentActor.id,
        mediaType: media.mediaType,
        mediaUrl: newUrls.join(','),
        description: media.description || '',
        label: media.label || '',
      }

      // 调用保存修改接口
      const result = await saveActorMedia({
        actorId: currentActor.id,
        actorMediaInfos: [updatedMedia],
      })

      if (result) {
        message.success('删除成功')
        // 刷新最新数据
        await refreshActorData()
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 删除媒体文件
  const handleDeleteMedia = async (mediaId: number) => {
    try {
      if (!currentActor?.id || !mediaId) {
        message.error('参数错误')

        return
      }

      setLoading(true)

      // 调用专门的媒体删除接口
      const result = await deleteActorMedia(mediaId)

      if (result) {
        message.success('删除成功')
        // 刷新最新数据
        await refreshActorData()
      } else {
        message.error('删除失败，请重试')
      }
    } catch (error) {
      message.error('删除失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 刷新演员数据
  const refreshActorData = async () => {
    if (!currentActor?.id) {
      return
    }

    try {
      const latestActor = await getActorById(currentActor.id)

      if (latestActor) {
        setCurrentActor(latestActor)
        onActorUpdate?.(latestActor)
      }
    } catch (error) {
      console.error('刷新演员数据失败:', error)
    }
  }

  // 渲染历史评价
  const renderEvaluationHistory = () => {
    const evaluations = currentActor?.personEvaluationList || []
    return <EvaluationHistory evaluations={evaluations} parentType={2} />
  }

  // Tab项目
  const items: TabsProps['items'] = [
    // 全部 tab
    {
      key: 'all',
      label: '全部动态',
      children:
        sortedMediaInfos.length > 0 ? (
          <Timeline
            className={styles.timeline}
            items={sortedMediaInfos.map(media => {
              const mediaTypeInfo = getMediaTypeInfo(media.mediaType)

              return {
                color: 'gray',
                dot: <Tag className="text-primary">{mediaTypeInfo.label}</Tag>,
                children: (
                  <Flex vertical gap={8}>
                    <Typography.Text strong>
                      {media.createTime ? dayjs(media.createTime).format(DATE_FORMAT_BASE) : '未知时间'}
                    </Typography.Text>
                    <Card size="small">
                      <Image.PreviewGroup
                        preview={[1, 3, 7].includes(media.mediaType)}
                        items={media.mediaUrl?.split(',').map(url => `${envUrl}${url}`)}>
                        <Flex wrap gap={12}>
                          <MediaRenderer height={120} width={90} media={media} />
                        </Flex>
                      </Image.PreviewGroup>
                    </Card>
                  </Flex>
                ),
              }
            })}
          />
        ) : (
          <Empty description="暂无数据" />
        ),
    },
    // 根据 MEDIA_TYPE_OPTIONS 动态生成 tab
    ...MEDIA_TYPE_OPTIONS.map(mediaType => ({
      key: `media-${mediaType.value}`,
      label: mediaType.label,
      children: renderMediaGrid(getMediaByTypes([mediaType.value]), '暂无数据', mediaType.value),
    })),
    // 作品信息 tab
    {
      key: 'works',
      label: '代表作品',
      children: renderWorkInfos(),
    },
    // 历史评价 tab
    {
      key: 'evaluations',
      label: `历史评价${
        currentActor?.personEvaluationList?.length ? `(${currentActor.personEvaluationList.length})` : ''
      }`,
      children: renderEvaluationHistory(),
    },
  ]

  return (
    <Flex vertical gap={24} className={styles.detail}>
      <Descriptions bordered size="small" column={3}>
        <Descriptions.Item label="姓名">
          <Typography.Text strong>{currentActor?.personName}</Typography.Text>
        </Descriptions.Item>
        {personTypeInfo && <Descriptions.Item label="类型">{personTypeInfo.label}</Descriptions.Item>}
        <Descriptions.Item label="性别">{getGenderText(currentActor?.gender || 0)}</Descriptions.Item>
        {!!currentActor?.status && (
          <Descriptions.Item label="状态">
            <Badge
              status={
                PERSON_STATUS_CONFIG[currentActor.status as keyof typeof PERSON_STATUS_CONFIG]?.color || 'default'
              }
              text={PERSON_STATUS_CONFIG[currentActor.status as keyof typeof PERSON_STATUS_CONFIG]?.label || '未知'}
            />
          </Descriptions.Item>
        )}

        {currentActor?.isInternal === 1 ? (
          <>
            <Descriptions.Item label="是否内部">
              <Typography.Text>内部员工</Typography.Text>
            </Descriptions.Item>
            {currentActor?.jobNumber && (
              <Descriptions.Item label="工号" span={2}>
                {currentActor.jobNumber}
              </Descriptions.Item>
            )}
            {/* 微信用户信息 */}
            {typeof currentActor?.isWxUser === 'number' && (
              <Descriptions.Item label="来源">
                {IS_WX_USER_CONFIG[currentActor.isWxUser]?.label || '未知'}
              </Descriptions.Item>
            )}
            {currentActor?.isWxUser === 1 && currentActor?.wxAccount && (
              <Descriptions.Item label="系统账号">
                <Typography.Text copyable>{currentActor.wxAccount}</Typography.Text>
              </Descriptions.Item>
            )}
            {currentActor?.isWxUser === 1 && currentActor?.wxNickName && (
              <Descriptions.Item label="微信昵称">{currentActor.wxNickName}</Descriptions.Item>
            )}
            {currentActor?.actingStyle && (
              <Descriptions.Item label="戏路风格" span={2}>
                {currentActor.actingStyle}
              </Descriptions.Item>
            )}
            {currentActor?.specialty && (
              <Descriptions.Item label="特长" span={2}>
                {currentActor.specialty}
              </Descriptions.Item>
            )}
            {currentActor?.cityName ? (
              <Descriptions.Item label="所在城市" span={1}>
                {currentActor.cityName}
              </Descriptions.Item>
            ) : null}
          </>
        ) : (
          <>
            <Descriptions.Item label="艺名">{currentActor?.stageName || '-'}</Descriptions.Item>
            <Descriptions.Item label="是否内部">
              <Typography.Text>外部演员</Typography.Text>
            </Descriptions.Item>

            {/* 微信用户信息 */}
            {typeof currentActor?.isWxUser === 'number' && (
              <Descriptions.Item label="来源">
                {IS_WX_USER_CONFIG[currentActor.isWxUser]?.label || '未知'}
              </Descriptions.Item>
            )}
            {currentActor?.isWxUser === 1 && currentActor?.wxAccount && (
              <Descriptions.Item label="系统账号">
                <Typography.Text copyable>{currentActor.wxAccount}</Typography.Text>
              </Descriptions.Item>
            )}
            {currentActor?.isWxUser === 1 && currentActor?.wxNickName && (
              <Descriptions.Item label="微信昵称">{currentActor.wxNickName}</Descriptions.Item>
            )}
            {currentActor?.idNumber && (
              <Descriptions.Item label="身份证">
                <Typography.Text copyable>{currentActor.idNumber}</Typography.Text>
              </Descriptions.Item>
            )}
            <Descriptions.Item label="出生年份">{currentActor?.dateOfBirth || '-'}</Descriptions.Item>
            <Descriptions.Item label="年龄">
              {currentActor?.dateOfBirth ? `${getAge(currentActor.dateOfBirth)} 岁` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="性别">{getGenderText(currentActor?.gender || 0)}</Descriptions.Item>
            <Descriptions.Item label="是否儿童">
              {currentActor?.isChildActor ? <Typography.Text type="danger">儿童</Typography.Text> : '否'}
            </Descriptions.Item>
            <Descriptions.Item label="身高">
              {currentActor?.height ? `${currentActor.height}cm` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="体重">
              {currentActor?.weight ? `${currentActor.weight}kg` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="三围">
              {[currentActor?.bust || '-', currentActor?.waist || '-', currentActor?.hips || '-'].join(' / ')}
            </Descriptions.Item>
            {currentActor?.school ? (
              <Descriptions.Item label="毕业院校">{currentActor?.school}</Descriptions.Item>
            ) : null}
            {currentActor?.cityName ? (
              <Descriptions.Item label="所在城市" span={1}>
                {currentActor.cityName}
              </Descriptions.Item>
            ) : null}
            {currentActor?.actingStyle ? (
              <Descriptions.Item label="戏路风格" span={2}>
                {currentActor?.actingStyle}
              </Descriptions.Item>
            ) : null}
            {currentActor?.specialty ? (
              <Descriptions.Item label="特长" span={2}>
                {currentActor?.specialty}
              </Descriptions.Item>
            ) : null}
          </>
        )}
      </Descriptions>
      <Tabs items={items} indicator={{ size: 32 }} />

      {/* 新增媒体弹窗 */}
      {modalVisible ? (
        <AddMedia
          form={form}
          type={selectedMediaType}
          title={`添加${
            selectedMediaType ? MEDIA_TYPE_OPTIONS.find(opt => opt.value === selectedMediaType)?.label : '媒体文件'
          }`}
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false)
            form.resetFields()
          }}
          onOk={handleSaveMedia}
          loading={loading}
        />
      ) : null}

      {/* 作品信息弹窗 */}
      {workModalVisible ? (
        <WorkInfoForm
          mode="modal"
          multiple={false}
          title={currentEditingWork ? '编辑代表作品' : '添加代表作品'}
          open={workModalVisible}
          loading={loading}
          form={workForm}
          onCancel={() => {
            setWorkModalVisible(false)
            setCurrentEditingWork(null)
            workForm.resetFields()
          }}
          onSubmit={handleSaveWork}
        />
      ) : null}
    </Flex>
  )
}

export default ActorDetailContent
