import NoImage from '@/components/NoImage'
import { DATE_FORMAT_BASE } from '@/consts/date'
import { FddVerifyStatus, IS_INTERNAL_CONFIG } from '@/consts/index'
import { copy } from '@/utils/copy'
import { envUrl } from '@/utils/request'
import { FireOutlined, ManOutlined, PlusOutlined, WechatOutlined, WomanOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import {
  Badge,
  Button,
  Card,
  Col,
  Divider,
  Flex,
  Image,
  List,
  message,
  Row,
  Space,
  Timeline,
  Tooltip,
  Typography,
} from 'antd'
import dayjs from 'dayjs'
import React, { useState } from 'react'
import usePersonStore from '../../../person/store'
import { IActorListItem } from '../store'
import styles from './List.scss'

interface IListProps {
  data: IActorListItem[]
  pagination: any
  loading: boolean
  onChange: (page: number, pageSize: number) => void
  onOperate: (type: string, actor?: IActorListItem) => void
}

// 岗位列表
const ActorList: React.FC<IListProps> = ({ data, pagination, loading, onChange, onOperate }) => {
  const [fetching, setFetching] = useState<number | null>(null)
  const { validRealName } = usePersonStore()

  const handleCardClick = (actor: IActorListItem, event: React.MouseEvent) => {
    const target = event.target as HTMLElement

    if (target?.className?.indexOf?.('full-h') > -1 || target?.className?.indexOf?.('ant-row') > -1) {
      onOperate && onOperate('view', actor)
    }
  }

  // 独立实名处理函数
  const handleRealName = async (actor: IActorListItem) => {
    if (fetching) {
      return
    }

    try {
      // 如果已有url，直接使用
      if (actor.fddCustomerVerifyUrl) {
        copy(actor.fddCustomerVerifyUrl)
        message.success('实名认证链接已复制到剪贴板')

        return
      }
      setFetching(actor.id)

      const res = await validRealName(actor.personId)

      setFetching(null)

      if (res?.url) {
        copy(res?.url)
        message.success('实名认证链接已复制到剪贴板')
      } else {
        message.error('获取实名认证链接异常')
      }
    } finally {
      setFetching(null)
    }
  }

  // 渲染演员卡片
  const renderActorCard = (actor: IActorListItem) => {
    const destMediaInfos = actor.mediaInfos
      ?.filter(media => [1, 3].includes(media.mediaType))
      .map(media => `${media.mediaUrl}`.split(','))
      .flat(Infinity)
      .map(url => `${envUrl}${url}`)

    const verifyStatus = actor.fddVerifyStatus
    const imgUrl = actor.headUrl ? `${envUrl}${actor.headUrl}` : destMediaInfos?.[0]
    const card = (
      <Card hoverable size="small" className="full-h hover-move" onClick={e => handleCardClick(actor, e)}>
        <Row gutter={16} align="middle">
          <Col span={12}>
            <Space size={16} align="start" className="full-h">
              <Image.PreviewGroup items={destMediaInfos}>
                {imgUrl ? (
                  <Image
                    width={90}
                    height={120}
                    className="radius img-cover"
                    preview={{ getContainer: document.body }}
                    onClick={e => e.stopPropagation()}
                    src={actor.headUrl ? `${envUrl}${actor.headUrl}` : destMediaInfos?.[0]}
                  />
                ) : (
                  <NoImage />
                )}
              </Image.PreviewGroup>
              <Flex vertical gap={8} className="full-h full-v">
                <Space size={2} split={<Divider type="vertical" />}>
                  <Typography.Text strong className="fs-xlg">
                    {actor.personName}
                  </Typography.Text>
                  {actor.stageName ? <Dict title="艺名" value={actor.stageName}></Dict> : null}
                  {actor.dateOfBirth ? <Typography.Text>{actor.dateOfBirth}年</Typography.Text> : null}
                  {actor.isChildActor ? <Typography.Text type="danger">儿童</Typography.Text> : null}
                  {typeof actor.isInternal === 'number'
                    ? `${IS_INTERNAL_CONFIG[actor.isInternal]?.label}演员` || '未知'
                    : null}
                  {typeof actor.isWxUser === 'number' && actor.isWxUser === 1 ? (
                    <Dict
                      title="来源"
                      value={
                        <Typography.Text type="success">
                          <WechatOutlined />
                        </Typography.Text>
                      }
                    />
                  ) : null}
                  {actor.isWxUser === 1 && actor.wxNickName ? <Dict title="微信昵称" value={actor.wxNickName} /> : null}
                </Space>
                <Space size={2} split={<Divider type="vertical" />}>
                  {actor.gender && [1, 2].includes(actor.gender) ? (
                    <Typography.Text className={actor.gender === 1 ? 'text-processing' : 'text-danger'}>
                      {actor.gender === 1 ? <ManOutlined /> : <WomanOutlined />}
                    </Typography.Text>
                  ) : null}
                  {actor.height ? (
                    <Tooltip title="身高">
                      <Typography.Text>{actor.height}cm</Typography.Text>
                    </Tooltip>
                  ) : null}
                  {actor.weight ? (
                    <Tooltip title="体重">
                      <Typography.Text>{actor.weight}kg</Typography.Text>
                    </Tooltip>
                  ) : null}

                  {actor.bust || actor.waist || actor.hips ? (
                    <Tooltip title="三围">
                      <Typography.Text>{[actor.bust, actor.waist, actor.hips].join('/')}</Typography.Text>
                    </Tooltip>
                  ) : null}
                </Space>
                <Space size={12}>
                  {actor.school && <Dict title="毕业院校" value={actor.school} />}
                  {actor.actingStyle && <Dict title="擅长风格" value={actor.actingStyle} />}
                  {actor.specialty && <Dict title="特长" value={actor.specialty} />}
                </Space>

                <Space size={24}>
                  {actor.updateTime ? (
                    <Space>
                      {actor.lastModifier ? <Typography.Text>{actor.lastModifier}</Typography.Text> : null}
                      <Dict
                        title="最近更新于"
                        value={
                          <Typography.Text type="secondary">
                            {dayjs(actor.updateTime).format(DATE_FORMAT_BASE)}
                          </Typography.Text>
                        }
                      />
                    </Space>
                  ) : (
                    <Space>
                      {actor.creator && <Typography.Text>{actor.creator}</Typography.Text>}
                      <Dict
                        title="添加于"
                        value={
                          <Typography.Text type="secondary">
                            {dayjs(actor.createTime).format(DATE_FORMAT_BASE)}
                          </Typography.Text>
                        }
                      />
                    </Space>
                  )}
                </Space>
              </Flex>
            </Space>
          </Col>
          <Col span={8}>
            {actor.workInfos?.length ? (
              <Timeline
                className={styles.timeline}
                items={actor.workInfos.slice(0, 3).map(work => ({
                  color: 'gray',
                  children: (
                    <Space size={12}>
                      <span>
                        {work.isHit && (
                          <Typography.Text type="danger">
                            <FireOutlined />
                          </Typography.Text>
                        )}
                        <Typography.Text strong>《{work.workName}》</Typography.Text>
                      </span>
                      <Typography.Text>{work.roleType}</Typography.Text>
                      <Typography.Text type="secondary">{work.roleDescription}</Typography.Text>
                    </Space>
                  ),
                }))}
              />
            ) : null}
          </Col>
          <Col span={3} className="text-right">
            {verifyStatus === FddVerifyStatus.Verified ? <Badge status="success" text="实名通过" /> : null}
            {actor.isInternal === 0 &&
            verifyStatus !== FddVerifyStatus.Verified &&
            verifyStatus !== FddVerifyStatus.Incomplete ? (
              <Button
                size="small"
                type="link"
                loading={actor.id === fetching}
                disabled={loading}
                onClick={e => {
                  e.stopPropagation()
                  handleRealName(actor)
                }}>
                去实名认证
              </Button>
            ) : null}
          </Col>
        </Row>
      </Card>
    )

    return (
      <List.Item>
        {actor.depart ? (
          <Badge.Ribbon color="#999" text="已离职">
            {card}
          </Badge.Ribbon>
        ) : (
          card
        )}
      </List.Item>
    )
  }

  return (
    <Flex vertical>
      <ListHeader title="演员列表" unitText="个" total={pagination.total}>
        <Button type="primary" ghost icon={<PlusOutlined />} onClick={() => onOperate && onOperate('create')}>
          添加演员
        </Button>
      </ListHeader>
      <List
        loading={loading}
        dataSource={data}
        split={false}
        renderItem={renderActorCard}
        rowKey="id"
        className={`list-sm ${styles.list}`}
        pagination={{
          ...pagination,
          onChange,
          onShowSizeChange: onChange,
        }}
      />
    </Flex>
  )
}

export default ActorList
