import PromptTextArea from '@/components/PromptTextArea'
import {
  GENDER_OPTIONS,
  IS_INTERNAL_OPTIONS,
  IS_WX_USER_CONFIG,
  PERSON_STATUS_OPTIONS,
  PERSON_TYPE_OPTIONS,
} from '@/consts'
import { parseIdCard } from '@/utils'
import filterMatch from '@/utils/filterMatch'
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { Fieldset } from '@fe/rockrose'
import { useDebounceFn } from 'ahooks'
import { Button, Card, Drawer, Flex, Form, Input, InputNumber, Radio, Select, Switch, Tag } from 'antd'
import React, { useCallback, useEffect } from 'react'
import UploadComponent from '../../../../../components/Upload'
import { CHINA_CITY_OPTIONS } from '../../../../../consts/city'
import usePersonStore from '../../../person/store'
import { type IActorListItem, type IPrBankAccountInfo, type ISavePrActors, prActorsUploadAPI } from '../store'

interface ActorFormProps {
  open: boolean
  actor?: IActorListItem
  onCancel: () => void
  onSubmit: (values: ISavePrActors) => void
}

const ActorForm: React.FC<ActorFormProps> = ({ open, actor, onCancel, onSubmit }) => {
  const [form] = Form.useForm()
  const formIsInternal = Form.useWatch('isInternal', form)
  const { getAllInteriorUser, allInteriorUserOptions } = usePersonStore()

  // 处理内部员工选择
  const handleInternalUserSelect = (val: any, opt: any) => {
    if (val) {
      form.setFieldValue('jobNumber', opt?.val || null)
      form.setFieldValue('gender', opt?.gender || null)
    }
  }

  // 身份证解析函数
  const parseIdCardAndFillForm = useCallback(
    (idNumber: string) => {
      if (idNumber && idNumber.length >= 15) {
        // 至少15位才开始解析
        const result = parseIdCard(idNumber)

        if (result.check) {
          // 身份证验证通过，自动填充相关字段
          form.setFieldsValue({
            gender: result.gender,
            dateOfBirth: result.dateOfBirth,
            isChildActor: result.isChild,
          })
        }
      }
    },
    [form]
  )

  // 使用ahooks的防抖hook
  const { run: debouncedParseIdCard } = useDebounceFn(parseIdCardAndFillForm, { wait: 500 })

  // 处理身份证号变化
  const handleIdNumberChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const idNumber = e.target.value.trim()

      debouncedParseIdCard(idNumber)
    },
    [debouncedParseIdCard]
  )

  // 处理是否内部变更
  const handleIsInternalChange = (val: any) => {
    form.resetFields(['personName', 'jobNumber', 'stageName'])
  }

  useEffect(() => {
    if (open) {
      if (actor) {
        // 编辑模式：填充现有数据
        const { mediaInfos = [], workInfos = [], createTime, updateTime, ...rest } = actor

        // 处理银行卡信息：如果有原来的单个银行字段，转换为数组格式
        let bankInfos: IPrBankAccountInfo[] = []

        if (actor.bankInfos && actor.bankInfos.length > 0) {
          // 如果有新的银行卡数组，直接使用
          bankInfos = actor.bankInfos
        }

        // 设置表单值
        form.setFieldsValue({
          ...rest,
          dateOfBirth: actor.dateOfBirth || null,
          bankInfos,
        })
      } else {
        // 新增模式：重置表单
        form.resetFields()
        form.setFieldsValue({ bankInfos: [] }) // 初始化银行卡为空数组
      }
    }
    if (open && allInteriorUserOptions?.length == 0) {
      getAllInteriorUser()
    }
  }, [open, actor, form, getAllInteriorUser])

  const handleFinish = async () => {
    try {
      const values = await form.validateFields()

      const formData: ISavePrActors = {
        id: actor?.id,
        personId: actor?.personId,
        personName: values.personName,
        stageName: values.stageName || '',
        gender: values.gender,
        dateOfBirth: values.dateOfBirth,
        isChildActor: values.isChildActor || false,
        isInternal: values.isInternal,
        personType: values.personType,
        status: values.status,
        height: values.height,
        weight: values.weight,
        bust: values.bust,
        waist: values.waist,
        hips: values.hips,
        idNumber: values.idNumber,
        // headUrl: values.headUrl,
        school: values.school,
        specialty: values.specialty,
        actingStyle: values.actingStyle,
        phone: values.phone,
        eMail: values.eMail,
        address: values.address,
        idCardFrontPhoto: values.idCardFrontPhoto,
        idCardVersoPhoto: values.idCardVersoPhoto,
        cityName: values.cityName,
        jobNumber: values.jobNumber,
      }

      // 处理银行卡信息
      if (values.bankInfos && values.bankInfos.length > 0) {
        // 清理银行卡信息中的时间字段
        formData.bankInfos = values.bankInfos.map((bank: any) => {
          const cleanBank = { ...bank }

          delete cleanBank.createTime
          delete cleanBank.updateTime

          return cleanBank
        })

        // 确保只有一张银行卡被设为默认
        const defaultCards = formData.bankInfos?.filter(bank => bank.isDefault)

        if (defaultCards && defaultCards.length > 1) {
          // 如果有多张默认卡，只保留第一张为默认
          formData?.bankInfos?.forEach((bank, index) => {
            bank.isDefault = index === 0 && bank.isDefault
          })
        } else if (defaultCards && defaultCards.length === 0 && formData.bankInfos?.length === 1) {
          // 如果只有一张卡且没有设为默认，自动设为默认
          formData.bankInfos[0].isDefault = true
        }
      }

      onSubmit(formData)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  // 处理默认银行卡的互斥设置
  const handleDefaultBankChange = useCallback(
    (changedIndex: number, isDefault: boolean) => {
      if (isDefault) {
        // 当前银行卡设置为默认时，将其他银行卡设置为非默认
        const bankInfos = form.getFieldValue('bankInfos') || []
        const updatedBankInfos = bankInfos.map((bank: IPrBankAccountInfo, index: number) => ({
          ...bank,
          isDefault: index === changedIndex,
        }))

        form.setFieldValue('bankInfos', updatedBankInfos)
      }
    },
    [form]
  )

  return (
    <Drawer
      title={actor ? '编辑演员' : '添加演员'}
      open={open}
      onClose={onCancel}
      width={800}
      extra={
        <Button type="primary" onClick={handleFinish}>
          立即保存
        </Button>
      }>
      <Form form={form} layout="horizontal" colon={false} labelCol={{ span: 4 }} wrapperCol={{ span: 18 }}>
        <Flex vertical gap={32}>
          <Fieldset title="1、基础信息" direction="vertical" className="no-padding-b">
            <Form.Item name="isInternal" label="是否内部" initialValue={0}>
              <Radio.Group options={IS_INTERNAL_OPTIONS} onChange={handleIsInternalChange} />
            </Form.Item>

            {formIsInternal === 1 ? (
              <>
                <Form.Item name="personName" label="姓名" rules={[{ required: true, message: '请选择内部员工' }]}>
                  <Select
                    placeholder="请选择内部员工"
                    options={allInteriorUserOptions}
                    showSearch
                    filterOption={(inputValue, option) => {
                      const result = filterMatch(inputValue, option?.label || '')

                      return Boolean(result)
                    }}
                    fieldNames={{ label: 'labelStr', value: 'label' }}
                    onSelect={handleInternalUserSelect}
                  />
                </Form.Item>
                <Form.Item name="jobNumber" label="工号" hidden></Form.Item>
                <Form.Item name="gender" label="性别" hidden></Form.Item>
              </>
            ) : (
              <>
                <Form.Item name="personName" label="姓名" rules={[{ required: true }]}>
                  <Input placeholder="输入姓名" />
                </Form.Item>
              </>
            )}

            {/* 内部员工不显示以下字段 */}
            {formIsInternal !== 1 && (
              <>
                <Form.Item name="stageName" label="艺名">
                  <Input placeholder="输入艺名" />
                </Form.Item>
                <Form.Item name="idNumber" label="身份证号">
                  <Input placeholder="输入身份证号" onChange={handleIdNumberChange} />
                </Form.Item>
                <Form.Item name="gender" label="性别" initialValue={3}>
                  <Radio.Group options={GENDER_OPTIONS} />
                </Form.Item>
                <Form.Item name="dateOfBirth" label="出生年份">
                  <InputNumber
                    placeholder="输入出生年份"
                    min={1900}
                    max={new Date().getFullYear()}
                    precision={0}
                    className="full-h"
                  />
                </Form.Item>
                <Form.Item name="isChildActor" label="是否儿童" initialValue={false}>
                  <Radio.Group
                    options={[
                      { value: true, label: '是' },
                      { value: false, label: '否' },
                    ]}
                  />
                </Form.Item>
                <Form.Item name="school" label="毕业院校">
                  <Input placeholder="输入毕业院校" />
                </Form.Item>
              </>
            )}
            <Form.Item
              name="personType"
              label="人员类型"
              initialValue={1}
              rules={[{ required: true, message: '请选择人员类型' }]}>
              <Select placeholder="请选择人员类型" options={PERSON_TYPE_OPTIONS} />
            </Form.Item>
            <Form.Item name="status" label="人员状态" initialValue={1}>
              <Radio.Group options={PERSON_STATUS_OPTIONS} />
            </Form.Item>
            {/* 微信信息 - 只读显示 */}
            {actor && typeof actor.isWxUser === 'number' && (
              <Form.Item label="微信用户">
                <Tag color={IS_WX_USER_CONFIG[actor.isWxUser]?.color || 'default'}>
                  {IS_WX_USER_CONFIG[actor.isWxUser]?.label || '未知'}
                </Tag>
              </Form.Item>
            )}
            {actor && actor.isWxUser === 1 && actor.wxAccount && (
              <Form.Item label="微信账号">
                <Input value={actor.wxAccount} disabled />
              </Form.Item>
            )}
            {actor && actor.isWxUser === 1 && actor.wxNickName && (
              <Form.Item label="微信昵称">
                <Input value={actor.wxNickName} disabled />
              </Form.Item>
            )}
            <Form.Item name="cityName" label="所在城市">
              <Select
                placeholder="选择所在城市"
                options={[...CHINA_CITY_OPTIONS]}
                showSearch
                allowClear
                filterOption={(inputValue, option) => Boolean(filterMatch(inputValue, option?.label || ''))}
              />
            </Form.Item>
            <Form.Item name="actingStyle" label="擅长风格">
              <PromptTextArea
                fragments={['傻白甜', '清纯', '叛逆', '绿茶', 'bitchy', '大叔', '霸总', '暖男', '恶男']}
              />
            </Form.Item>
            <Form.Item name="specialty" label="特长">
              <PromptTextArea fragments={['英语', '粤语', '舞蹈', '武术', '戏曲', '声乐', '说唱']} />
            </Form.Item>
          </Fieldset>

          {/* 外部员工才显示身材信息 */}
          {formIsInternal !== 1 && (
            <Fieldset title="2、身材信息" direction="vertical" className="no-padding-b">
              <Form.Item name="height" label="身高(cm)">
                <InputNumber placeholder="输入身高" min={50} max={200} className="full-h" />
              </Form.Item>
              <Form.Item name="weight" label="体重(kg)">
                <InputNumber placeholder="输入体重" min={10} max={200} className="full-h" />
              </Form.Item>
              <Form.Item name="bust" label="胸围(cm)">
                <InputNumber placeholder="输入胸围" min={50} max={150} className="full-h" />
              </Form.Item>
              <Form.Item name="waist" label="腰围(cm)">
                <InputNumber placeholder="输入腰围" min={40} max={120} className="full-h" />
              </Form.Item>
              <Form.Item name="hips" label="臀围(cm)">
                <InputNumber placeholder="输入臀围" min={50} max={150} className="full-h" />
              </Form.Item>
            </Fieldset>
          )}

          {/* 外部员工才显示联系信息 */}
          {formIsInternal !== 1 && (
            <Fieldset title="3、联系信息" direction="vertical" className="no-padding-b">
              <Form.Item name="eMail" label="邮箱" required>
                <Input placeholder="输入邮箱地址" />
              </Form.Item>
              <Form.Item name="phone" label="手机号">
                <Input placeholder="输入手机号" />
              </Form.Item>
              <Form.Item name="address" label="联系地址">
                <Input.TextArea placeholder="输入联系地址" rows={2} />
              </Form.Item>
              <Form.Item name="idCardFrontPhoto" label="身份证正面照">
                <UploadComponent action={prActorsUploadAPI} type="image" accept=".png,.jpg,.jpeg" multiple={false} />
              </Form.Item>
              <Form.Item name="idCardVersoPhoto" label="身份证反面照">
                <UploadComponent action={prActorsUploadAPI} type="image" accept=".png,.jpg,.jpeg" multiple={false} />
              </Form.Item>
            </Fieldset>
          )}

          {/* 外部员工才显示银行卡 */}
          {formIsInternal !== 1 && (
            <Fieldset title="4、银行卡" direction="vertical" className="no-padding-b">
              <Form.List name="bankInfos">
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, ...restField }) => (
                      <Card
                        key={key}
                        size="small"
                        title={`银行卡 ${name + 1}`}
                        extra={
                          <Button
                            type="text"
                            danger
                            size="small"
                            icon={<MinusCircleOutlined />}
                            onClick={() => remove(name)}>
                            删除
                          </Button>
                        }
                        style={{ marginBottom: 16 }}>
                        <Flex vertical gap={16}>
                          <Form.Item
                            {...restField}
                            name={[name, 'accountName']}
                            label="账户名"
                            rules={[{ required: true, message: '请输入账户名称' }]}>
                            <Input placeholder="请输入账户名称" />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            name={[name, 'accountNumber']}
                            label="卡号"
                            rules={[{ required: true, message: '请输入卡号' }]}>
                            <Input placeholder="请输入卡号" />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            name={[name, 'bankName']}
                            label="开户行"
                            rules={[{ required: true, message: '请输入开户行' }]}>
                            <Input placeholder="请输入开户行" />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            name={[name, 'isDefault']}
                            valuePropName="checked"
                            label="默认银行卡">
                            <Switch
                              checkedChildren="默认"
                              unCheckedChildren="普通"
                              onChange={checked => handleDefaultBankChange(name, checked)}
                            />
                          </Form.Item>
                        </Flex>
                      </Card>
                    ))}
                    <Form.Item>
                      <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                        添加银行卡
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>
            </Fieldset>
          )}
        </Flex>
      </Form>
    </Drawer>
  )
}

export default ActorForm
