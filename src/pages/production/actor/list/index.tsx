import { PAGINATION } from '@/consts'
import useSyncParams, { formatUrlState, initFormFromUrlState, parsePagination } from '@/hooks/useSyncParams'
import { Flex, Form, message } from 'antd'
import React, { useEffect, useState } from 'react'
import ActorForm from './components/Add'
import ActorDetail from './components/Detail'
import List from './components/List'
import Search from './components/Search'
import useActorListStore, { IActorListItem, IActorListSearchParams, ISavePrActors } from './store'

const ActorInfo: React.FC = () => {
  const [urlState, setUrlState] = useSyncParams<IActorListSearchParams>()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<IActorListItem[]>([])
  const [pagination, setPagination] = useState(PAGINATION)

  const [show, setShow] = useState(false)
  const [operateData, setOperateData] = useState<any | IActorListItem>()
  const [operateType, setOperateType] = useState<any | IOperateType>()

  const { fetchActorList, saveActor, deleteActor } = useActorListStore()

  // 初始化URL数据
  const initParams = () => {
    // 使用工具函数简化表单初始化
    initFormFromUrlState(urlState, form, {
      numberFields: ['gender', 'isInternal'],
      arrayFields: ['cityName'],
      excludeFields: ['pageIndex', 'pageSize'],
    })

    // 使用工具函数处理分页
    const { current, pageSize } = parsePagination(urlState, {
      current: pagination.current,
      pageSize: pagination.pageSize,
    })

    handleSearch(current, pageSize)
  }

  const handleSearch = async (current = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true)
    try {
      const values = form.getFieldsValue()
      const searchParams: IActorListSearchParams = {
        pageIndex: current,
        pageSize,
        ...values,
      }

      const result = await fetchActorList(searchParams)

      if (result) {
        setDataSource(result.list)
        setPagination(prev => ({
          ...prev,
          total: result.total,
          current: result.pageIndex,
          pageSize: result.pageSize,
        }))

        // 同步URL参数 - 使用工具函数格式化
        setUrlState(
          formatUrlState(
            {
              personName: values.personName || '',
              stageName: values.stageName || '',
              isChildActor: values.isChildActor,
              gender: values.gender,
              cityName: values.cityName,
              isInternal: values.isInternal,
              pageSize: result.pageSize,
              pageIndex: result.pageIndex,
            },
            { arrayFields: ['cityName'] }
          )
        )
      } else {
        setDataSource([])
      }
    } catch (error) {
      setDataSource([])
    } finally {
      setLoading(false)
    }
  }

  const handleOperate = (type: string, data?: IActorListItem) => {
    if (type === 'delete') {
      data && handleDelete(data)
    } else if (['create', 'edit', 'view'].includes(type)) {
      setOperateData(data || null)
      setOperateType(type as IOperateType)
      setShow(true)
    }
  }

  // 从详情页面编辑
  const handleEditFromDetail = (actor: IActorListItem) => {
    setOperateData(actor)
    setOperateType('edit')
    // 保持抽屉打开状态，但切换到编辑模式
  }

  const handleOperateClose = (fresh = true) => {
    setOperateData(null)
    setOperateType('')
    setShow(false)
    if (fresh) {
      handleSearch()
    }
  }

  // 表单提交
  const handleFormSubmit = async (values: ISavePrActors) => {
    try {
      let param = {...operateData,...values,}

      const result = await saveActor(param)

      if (result) {
        message.success(operateType == 'edit' ? '更新成功' : '新增成功')
        handleOperateClose()
        await handleSearch(1)
      }
    } catch (error) {
      message.error('操作失败')
    }
  }

  // 删除演员
  const handleDelete = async (actor: IActorListItem) => {
    if (!actor.id) {
      message.error('演员信息不完整')

      return
    }

    try {
      const result = await deleteActor(actor.id)

      if (result) {
        message.success('删除成功')
        handleOperateClose(false) // 关闭详情抽屉
        await handleSearch(1) // 刷新列表，回到第一页
      }
    } catch (error) {
      console.error('删除演员失败:', error)
      message.error('删除失败，请重试')
    }
  }

  // 从详情页面删除
  const handleDeleteFromDetail = (actor: IActorListItem) => {
    handleDelete(actor)
  }

  useEffect(() => {
    initParams()
  }, [])

  return (
    <Flex vertical gap={24}>
      <Search form={form} onSearch={handleSearch} onOperate={handleOperate} onReset={() => handleSearch(1)} />
      <List
        data={dataSource}
        loading={loading}
        onOperate={handleOperate}
        onChange={handleSearch}
        pagination={pagination}
      />

      {/* 演员详情抽屉 */}
      <ActorDetail
        open={show && operateType === 'view'}
        actor={operateData ?? void 0}
        onClose={() => handleOperateClose(false)}
        onEdit={handleEditFromDetail}
        onDelete={handleDeleteFromDetail}
      />

      {/* 演员表单模态框 */}
      <ActorForm
        open={show && ['edit', 'create'].includes(operateType)}
        actor={operateData ?? void 0}
        onCancel={handleOperateClose}
        onSubmit={handleFormSubmit}
      />
    </Flex>
  )
}

export default ActorInfo
