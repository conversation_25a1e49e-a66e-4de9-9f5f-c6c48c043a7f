import { create } from 'zustand'
import { post, get as requestGet } from '../../../../utils/request'
import type { IPrPersonEvaluation } from '../../project/list/store'

export const prActorsUploadAPI = '/PrActors/UploadFile'
// 演员分页查询参数（对应API的PagePrActorsDto）
export interface IActorListSearchParams {
  pageIndex: number // 页码
  pageSize: number // 页面大小
  personName?: string // 姓名Id
  stageName?: string // 艺名Id
  isChildActor?: boolean // 是否儿童演员 (0否，1是)
  gender?: number // 性别 (1男，2女，3其他)
  cityName?: Array<string> // 城市
  isWxUser?: number // 是否微信注册用户
  wxNickName?: string // 微信账号昵称
  status?: number // 人员状态 (1正常，2禁用)
}

// API响应的演员信息（对应API的PrActors）
export interface IActorListItem {
  creator?: string // 创建人
  lastModifier?: string // 最后修改人
  id: number // 演员ID
  personId: number // 基础信息ID
  stageName: string // 艺名（必填）
  height?: number // 身高 (cm)
  weight?: number // 体重 (kg)
  bust?: number // 胸围 (cm)
  waist?: number // 腰围 (cm)
  hips?: number // 臀围 (cm)
  headUrl?: string // 头像图片地址
  school?: string // 毕业院校
  specialty?: string // 特长
  actingStyle?: string // 戏路风格
  // 基础人员信息字段
  personName: string // 姓名
  dateOfBirth?: number // 出生年份
  isChildActor?: boolean // 是否儿童演员
  gender?: number // 性别 (1男，2女，3其他)
  idNumber?: string // 身份证号码
  idCardFrontPhoto?: string // 身份证正面照
  idCardVersoPhoto?: string // 身份证反面照
  address?: string // 地址
  phone?: string // 电话
  eMail?: string // 邮箱
  bankInfos?: IPrBankAccountInfo[] // 银行卡信息列表
  createTime: string // 创建时间
  updateTime: string // 更新时间
  mediaInfos?: IActorMediaInfo[] // 媒体信息
  workInfos?: IActorWorkInfo[] // 作品信息
  isInternal?: number // 是否内部 (0否，1是)
  jobNumber?: string // 工号
  cityName?: string // 城市
  fddVerifyStatus?: number // 实名认证状态个人 0：未激活； 1：未认证； 2：审核通过； 3：已提交待审核； 4：审核不通过
  fddCustomerVerifyUrl?: string // 法大大用户实名认证地址
  wxAccount?: string // 微信注册用户账号
  wxNickName?: string // 微信账号昵称
  isWxUser?: number // 是否微信注册用户（只读）
  personType?: number // 人员类型（1个人，2团体）
  status?: number // 人员状态 (1正常，2禁用)
  depart?: boolean //
  personEvaluationList?: Array<IPrPersonEvaluation>
}

// 演员媒体信息（对应API的PrActorMedia）
export interface IActorMediaInfo {
  id?: number // 媒体ID
  actorId: number // 关联演员ID
  mediaType: number // 媒体类型 (1近照，2self-tape,3剧集表演,4最新报价)
  mediaUrl?: string // 媒体文件URL或路径
  description?: string // 媒体描述
  label?: string // 媒体标签
  createTime?: string // 上传时间
  updateTime?: string // 更新时间
}

// 演员作品信息（对应API的PrActorWorks）
export interface IActorWorkInfo {
  id?: number // 作品ID
  actorId: number // 关联演员ID
  workName?: string // 作品名称
  isHit: boolean // 是否爆款(0-否,1-是)
  roleType?: string // 角色类型 (女一,女二,女配,男一,男二,男配)
  roleDescription?: string // 角色描述
  workType?: string // 作品类型(如武侠、现代等)
  sort: number // 序号
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 演员名称信息（对应API的PrActorsDto）
export interface IActorNameItem {
  id: number
  stageName?: string // 艺名
}

// API响应类型
export interface IActorListResponse {
  list: IActorListItem[]
  total: number
  pageIndex: number
  pageSize: number
}

// 保存演员信息参数（对应API的SavePrActors）
export interface ISavePrActors {
  id?: number // 演员ID
  personId?: number // 基础信息ID
  stageName: string // 艺名（必填）
  height?: number // 身高 (cm)
  weight?: number // 体重 (kg)
  bust?: number // 胸围 (cm)
  waist?: number // 腰围 (cm)
  hips?: number // 臀围 (cm)
  idNumber?: string // 身份证号
  headUrl?: string // 头像图片地址
  school?: string // 毕业院校
  specialty?: string // 特长
  actingStyle?: string // 戏路风格
  personName: string // 人员姓名（必填）
  dateOfBirth?: number // 出生年份
  isChildActor?: boolean // 是否儿童演员 (0否，1是)
  gender: number // 性别
  idCardFrontPhoto?: string // 身份证正面照
  idCardVersoPhoto?: string // 身份证反面照
  address?: string // 联系地址
  phone?: string // 手机号码
  eMail?: string // 邮箱地址
  bankInfos?: IPrBankAccountInfo[] // 银行卡信息列表
  isInternal?: number // 是否内部 (0否，1是)
  cityName?: string // 城市
  jobNumber?: string
  personType?: number // 人员类型（1个人，2团体）
  status?: number // 人员状态 (1正常，2禁用)
}

// 保存演员媒体信息参数（对应API的SavePrActorMedia）
export interface ISavePrActorMedia {
  actorId: number // 演员Id
  actorMediaInfos?: IActorMediaInfo[] // 媒介数组
}

// 保存演员作品信息参数（对应API的SavePrActorWork）
export interface ISavePrActorWork {
  actorId: number // 演员Id
  actorWorkInfos?: IActorWorkInfo[] // 作品数组
}

// 银行账户信息接口（与人员模块共用）
export interface IPrBankAccountInfo {
  id?: number // 银行卡信息Id
  personId?: number // 关联人员ID
  isDefault: boolean // 是否默认
  bankName?: string // 开户行名称
  accountName?: string // 账户名称
  accountNumber?: string // 卡号
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

export interface IActorListStore {
  allActorNames: IActorNameItem[] // 所有演员名称列表
  // API调用方法
  fetchActorList: (params: IActorListSearchParams) => Promise<IActorListResponse | null>
  getActorById: (id: number) => Promise<IActorListItem | null>
  saveActor: (actorData: ISavePrActors) => Promise<boolean>
  saveActorMedia: (mediaData: ISavePrActorMedia) => Promise<boolean>
  saveActorWorks: (workData: ISavePrActorWork) => Promise<boolean>
  deleteActorMedia: (id: number) => Promise<boolean>
  deleteActorWorks: (id: number) => Promise<boolean>
  getAllActorNames: () => Promise<IActorNameItem[] | null>
  deleteActor: (id: number) => Promise<boolean>
}

export default create<IActorListStore>(set => ({
  allActorNames: [],
  // 获取演员列表
  fetchActorList: async (params: IActorListSearchParams) => {
    try {
      const { data, status } = await post<any, any>('/PrActors/GetList', params)

      if (status && data) {
        return {
          list: data.dataList || [],
          total: data.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      console.error('获取演员列表失败:', error)

      return null
    }
  },

  // 根据ID获取演员详细信息
  getActorById: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrActors/GetById?id=${id}`)

      if (status && data) {
        return data?.dataList || null
      }

      return null
    } catch (error) {
      console.error('获取演员详细信息失败:', error)

      return null
    }
  },

  // 保存演员信息
  saveActor: async (actorData: ISavePrActors) => {
    try {
      const { data, status } = await post<any, any>('/PrActors/Save', actorData)

      if (status) {
        return data || true
      }

      return null
    } catch (error) {
      console.error('保存演员信息失败:', error)

      return false
    }
  },

  // 保存演员媒体信息
  saveActorMedia: async (mediaData: ISavePrActorMedia) => {
    try {
      const { data, status } = await post<any, any>('/PrActors/SaveActorMedia', mediaData)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('保存演员媒体信息失败:', error)

      return false
    }
  },

  // 保存演员作品信息
  saveActorWorks: async (workData: ISavePrActorWork) => {
    try {
      const { data, status } = await post<any, any>('/PrActors/SaveActorWorks', workData)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('保存演员作品信息失败:', error)

      return false
    }
  },

  // 获取所有演员名称(不分页)
  getAllActorNames: async () => {
    try {
      const { data, status } = await requestGet<any, any>('/PrActors/AllName')

      if (status && data) {
        set({ allActorNames: Array.isArray(data?.dataList) ? data?.dataList : [] })

        return Array.isArray(data) ? data : []
      }

      return []
    } catch (error) {
      console.error('获取演员名称失败:', error)

      return []
    }
  },

  // 删除演员（软删除）
  deleteActor: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrActors/Delete?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除演员失败:', error)

      return false
    }
  },

  // 删除演员媒体信息
  deleteActorMedia: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrActors/DeleteActorMedia?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除演员媒体信息失败:', error)

      return false
    }
  },

  // 删除演员作品信息
  deleteActorWorks: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, any>(`/PrActors/DeleteActorWorks?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除演员作品信息失败:', error)

      return false
    }
  },
}))
