```
{
  "openapi": "3.0.1",
  "info": {
    "title": "CdProductionServerWebApi API",
    "description": "API for CdProductionServerWebApi",
    "contact": {
      "name": " ",
      "email": ""
    },
    "version": "v1"
  },
  "servers": [],
  "paths": {
    "/PrPartner/GetList": {
      "post": {
        "tags": [
          "PrPartner"
        ],
        "summary": "获取列表",
        "requestBody": {
          "description": "",
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/PagePartnerBasicDto"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/PagePartnerBasicDto"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/PagePartnerBasicDto"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/PrPartnerBasic"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/PrPartnerBasic"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/PrPartnerBasic"
                }
              }
            }
          }
        },
        "security": [
          {
            "Authorization": [
              "global"
            ]
          }
        ],
        "operationId": "b81df13ccdd4ee804b7dfc29340be495"
      }
    }
  },
  "components": {
    "PagePartnerBasicDto": {
      "type": "object",
      "properties": {
        "pageIndex": {
          "type": "integer",
          "description": "一页",
          "format": "int32",
          "refType": null
        },
        "pageSize": {
          "type": "integer",
          "description": "一页数量",
          "format": "int32",
          "refType": null
        },
        "partnerName": {
          "type": "string",
          "description": "合作商名称",
          "nullable": true,
          "refType": null
        },
        "partnerType": {
          "type": "integer",
          "description": "合作商类型(1:供应商,2:个人)",
          "format": "int32",
          "nullable": true,
          "refType": null
        },
        "legalRepresentative": {
          "type": "string",
          "description": "法定代表人名称",
          "nullable": true,
          "refType": null
        },
        "contactPerson": {
          "type": "string",
          "description": "日常联系人",
          "nullable": true,
          "refType": null
        },
        "shortName": {
          "type": "string",
          "description": "简称",
          "nullable": true,
          "refType": null
        },
        "status": {
          "type": "integer",
          "description": "状态（1正常，2禁用）",
          "format": "int32",
          "nullable": true,
          "refType": null
        }
      },
      "additionalProperties": false,
      "description": "分页"
    },
    "PrPartnerBasic": {
      "type": "object",
      "properties": {
        "creator": {
          "type": "string",
          "description": "创建人",
          "nullable": true
        },
        "lastModifier": {
          "type": "string",
          "description": "最后修改人",
          "nullable": true
        },
        "id": {
          "type": "integer",
          "description": "合作商Id",
          "format": "int32"
        },
        "partnerName": {
          "type": "string",
          "description": "合作商名称",
          "nullable": true
        },
        "partnerCode": {
          "type": "string",
          "description": "合作商编码（系统唯一）",
          "nullable": true
        },
        "shortName": {
          "type": "string",
          "description": "简称",
          "nullable": true
        },
        "partnerType": {
          "type": "integer",
          "description": "合作商类型(1:供应商,2:个人)",
          "format": "int32"
        },
        "legalRepresentative": {
          "type": "string",
          "description": "法定代表人名称",
          "nullable": true
        },
        "legalPhone": {
          "type": "string",
          "description": "法人联系电话",
          "nullable": true
        },
        "legalIdNumber": {
          "type": "string",
          "description": "法人证件号码",
          "nullable": true
        },
        "legalIdFrontPath": {
          "type": "string",
          "description": "法人身份证正面路径",
          "nullable": true
        },
        "legalIdBackPath": {
          "type": "string",
          "description": "法人身份证背面路径",
          "nullable": true
        },
        "contactPerson": {
          "type": "string",
          "description": "日常联系人",
          "nullable": true
        },
        "contactPhone": {
          "type": "string",
          "description": "联系人电话",
          "nullable": true
        },
        "contactEmail": {
          "type": "string",
          "description": "联系人邮箱",
          "nullable": true
        },
        "address": {
          "type": "string",
          "description": "注册地址",
          "nullable": true
        },
        "actualAddress": {
          "type": "string",
          "description": "实际经营地址",
          "nullable": true
        },
        "businessLicense": {
          "type": "string",
          "description": "营业执照号",
          "nullable": true
        },
        "businessLicensePath": {
          "type": "string",
          "description": "营业执照图片路径",
          "nullable": true
        },
        "remark": {
          "type": "string",
          "description": "备注信息",
          "nullable": true
        },
        "serviceContent": {
          "type": "string",
          "description": "服务内容",
          "nullable": true
        },
        "status": {
          "type": "integer",
          "description": "人员状态（1正常，2禁用）",
          "format": "int32"
        },
        "isDelete": {
          "type": "boolean",
          "description": "是否删除"
        },
        "createTime": {
          "type": "string",
          "description": "创建时间",
          "format": "date-time"
        },
        "updateTime": {
          "type": "string",
          "description": "更新时间",
          "format": "date-time"
        },
        "bankInfos": {
          "type": "array",
          "items": {
            "$ref": "#/components/schemas/PrBankAccountInfo"
          },
          "description": "银行卡信息列表",
          "nullable": true
        },
        "fddVerifyStatus": {
          "type": "integer",
          "description": "实名认证状态个人 0：未激活； 1：未认证； 2：审核通过； 3：已提交待审核； 4：审核不通过",
          "format": "int32",
          "nullable": true
        },
        "fddCustomerVerifyUrl": {
          "type": "string",
          "description": "法大大用户实名认证地址",
          "nullable": true
        }
      },
      "additionalProperties": false,
      "description": "合作商基础信息"
    },
    "PrBankAccountInfo": {
      "type": "object",
      "properties": {
        "id": {
          "type": "integer",
          "description": "银行卡信息Id",
          "format": "int32",
          "refType": null
        },
        "parentType": {
          "type": "integer",
          "description": "银行信息类型 (0人员,1合作商)",
          "format": "int32",
          "refType": null
        },
        "personId": {
          "type": "integer",
          "description": "关联人员ID",
          "format": "int32",
          "refType": null
        },
        "isDefault": {
          "type": "boolean",
          "description": "是否默认",
          "refType": null
        },
        "bankName": {
          "type": "string",
          "description": "开户行名称",
          "nullable": true,
          "refType": null
        },
        "accountName": {
          "type": "string",
          "description": "账户名称",
          "nullable": true,
          "refType": null
        },
        "accountNumber": {
          "type": "string",
          "description": "卡号",
          "nullable": true,
          "refType": null
        },
        "createTime": {
          "type": "string",
          "description": "创建时间",
          "format": "date-time",
          "refType": null
        },
        "updateTime": {
          "type": "string",
          "description": "更新时间",
          "format": "date-time",
          "refType": null
        }
      },
      "additionalProperties": false,
      "description": "客户账户信息表"
    }
  }
}
```

```
{
  "openapi": "3.0.1",
  "info": {
    "title": "CdProductionServerWebApi API",
    "description": "API for CdProductionServerWebApi",
    "contact": {
      "name": " ",
      "email": ""
    },
    "version": "v1"
  },
  "servers": [],
  "paths": {
    "/PrPartner/GetById": {
      "get": {
        "tags": [
          "PrPartner"
        ],
        "summary": "根据Id获取合作商信息",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "description": "",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/PrPartnerBasic"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/PrPartnerBasic"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/PrPartnerBasic"
                }
              }
            }
          }
        },
        "security": [
          {
            "Authorization": [
              "global"
            ]
          }
        ],
        "operationId": "135e28b742cc40c9eecbcdd7e947ea7e"
      }
    }
  },
  "components": {
    "PrPartnerBasic": {
      "type": "object",
      "properties": {
        "creator": {
          "type": "string",
          "description": "创建人",
          "nullable": true
        },
        "lastModifier": {
          "type": "string",
          "description": "最后修改人",
          "nullable": true
        },
        "id": {
          "type": "integer",
          "description": "合作商Id",
          "format": "int32"
        },
        "partnerName": {
          "type": "string",
          "description": "合作商名称",
          "nullable": true
        },
        "partnerCode": {
          "type": "string",
          "description": "合作商编码（系统唯一）",
          "nullable": true
        },
        "shortName": {
          "type": "string",
          "description": "简称",
          "nullable": true
        },
        "partnerType": {
          "type": "integer",
          "description": "合作商类型(1:供应商,2:个人)",
          "format": "int32"
        },
        "legalRepresentative": {
          "type": "string",
          "description": "法定代表人名称",
          "nullable": true
        },
        "legalPhone": {
          "type": "string",
          "description": "法人联系电话",
          "nullable": true
        },
        "legalIdNumber": {
          "type": "string",
          "description": "法人证件号码",
          "nullable": true
        },
        "legalIdFrontPath": {
          "type": "string",
          "description": "法人身份证正面路径",
          "nullable": true
        },
        "legalIdBackPath": {
          "type": "string",
          "description": "法人身份证背面路径",
          "nullable": true
        },
        "contactPerson": {
          "type": "string",
          "description": "日常联系人",
          "nullable": true
        },
        "contactPhone": {
          "type": "string",
          "description": "联系人电话",
          "nullable": true
        },
        "contactEmail": {
          "type": "string",
          "description": "联系人邮箱",
          "nullable": true
        },
        "address": {
          "type": "string",
          "description": "注册地址",
          "nullable": true
        },
        "actualAddress": {
          "type": "string",
          "description": "实际经营地址",
          "nullable": true
        },
        "businessLicense": {
          "type": "string",
          "description": "营业执照号",
          "nullable": true
        },
        "businessLicensePath": {
          "type": "string",
          "description": "营业执照图片路径",
          "nullable": true
        },
        "remark": {
          "type": "string",
          "description": "备注信息",
          "nullable": true
        },
        "serviceContent": {
          "type": "string",
          "description": "服务内容",
          "nullable": true
        },
        "status": {
          "type": "integer",
          "description": "人员状态（1正常，2禁用）",
          "format": "int32"
        },
        "isDelete": {
          "type": "boolean",
          "description": "是否删除"
        },
        "createTime": {
          "type": "string",
          "description": "创建时间",
          "format": "date-time"
        },
        "updateTime": {
          "type": "string",
          "description": "更新时间",
          "format": "date-time"
        },
        "bankInfos": {
          "type": "array",
          "items": {
            "$ref": "#/components/schemas/PrBankAccountInfo"
          },
          "description": "银行卡信息列表",
          "nullable": true
        },
        "fddVerifyStatus": {
          "type": "integer",
          "description": "实名认证状态个人 0：未激活； 1：未认证； 2：审核通过； 3：已提交待审核； 4：审核不通过",
          "format": "int32",
          "nullable": true
        },
        "fddCustomerVerifyUrl": {
          "type": "string",
          "description": "法大大用户实名认证地址",
          "nullable": true
        }
      },
      "additionalProperties": false,
      "description": "合作商基础信息"
    },
    "PrBankAccountInfo": {
      "type": "object",
      "properties": {
        "id": {
          "type": "integer",
          "description": "银行卡信息Id",
          "format": "int32",
          "refType": null
        },
        "parentType": {
          "type": "integer",
          "description": "银行信息类型 (0人员,1合作商)",
          "format": "int32",
          "refType": null
        },
        "personId": {
          "type": "integer",
          "description": "关联人员ID",
          "format": "int32",
          "refType": null
        },
        "isDefault": {
          "type": "boolean",
          "description": "是否默认",
          "refType": null
        },
        "bankName": {
          "type": "string",
          "description": "开户行名称",
          "nullable": true,
          "refType": null
        },
        "accountName": {
          "type": "string",
          "description": "账户名称",
          "nullable": true,
          "refType": null
        },
        "accountNumber": {
          "type": "string",
          "description": "卡号",
          "nullable": true,
          "refType": null
        },
        "createTime": {
          "type": "string",
          "description": "创建时间",
          "format": "date-time",
          "refType": null
        },
        "updateTime": {
          "type": "string",
          "description": "更新时间",
          "format": "date-time",
          "refType": null
        }
      },
      "additionalProperties": false,
      "description": "客户账户信息表"
    }
  }
}
```

```
{
  "openapi": "3.0.1",
  "info": {
    "title": "CdProductionServerWebApi API",
    "description": "API for CdProductionServerWebApi",
    "contact": {
      "name": " ",
      "email": ""
    },
    "version": "v1"
  },
  "servers": [],
  "paths": {
    "/PrPartner/Save": {
      "post": {
        "tags": [
          "PrPartner"
        ],
        "summary": "保存合作商",
        "requestBody": {
          "description": "",
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/PrPartnerBasic"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/PrPartnerBasic"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/PrPartnerBasic"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              }
            }
          }
        },
        "security": [
          {
            "Authorization": [
              "global"
            ]
          }
        ],
        "operationId": "3d1adc7a73355a5f19229ac6d0d43db4"
      }
    }
  },
  "components": {
    "PrPartnerBasic": {
      "type": "object",
      "properties": {
        "creator": {
          "type": "string",
          "description": "创建人",
          "nullable": true,
          "refType": null
        },
        "lastModifier": {
          "type": "string",
          "description": "最后修改人",
          "nullable": true,
          "refType": null
        },
        "id": {
          "type": "integer",
          "description": "合作商Id",
          "format": "int32",
          "refType": null
        },
        "partnerName": {
          "type": "string",
          "description": "合作商名称",
          "nullable": true,
          "refType": null
        },
        "partnerCode": {
          "type": "string",
          "description": "合作商编码（系统唯一）",
          "nullable": true,
          "refType": null
        },
        "shortName": {
          "type": "string",
          "description": "简称",
          "nullable": true,
          "refType": null
        },
        "partnerType": {
          "type": "integer",
          "description": "合作商类型(1:供应商,2:个人)",
          "format": "int32",
          "refType": null
        },
        "legalRepresentative": {
          "type": "string",
          "description": "法定代表人名称",
          "nullable": true,
          "refType": null
        },
        "legalPhone": {
          "type": "string",
          "description": "法人联系电话",
          "nullable": true,
          "refType": null
        },
        "legalIdNumber": {
          "type": "string",
          "description": "法人证件号码",
          "nullable": true,
          "refType": null
        },
        "legalIdFrontPath": {
          "type": "string",
          "description": "法人身份证正面路径",
          "nullable": true,
          "refType": null
        },
        "legalIdBackPath": {
          "type": "string",
          "description": "法人身份证背面路径",
          "nullable": true,
          "refType": null
        },
        "contactPerson": {
          "type": "string",
          "description": "日常联系人",
          "nullable": true,
          "refType": null
        },
        "contactPhone": {
          "type": "string",
          "description": "联系人电话",
          "nullable": true,
          "refType": null
        },
        "contactEmail": {
          "type": "string",
          "description": "联系人邮箱",
          "nullable": true,
          "refType": null
        },
        "address": {
          "type": "string",
          "description": "注册地址",
          "nullable": true,
          "refType": null
        },
        "actualAddress": {
          "type": "string",
          "description": "实际经营地址",
          "nullable": true,
          "refType": null
        },
        "businessLicense": {
          "type": "string",
          "description": "营业执照号",
          "nullable": true,
          "refType": null
        },
        "businessLicensePath": {
          "type": "string",
          "description": "营业执照图片路径",
          "nullable": true,
          "refType": null
        },
        "remark": {
          "type": "string",
          "description": "备注信息",
          "nullable": true,
          "refType": null
        },
        "serviceContent": {
          "type": "string",
          "description": "服务内容",
          "nullable": true,
          "refType": null
        },
        "status": {
          "type": "integer",
          "description": "人员状态（1正常，2禁用）",
          "format": "int32",
          "refType": null
        },
        "isDelete": {
          "type": "boolean",
          "description": "是否删除",
          "refType": null
        },
        "createTime": {
          "type": "string",
          "description": "创建时间",
          "format": "date-time",
          "refType": null
        },
        "updateTime": {
          "type": "string",
          "description": "更新时间",
          "format": "date-time",
          "refType": null
        },
        "bankInfos": {
          "type": "array",
          "items": {
            "$ref": "#/components/schemas/PrBankAccountInfo"
          },
          "description": "银行卡信息列表",
          "nullable": true,
          "refType": "PrBankAccountInfo"
        },
        "fddVerifyStatus": {
          "type": "integer",
          "description": "实名认证状态个人 0：未激活； 1：未认证； 2：审核通过； 3：已提交待审核； 4：审核不通过",
          "format": "int32",
          "nullable": true,
          "refType": null
        },
        "fddCustomerVerifyUrl": {
          "type": "string",
          "description": "法大大用户实名认证地址",
          "nullable": true,
          "refType": null
        }
      },
      "additionalProperties": false,
      "description": "合作商基础信息"
    },
    "WebResponseContent": {
      "type": "object",
      "properties": {
        "status": {
          "type": "boolean"
        },
        "code": {
          "type": "string",
          "nullable": true
        },
        "message": {
          "type": "string",
          "nullable": true
        },
        "data": {
          "nullable": true
        }
      },
      "additionalProperties": false
    },
    "PrBankAccountInfo": {
      "type": "object",
      "properties": {
        "id": {
          "type": "integer",
          "description": "银行卡信息Id",
          "format": "int32",
          "refType": null
        },
        "parentType": {
          "type": "integer",
          "description": "银行信息类型 (0人员,1合作商)",
          "format": "int32",
          "refType": null
        },
        "personId": {
          "type": "integer",
          "description": "关联人员ID",
          "format": "int32",
          "refType": null
        },
        "isDefault": {
          "type": "boolean",
          "description": "是否默认",
          "refType": null
        },
        "bankName": {
          "type": "string",
          "description": "开户行名称",
          "nullable": true,
          "refType": null
        },
        "accountName": {
          "type": "string",
          "description": "账户名称",
          "nullable": true,
          "refType": null
        },
        "accountNumber": {
          "type": "string",
          "description": "卡号",
          "nullable": true,
          "refType": null
        },
        "createTime": {
          "type": "string",
          "description": "创建时间",
          "format": "date-time",
          "refType": null
        },
        "updateTime": {
          "type": "string",
          "description": "更新时间",
          "format": "date-time",
          "refType": null
        }
      },
      "additionalProperties": false,
      "description": "客户账户信息表"
    }
  }
}
```

```
{
  "openapi": "3.0.1",
  "info": {
    "title": "CdProductionServerWebApi API",
    "description": "API for CdProductionServerWebApi",
    "contact": {
      "name": " ",
      "email": ""
    },
    "version": "v1"
  },
  "servers": [],
  "paths": {
    "/PrPartner/Delete": {
      "get": {
        "tags": [
          "PrPartner"
        ],
        "summary": "删除合作商",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "description": "",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              }
            }
          }
        },
        "security": [
          {
            "Authorization": [
              "global"
            ]
          }
        ],
        "operationId": "5d97127f82391afb33ed2fa136e83330"
      }
    }
  },
  "components": {
    "WebResponseContent": {
      "type": "object",
      "properties": {
        "status": {
          "type": "boolean"
        },
        "code": {
          "type": "string",
          "nullable": true
        },
        "message": {
          "type": "string",
          "nullable": true
        },
        "data": {
          "nullable": true
        }
      },
      "additionalProperties": false
    }
  }
}
```

```
{
  "openapi": "3.0.1",
  "info": {
    "title": "CdProductionServerWebApi API",
    "description": "API for CdProductionServerWebApi",
    "contact": {
      "name": " ",
      "email": ""
    },
    "version": "v1"
  },
  "servers": [],
  "paths": {
    "/PrPartner/Delete": {
      "get": {
        "tags": [
          "PrPartner"
        ],
        "summary": "删除合作商",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "description": "",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              }
            }
          }
        },
        "security": [
          {
            "Authorization": [
              "global"
            ]
          }
        ],
        "operationId": "5d97127f82391afb33ed2fa136e83330"
      }
    }
  },
  "components": {
    "WebResponseContent": {
      "type": "object",
      "properties": {
        "status": {
          "type": "boolean"
        },
        "code": {
          "type": "string",
          "nullable": true
        },
        "message": {
          "type": "string",
          "nullable": true
        },
        "data": {
          "nullable": true
        }
      },
      "additionalProperties": false
    }
  }
}
```

```
{
  "openapi": "3.0.1",
  "info": {
    "title": "CdProductionServerWebApi API",
    "description": "API for CdProductionServerWebApi",
    "contact": {
      "name": " ",
      "email": ""
    },
    "version": "v1"
  },
  "servers": [],
  "paths": {
    "/PrPartner/ValidPartnerRealName": {
      "get": {
        "tags": [
          "PrPartner"
        ],
        "summary": "判读用户是否已实名\r\nValidResult：0信息不全，1已实名，2未实名",
        "parameters": [
          {
            "name": "id",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/WebResponseContent"
                }
              }
            }
          }
        },
        "security": [
          {
            "Authorization": [
              "global"
            ]
          }
        ],
        "operationId": "5eba41cda0dca30ff37efc07a8a1f781"
      }
    }
  },
  "components": {
    "WebResponseContent": {
      "type": "object",
      "properties": {
        "status": {
          "type": "boolean"
        },
        "code": {
          "type": "string",
          "nullable": true
        },
        "message": {
          "type": "string",
          "nullable": true
        },
        "data": {
          "nullable": true
        }
      },
      "additionalProperties": false
    }
  }
}
```