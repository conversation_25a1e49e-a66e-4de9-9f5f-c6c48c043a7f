import { create } from 'zustand'
import { post, get as requestGet } from '../../../utils/request'

export const prPartnerUploadAPI = '/PrActors/UploadFile'

// 银行账户信息接口（合作商）
export interface IPrBankAccountInfo {
  id?: number // 银行卡信息Id
  parentType?: number // 银行信息类型 (0人员,1合作商)
  personId?: number // 关联人员ID
  isDefault: boolean // 是否默认
  bankName?: string // 开户行名称
  accountName?: string // 账户名称
  accountNumber?: string // 卡号
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 合作商分页查询参数（对应API的PagePartnerBasicDto）
export interface IPartnerListSearchParams {
  pageIndex: number // 页码
  pageSize: number // 每页数量
  partnerName?: string // 合作商名称
  partnerType?: number // 合作商类型(1:供应商,2:个人)
  legalRepresentative?: string // 法定代表人名称
  contactPerson?: string // 日常联系人
  shortName?: string // 简称
  status?: number // 状态（1正常，2禁用）
}

// API响应的合作商信息（对应API的PrPartnerBasic）
export interface IPartnerListItem {
  creator?: string // 创建人
  lastModifier?: string // 最后修改人
  id: number // 合作商Id
  partnerName?: string // 合作商名称
  partnerCode?: string // 合作商编码（系统唯一）
  shortName?: string // 简称
  partnerType: number // 合作商类型(1:供应商,2:个人)
  legalRepresentative?: string // 法定代表人名称
  legalPhone?: string // 法人联系电话
  legalIdNumber?: string // 法人证件号码
  legalIdFrontPath?: string // 法人身份证正面路径
  legalIdBackPath?: string // 法人身份证背面路径
  contactPerson?: string // 日常联系人
  contactPhone?: string // 联系人电话
  contactEmail?: string // 联系人邮箱
  address?: string // 注册地址
  actualAddress?: string // 实际经营地址
  businessLicense?: string // 营业执照号
  businessLicensePath?: string // 营业执照图片路径
  remark?: string // 备注信息
  serviceContent?: string // 服务内容
  status: number // 人员状态（1正常，2禁用）
  isDelete: boolean // 是否删除
  createTime: string // 创建时间
  updateTime: string // 更新时间
  bankInfos?: IPrBankAccountInfo[] // 银行卡信息列表
  fddVerifyStatus?: number // 实名认证状态个人 0：未激活； 1：未认证； 2：审核通过； 3：已提交待审核； 4：审核不通过
  fddCustomerVerifyUrl?: string // 法大大用户实名认证地址
}

// 合作商名称选项（对应API的OptionDto）
export interface IPartnerOptionItem {
  val?: string // 键（合作商ID）
  label?: string // 值（合作商名称）
}

// API响应类型
export interface IPartnerListResponse {
  list: IPartnerListItem[]
  total: number
  pageIndex: number
  pageSize: number
}

// 实名验证响应类型
export interface IValidRealNameResponse {
  validResult: number // 0信息不全，1已实名，2未实名
  url?: string // 实名认证或完善信息的链接
}

export interface IPartnerListStore {
  allPartnerOptions: IPartnerOptionItem[] // 所有合作商选项列表
  // API调用方法
  fetchPartnerList: (params: IPartnerListSearchParams) => Promise<IPartnerListResponse | null>
  getPartnerById: (id: number) => Promise<IPartnerListItem | null>
  savePartner: (partnerData: IPartnerListItem) => Promise<boolean>
  deletePartner: (id: number) => Promise<boolean>
  validPartnerRealName: (id: number) => Promise<IValidRealNameResponse | null>
}

export default create<IPartnerListStore>((set, get) => ({
  allPartnerOptions: [],

  // 获取合作商列表
  fetchPartnerList: async (params: IPartnerListSearchParams) => {
    try {
      const { data, status } = await post<any, any>('/PrPartner/GetList', params)

      if (status && data) {
        return {
          list: data.dataList || [],
          total: data.totalCount || 0,
          pageIndex: params.pageIndex,
          pageSize: params.pageSize,
        }
      }

      return null
    } catch (error) {
      console.error('获取合作商列表失败:', error)
      return null
    }
  },

  // 根据ID获取合作商详情
  getPartnerById: async (id: number) => {
    try {
      const { data, status } = await requestGet(`/PrPartner/GetById?id=${id}`)

      if (status && data) {
        return data?.dataList || null
      }

      return null
    } catch (error) {
      console.error('获取合作商详情失败:', error)
      return null
    }
  },

  // 保存合作商信息
  savePartner: async (partnerData: IPartnerListItem) => {
    try {
      const { data, status } = await post<any, any>('/PrPartner/Save', partnerData)

      if (status) {
        return data || true
      }

      return false
    } catch (error) {
      console.error('保存合作商信息失败:', error)
      return false
    }
  },

  // 删除合作商
  deletePartner: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrPartner/Delete?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除合作商失败:', error)
      return false
    }
  },

  // 判断合作商是否已实名
  validPartnerRealName: async (id: number) => {
    try {
      const { data, status } = await requestGet<any, { data: IValidRealNameResponse; status: boolean }>(
        `/PrPartner/ValidPartnerRealName?id=${id}`
      )

      if (status && data) {
        return data
      }

      return null
    } catch (error) {
      console.error('验证合作商实名信息失败:', error)
      return null
    }
  },
}))
