import useSyncParams, { initFormFromUrlState, parsePagination } from '@/hooks/useSyncParams'
import { Flex, Form, message } from 'antd'
import React, { useEffect, useState } from 'react'
import { PAGINATION } from '../../../../consts'
import usePartnerListStore, { IPartnerListItem, IPartnerListSearchParams } from '../store'
import PartnerForm from './components/Add'
import PartnerDetail from './components/Detail'
import PartnerList from './components/List'
import PartnerSearch from './components/Search'

const PartnerInfo: React.FC = () => {
  const [urlState, setUrlState] = useSyncParams<IPartnerListSearchParams>()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [dataSource, setDataSource] = useState<IPartnerListItem[]>([])
  const [pagination, setPagination] = useState(PAGINATION)

  const [show, setShow] = useState(false)
  const [operateData, setOperateData] = useState<any | IPartnerListItem>()
  const [operateType, setOperateType] = useState<string>('')

  const { fetchPartnerList, savePartner, deletePartner } = usePartnerListStore()

  // 初始化URL数据
  const initParams = () => {
    // 使用工具函数简化表单初始化
    initFormFromUrlState(urlState, form, {
      numberFields: ['partnerType', 'status'],
      excludeFields: ['pageIndex', 'pageSize'],
    })

    // 使用工具函数处理分页
    const { current, pageSize } = parsePagination(urlState, {
      current: pagination.current,
      pageSize: pagination.pageSize,
    })

    handleSearch(current, pageSize)
  }

  const handleSearch = async (current = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true)
    try {
      const values = form.getFieldsValue()
      const searchParams: IPartnerListSearchParams = {
        pageIndex: current,
        pageSize,
        ...values,
      }

      const result = await fetchPartnerList(searchParams)

      if (result) {
        setDataSource(result.list)
        setPagination(prev => ({
          ...prev,
          total: result.total,
          current: result.pageIndex,
          pageSize: result.pageSize,
        }))

        // 同步URL参数
        setUrlState({
          partnerName: values.partnerName || '',
          partnerType: values.partnerType,
          legalRepresentative: values.legalRepresentative || '',
          contactPerson: values.contactPerson || '',
          shortName: values.shortName || '',
          status: values.status,
          pageSize: result.pageSize,
          pageIndex: result.pageIndex,
        })
      } else {
        setDataSource([])
      }
    } catch (error) {
      setDataSource([])
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (data: IPartnerListItem) => {
    try {
      const result = await deletePartner(data.id)

      if (result) {
        message.success('删除成功')
        handleOperateClose(false)
        await handleSearch(1)
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 主分发
  const handleOperate = (type: string, data?: IPartnerListItem) => {
    if (type === 'delete') {
      data && handleDelete(data)
    } else if (['create', 'edit', 'view'].includes(type)) {
      setOperateData(data || null)
      setOperateType(type)
      setShow(true)
    }
  }

  const handleOperateClose = (fresh = true) => {
    setOperateData(null)
    setOperateType('')
    setShow(false)
    if (fresh) {
      handleSearch()
    }
  }

  // 表单提交
  const handleFormSubmit = async (values: IPartnerListItem) => {
    try {
      let param = { ...operateData, ...values }

      const result = await savePartner(param)

      if (result) {
        message.success(operateType == 'edit' ? '更新成功' : '新增成功')
        handleOperateClose()
        await handleSearch(1)
      }
    } catch (error) {
      message.error('操作失败')
    }
  }

  // 从详情页面编辑
  const handleEditFromDetail = (partner: IPartnerListItem) => {
    setOperateData(partner)
    setOperateType('edit')
    // 保持抽屉打开状态，但切换到编辑模式
  }

  // 从详情页面删除
  const handleDeleteFromDetail = (partner: IPartnerListItem) => {
    handleDelete(partner)
  }

  useEffect(() => {
    initParams()
  }, [])

  return (
    <Flex vertical gap={24}>
      <PartnerSearch form={form} onSearch={handleSearch} onReset={() => handleSearch(1)} loading={loading} />
      <PartnerList
        data={dataSource}
        loading={loading}
        onOperate={handleOperate}
        onChange={handleSearch}
        pagination={pagination}
      />

      {/* 合作商详情抽屉 */}
      <PartnerDetail
        open={show && operateType === 'view'}
        partner={operateData ?? void 0}
        onClose={handleOperateClose}
        onEdit={handleEditFromDetail}
        onDelete={handleDeleteFromDetail}
      />

      {/* 合作商表单模态框 */}
      <PartnerForm
        open={show && ['edit', 'create'].includes(operateType)}
        partner={operateData ?? void 0}
        onCancel={handleOperateClose}
        onSubmit={handleFormSubmit}
      />
    </Flex>
  )
}

export default PartnerInfo
