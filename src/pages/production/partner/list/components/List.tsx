import { PARTNER_TYPE_CONFIG, PARTNER_STATUS_CONFIG } from '../../../../../consts/partner'
import { FDD_VERIFY_STATUS_CONFIG } from '../../../../../consts'
import { DATE_FORMAT_BASE } from '../../../../../consts/date'
import { copy } from '../../../../../utils/copy'
import { PlusOutlined, BankOutlined, UserOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Badge, Button, Card, Divider, Flex, List, message, Space, Tag, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useState } from 'react'
import usePartnerListStore from '../../store'
import { IPartnerListItem } from '../../store'
import styles from './List.scss'

// 合作商列表
const PartnerList: React.FC<any> = ({ data, pagination, loading, onChange, onOperate }) => {
  const [fetching, setFetching] = useState<number | null>(null)
  const { validPartnerRealName } = usePartnerListStore()

  // 独立实名处理函数
  const handleRealName = async (partner: IPartnerListItem) => {
    if (fetching) {
      return
    }

    try {
      // 如果已有url，直接使用
      if (partner.fddCustomerVerifyUrl) {
        copy(partner.fddCustomerVerifyUrl)
        message.success('实名认证链接已复制到剪贴板')
        return
      }
      setFetching(partner.id)

      const res = await validPartnerRealName(partner.id)

      setFetching(null)

      if (res?.url) {
        copy(res?.url)
        message.success('实名认证链接已复制到剪贴板')
      } else {
        message.error('获取实名认证链接异常')
      }
    } finally {
      setFetching(null)
    }
  }

  // 渲染合作商卡片
  const renderPartnerCard = (partner: IPartnerListItem) => {
    const partnerTypeInfo = PARTNER_TYPE_CONFIG[partner.partnerType] || { label: '未知', color: 'default' }
    const statusInfo = PARTNER_STATUS_CONFIG[partner.status] || { label: '未知', color: 'default' }
    const verifyStatus = partner.fddVerifyStatus
    const verifyStatusInfo = FDD_VERIFY_STATUS_CONFIG[verifyStatus || 0] || { label: '未知', color: 'default' }

    const ListItemCard = (
      <Card hoverable size="small" className="full-h hover-move" onClick={() => onOperate && onOperate('view', partner)}>
        <Flex justify="space-between" align="center">
          <Flex flex={1} gap={16} vertical>
            <Space size={2} split={<Divider type="vertical" />}>
              <Typography.Text strong className="fs-lg">
                {partner.partnerName}
              </Typography.Text>
              <Tag className="no-margin" color={partnerTypeInfo.color}>
                {partnerTypeInfo.label === '供应商' ? <BankOutlined /> : <UserOutlined />}
                <span className="ml-1">{partnerTypeInfo.label}</span>
              </Tag>
              <Tag className="no-margin" color={statusInfo.color}>
                {statusInfo.label}
              </Tag>
              {partner.shortName ? <Dict title="简称" value={partner.shortName} /> : null}
              {partner.partnerCode ? <Dict title="编码" value={partner.partnerCode} /> : null}
            </Space>
            
            <Space size={2} split={<Divider type="vertical" />}>
              {partner.legalRepresentative ? (
                <Dict
                  title={partner.partnerType === 2 ? "姓名" : "法定代表人"}
                  value={partner.legalRepresentative}
                />
              ) : null}
              {partner.contactPerson ? (
                <Dict
                  title={partner.partnerType === 2 ? "联系人" : "日常联系人"}
                  value={partner.contactPerson}
                />
              ) : null}
              {partner.contactPhone ? (
                <Dict
                  title={partner.partnerType === 2 ? "手机号码" : "联系电话"}
                  value={partner.contactPhone}
                />
              ) : null}
              {partner.serviceContent ? <Dict title="服务内容" value={partner.serviceContent} /> : null}
            </Space>
            
            <Space size={24}>
              {partner.updateTime ? (
                <Space>
                  {partner.lastModifier ? <Typography.Text>{partner.lastModifier}</Typography.Text> : null}
                  <Dict
                    title="最近更新于"
                    value={
                      <Typography.Text type="secondary">
                        {dayjs(partner.updateTime).format(DATE_FORMAT_BASE)}
                      </Typography.Text>
                    }
                  />
                </Space>
              ) : (
                <Space>
                  {partner.creator ? <Typography.Text>{partner.creator}</Typography.Text> : null}
                  <Dict
                    title="添加于"
                    value={
                      <Typography.Text type="secondary">
                        {dayjs(partner.createTime).format(DATE_FORMAT_BASE)}
                      </Typography.Text>
                    }
                  />
                </Space>
              )}
            </Space>
          </Flex>
          <Space className="w100" direction="vertical" size="small">
            {verifyStatus === 2 ? (
              <Badge status="success" text="实名通过" />
            ) : (
              <Badge status={verifyStatusInfo.color as any} text={verifyStatusInfo.label} />
            )}
            {verifyStatus !== 2  ? (
              <Button
                size="small"
                type="link"
                loading={partner.id === fetching}
                disabled={loading}
                onClick={e => {
                  e.stopPropagation()
                  handleRealName(partner)
                }}>
                去实名认证
              </Button>
            ) : null}
          </Space>
        </Flex>
      </Card>
    )

    return (
      <List.Item>
        {partner.status === 2 ? (
          <Badge.Ribbon color="#999" text="禁止合作">
            {ListItemCard}
          </Badge.Ribbon>
        ) : (
          ListItemCard
        )}
      </List.Item>
    )
  }

  return (
    <Flex vertical>
      <ListHeader title="合作商列表" total={pagination?.total} unitText="个">
        <Button type="primary" ghost icon={<PlusOutlined />} onClick={() => onOperate && onOperate('create')}>
          添加合作商
        </Button>
      </ListHeader>
      <List
        loading={loading}
        dataSource={data}
        split={false}
        renderItem={renderPartnerCard}
        rowKey="id"
        className={`${styles.list} list-sm`}
        pagination={{
          ...pagination,
          onChange,
          onShowSizeChange: onChange,
        }}
      />
    </Flex>
  )
}

export default PartnerList
