import EnvImage from '@/components/EnvImage'
import { Dict } from '@fe/rockrose'
import { Badge, Card, Descriptions, Flex, Space, Tabs, Tag, Typography } from 'antd'
import React from 'react'
import {
  PARTNER_TYPE_CONFIG,
  PARTNER_STATUS_CONFIG,
} from '../../../../../consts/partner'
import { FDD_VERIFY_STATUS_CONFIG } from '../../../../../consts'
import { IPartnerListItem } from '../../store'

interface PartnerDetailContentProps {
  partner?: IPartnerListItem
}

const PartnerDetailContent: React.FC<PartnerDetailContentProps> = ({ partner }) => {
  if (!partner) {
    return null
  }

  const partnerTypeInfo = PARTNER_TYPE_CONFIG[partner.partnerType] || { label: '未知', color: 'default' }
  const statusInfo = partner.status ? PARTNER_STATUS_CONFIG[partner.status as keyof typeof PARTNER_STATUS_CONFIG] : null
  const verifyStatusInfo = partner.fddVerifyStatus ? FDD_VERIFY_STATUS_CONFIG[partner.fddVerifyStatus as keyof typeof FDD_VERIFY_STATUS_CONFIG] : null

  const descriptions = (
    <Descriptions size="small" column={3} bordered title="1、基本信息">
      <Descriptions.Item label="合作商名称">
        <Typography.Text strong>{partner.partnerName}</Typography.Text>
      </Descriptions.Item>
      <Descriptions.Item label="合作商类型">
        <Tag color={partnerTypeInfo.color}>{partnerTypeInfo.label}</Tag>
      </Descriptions.Item>
      <Descriptions.Item label="合作状态">
        {statusInfo && <Badge status={statusInfo.color as any} text={statusInfo.label}></Badge>}
      </Descriptions.Item>

      {partner.shortName && <Descriptions.Item label="简称">{partner.shortName}</Descriptions.Item>}
      {partner.partnerCode && <Descriptions.Item label="合作商编码">{partner.partnerCode}</Descriptions.Item>}
      {verifyStatusInfo && (
        <Descriptions.Item label="实名认证状态">
          <Badge status={verifyStatusInfo.color as any} text={verifyStatusInfo.label}></Badge>
        </Descriptions.Item>
      )}

      {partner.serviceContent && (
        <Descriptions.Item label="服务内容" span={3}>
          {partner.serviceContent}
        </Descriptions.Item>
      )}
    </Descriptions>
  )

  const legalInfo = (
    <Descriptions size="small" column={2} bordered title={partner.partnerType === 2 ? "2、个人信息" : "2、法定代表人信息"}>
      {partner.legalRepresentative && (
        <Descriptions.Item label={partner.partnerType === 2 ? "姓名" : "法定代表人"}>{partner.legalRepresentative}</Descriptions.Item>
      )}
      {partner.legalPhone && (
        <Descriptions.Item label={partner.partnerType === 2 ? "手机号码" : "法人电话"}>
          <Typography.Text copyable>{partner.legalPhone}</Typography.Text>
        </Descriptions.Item>
      )}
      {partner.legalIdNumber && (
        <Descriptions.Item label={partner.partnerType === 2 ? "身份证号" : "法人证件号"}>
          <Typography.Text copyable>{partner.legalIdNumber}</Typography.Text>
        </Descriptions.Item>
      )}

      {partner.legalIdFrontPath && (
        <Descriptions.Item label="身份证正面">
          <EnvImage src={partner.legalIdFrontPath} width={120} height={80} />
        </Descriptions.Item>
      )}
      {partner.legalIdBackPath && (
        <Descriptions.Item label="身份证背面">
          <EnvImage src={partner.legalIdBackPath} width={120} height={80} />
        </Descriptions.Item>
      )}
    </Descriptions>
  )

  const contactInfo = (
    <Descriptions size="small" column={2} bordered title="3、联系信息">
      {partner.contactPerson && <Descriptions.Item label="日常联系人">{partner.contactPerson}</Descriptions.Item>}
      {partner.contactPhone && (
        <Descriptions.Item label="联系电话">
          <Typography.Text copyable>{partner.contactPhone}</Typography.Text>
        </Descriptions.Item>
      )}
      {partner.contactEmail && (
        <Descriptions.Item label="联系邮箱">
          <Typography.Link href={`mailto:${partner.contactEmail}`} copyable>
            {partner.contactEmail}
          </Typography.Link>
        </Descriptions.Item>
      )}
    </Descriptions>
  )

  const addressInfo = (
    <Descriptions size="small" column={1} bordered title="4、地址信息">
      {partner.address && (
        <Descriptions.Item label={partner.partnerType === 2 ? "户籍地址" : "注册地址"}>
          {partner.address}
        </Descriptions.Item>
      )}
      {partner.actualAddress && (
        <Descriptions.Item label={partner.partnerType === 2 ? "现居住地址" : "实际经营地址"}>
          {partner.actualAddress}
        </Descriptions.Item>
      )}
    </Descriptions>
  )

  const businessInfo = partner.partnerType === 1 ? (
    <Descriptions size="small" column={2} bordered title="5、营业执照信息">
      {partner.businessLicense && (
        <Descriptions.Item label="营业执照号">
          <Typography.Text copyable>{partner.businessLicense}</Typography.Text>
        </Descriptions.Item>
      )}
      {partner.businessLicensePath && (
        <Descriptions.Item label="营业执照">
          <EnvImage src={partner.businessLicensePath} width={200} height={150} />
        </Descriptions.Item>
      )}
    </Descriptions>
  ) : null

  const bankInfo = (
    <div>
      <Typography.Title level={5}>{partner.partnerType === 2 ? "5、银行账户信息" : "6、银行账户信息"}</Typography.Title>
      {partner.bankInfos && partner.bankInfos.length > 0 ? (
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          {partner.bankInfos.map((bank, index) => (
            <Card
              key={index}
              size="small"
              title={
                <Space>
                  <span>银行卡 {index + 1}</span>
                  {bank.isDefault && <Tag color="green">默认</Tag>}
                </Space>
              }>
              <Descriptions size="small" column={2}>
                <Descriptions.Item label="开户行">{bank.bankName}</Descriptions.Item>
                <Descriptions.Item label="账户名称">{bank.accountName}</Descriptions.Item>
                <Descriptions.Item label="卡号">
                  <Typography.Text copyable>{bank.accountNumber}</Typography.Text>
                </Descriptions.Item>
              </Descriptions>
            </Card>
          ))}
        </Space>
      ) : (
        <Typography.Text type="secondary">暂无银行账户信息</Typography.Text>
      )}
    </div>
  )

  const remarkInfo = partner.remark ? (
    <Descriptions size="small" column={1} bordered title={partner.partnerType === 2 ? "6、备注信息" : "7、备注信息"}>
      <Descriptions.Item label="备注">
        {partner.remark}
      </Descriptions.Item>
    </Descriptions>
  ) : null

  const tabItems = [
    {
      key: 'basic',
      label: '基本信息',
      children: (
        <Flex vertical gap={24}>
          {descriptions}
          {legalInfo}
          {contactInfo}
          {addressInfo}
          {businessInfo}
          {bankInfo}
          {remarkInfo}
        </Flex>
      ),
    },
  ]

  return (
    <Tabs
      defaultActiveKey="basic"
      items={tabItems}
      tabBarStyle={{ marginBottom: 24 }}
    />
  )
}

export default PartnerDetailContent
