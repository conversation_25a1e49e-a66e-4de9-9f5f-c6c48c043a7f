import useIndexStore from '@/store'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, <PERSON>er, Popconfirm, Space } from 'antd'
import React from 'react'
import { IPartnerListItem } from '../../store'
import PartnerDetailContent from './PartnerDetailContent'

interface PartnerDetailProps {
  open: boolean
  partner?: IPartnerListItem
  onClose: () => void
  onEdit: (partner: IPartnerListItem) => void
  onDelete: (partner: IPartnerListItem) => void
}

const PartnerDetail: React.FC<PartnerDetailProps> = ({ open, partner, onClose, onEdit, onDelete }) => {
  const { authorBtn } = useIndexStore()

  return (
    <Drawer
      title={`详情 - ${partner?.partnerName || ''}`}
      placement="right"
      onClose={onClose}
      open={open}
      width={900}
      destroyOnHidden
      styles={{ body: { paddingTop: 0 } }}
      extra={
        partner && (
          <Space.Compact>
            {authorBtn.includes('删除') ? (
              <Popconfirm
                title="警告"
                description={`确定要删除【${partner.partnerName}】吗？`}
                onConfirm={() => onDelete?.(partner)}
                okText="确定删除"
                cancelText="取消">
                <Button type="default" shape="round" className="text-primary" icon={<DeleteOutlined />}>
                  删除
                </Button>
              </Popconfirm>
            ) : null}
            <Button
              type="default"
              shape="round"
              className="text-primary"
              icon={<EditOutlined />}
              onClick={() => onEdit?.(partner)}>
              编辑
            </Button>
          </Space.Compact>
        )
      }>
      <PartnerDetailContent partner={partner} />
    </Drawer>
  )
}

export default PartnerDetail
