import { useDebounceFn } from 'ahooks'
import { Button, Form, Input, Select, Space } from 'antd'
import React from 'react'
import { PARTNER_TYPE_OPTIONS, PARTNER_STATUS_OPTIONS } from '../../../../../consts/partner'

interface PartnerSearchProps {
  loading?: boolean
  form: any
  onReset?: () => void
  onSearch: (pageIndex: number) => void
}

const PartnerSearch: React.FC<PartnerSearchProps> = ({ loading = false, onSearch, form }) => {
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(1), { wait: 500 })

  return (
    <Form form={form} layout="vertical" onValuesChange={onSearchDebounce} colon={false} className="search-form">
      <Space wrap size={24}>
        <Form.Item name="partnerName" label="合作商名称">
          <Input className="w200" placeholder="支持模糊查询" allowClear />
        </Form.Item>
        
        <Form.Item name="shortName" label="简称">
          <Input className="w200" placeholder="支持模糊查询" allowClear />
        </Form.Item>
        
        <Form.Item name="partnerType" label="合作商类型">
          <Select
            className="w200"
            placeholder="选择合作商类型"
            allowClear
            options={PARTNER_TYPE_OPTIONS}
          />
        </Form.Item>

        <Form.Item name="legalRepresentative" label="法定代表人">
          <Input className="w200" placeholder="支持模糊查询" allowClear />
        </Form.Item>

        <Form.Item name="contactPerson" label="联系人">
          <Input className="w200" placeholder="支持模糊查询" allowClear />
        </Form.Item>

        <Form.Item name="status" label="合作状态">
          <Select className="w200" placeholder="默认全选" allowClear options={PARTNER_STATUS_OPTIONS} />
        </Form.Item>
        
        <Form.Item label=" ">
          <Button type="primary" onClick={onSearchDebounce} loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Space>
    </Form>
  )
}

export default PartnerSearch
