import { Card, Empty, Flex, Form, message, Spin, Splitter, Statistic, Tabs } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useMemo, useState } from 'react'
import useRoomStore, {
  IGroupCommunityDto,
  IParaGroupRoomDto,
  IPrProductionPersonRoom,
  IRoomDto,
  ISavePersonRoomDto,
} from '../store'
import AssignPersonModal from './components/AssignPersonModal'
import ChangeRoomModal from './components/ChangeRoomModal'
import CheckinDetailsDrawer from './components/CheckinDetailsModal'
import CommunityRoomList from './components/CommunityRoomList'
import GroupSearch from './components/Search'
import StatusLegend from './components/StatusLegend'

// 宿舍群组管理页面
const RoomGroupManagement: React.FC = () => {
  const [loading, setLoading] = useState(false)
  const [searchForm] = Form.useForm()
  const [assignModalOpen, setAssignModalOpen] = useState(false)
  const [detailsModalOpen, setDetailsModalOpen] = useState(false)
  const [changeRoomModalOpen, setChangeRoomModalOpen] = useState(false)
  const [selectedRoom, setSelectedRoom] = useState<IRoomDto | null>(null)
  const [selectedCommunity, setSelectedCommunity] = useState<IGroupCommunityDto | null>(null)
  const [changeRoomPerson, setChangeRoomPerson] = useState<IPrProductionPersonRoom | null>(null)
  const [changeRoomCheckinDates, setChangeRoomCheckinDates] = useState<string[]>([])

  const { fetchGroupList, savePersonRoom, deletePersonRoom, changePersonRoom, groupList, roomStatistics } =
    useRoomStore()

  // 获取宿舍群组列表
  const fetchData = async (params?: IParaGroupRoomDto) => {
    setLoading(true)
    try {
      await fetchGroupList(params || {})
    } catch (error) {
      console.error('获取宿舍群组列表失败:', error)
      message.error('获取宿舍群组列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始化加载数据
  useEffect(() => {
    handleSearch()
  }, [])

  // 搜索处理
  const handleSearch = () => {
    const values = searchForm.getFieldsValue()
    const params: IParaGroupRoomDto = {
      communityName: values.communityName?.trim(),
      startDate: values?.dateRange?.[0]?.format('YYYY-MM-DD') || null,
      endDate: values?.dateRange?.[1]?.format('YYYY-MM-DD') || null,
    }

    fetchData(params)
  }

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields()
    handleSearch()
  }

  // 打开分配人员弹窗
  const handleAssignPerson = (room: IRoomDto, community: IGroupCommunityDto) => {
    setSelectedRoom(room)
    setSelectedCommunity(community)
    setAssignModalOpen(true)
  }

  // 打开入住详情弹窗
  const handleViewDetails = (room: IRoomDto, community: IGroupCommunityDto) => {
    setSelectedRoom(room)
    setSelectedCommunity(community)
    setDetailsModalOpen(true)
  }

  // 分配人员提交
  const handleAssignSubmit = async (values: ISavePersonRoomDto) => {
    try {
      const success = await savePersonRoom(values)

      if (success) {
        message.success('人员分配成功')
        setAssignModalOpen(false)
        handleSearch()
      }
    } catch (error) {
      console.error('分配人员失败:', error)
      message.error('分配人员失败')
    }
  }

  // 关闭分配人员弹窗并刷新
  const handleAssignModalClose = () => {
    setAssignModalOpen(false)
    handleSearch() // 弹窗关闭时刷新数据
  }

  // 删除人员
  const handleDeletePerson = async (person: IPrProductionPersonRoom, checkinDates: string[]) => {
    try {
      // 根据checkinDates计算startDate和endDate，一次性删除
      const dates = checkinDates.map(date => dayjs(date))
      const sortedDates = dates.sort((a, b) => a.valueOf() - b.valueOf())
      const startDate = sortedDates[0].format('YYYY-MM-DD') // 最早的入住日期
      const endDate = sortedDates[sortedDates.length - 1].format('YYYY-MM-DD') // 最晚的入住日期

      const params: ISavePersonRoomDto = {
        productionId: person.productionId,
        personId: person.personId,
        roomId: person.roomId,
        startDate,
        endDate,
      }

      const success = await deletePersonRoom(params)

      if (success) {
        message.success(`成功删除 ${person.personName} 的所有入住记录`)
        setDetailsModalOpen(false)
        handleSearch() // 重新加载数据
      }
    } catch (error) {
      console.error('删除人员失败:', error)
      message.error('删除人员失败')
    }
  }

  // 关闭入住详情弹窗并刷新
  const handleDetailsModalClose = () => {
    setDetailsModalOpen(false)
    handleSearch() // 弹窗关闭时刷新数据
  }

  // 处理换房
  const handleChangeRoom = (person: IPrProductionPersonRoom, checkinDates: string[]) => {
    setChangeRoomPerson(person)
    setChangeRoomCheckinDates(checkinDates)
    setChangeRoomModalOpen(true)
  }

  // 换房成功回调
  const handleChangeRoomSuccess = () => {
    setChangeRoomModalOpen(false)
    setChangeRoomPerson(null)
    setChangeRoomCheckinDates([])
    setDetailsModalOpen(false)
    handleSearch() // 重新加载数据
  }

  // 关闭换房弹窗
  const handleChangeRoomModalClose = () => {
    setChangeRoomModalOpen(false)
    setChangeRoomPerson(null)
    setChangeRoomCheckinDates([])
  }

  // 获取表单中的状态筛选值
  const statusFilterValue = Form.useWatch('statusFilter', searchForm) || 'all'

  // 获取搜索时间范围
  const searchDateRange = Form.useWatch('dateRange', searchForm)

  // 根据状态筛选房间
  const filteredGroupList = useMemo(() => {
    // 如果是全部或未设置筛选条件，返回原始数据
    if (statusFilterValue === 'all') {
      return groupList
    }

    // 复制一份数据进行过滤
    return groupList
      .map(community => {
        const filteredRooms = community.roomInfos?.filter(room => {
          const personCount = room.personCount || 0
          const maxCapacity = room.roomType // 枚举值直接对应房间容量

          switch (statusFilterValue) {
            case 'available': // 空闲
              return personCount === 0 && room.status !== 0
            case 'partial': // 部分入住
              return personCount > 0 && personCount < maxCapacity
            case 'full': // 已满员
              return personCount >= maxCapacity
            case 'disabled': // 已禁用
              return room.status === 0
            default:
              return true
          }
        })

        // 返回过滤后的社区数据
        return {
          ...community,
          roomInfos: filteredRooms,
        }
      })
      .filter(community => community.roomInfos && community.roomInfos.length > 0) // 过滤掉没有房间的社区
  }, [groupList, statusFilterValue])

  return (
    <Flex vertical gap={24}>
      <Flex justify="space-between" align="center">
        <GroupSearch form={searchForm} onSearch={handleSearch} onReset={handleReset} />
      </Flex>

      {/* 统计区域 */}
      {groupList.length > 0 && (
        <Card size="small">
          <Splitter>
            <Splitter.Panel className="text-center">
              <Statistic title="房间数" suffix={<span className="fs-lg">间</span>} value={roomStatistics.totalRooms} />
            </Splitter.Panel>
            <Splitter.Panel className="text-center">
              <Statistic
                title="房间数 * 床位 * 时间段 = 床位数"
                suffix={<span className="fs-lg">个</span>}
                value={roomStatistics.totalCount}
              />
            </Splitter.Panel>
            <Splitter.Panel className="text-center">
              <Statistic
                title="入住人次"
                suffix={<span className="fs-lg">人次</span>}
                value={roomStatistics.useCount}
              />
            </Splitter.Panel>
            <Splitter.Panel className="text-center">
              <Statistic
                title="入住率"
                suffix={<span className="fs-lg">%</span>}
                precision={0}
                value={(roomStatistics.useCount / roomStatistics.totalCount) * 100}
              />
            </Splitter.Panel>
          </Splitter>
        </Card>
      )}

      {/* 列表区域 */}
      <Spin spinning={loading}>
        {filteredGroupList.length > 0 ? (
          <Tabs
            indicator={{ size: 42 }}
            tabBarExtraContent={<StatusLegend />}
            items={filteredGroupList.map(item => ({
              key: `${item.id}`,
              label: item.communityName,
              children: (
                <CommunityRoomList
                  key={item.id}
                  community={item}
                  searchDateRange={searchDateRange}
                  onAssignPerson={handleAssignPerson}
                  onViewDetails={handleViewDetails}
                />
              ),
            }))}
          />
        ) : (
          <Empty />
        )}
      </Spin>

      {/* 分配人员弹窗 */}
      {assignModalOpen ? (
        <AssignPersonModal
          open={assignModalOpen}
          roomInfo={selectedRoom}
          communityInfo={selectedCommunity}
          onCancel={handleAssignModalClose}
          onSubmit={handleAssignSubmit}
        />
      ) : null}

      {/* 入住详情侧边栏 */}
      {detailsModalOpen ? (
        <CheckinDetailsDrawer
          open={detailsModalOpen}
          room={selectedRoom}
          community={selectedCommunity}
          onCancel={handleDetailsModalClose}
          onDeletePerson={handleDeletePerson}
          onChangeRoom={handleChangeRoom}
          onCheckoutSuccess={() => {
            setDetailsModalOpen(false)
            handleSearch() // 重新加载数据
          }}
        />
      ) : null}

      {/* 换房弹窗 */}
      {changeRoomModalOpen ? (
        <ChangeRoomModal
          open={changeRoomModalOpen}
          person={changeRoomPerson}
          checkinDates={changeRoomCheckinDates}
          groupList={groupList}
          onCancel={handleChangeRoomModalClose}
          onSuccess={handleChangeRoomSuccess}
        />
      ) : null}
    </Flex>
  )
}

export default RoomGroupManagement
