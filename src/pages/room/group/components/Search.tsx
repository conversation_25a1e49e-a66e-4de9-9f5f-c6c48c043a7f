import DateRangeCascader from '@/components/DateRangeCascader'
import useCommunityStore from '@/pages/system/community/store'
import { useDebounceFn } from 'ahooks'
import { DatePicker, Form, Select } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect } from 'react'

const { RangePicker } = DatePicker

// 房间状态筛选选项
const ROOM_STATUS_FILTER_OPTIONS = [
  { label: '全部', value: 'all' },
  { label: '空闲', value: 'available' },
  { label: '部分入住', value: 'partial' },
  { label: '满员', value: 'full' },
  { label: '禁用', value: 'disabled' },
]

interface IGroupSearchProps {
  form: any
  loading?: boolean
  onSearch: () => void
  onReset: () => void
}

const GroupSearch: React.FC<IGroupSearchProps> = ({ form, loading = false, onSearch, onReset }) => {
  const { allCommunityOptions, getAllCommunityNames } = useCommunityStore()
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(), { wait: 500 })

  useEffect(() => {
    getAllCommunityNames()
  }, [])

  return (
    <Form form={form} layout="inline" colon={false} onValuesChange={onSearchDebounce}>
      <Form.Item name="dateRange" initialValue={[dayjs(), dayjs().add(1, 'day')]}>
        <DateRangeCascader placeholder={['开始时间', '结束时间']} allowClear={false} />
      </Form.Item>
      <Form.Item name="statusFilter" label="状态" initialValue="all">
        <Select className="w200" placeholder="选择状态" options={ROOM_STATUS_FILTER_OPTIONS} />
      </Form.Item>
    </Form>
  )
}

export default GroupSearch
