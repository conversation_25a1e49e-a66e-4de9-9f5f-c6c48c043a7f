import { ACTOR_ROLE_TYPE_CONFIG, ROLE_TYPE_CONFIG } from '@/consts'
import { SearchOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { useDebounceFn } from 'ahooks'
import { Card, Divider, Empty, Flex, Input, Space, Typography } from 'antd'
import React, { useEffect, useState } from 'react'
import { useDrag } from 'react-dnd'
import { IPrProductionAllPerson } from '../../../production/project/list/store'

const { Text } = Typography

interface IProjectPersonListProps {
  data: IPrProductionAllPerson[]
  productionId?: number
}

interface IPersonCardProps {
  person: IPrProductionAllPerson
  productionId: number
}

const PersonCard: React.FC<IPersonCardProps> = ({ person, productionId }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'person',
    item: { person, productionId },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  })

  // 获取角色类型配置
  const getRoleTypeConfig = () => {
    if (person.parentType === 2) {
      // 演员
      return ACTOR_ROLE_TYPE_CONFIG[person.roleType || 0]
    } else {
      // 人员
      return ROLE_TYPE_CONFIG[person.roleType || 0]
    }
  }

  const roleTypeConfig = getRoleTypeConfig()

  return (
    <div ref={drag} className={`hover-move cursor-move ${isDragging ? 'border-primary' : ''}`}>
      <Card size="small">
        <Flex justify="space-between">
          <Space size={0} split={<Divider type="vertical" />}>
            <Text strong>{person.personName}</Text>
            <Text>{roleTypeConfig?.label}</Text>
          </Space>
          {person.personRoom && (
            <Space size={0} split={<Divider type="vertical" />}>
              <Dict title="入住" value={person.personRoom.fullRoomNumber} />
              <Text>
                {person.personRoomDates && person.personRoomDates.length > 0
                  ? `${person.personRoomDates.length}天`
                  : null}
              </Text>
            </Space>
          )}
        </Flex>
      </Card>
    </div>
  )
}

const ProjectPersonList: React.FC<IProjectPersonListProps> = ({ data, productionId }) => {
  const [persons, setPersons] = useState(data)
  const [search, setSearch] = useState('')

  // 搜索
  const handleSearch = e => setSearch(e.target.value)
  const { run: debounceSearch } = useDebounceFn(handleSearch, { wait: 500 })

  useEffect(() => {
    const persons = data.filter(person => person.personName?.includes(search))
    setPersons(persons)
  }, [data, search])

  if (!productionId) {
    return <Empty description="请先选择项目" />
  }

  return data.length ? (
    <Flex vertical>
      <ListHeader title="项目组人员" total={data.length} unitText="个">
        <Input prefix={<SearchOutlined />} onChange={debounceSearch} placeholder="输入人员关键字搜索" />
      </ListHeader>
      <Flex vertical gap={12}>
        {persons.map(person => (
          <PersonCard key={person.id} person={person} productionId={productionId} />
        ))}
      </Flex>
    </Flex>
  ) : (
    <Empty />
  )
}

export default ProjectPersonList
