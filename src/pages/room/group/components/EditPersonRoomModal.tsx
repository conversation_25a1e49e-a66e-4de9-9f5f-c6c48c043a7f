import { DatePicker, Form, message, Modal } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import useRoomStore, { IPrProductionPersonRoom, ISavePersonRoomDto } from '../../store'

const { RangePicker } = DatePicker

interface IEditPersonRoomModalProps {
    open: boolean
    person: IPrProductionPersonRoom
    checkinDates: string[]
    onCancel: () => void
    onSuccess?: () => void
}

const EditPersonRoomModal: React.FC<IEditPersonRoomModalProps> = ({
    open,
    person,
    checkinDates,
    onCancel,
    onSuccess,
}) => {
    const [form] = Form.useForm()
    const [saving, setSaving] = useState(false)
    const { savePersonRoom } = useRoomStore()

    useEffect(() => {
        if (open && checkinDates && checkinDates.length > 0) {
            form.resetFields()
            
            // 根据checkinDates计算当前的startDate和endDate
            const dates = checkinDates.map(date => dayjs(date))
            const sortedDates = dates.sort((a, b) => a.valueOf() - b.valueOf())
            const startDate = sortedDates[0]
            const endDate = sortedDates[sortedDates.length - 1]

            form.setFieldsValue({
                dateRange: [startDate, endDate]
            })
        }
    }, [open, checkinDates, form])

    const handleSubmit = async () => {
        if (!person.personId || !person.roomId) return

        try {
            setSaving(true)
            const values = await form.validateFields()
            const { dateRange } = values

            if (!dateRange || dateRange.length !== 2) {
                message.error('请选择入住时间范围')
                return
            }

            const submitData: ISavePersonRoomDto = {
                productionId: person.productionId,
                personId: person.personId,
                roomId: person.roomId,
                startDate: dateRange[0].format('YYYY-MM-DD'),
                endDate: dateRange[1].format('YYYY-MM-DD'),
            }

            const success = await savePersonRoom(submitData)

            if (success) {
                message.success(`成功更新 ${person.personName} 的入住信息`)
                form.resetFields()
                onCancel()
                onSuccess?.()
            }
        } catch (error) {
            console.error('更新入住信息失败:', error)
            message.error('更新入住信息失败')
        } finally {
            setSaving(false)
        }
    }

    const handleCancel = () => {
        form.resetFields()
        onCancel()
    }

    return (
        <Modal
            title={`编辑 ${person.personName} 的入住信息`}
            open={open}
            onCancel={handleCancel}
            onOk={handleSubmit}
            width={600}
            destroyOnHidden
            confirmLoading={saving}
        >
            <Form form={form} layout="vertical" className="mt-16">
                <Form.Item 
                    name="dateRange" 
                    label="入住时间" 
                    rules={[{ required: true, message: '请选择入住时间范围' }]}
                >
                    <RangePicker
                        showTime={false}
                        format="YYYY-MM-DD"
                        placeholder={['入住开始时间', '入住结束时间']}
                        className="w-full"
                    />
                </Form.Item>
            </Form>
        </Modal>
    )
}

export default EditPersonRoomModal
