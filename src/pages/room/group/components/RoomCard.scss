@use 'sass:color';
@use '/src/assets/styles/variables' as *;

.checkinDetails {
  margin-top: 8px;

  .title {
    font-size: 12px;
    font-weight: 500;
    color: #666;
  }

  .gridContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 2px;
    max-width: 100%;
  }

  .gridItem {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 20px;
    height: 20px;
    font-size: 10px;
    font-weight: 500;
    border-radius: 2px;
    color: #333;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 2px 4px rgb(0 0 0 / 10%);
    }

    // 空闲状态
    &.statusEmpty {
      color: #666;
      background-color: #f0f0f0;
    }

    // 部分入住状态
    &.statusPartial {
      color: #fff;
      background-color: $color-success;
    }

    // 已满员状态
    &.statusFull {
      color: #fff;
      background-color: $color-primary;
    }
  }
}

// 响应式调整
@media (width <= 768px) {
  .checkinDetails {
    .gridItem {
      width: 18px;
      height: 18px;
      font-size: 9px;
    }
  }
}
