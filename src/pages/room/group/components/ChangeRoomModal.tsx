import { DatePicker, Form, message, Modal, Select } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useMemo, useState } from 'react'
import useRoomStore, { IChangePersonRoomDto, IGroupCommunityDto, IPrProductionPersonRoom, IRoomDto } from '../../store'

interface IChangeRoomModalProps {
  open: boolean
  person: IPrProductionPersonRoom | null
  checkinDates: string[]
  groupList: IGroupCommunityDto[]
  onCancel: () => void
  onSuccess: () => void
}

const ChangeRoomModal: React.FC<IChangeRoomModalProps> = ({
  open,
  person,
  checkinDates,
  groupList,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [selectedRoomId, setSelectedRoomId] = useState<number | undefined>()
  const [selectedRoom, setSelectedRoom] = useState<IRoomDto | null>(null)

  const { changePersonRoom } = useRoomStore()

  // 获取所有可选房间
  const availableRooms = useMemo(() => {
    const rooms: Array<IRoomDto & { communityName?: string }> = []

    groupList.forEach(community => {
      if (community.roomInfos) {
        community.roomInfos.forEach(room => {
          // 排除当前房间
          if (room.roomId !== person?.roomId) {
            rooms.push({
              ...room,
              communityName: community.communityName
            })
          }
        })
      }
    })

    return rooms
  }, [groupList, person?.roomId])

  // 获取选中房间的人员列表
  const roomPersons = useMemo(() => {
    if (!selectedRoom?.personRoom) return []

    // 按人员分组，去重
    const personGroups: Record<number, IPrProductionPersonRoom> = {}

    selectedRoom.personRoom.forEach(personRecord => {
      if (personRecord.personId && !personGroups[personRecord.personId]) {
        personGroups[personRecord.personId] = personRecord
      }
    })

    return Object.values(personGroups)
  }, [selectedRoom])

  // 处理房间选择
  const handleRoomChange = (roomId: number) => {
    setSelectedRoomId(roomId)
    const room = availableRooms.find(r => r.roomId === roomId)
    setSelectedRoom(room || null)

    // 清空人员选择
    form.setFieldValue('changePersonId', undefined)
  }

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    if (!person) return

    setLoading(true)

    try {
      const params: IChangePersonRoomDto = {
        personId: person.personId,
        roomId: person.roomId,
        changePersonId: values.changePersonId || 0, // 如果没有选择人员，传0表示不换人
        changeRoomId: values.changeRoomId,
        startDate: values.startDate.format('YYYY-MM-DD')
      }

      const success = await changePersonRoom(params)

      if (success) {
        message.success('换房成功')
        form.resetFields()
        onSuccess()
      } else {
        message.error('换房失败')
      }
    } catch (error) {
      message.error('操作失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  // 重置表单
  useEffect(() => {
    if (open && person) {
      form.resetFields()
      setSelectedRoomId(undefined)
      setSelectedRoom(null)
    }
  }, [open, person, form])

  // 生成可选日期范围
  const availableDates = useMemo(() => {
    return checkinDates.map(date => dayjs(date))
  }, [checkinDates])

  // 禁用不在范围内的日期
  const disabledDate = (current: dayjs.Dayjs) => {
    return !availableDates.some(date => date.isSame(current, 'day'))
  }

  return (
    <Modal
      title={person?.personName ? `${person.personName} - 换房` : "换房"}
      open={open}
      onCancel={onCancel}
      onOk={() => form.submit()}
      confirmLoading={loading}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        preserve={false}
      >
      
        <Form.Item label="入住日期范围">
          <span>
            {checkinDates.length <= 3
              ? checkinDates.join(', ')
              : `${checkinDates[0]} ~ ${checkinDates[checkinDates.length - 1]}`}
          </span>
        </Form.Item>

        <Form.Item
          label="更换起始时间"
          name="startDate"
          rules={[{ required: true, message: '请选择更换起始时间' }]}
        >
          <DatePicker
            placeholder="请选择更换起始时间"
            style={{ width: '100%' }}
            format="YYYY-MM-DD"
            disabledDate={disabledDate}
            showToday={false}
          />
        </Form.Item>

        <Form.Item
          label="目标房间"
          name="changeRoomId"
          rules={[{ required: true, message: '请选择目标房间' }]}
        >
          <Select
            placeholder="请选择目标房间"
            onSelect={handleRoomChange}
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={availableRooms.map(room => ({
              value: room.roomId,
              label: `${room.communityName} - ${room.fullRoomNumber || `${room.buildingNumber}-${room.roomNumber}`}`,
            }))}
          />
        </Form.Item>

        {roomPersons.length > 0 && (
          <Form.Item
            label="目标房间现有人员"
            name="changePersonId"
             rules={[{ required: true, message: '请选择人员' }]}
          >
            <Select
              placeholder="选择要互换的人员"
              allowClear
              options={roomPersons.map(p => ({
                value: p.personId,
                label: `${p.personName}`,
              }))}
            />
          </Form.Item>
        )}
      </Form>
    </Modal >
  )
}

export default ChangeRoomModal
