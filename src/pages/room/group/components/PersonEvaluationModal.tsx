import useProjectListStore, { IPrPersonEvaluation } from '@/pages/production/project/list/store'
import { Button, Form, Input, Modal, Rate, Space, message } from 'antd'
import React, { useEffect } from 'react'

const { TextArea } = Input

interface IPersonEvaluationModalProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  productionId: number
  personId: number
  personName: string
}

const PersonEvaluationModal: React.FC<IPersonEvaluationModalProps> = ({
  open,
  onCancel,
  onSuccess,
  productionId,
  personId,
  personName,
}) => {
  const [form] = Form.useForm()
  const [saving, setSaving] = React.useState(false)
  const { savePersonEvaluation } = useProjectListStore()

  useEffect(() => {
    if (open) {
      form?.resetFields()
    }
  }, [open])

  const handleSubmit = () => {
    form.submit()
  }

  const handleFinish = async (values: any) => {
    setSaving(true)
    try {
      const evaluationData: IPrPersonEvaluation = {
        productionId,
        personId,
        score: values.score,
        comment: values.comment,
        evaluationType: 1,
      }

      const success = await savePersonEvaluation(evaluationData)

      if (success) {
        message.success('评价保存成功')
        onSuccess()
      }
    } catch (error) {
      console.error('保存评价失败:', error)
      message.error('操作失败，请重试')
    } finally {
      setSaving(false)
    }
  }

  const handleClose = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={`添加评价 - ${personName}`}
      open={open}
      onCancel={handleClose}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={handleClose}>取消</Button>
            <Button type="primary" onClick={handleSubmit} loading={saving}>
              {'保存评价'}
            </Button>
          </Space>
        </div>
      }
      width={600}
      maskClosable={false}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFinish}
        initialValues={{
          score: 5,
          comment: '',
        }}>
        <Form.Item name="score" label="评分" rules={[{ required: true, message: '请给出评分' }]}>
          <Rate allowHalf={false} count={10} style={{ fontSize: 20 }} />
        </Form.Item>

        <Form.Item
          name="comment"
          label="评价内容"
          rules={[
            { required: true, message: '请填写评价内容' },
            { min: 5, message: '评价内容至少5个字符' },
            { max: 500, message: '评价内容不能超过500个字符' },
          ]}>
          <TextArea rows={4} placeholder="请详细描述对该人员住宿方面的评价..." showCount maxLength={500} />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default PersonEvaluationModal
