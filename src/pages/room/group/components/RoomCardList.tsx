import { Card, Flex, Tag, Typography } from 'antd'
import React from 'react'
import { useDrop } from 'react-dnd'
import { ROOM_LEVEL_CONFIG, ROOM_TYPE_CONFIG } from '../../../../consts'
import { IRoomListItem } from '../../store'
import { IPrProductionAllPerson } from '../../../production/project/list/store'
import styles from './RoomCardList.scss'

const { Text } = Typography

interface IRoomCardListProps {
  rooms: IRoomListItem[]
  onPersonDrop: (room: IRoomListItem, person: IPrProductionAllPerson, productionId: number) => void
}

interface IRoomCardProps {
  room: IRoomListItem
  onPersonDrop: (room: IRoomListItem, person: IPrProductionAllPerson, productionId: number) => void
}

const RoomCard: React.FC<IRoomCardProps> = ({ room, onPersonDrop }) => {
  const [{ isOver }, drop] = useDrop({
    accept: 'person',
    drop: (item: { person: IPrProductionAllPerson; productionId: number }) => {
      onPersonDrop(room, item.person, item.productionId)
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  })

  const roomTypeConfig = ROOM_TYPE_CONFIG[room.roomType]
  const roomLevelConfig = ROOM_LEVEL_CONFIG[room.roomLevel]

  return (
    <div
      ref={drop}
      className={`${styles.roomCard} ${isOver ? styles.roomCardHover : ''}`}
    >
      <Card
        size="small"
        title={
          <Flex justify="space-between" align="center">
            <Text strong>{room.fullRoomNumber}</Text>
            <Flex gap={4}>
              <Tag color={roomTypeConfig?.color}>{roomTypeConfig?.label}</Tag>
              <Tag color={roomLevelConfig?.color}>{roomLevelConfig?.label}</Tag>
            </Flex>
          </Flex>
        }
        style={{ 
          height: '100%',
          border: isOver ? '2px dashed #1890ff' : '1px solid #d9d9d9',
          backgroundColor: isOver ? '#f0f8ff' : '#fff'
        }}
      >
        <Flex vertical gap={8}>
          <Text type="secondary">小区：{room.prCommunity?.communityName}</Text>
          <Text type="secondary">地址：{room.prCommunity?.communityArea}</Text>
          {room.remark && (
            <Text type="secondary">备注：{room.remark}</Text>
          )}
        </Flex>
      </Card>
    </div>
  )
}

const RoomCardList: React.FC<IRoomCardListProps> = ({ rooms, onPersonDrop }) => {
  return (
    <div className={styles.roomCardList}>
      {rooms.map(room => (
        <RoomCard
          key={room.id}
          room={room}
          onPersonDrop={onPersonDrop}
        />
      ))}
    </div>
  )
}

export default RoomCardList
