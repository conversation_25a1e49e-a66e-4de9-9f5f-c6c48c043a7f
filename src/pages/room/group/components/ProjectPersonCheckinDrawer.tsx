import ProjectSelector from '@/pages/production/project/components/ProjectSelector'
import { Drawer, Flex, Form, Space, Splitter, Typography, message } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useMemo, useState } from 'react'
import useProjectStore, { IPrProductionAllPerson, IProductionListItem } from '../../../production/project/list/store'
import useRoomStore, { IGroupCommunityDto, IParaGroupRoomDto, IRoomDto } from '../../store'
import CommunityRoomSection from './CommunityRoomSection'
import CommunitySearch from './CommunitySearch'
import styles from './ProjectPersonCheckinDrawer.scss'
import ProjectPersonList from './ProjectPersonList'
import RoomAllocationModal from './RoomAllocationModal'

interface IProjectPersonCheckinDrawerProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
}

interface IAllocationData {
  room: IRoomDto
  person: IPrProductionAll<PERSON>erson
  productionId: number
  community: IGroupCommunityDto
  project: IProductionListItem | null
}

const ProjectPersonCheckinDrawer: React.FC<IProjectPersonCheckinDrawerProps> = ({ open, onCancel, onSuccess }) => {
  const [searchForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [groupList, setGroupList] = useState<IGroupCommunityDto[]>([])
  const [persons, setPersons] = useState<IPrProductionAllPerson[]>([])

  const [selectedProjectId, setSelectedProjectId] = useState<number | undefined>()
  const [allocationModalOpen, setAllocationModalOpen] = useState(false)
  const [allocationData, setAllocationData] = useState<IAllocationData | null>(null)
  const [selectedProject, setSelectedProject] = useState<IProductionListItem | null>(null)

  const { fetchGroupList } = useRoomStore()
  const { getAllPersonList } = useProjectStore()

  // 计算项目时间范围
  const projectDateRange = useMemo((): [any, any] | null => {
    if (selectedProject?.startDate && selectedProject?.endDate) {
      const startDate = dayjs(selectedProject.startDate)
      const endDate = dayjs(selectedProject.endDate)

      if (startDate.isValid() && endDate.isValid()) {
        return [startDate, endDate] as [any, any]
      }
    }
    return null
  }, [selectedProject])

  // 搜索房间
  const handleSearch = async () => {
    setLoading(true)
    try {
      const values = searchForm.getFieldsValue()
      const params: IParaGroupRoomDto = {
        startDate: values.dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: values.dateRange?.[1]?.format('YYYY-MM-DD'),
      }

      const result = await fetchGroupList(params)
      if (result) {
        setGroupList(result)
      } else {
        setGroupList([])
      }
    } catch (error) {
      console.error('获取房间列表失败:', error)
      message.error('获取房间列表失败')
      setGroupList([])
    } finally {
      setLoading(false)
    }
  }

  // 获取项目人员列表
  const fetchPersonList = async (productionId: number) => {
    if (!productionId) return

    setLoading(true)
    try {
      const result = await getAllPersonList(productionId)
      if (result) {
        setPersons(result)
      } else {
        setPersons([])
      }
    } catch (error) {
      console.error('获取项目人员列表失败:', error)
      message.error('获取项目人员列表失败')
      setPersons([])
    } finally {
      setLoading(false)
    }
  }

  // 处理项目选择
  const handleProjectSelect = async (projectId: number, option: any) => {
    if (projectId) {
      setSelectedProjectId(projectId)
      setSelectedProject(option)

      searchForm.setFieldsValue({
        dateRange: [dayjs(option.startDate).add(-1, 'day'), dayjs(option.endDate).add(1, 'day')],
      })

      await fetchPersonList(projectId)
      await handleSearch()
    }
  }

  // 处理人员拖拽到房间
  const handlePersonDrop = (
    room: IRoomDto,
    person: IPrProductionAllPerson,
    productionId: number,
    community: IGroupCommunityDto
  ) => {
    setAllocationData({ room, person, productionId, community, project: selectedProject })
    setAllocationModalOpen(true)
  }

  // 处理分配确认
  const handleAllocationConfirm = () => {
    setAllocationModalOpen(false)
    setAllocationData(null)
    message.success('房间分配成功')

    handleSearch()
    selectedProjectId && fetchPersonList(selectedProjectId)
  }

  // 处理分配取消
  const handleAllocationCancel = () => {
    setAllocationModalOpen(false)
    setAllocationData(null)
  }

  useEffect(() => {
    handleSearch()
  }, [])

  return (
    <>
      <Drawer
        title={
          <Flex justify="space-between" align="center">
            <Space align="center">
              项目人员入住
              <Typography.Text type="secondary" className="fw-normal">
                (右边选择项目后，将项目组人员拖动到房间)
              </Typography.Text>
            </Space>
            <Space size={24}>
              <ProjectSelector
                placeholder="请选择项目"
                autoFocus
                className="w300 fw-normal"
                defaultOpen
                popupMatchSelectWidth
                value={selectedProjectId}
                onChange={projectId => setSelectedProjectId(projectId)}
                onSelect={handleProjectSelect}
                allowClear={false}
              />
              <CommunitySearch form={searchForm} onSearch={handleSearch} />
            </Space>
          </Flex>
        }
        open={open}
        onClose={onCancel}
        width="90%"
        className={styles.container}
        destroyOnHidden>
        <Splitter className="full-v">
          <Splitter.Panel defaultSize="60%" min="50%" max="70%">
            <CommunityRoomSection
              data={groupList}
              onPersonDrop={handlePersonDrop}
              projectDateRange={projectDateRange}
            />
          </Splitter.Panel>
          <Splitter.Panel className="full-v">
            <ProjectPersonList data={persons} productionId={selectedProjectId} />
          </Splitter.Panel>
        </Splitter>
      </Drawer>

      {/* 房间分配确认弹窗 */}
      {allocationModalOpen ? (
        <RoomAllocationModal
          open={allocationModalOpen}
          room={allocationData?.room || null}
          person={allocationData?.person || null}
          productionId={allocationData?.productionId || 0}
          project={allocationData?.project || null}
          onConfirm={handleAllocationConfirm}
          onCancel={handleAllocationCancel}
        />
      ) : null}
    </>
  )
}

export default ProjectPersonCheckinDrawer
