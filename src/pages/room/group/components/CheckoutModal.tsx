import { DatePicker, Form, message, Modal } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import useRoomStore, { IPrProductionPersonRoom, ISavePersonRoomDto } from '../../store'

const { RangePicker } = DatePicker

interface ICheckoutModalProps {
  open: boolean
  person: IPrProductionPersonRoom | null
  checkinDates?: string[]
  onCancel: () => void
  onSuccess: () => void
}

const CheckoutModal: React.FC<ICheckoutModalProps> = ({
  open,
  person,
  checkinDates,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm()
  const [saving, setSaving] = useState(false)
  const { deletePersonRoom } = useRoomStore()

  useEffect(() => {
    if (open && person) {
      form.resetFields()

      // 如果有入住日期，使用入住日期范围作为默认退房时间
      if (checkinDates && checkinDates.length > 0) {
        const startDate = dayjs(checkinDates[0])
        const endDate = dayjs(checkinDates[checkinDates.length - 1])
        form.setFieldsValue({
          dateRange: [startDate, endDate],
        })
      } else {
        // 默认设置今天作为退房时间
        const today = dayjs()
        form.setFieldsValue({
          dateRange: [today, today],
        })
      }
    }
  }, [open, person, checkinDates])

  const handleSubmit = async () => {
    if (!person) return

    try {
      setSaving(true)
      const values = await form.validateFields()
      const { dateRange } = values

      if (!dateRange || dateRange.length !== 2) {
        message.error('请选择退房时间范围')
        return
      }

      const submitData: ISavePersonRoomDto = {
        productionId: person.productionId,
        personId: person.personId,
        roomId: person.roomId,
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      }

      const success = await deletePersonRoom(submitData)

      if (success) {
        message.success(`${person.personName} 退房成功`)
        form.resetFields()
        onCancel()
        onSuccess()
      }
    } catch (error) {
      console.error('退房失败:', error)
      message.error('退房失败')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={`退房 - ${person?.personName || ''}`}
      open={open}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={saving}
      okText="确认退房"
      cancelText="取消"
      width={500}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="dateRange"
          label="退房时间段"
          rules={[{ required: true, message: '请选择退房时间段' }]}
        >
          <RangePicker
            showTime={false}
            format="YYYY-MM-DD"
            placeholder={['退房开始时间', '退房结束时间']}
            className="w-full"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default CheckoutModal
