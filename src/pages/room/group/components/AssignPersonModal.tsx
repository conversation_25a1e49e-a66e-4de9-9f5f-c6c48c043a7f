import ProjectSelector from '@/pages/production/project/components/ProjectSelector'
import useProjectStore from '@/pages/production/project/list/store'
import filterMatch from '@/utils/filterMatch'
import { DatePicker, Form, message, Modal, Select } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { IGroupCommunityDto, IRoomDto, ISavePersonRoomDto } from '../../store'

const { RangePicker } = DatePicker

interface IAssignPersonModalProps {
  open: boolean
  roomInfo?: IRoomDto | null
  communityInfo?: IGroupCommunityDto | null
  onCancel: () => void
  onSubmit: (values: ISavePersonRoomDto) => void
}

const AssignPersonModal: React.FC<IAssignPersonModalProps> = ({ open, roomInfo, onCancel, onSubmit }) => {
  const [form] = Form.useForm()
  const formProductionId = Form.useWatch('productionId', form)
  const { getAllPersonList } = useProjectStore()
  const [opts, setOpts] = useState<Array<any>>([])

  const getOpts = async () => {
    const res = await getAllPersonList(formProductionId)

    setOpts(res || [])
  }

  useEffect(() => {
    if (formProductionId) {
      getOpts()
    } else {
      setOpts([])
    }
  }, [formProductionId])
  useEffect(() => {
    if (open && roomInfo) {
      form.resetFields()
      const today = dayjs()
      const nextMonth = today.add(1, 'day')

      form.setFieldsValue({
        roomId: roomInfo.roomId,
        dateRange: [today, nextMonth],
      })
    }
  }, [open, roomInfo, form])

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      const { personId, productionId, dateRange } = values

      if (!dateRange || dateRange.length !== 2) {
        message.error('请选择入住时间范围')

        return
      }

      const submitData: ISavePersonRoomDto = {
        productionId: productionId || 0, // 如果没有项目ID，使用0
        personId,
        roomId: roomInfo?.roomId || 0,
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      }

      onSubmit(submitData)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={roomInfo?.fullRoomNumber || `${roomInfo?.buildingNumber}-${roomInfo?.roomNumber}`}
      open={open}
      onCancel={handleCancel}
      onOk={handleSubmit}
      okText="立即分配"
      width={600}
      destroyOnHidden>
      <Form form={form} layout="vertical" className="mt-16">
        <Form.Item name="productionId" label="项目">
          <ProjectSelector
            placeholder="先选择项目"
            onSelect={() => {
              form.setFieldValue('personId', undefined)
            }}
          />
        </Form.Item>
        <Form.Item name="personId" label="选择人员" rules={[{ required: true, message: '请选择人员' }]}>
          <Select
            placeholder="请选择人员"
            options={opts}
            disabled={!formProductionId}
            showSearch
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.personName || '')

              return Boolean(result)
            }}
            fieldNames={{ label: 'personName', value: 'personId' }}
          />
        </Form.Item>

        <Form.Item name="dateRange" label="入住时间" rules={[{ required: true, message: '请选择入住时间范围' }]}>
          <RangePicker
            showTime={false}
            format="YYYY-MM-DD "
            placeholder={['入住开始时间', '入住结束时间']}
            className="w-full"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default AssignPersonModal
