import { DATE_FORMAT_DAY } from '@/consts/date'
import { ListHeader } from '@fe/rockrose'
import { But<PERSON>, Card, Divider, Drawer, Empty, Flex, Popconfirm, Space, Splitter, Statistic, Tabs, Tag, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useMemo, useState } from 'react'
import { CLEAN_STATUS_CONFIG } from '../../../../consts'
import { IGroupCommunityDto, IPrProductionPersonRoom, IPrRoomClean, IRoomDto } from '../../store'
import useRoomStore from '../../store'
import CheckoutModal from './CheckoutModal'
import EditPersonRoomModal from './EditPersonRoomModal'
import PersonEvaluationModal from './PersonEvaluationModal'

const { Text } = Typography

interface ICheckinDetailsDrawerProps {
  open: boolean
  room?: IRoomDto | null
  community?: IGroupCommunityDto | null
  onCancel: () => void
  onDeletePerson: (person: IPrProductionPersonRoom, checkinDates: string[]) => void
  onCheckoutSuccess?: () => void
  onChangeRoom?: (person: IPrProductionPersonRoom, checkinDates: string[]) => void
}

const CheckinDetailsDrawer: React.FC<ICheckinDetailsDrawerProps> = ({
  open,
  room,
  community,
  onCancel,
  onDeletePerson,
  onCheckoutSuccess,
  onChangeRoom,
}) => {
  const roomStore = useRoomStore()

  // 评价相关状态
  const [evaluationModalOpen, setEvaluationModalOpen] = useState(false)
  const [selectedPerson, setSelectedPerson] = useState<IPrProductionPersonRoom | null>(null)

  // 退房相关状态
  const [checkoutModalOpen, setCheckoutModalOpen] = useState(false)
  const [checkoutPerson, setCheckoutPerson] = useState<IPrProductionPersonRoom | null>(null)
  const [checkoutPersonDates, setCheckoutPersonDates] = useState<string[]>([])

  // 编辑相关状态
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [editPerson, setEditPerson] = useState<IPrProductionPersonRoom | null>(null)
  const [editPersonDates, setEditPersonDates] = useState<string[]>([])

  // 获取人员列表
  const personList = room?.personRoom || []

  // 按人员分组入住记录
  const personGroups = useMemo(() => {
    const groups: Record<number, IPrProductionPersonRoom[]> = {}

    personList.forEach(person => {
      if (person.personId) {
        if (!groups[person.personId]) {
          groups[person.personId] = []
        }
        groups[person.personId].push(person)
      }
    })

    // 对每个人的入住记录按日期排序
    Object.keys(groups).forEach(personId => {
      groups[Number(personId)].sort((a, b) => {
        if (!a.checkinDate || !b.checkinDate) {
          return 0
        }

        return dayjs(a.checkinDate).valueOf() - dayjs(b.checkinDate).valueOf()
      })
    })

    return groups
  }, [personList])

  // 统计实际人数
  const actualPersonCount = Object.keys(personGroups).length

  // 处理评价人员
  const handleEvaluatePerson = (person: IPrProductionPersonRoom) => {
    setSelectedPerson(person)
    setEvaluationModalOpen(true)
  }

  // 评价成功回调
  const handleEvaluationSuccess = () => {
    setEvaluationModalOpen(false)
    setSelectedPerson(null)
  }

  // 处理退房人员
  const handleCheckoutPerson = (person: IPrProductionPersonRoom, checkinDates: string[]) => {
    setCheckoutPerson(person)
    setCheckoutPersonDates(checkinDates)
    setCheckoutModalOpen(true)
  }

  // 退房成功回调
  const handleCheckoutSuccess = () => {
    setCheckoutModalOpen(false)
    setCheckoutPerson(null)
    setCheckoutPersonDates([])
    onCheckoutSuccess?.()
  }

  // 处理编辑人员
  const handleEditPerson = (person: IPrProductionPersonRoom, checkinDates: string[]) => {
    setEditPerson(person)
    setEditPersonDates(checkinDates)
    setEditModalOpen(true)
  }

  // 编辑成功回调
  const handleEditSuccess = () => {
    setEditModalOpen(false)
    setEditPerson(null)
    setEditPersonDates([])
    onCheckoutSuccess?.() // 刷新数据
  }

  // 更新保洁状态
  const handleUpdateCleanStatus = async (cleanId: number, newStatus: number) => {
    const success = await roomStore.updateCleanStatus(cleanId, newStatus)
    if (success) {
      onCheckoutSuccess?.() // 刷新数据
    }
  }

  // 如果没有房间或小区信息，不渲染组件
  if (!room || !community) {
    return null
  }

  // 渲染人员分组卡片
  const renderPersonGroupCard = (personId: number, records: IPrProductionPersonRoom[]) => {
    const firstRecord = records[0]
    const checkinDates = records
      .filter(record => record.checkinDate)
      .map(record => dayjs(record.checkinDate).format(DATE_FORMAT_DAY))
      .sort()

    // 判断是否可以退房：checkinDates 的最后一天大于当前天
    const lastCheckinDate = checkinDates[checkinDates.length - 1]
    const today = dayjs().format(DATE_FORMAT_DAY)
    const canCheckout = lastCheckinDate && dayjs(lastCheckinDate).isAfter(dayjs(today), 'day')

    return (
      <Card key={personId} size="small">
        <Flex justify="space-between" align="center">
          <Flex vertical gap={8}>
            <Space size={0} split={<Divider type="vertical" />}>
              <Text strong>{firstRecord.personName}</Text>
              {firstRecord.phone && <Text>{firstRecord.phone}</Text>}
              {firstRecord.productionName && (
                <Text>
                  {firstRecord.productionName}({firstRecord.secondProductionCode})
                </Text>
              )}
            </Space>
            {checkinDates.length > 0 && (
              <Text type="secondary">
                {checkinDates.length <= 3
                  ? ` ${checkinDates.join(', ')}`
                  : ` ${checkinDates[0]} ~ ${checkinDates[checkinDates.length - 1]}`}
              </Text>
            )}
          </Flex>
          <Space size={0} split={<Divider type="vertical" />}>
            <Popconfirm
              title="确认删除"
              description={`确定要将 ${firstRecord.personName} 的所有入住记录从房间中删除吗？`}
              onConfirm={() => {
                // 传递该人员的所有入住日期，批量删除
                onDeletePerson(firstRecord, checkinDates)
              }}
              okText="确定"
              cancelText="取消">
              <Typography.Link>删除</Typography.Link>
            </Popconfirm>
            {canCheckout && (
              <Typography.Link onClick={() => handleCheckoutPerson(firstRecord, checkinDates)}>退房</Typography.Link>
            )}
            {onChangeRoom && (
              <Typography.Link onClick={() => onChangeRoom(firstRecord, checkinDates)}>换房</Typography.Link>
            )}
            <Typography.Link onClick={() => handleEditPerson(firstRecord, checkinDates)}>编辑</Typography.Link>
            <Typography.Link onClick={() => handleEvaluatePerson(firstRecord)}>评价</Typography.Link>
          </Space>
        </Flex>
      </Card>
    )
  }

  // 渲染保洁记录卡片
  const renderCleanRecordCard = (cleanRecord: IPrRoomClean) => {
    const cleanStatusConfig = CLEAN_STATUS_CONFIG[cleanRecord.status as keyof typeof CLEAN_STATUS_CONFIG]

    return (
      <Card key={cleanRecord.id} size="small">
        <Flex justify="space-between" align="center">
          <Flex vertical gap={8}>
            <Space size={0} split={<Divider type="vertical" />}>
              <Tag color={cleanStatusConfig.color}>{cleanStatusConfig.label}</Tag>
              <Typography.Text>
                开始时间: {dayjs(cleanRecord.beginTime).format('YYYY-MM-DD HH:mm')}
              </Typography.Text>
              {cleanRecord.endTime && (
                <Typography.Text>
                  结束时间: {dayjs(cleanRecord.endTime).format('YYYY-MM-DD HH:mm')}
                </Typography.Text>
              )}
            </Space>
            {cleanRecord.lastModifier && (
              <Typography.Text type="secondary">
                最后修改人: {cleanRecord.lastModifier}
              </Typography.Text>
            )}
          </Flex>
          <Space size={0} split={<Divider type="vertical" />}>
            {cleanRecord.status === 0 && (
              <Button
                type="link"
                size="small"
                onClick={() => handleUpdateCleanStatus(cleanRecord.id, 1)}>
                开始打扫
              </Button>
            )}
            {cleanRecord.status === 1 && (
              <Button
                type="link"
                size="small"
                onClick={() => handleUpdateCleanStatus(cleanRecord.id, 2)}>
                完成打扫
              </Button>
            )}
          </Space>
        </Flex>
      </Card>
    )
  }

  return (
    <>
      <Drawer
        title={room.fullRoomNumber || `${room.buildingNumber}-${room.roomNumber}`}
        open={open}
        onClose={onCancel}
        width={800}>
        <Tabs
          defaultActiveKey="checkin"
          items={[
            {
              key: 'checkin',
              label: '入住详情',
              children: (
                <Flex vertical gap={24}>
                  <Card size="small">
                    <Splitter>
                      <Splitter.Panel className="text-center">
                        <Statistic title="当前入住" suffix="次" value={actualPersonCount} />
                      </Splitter.Panel>
                      <Splitter.Panel className="text-center">
                        <Statistic title="历史入住" suffix="次" value={personList.length} />
                      </Splitter.Panel>
                    </Splitter>
                  </Card>

                  <div>
                    <ListHeader title={`当前入住`} className="no-margin" />
                    {actualPersonCount > 0 ? (
                      <Flex vertical gap={12}>
                        {Object.entries(personGroups).map(([personId, records]) =>
                          renderPersonGroupCard(Number(personId), records)
                        )}
                      </Flex>
                    ) : (
                      <Empty />
                    )}
                  </div>
                </Flex>
              ),
            },
            {
              key: 'clean',
              label: '保洁详情',
              children: (
                <div>
                  <ListHeader title={`保洁记录`} className="no-margin" />
                  {room.roomClean && room.roomClean.length > 0 ? (
                    <Flex vertical gap={12}>
                      {room.roomClean.map(cleanRecord => renderCleanRecordCard(cleanRecord))}
                    </Flex>
                  ) : (
                    <Empty description="暂无保洁记录" />
                  )}
                </div>
              ),
            },
          ]}
        />
      </Drawer>

      {/* 人员评价弹窗 */}
      {selectedPerson && evaluationModalOpen ? (
        <PersonEvaluationModal
          open={evaluationModalOpen}
          onCancel={() => setEvaluationModalOpen(false)}
          onSuccess={handleEvaluationSuccess}
          productionId={selectedPerson.productionId}
          personId={selectedPerson.personId}
          personName={selectedPerson.personName || ''}
        />
      ) : null}

      {/* 退房弹窗 */}
      {checkoutPerson && checkoutModalOpen ? (
        <CheckoutModal
          open={checkoutModalOpen}
          person={checkoutPerson}
          checkinDates={checkoutPersonDates}
          onCancel={() => {
            setCheckoutModalOpen(false)
            setCheckoutPerson(null)
            setCheckoutPersonDates([])
          }}
          onSuccess={handleCheckoutSuccess}
        />
      ) : null}

      {/* 编辑入住信息弹窗 */}
      {editPerson && editModalOpen ? (
        <EditPersonRoomModal
          open={editModalOpen}
          person={editPerson}
          checkinDates={editPersonDates}
          onCancel={() => {
            setEditModalOpen(false)
            setEditPerson(null)
            setEditPersonDates([])
          }}
          onSuccess={handleEditSuccess}
        />
      ) : null}
    </>
  )
}

export default CheckinDetailsDrawer
