import { LikeOutlined, ManOutlined, WomanOutlined } from '@ant-design/icons'
import { Card, Empty, Flex, Space, Tag, Tooltip, Typography } from 'antd'
import React, { useMemo } from 'react'
import { useDrop } from 'react-dnd'
import { CLEAN_STATUS_CONFIG, ROOM_LEVEL_CONFIG, ROOM_STATUS_CONFIG, ROOM_TYPE_CONFIG, RoomLevel } from '../../../../consts'
import { IPrProductionAllPerson } from '../../../production/project/list/store'
import { IGroupCommunityDto, IRoomDto } from '../../store'
import styles from './GroupRoomCardList.scss'

const { Text, Title } = Typography

interface IGroupRoomCardListProps {
  groups: IGroupCommunityDto[]
  onPersonDrop: (
    room: IRoomDto,
    person: IPrProductionAllPerson,
    productionId: number,
    community: IGroupCommunityDto
  ) => void
}

interface IRoomCardProps {
  room: IRoomDto
  community: IGroupCommunityDto
  onPersonDrop: (
    room: IRoomDto,
    person: IPrProductionAllPerson,
    productionId: number,
    community: IGroupCommunityDto
  ) => void
}

const RoomCard: React.FC<IRoomCardProps> = ({ room, community, onPersonDrop }) => {
  const disabled = room.status === 0

  const [{ isOver }, drop] = useDrop({
    accept: 'person',
    drop: (item: { person: IPrProductionAllPerson; productionId: number }) => {
      if (!disabled) {
        onPersonDrop(room, item.person, item.productionId, community)
      }
    },
    collect: monitor => ({
      isOver: monitor.isOver() && !disabled,
    }),
  })

  // 计算去重后的入住人员信息列表（包含姓名和性别）
  const uniquePersons = useMemo(() => {
    if (!room.personRoom || room.personRoom.length === 0) {
      return []
    }

    // 使用 Map 按 personId 去重，保留姓名和性别信息
    const personMap = new Map<number, { name: string; gender?: number }>()
    room.personRoom.forEach(person => {
      if (person.personId && person.personName) {
        personMap.set(person.personId, {
          name: person.personName,
          gender: person.gender
        })
      }
    })

    return Array.from(personMap.values())
  }, [room.personRoom])

  const roomTypeConfig = ROOM_TYPE_CONFIG[room.roomType as keyof typeof ROOM_TYPE_CONFIG]
  const roomLevelConfig = ROOM_LEVEL_CONFIG[room.roomLevel as keyof typeof ROOM_LEVEL_CONFIG]
  const roomStatusConfig = ROOM_STATUS_CONFIG[room.status as keyof typeof ROOM_STATUS_CONFIG]
  const cleanStatusConfig = room.feRoomCleanStatus !== undefined
    ? CLEAN_STATUS_CONFIG[room.feRoomCleanStatus as keyof typeof CLEAN_STATUS_CONFIG]
    : null

  const cardContent = (
    <div ref={drop} className={`${isOver ? 'border-primary' : ''}`}>
      <Card size="small" className={disabled ? 'bg-secondary' : ''}>
        <Flex vertical gap={6}>
          {/* 房间号和标签 */}
          <Flex justify="space-between" align="center">
            <Text strong>{room.fullRoomNumber}</Text>
            <Flex gap={4}>
              <Tag className={`${RoomLevel.EXCELLENT === room.roomLevel ? 'text-primary' : ''} no-margin`}>
                <Space size={4}>
                  {RoomLevel.EXCELLENT === room.roomLevel && <LikeOutlined />}
                  {roomLevelConfig.label}
                </Space>
              </Tag>
              <Tag className="no-margin">{roomTypeConfig?.label}</Tag>
            </Flex>
          </Flex>

          {/* 房间信息 */}
          <Flex justify="space-between" align="center">
            <Flex gap={4} align="center" wrap>
              {uniquePersons.length > 0 ? (
                <Text type="secondary" className="fs-sm">
                  {uniquePersons.map((person, index) => (
                    <span key={index}>
                      {person.name}
                      {person.gender && [1, 2].includes(person.gender) ? (
                        <Typography.Text className={person.gender === 1 ? 'text-processing' : 'text-danger'}>
                          {person.gender === 1 ? <ManOutlined /> : <WomanOutlined />}
                        </Typography.Text>
                      ) : null}
                      {index < uniquePersons.length - 1 ? '、' : ''}
                    </span>
                  ))}入住
                </Text>
              ) : (
                <Text type="secondary" className={`fs-sm ${disabled ? '' : 'text-success'}`}>
                  空闲
                </Text>
              )}
              {cleanStatusConfig && (
                <Tag color={cleanStatusConfig.color} className="no-margin fs-xs">
                  {cleanStatusConfig.label}
                </Tag>
              )}
            </Flex>
            {disabled && <Tag color="error">{roomStatusConfig?.label}</Tag>}
          </Flex>
        </Flex>
      </Card>
    </div>
  )

  if (disabled) {
    return (
      <Tooltip title="该房间已禁用，无法分配人员" placement="top">
        {cardContent}
      </Tooltip>
    )
  }

  return cardContent
}

interface ICommunityGroupProps {
  community: IGroupCommunityDto
  onPersonDrop: (
    room: IRoomDto,
    person: IPrProductionAllPerson,
    productionId: number,
    community: IGroupCommunityDto
  ) => void
}

const CommunityGroup: React.FC<ICommunityGroupProps> = ({ community, onPersonDrop }) => {
  return community.roomInfos && community.roomInfos.length > 0 ? (
    <div className={styles.roomGrid}>
      {community.roomInfos.map(room => (
        <RoomCard key={room.roomId} room={room} community={community} onPersonDrop={onPersonDrop} />
      ))}
    </div>
  ) : (
    <Empty />
  )
}

export default CommunityGroup
