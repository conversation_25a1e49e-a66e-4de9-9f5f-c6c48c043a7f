import { CaretRightOutlined } from '@ant-design/icons'
import { Col, Collapse, Empty, Flex, Row, Space, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useMemo } from 'react'
import { IGroupCommunityDto, IRoomDto } from '../../store'
import styles from './CommunityRoomList.scss'
import RoomCard from './RoomCard'

const { Text } = Typography

interface ICommunityCardProps {
  community: IGroupCommunityDto
  searchDateRange?: [dayjs.Dayjs, dayjs.Dayjs] | null // 搜索时间范围
  onAssignPerson: (room: IRoomDto, community: IGroupCommunityDto) => void
  onViewDetails: (room: IRoomDto, community: IGroupCommunityDto) => void
}

const CommunityCard: React.FC<ICommunityCardProps> = ({
  community,
  searchDateRange,
  onAssignPerson,
  onViewDetails,
}) => {
  // 按楼栋号分组房间
  const roomsByBuilding = useMemo(() => {
    if (!community.roomInfos) {
      return {}
    }

    return community.roomInfos.reduce((acc, room) => {
      const buildingNumber = room.buildingNumber

      if (!acc[buildingNumber]) {
        acc[buildingNumber] = []
      }
      acc[buildingNumber].push(room)

      return acc
    }, {} as Record<number, IRoomDto[]>)
  }, [community.roomInfos])

  // 获取排序后的楼栋号列表
  const sortedBuildingNumbers = useMemo(
    () =>
      Object.keys(roomsByBuilding)
        .map(Number)
        .sort((a, b) => a - b),
    [roomsByBuilding]
  )

  return (
    <Flex vertical gap={16}>
      {community.roomInfos && community.roomInfos.length > 0 ? (
        <Collapse
          className={styles.collapse}
          expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
          defaultActiveKey={sortedBuildingNumbers}
          items={sortedBuildingNumbers.map(item => ({
            key: item,
            label: (
              <Space size="large" align="center">
                <Typography.Text strong className="fs-lg">
                  {item}号楼
                </Typography.Text>

                <Typography.Text type="secondary">
                  共<Typography.Text strong> {roomsByBuilding[item].length} </Typography.Text>个房间
                </Typography.Text>
              </Space>
            ),
            children: (
              <Row gutter={[16, 16]}>
                {roomsByBuilding[item].map(room => (
                  <Col key={room.roomId} xs={24} sm={12} md={8} lg={4}>
                    <RoomCard
                      room={room}
                      community={community}
                      searchDateRange={searchDateRange}
                      onAssignPerson={onAssignPerson}
                      onViewDetails={onViewDetails}
                    />
                  </Col>
                ))}
              </Row>
            ),
          }))}
          ghost
        />
      ) : (
        <Empty />
      )}
    </Flex>
  )
}

export default CommunityCard
