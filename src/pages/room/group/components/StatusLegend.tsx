import { Button, Flex, Space, Typography } from 'antd'
import React from 'react'
import styles from './StatusLegend.scss'

// 入住状态图例组件
const StatusLegend: React.FC = () => {
  return (
    <Space size={16}>
      <Flex align="center" gap={4}>
        <Button color="default" variant="filled" size="small" className={styles.icon}></Button>
        <Typography.Text type="secondary" className="fs-sm">
          空闲
        </Typography.Text>
      </Flex>
      <Flex align="center" gap={4}>
        <Button color="green" variant="solid" size="small" className={styles.icon}></Button>
        <Typography.Text type="secondary" className="fs-sm">
          部分入住
        </Typography.Text>
      </Flex>
      <Flex align="center" gap={4}>
        <Button type="primary" size="small" className={styles.icon}></Button>
        <Typography.Text type="secondary" className="fs-sm">
          满员
        </Typography.Text>
      </Flex>
    </Space>
  )
}

export default StatusLegend
