import { Empty, Tabs } from 'antd'
import React from 'react'
import { IPrProductionAllPerson } from '../../../production/project/list/store'
import { IGroupCommunityDto, IRoomDto } from '../../store'
import CommunityGroup from './GroupRoomCardList'

interface ICommunityRoomSectionProps {
  data: IGroupCommunityDto[]
  onPersonDrop: (
    room: IRoomDto,
    person: IPrProductionAllPerson,
    productionId: number,
    community: IGroupCommunityDto
  ) => void
  projectDateRange?: [any, any] | null // 项目时间范围
}

const CommunityRoomSection: React.FC<ICommunityRoomSectionProps> = ({ data, onPersonDrop }) => {
  return data.length > 0 ? (
    <Tabs
      indicator={{ size: 42 }}
      items={data.map(group => ({
        label: group.communityName,
        key: `${group.id}`,
        children: <CommunityGroup community={group} onPersonDrop={onPersonDrop} />,
      }))}
    />
  ) : (
    <Empty />
  )
}

export default CommunityRoomSection
