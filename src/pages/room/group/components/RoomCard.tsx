import { LikeOutlined } from '@ant-design/icons'
import { Button, Card, Descriptions, Flex, Space, Tag, Tooltip, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useMemo } from 'react'
import { CLEAN_STATUS_CONFIG, CleanStatus, ROOM_LEVEL_CONFIG, ROOM_TYPE_CONFIG, RoomLevel } from '../../../../consts'
import { IGroupCommunityDto, IRoomDto } from '../../store'
import styles from './RoomCard.scss'

const { Text } = Typography

interface IRoomCardProps {
  room: IRoomDto
  community: IGroupCommunityDto
  searchDateRange?: [dayjs.Dayjs, dayjs.Dayjs] | null // 搜索时间范围
  onAssignPerson: (room: IRoomDto, community: IGroupCommunityDto) => void
  onViewDetails: (room: IRoomDto, community: IGroupCommunityDto) => void
}

const RoomCard: React.FC<IRoomCardProps> = ({ room, community, searchDateRange, onAssignPerson, onViewDetails }) => {
  const typeConfig = ROOM_TYPE_CONFIG[room.roomType as keyof typeof ROOM_TYPE_CONFIG]
  const levelConfig = ROOM_LEVEL_CONFIG[room.roomLevel as keyof typeof ROOM_LEVEL_CONFIG]
  const cleanStatusConfig =
    room.feRoomCleanStatus !== undefined
      ? CLEAN_STATUS_CONFIG[room.feRoomCleanStatus as keyof typeof CLEAN_STATUS_CONFIG]
      : null

  // 分配人员
  const handleAssignPerson = (event: React.MouseEvent) => {
    event.stopPropagation()
    onAssignPerson(room, community)
  }

  // 使用 store 中计算好的 personCount 衍生字段
  const personCount = room.personCount || 0
  const maxCapacity = room.roomType // 枚举值直接对应房间容量：1=单床房1人，2=双床房2人，3=三床房3人

  // 生成搜索时间范围内的所有日期
  const dateRange = useMemo(() => {
    if (!searchDateRange || !searchDateRange[0] || !searchDateRange[1]) {
      return []
    }

    const dates: string[] = []
    const startDate = searchDateRange[0].startOf('day')
    const endDate = searchDateRange[1].startOf('day')
    let currentDate = startDate

    while (currentDate.isBefore(endDate) || currentDate.isSame(endDate)) {
      dates.push(currentDate.format('YYYY-MM-DD'))
      currentDate = currentDate.add(1, 'day')
    }

    return dates
  }, [searchDateRange])

  // 获取房间在每个日期的入住状态
  const checkinStatus = useMemo(() => {
    const statusMap: Record<string, number> = {}

    if (room.personRoom && dateRange.length > 0) {
      dateRange.forEach(date => {
        // 统计该日期有多少人入住
        const checkinCount =
          room.personRoom?.filter(
            person => person.checkinDate && dayjs(person.checkinDate).format('YYYY-MM-DD') === date
          ).length || 0

        statusMap[date] = checkinCount
      })
    }

    return statusMap
  }, [room, dateRange])

  // 计算入住统计
  const checkinStats = useMemo(() => {
    if (dateRange.length === 0) {
      return { totalDays: 0, occupiedDays: 0, occupancyRate: 0 }
    }

    const totalDays = dateRange.length
    const occupiedDays = dateRange.filter(date => (checkinStatus[date] || 0) > 0).length
    const occupancyRate = totalDays > 0 ? Math.round((occupiedDays / totalDays) * 100) : 0

    return { totalDays, occupiedDays, occupancyRate }
  }, [dateRange, checkinStatus])

  // 渲染入住明细格子
  const renderCheckinDetails = () => {
    if (dateRange.length === 0) {
      return null
    }

    return (
      <div className={styles.checkinDetails}>
        <Flex justify="space-between" align="center" style={{ marginBottom: 4 }}>
          <div className={styles.title}>入住明细</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{checkinStats.occupancyRate}%</div>
        </Flex>
        <div className={styles.gridContainer}>
          {dateRange.map(date => {
            const count = checkinStatus[date] || 0
            const percentage = maxCapacity > 0 ? Math.round((count / maxCapacity) * 100) : 0

            // 根据入住率确定样式类
            let statusClass = styles.statusEmpty // 空闲
            let statusText = '空闲'

            if (percentage >= 100) {
              statusClass = styles.statusFull // 已满员
              statusText = '满员'
            } else if (percentage > 0) {
              statusClass = styles.statusPartial // 部分入住
              statusText = '部分入住'
            }

            const tooltipTitle = (
              <Descriptions title={dayjs(date).format('YYYY-MM-DD')} column={1} size="small" bordered className="fs-sm">
                <Descriptions.Item label="人数">
                  {count}/{maxCapacity}
                </Descriptions.Item>
                <Descriptions.Item label="状态">{statusText}</Descriptions.Item>
                <Descriptions.Item label="入住率">{percentage}%</Descriptions.Item>
              </Descriptions>
            )

            return (
              <Tooltip key={date} color="#fff" title={tooltipTitle} placement="top">
                <div className={`${styles.gridItem} ${statusClass}`}>{dayjs(date).date()}</div>
              </Tooltip>
            )
          })}
        </div>
      </div>
    )
  }

  return (
    <Card
      size="small"
      className={`hover-move ${room.status === 0 ? 'bg-secondary' : ''}`}
      onClick={() => onViewDetails(room, community)}>
      <Flex vertical gap={12}>
        {/* 房间号和标签 */}
        <Flex justify="space-between" align="center">
          <Text strong className="fs-lg">
            {room.fullRoomNumber || `${room.buildingNumber}-${room.roomNumber}`}
          </Text>
          {personCount > 0 ? (
            <Tag color={personCount > maxCapacity ? 'error' : 'default'} className="no-margin">
              {personCount}/{maxCapacity}
            </Tag>
          ) : room.status === 0 ? (
            <Tag color="error" className="no-margin">
              禁用
            </Tag>
          ) : cleanStatusConfig && CleanStatus.COMPLETED !== room.feRoomCleanStatus ? (
            <Tag color={cleanStatusConfig.color} className="no-margin">
              {cleanStatusConfig.label}
            </Tag>
          ) : (
            <Tag className="text-success no-margin">空闲</Tag>
          )}
        </Flex>

        {/* 房间类型和等级标签 */}
        <Space size={2}>
          <Tag className={RoomLevel.EXCELLENT === room.roomLevel ? 'text-primary' : ''}>
            <Space size={4}>
              {RoomLevel.EXCELLENT === room.roomLevel && <LikeOutlined />}
              {levelConfig.label}
            </Space>
          </Tag>
          <Tag>{typeConfig.label}</Tag>
        </Space>

        {/* 入住明细 */}
        {renderCheckinDetails()}

        {/* 操作按钮 */}
        <Button
          color="default"
          size="small"
          variant="filled"
          className={personCount >= maxCapacity || room.status === 0 ? '' : 'text-success'}
          block
          onClick={handleAssignPerson}
          disabled={personCount >= maxCapacity || room.status === 0}>
          {room.status === 0 ? '已禁用' : personCount >= maxCapacity ? '已满员' : '分配人员'}
        </Button>
      </Flex>
    </Card>
  )
}

export default RoomCard
