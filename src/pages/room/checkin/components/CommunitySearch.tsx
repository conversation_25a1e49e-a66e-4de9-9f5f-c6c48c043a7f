import { useDebounceFn } from 'ahooks'
import { DatePicker, Form } from 'antd'
import dayjs from 'dayjs'
import React from 'react'

const { RangePicker } = DatePicker

interface ICommunitySearchProps {
  form: any
  onSearch: () => void
}

const CommunitySearch: React.FC<ICommunitySearchProps> = ({ form, onSearch }) => {
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(), { wait: 500 })

  return (
    <Form form={form} colon={false} layout="inline" onValuesChange={onSearchDebounce}>
      <Form.Item name="dateRange" initialValue={[dayjs(), dayjs().add(1, 'day')]} noStyle>
        <RangePicker placeholder={['开始日期', '结束日期']} style={{ width: '100%' }} allowClear={false} />
      </Form.Item>
    </Form>
  )
}

export default CommunitySearch
