import { ACTOR_ROLE_TYPE_CONFIG, ROLE_TYPE_CONFIG } from '@/consts'
import { MoreOutlined, SearchOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { useDebounceFn } from 'ahooks'
import { Button, Card, Divider, Dropdown, Empty, Flex, Input, Space, Typography, message } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { useDrag } from 'react-dnd'
import { IPrProductionAllPerson } from '../../../production/project/list/store'
import useRoomStore, { ISavePersonRoomDto } from '../../store'
import EditPersonRoomModal from './EditPersonRoomModal'

const { Text } = Typography

interface IProjectPersonListProps {
  data: IPrProductionAllPerson[]
  productionId?: number
  onPersonRoomDeleted?: () => void
}

interface IPersonCardProps {
  person: IPrProductionAllPerson
  productionId: number
  onPersonRoomDeleted?: () => void
}

const PersonCard: React.FC<IPersonCardProps> = ({ person, productionId, onPersonRoomDeleted }) => {
  const { deletePersonRoom } = useRoomStore()
  const [editModalOpen, setEditModalOpen] = useState(false)

  const [{ isDragging }, drag] = useDrag({
    type: 'person',
    item: { person, productionId },
    collect: monitor => ({
      isDragging: monitor.isDragging(),
    }),
  })

  // 获取角色类型配置
  const getRoleTypeConfig = () => {
    if (person.parentType === 2) {
      // 演员
      return ACTOR_ROLE_TYPE_CONFIG[String(person.roleType || 0) as keyof typeof ACTOR_ROLE_TYPE_CONFIG]
    } else {
      // 人员
      return ROLE_TYPE_CONFIG[(person.roleType || 0) as keyof typeof ROLE_TYPE_CONFIG]
    }
  }

  // 处理编辑入住信息
  const handleEditPersonRoom = () => {
    setEditModalOpen(true)
  }

  // 处理删除入住信息
  const handleDeletePersonRoom = async () => {
    if (!person.personRoom || !person.personRoomDates || person.personRoomDates.length === 0) {
      message.error('没有入住信息可删除')
      return
    }

    try {
      // 根据personRoomDates计算startDate和endDate
      const dates = person.personRoomDates.map(date => dayjs(date))
      const sortedDates = dates.sort((a, b) => a.valueOf() - b.valueOf())
      const startDate = sortedDates[0].format('YYYY-MM-DD') // 最早的入住日期
      const endDate = sortedDates[sortedDates.length - 1].format('YYYY-MM-DD') // 最晚的入住日期

      const params: ISavePersonRoomDto = {
        productionId: person.personRoom.productionId || productionId,
        personId: person.personId!,
        roomId: person.personRoom.roomId!,
        startDate,
        endDate,
      }

      const success = await deletePersonRoom(params)

      if (success) {
        message.success(`成功删除 ${person.personName} 的入住信息`)
        onPersonRoomDeleted?.()
      }
    } catch (error) {
      console.error('删除入住信息失败:', error)
      message.error('删除入住信息失败')
    }
  }

  const roleTypeConfig = getRoleTypeConfig()

  return (
    <div ref={drag} className={`hover-move cursor-move ${isDragging ? 'border-primary' : ''}`}>
      <Card size="small">
        <Flex justify="space-between">
          <Space size={0} split={<Divider type="vertical" />}>
            <Text strong>{person.personName}</Text>
            <Text>{roleTypeConfig?.label}</Text>
          </Space>
          {person.personRoom && (
            <Space size={0} split={<Divider type="vertical" />}>
              <Dict title="入住" value={person.personRoom.fullRoomNumber} />
              <Text>
                {person.personRoomDates && person.personRoomDates.length > 0
                  ? `${person.personRoomDates.length}天`
                  : null}
              </Text>
              <Dropdown
                menu={{
                  items: [
                    {
                      key: '1',
                      label: '编辑',
                      onClick: () => {
                        handleEditPersonRoom()
                      },
                    },
                    {
                      key: '2',
                      danger: true,
                      label: '退房',
                      onClick: () => {
                        handleDeletePersonRoom()
                      },
                    },
                  ],
                }}>
                <Button icon={<MoreOutlined />} type="text" />
              </Dropdown>
            </Space>
          )}
        </Flex>
      </Card>
      {person.personRoom && editModalOpen && (
        <EditPersonRoomModal
          open={editModalOpen}
          person={person}
          productionId={productionId}
          onCancel={() => setEditModalOpen(false)}
          onSuccess={() => {
            setEditModalOpen(false)
            onPersonRoomDeleted?.() // 刷新数据
          }}
        />
      )}
    </div>
  )
}

const ProjectPersonList: React.FC<IProjectPersonListProps> = ({ data, productionId, onPersonRoomDeleted }) => {
  const [persons, setPersons] = useState(data)
  const [search, setSearch] = useState('')

  // 搜索
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => setSearch(e.target.value)
  const { run: debounceSearch } = useDebounceFn(handleSearch, { wait: 500 })

  useEffect(() => {
    const persons = data.filter(person => person.personName?.includes(search))
    setPersons(persons)
  }, [data, search])

  if (!productionId) {
    return <Empty description="请先选择项目" />
  }

  return data.length ? (
    <Flex vertical>
      <ListHeader title="项目组人员" total={data.length} unitText="个">
        <Input prefix={<SearchOutlined />} onChange={debounceSearch} placeholder="输入人员关键字搜索" />
      </ListHeader>
      <Flex vertical gap={12}>
        {persons.map(person => (
          <PersonCard
            key={person.personId}
            person={person}
            productionId={productionId}
            onPersonRoomDeleted={onPersonRoomDeleted}
          />
        ))}
      </Flex>
    </Flex>
  ) : (
    <Empty />
  )
}

export default ProjectPersonList
