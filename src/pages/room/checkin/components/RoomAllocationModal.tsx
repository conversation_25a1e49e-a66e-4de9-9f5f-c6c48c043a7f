import { DatePicker, Descriptions, Form, Modal, Tag, Typography, message } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import { ACTOR_ROLE_TYPE_CONFIG, ROLE_TYPE_CONFIG, ROOM_LEVEL_CONFIG, ROOM_TYPE_CONFIG } from '../../../../consts'
import { IPrProductionAllPerson, IProductionListItem } from '../../../production/project/list/store'
import useRoomStore, { IRoomDto, ISavePersonRoomDto } from '../../store'

const { RangePicker } = DatePicker
const { Text } = Typography

interface IRoomAllocationModalProps {
  open: boolean
  room: IRoomDto | null
  person: IPrProductionAllPerson | null
  productionId: number
  project: IProductionListItem | null
  dateRange: [any, any] | null
  onConfirm: () => void
  onCancel: () => void
}

const RoomAllocationModal: React.FC<IRoomAllocationModalProps> = ({
  open,
  room,
  person,
  productionId,
  project,
  dateRange,
  onConfirm,
  onCancel,
}) => {
  const [form] = Form.useForm()
  const [saving, setSaving] = useState(false)
  const { savePersonRoom } = useRoomStore()

  useEffect(() => {
    if (open && room && person) {
      // 设置默认日期范围
      let startDate = dayjs(dateRange?.[0])
      let endDate =  dayjs(dateRange?.[1])
      
      form.setFieldsValue({
        dateRange: [startDate, endDate]
      })
      handleConfirm()
    }
  }, [open, room, person, project, form])

  // 获取角色类型配置
  const getRoleTypeConfig = () => {
    if (!person) return null
    if (person.parentType === 2) {
      // 演员
      return ACTOR_ROLE_TYPE_CONFIG[String(person.roleType || 0) as keyof typeof ACTOR_ROLE_TYPE_CONFIG]
    } else {
      // 人员
      return ROLE_TYPE_CONFIG[(person.roleType || 0) as keyof typeof ROLE_TYPE_CONFIG]
    }
  }

  // 处理确认分配
  const handleConfirm = async () => {
    if (!room || !person) return

    try {
      const values = await form.validateFields()
      const [startDate, endDate] = values.dateRange

      setSaving(true)

      const params: ISavePersonRoomDto = {
        productionId,
        personId: person.personId!,
        roomId: room.roomId,
        startDate: startDate.format('YYYY-MM-DD'),
        endDate: endDate.format('YYYY-MM-DD'),
      }

      const success = await savePersonRoom(params)

      if (success) {
        form.resetFields()
        onConfirm()
      }
    } catch (error) {
      console.error('房间分配失败:', error)
      message.error('房间分配失败')
    } finally {
      setSaving(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  const roleTypeConfig = getRoleTypeConfig()
  const roomTypeConfig = room ? ROOM_TYPE_CONFIG[room.roomType as keyof typeof ROOM_TYPE_CONFIG] : null
  const roomLevelConfig = room ? ROOM_LEVEL_CONFIG[room.roomLevel as keyof typeof ROOM_LEVEL_CONFIG] : null

  return (
    <Modal
      title="确认房间分配"
      open={open}
      onOk={handleConfirm}
      onCancel={handleCancel}
      okText="确认分配"
      cancelText="取消"
      confirmLoading={saving}
      width={600}
      destroyOnHidden
    >
      {room && person && (
        <>
          <Descriptions title="分配信息" bordered column={1} style={{ marginBottom: 24 }}>
            <Descriptions.Item label="人员姓名">
              <Text strong>{person.personName}</Text>
              {roleTypeConfig && (
                <Tag color={roleTypeConfig.color} style={{ marginLeft: 8 }}>
                  {roleTypeConfig.label}
                </Tag>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="人员类型">
              {person.parentType === 1 ? '人员' : '演员'}
            </Descriptions.Item>
            <Descriptions.Item label="房间号">
              <Text strong>{room.fullRoomNumber}</Text>
            </Descriptions.Item>
            <Descriptions.Item label="房间类型">
              {roomTypeConfig && (
                <Tag color={roomTypeConfig.color}>
                  {roomTypeConfig.label}
                </Tag>
              )}
              {roomLevelConfig && (
                <Tag color={roomLevelConfig.color} style={{ marginLeft: 8 }}>
                  {roomLevelConfig.label}
                </Tag>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="房间信息">
              楼栋{room.buildingNumber}栋 {room.roomNumber}号
              {room.roomPosition && ` (${room.roomPosition})`}
            </Descriptions.Item>
          </Descriptions>

          <Form form={form} layout="vertical">
            <Form.Item
              name="dateRange"
              label="入住日期范围"
              rules={[{ required: true, message: '请选择入住日期范围' }]}
            >
              <RangePicker
                style={{ width: '100%' }}
                placeholder={['开始日期', '结束日期']}
                disabledDate={(current) => current && current < dayjs().startOf('day')}
              />
            </Form.Item>
          </Form>
        </>
      )}
    </Modal>
  )
}

export default RoomAllocationModal
