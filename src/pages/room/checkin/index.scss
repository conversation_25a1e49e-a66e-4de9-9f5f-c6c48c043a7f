.container {
  display: flex;
  height: calc(100vh - 110px);
  flex-direction: column;
}

.header {
  padding: 16px 0;
  border-bottom: 1px dotted #f0f0f0;
  flex-shrink: 0;
}

.content {
  flex: 1;
  overflow: hidden;
  padding-top: 8px;

  :global {
    .ant-splitter-panel {
      overflow-y: auto;
      padding-bottom: 24px;
      height: 100%;

      &:first-child {
        padding-right: 16px;
      }

      &:last-child {
        padding-top: 12px;
        padding-inline: 16px;
      }
    }

    .ant-splitter-bar-dragger::before {
      width: 1px !important;
    }
  }
}
