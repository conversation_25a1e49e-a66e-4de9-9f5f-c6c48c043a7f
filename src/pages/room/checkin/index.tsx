import ProjectSelector from '@/pages/production/project/components/ProjectSelector'
import { exportRoomWord } from '@/utils/export'
import { DownloadOutlined, SwapOutlined } from '@ant-design/icons'
import { Alert, Button, Flex, Form, Space, Splitter, Typography, message } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import AutoRoomAllocationSidebar from '../../production/project/components/AutoRoomAllocationSidebar'
import useProjectStore, { IPrProductionAllPerson, IProductionListItem } from '../../production/project/list/store'
import useRoomStore, { IGroupCommunityDto, IParaGroupRoomDto, IRoomDto } from '../store'
import CommunityRoomSection from './components/CommunityRoomSection'
import CommunitySearch from './components/CommunitySearch'
import ProjectPersonList from './components/ProjectPersonList'
import RoomAllocationModal from './components/RoomAllocationModal'
import styles from './index.scss'
const fitlerProject = (item: IProductionListItem) => {
  return item?.startDate && item?.endDate
}
interface IAllocationData {
  room: IRoomDto
  person: IPrProductionAllPerson
  productionId: number
  community: IGroupCommunityDto
  project: IProductionListItem | null
}

const ProjectPersonCheckinPage: React.FC = () => {
  const [searchForm] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [groupList, setGroupList] = useState<IGroupCommunityDto[]>([])
  const [persons, setPersons] = useState<IPrProductionAllPerson[]>([])

  const [selectedProjectId, setSelectedProjectId] = useState<number | undefined>()
  const [allocationModalOpen, setAllocationModalOpen] = useState(false)
  const [allocationData, setAllocationData] = useState<IAllocationData | null>(null)
  const [selectedProject, setSelectedProject] = useState<IProductionListItem | null>(null)
  const [autoAllocationOpen, setAutoAllocationOpen] = useState(false)

  const { fetchGroupList } = useRoomStore()
  const { getAllPersonList } = useProjectStore()

  const formDataRange = Form.useWatch('dateRange', searchForm)

  // 搜索房间
  const handleSearch = async () => {
    setLoading(true)
    try {
      const values = searchForm.getFieldsValue()
      const params: IParaGroupRoomDto = {
        startDate: values.dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: values.dateRange?.[1]?.format('YYYY-MM-DD'),
      }

      const result = await fetchGroupList(params)
      if (result) {
        setGroupList(result)
      } else {
        setGroupList([])
      }
    } catch (error) {
      console.error('获取房间列表失败:', error)
      message.error('获取房间列表失败')
      setGroupList([])
    } finally {
      setLoading(false)
    }
  }

  // 获取项目人员列表
  const fetchPersonList = async (productionId: number) => {
    if (!productionId) return

    setLoading(true)
    try {
      const result = await getAllPersonList(productionId)
      if (result) {
        setPersons(result)
      } else {
        setPersons([])
      }
    } catch (error) {
      console.error('获取项目人员列表失败:', error)
      message.error('获取项目人员列表失败')
      setPersons([])
    } finally {
      setLoading(false)
    }
  }

  // 处理项目选择
  const handleProjectSelect = async (projectId: number, option: any) => {
    if (projectId) {
      setSelectedProjectId(projectId)
      setSelectedProject(option)

      searchForm.setFieldsValue({
        dateRange: [dayjs(option.startDate).add(-1, 'day'), dayjs(option.endDate).add(1, 'day')],
      })

      await fetchPersonList(projectId)
      await handleSearch()
    }
  }

  // 处理人员拖拽到房间
  const handlePersonDrop = (
    room: IRoomDto,
    person: IPrProductionAllPerson,
    productionId: number,
    community: IGroupCommunityDto
  ) => {
    setAllocationData({ room, person, productionId, community, project: selectedProject })
    setAllocationModalOpen(true)
  }

  // 处理分配确认
  const handleAllocationConfirm = () => {
    setAllocationModalOpen(false)
    setAllocationData(null)
    message.success('房间分配成功')

    handleSearch()
    selectedProjectId && fetchPersonList(selectedProjectId)
  }

  // 处理人员入住信息删除
  const handlePersonRoomDeleted = () => {
    handleSearch()
    selectedProjectId && fetchPersonList(selectedProjectId)
  }

  // 处理分配取消
  const handleAllocationCancel = () => {
    setAllocationModalOpen(false)
    setAllocationData(null)
  }

  // 处理自动分配房间
  const handleAutoAllocation = () => {
    setAutoAllocationOpen(true)
  }

  // 处理导出邀请信
  const handleExportInvitation = async () => {
    if (!selectedProjectId) {
      message.warning('请先选择项目')
      return
    }

    try {
      const filename = selectedProject?.productionName ? `${selectedProject.productionName}_邀请信.docx` : '邀请信.docx'
      await exportRoomWord(selectedProjectId, filename)
      message.success('邀请信导出成功')
    } catch (error) {
      message.error('邀请信导出失败')
    }
  }

  // 处理自动分配房间成功
  const handleAutoAllocationSuccess = () => {
    setAutoAllocationOpen(false)
    handleSearch()
    selectedProjectId && fetchPersonList(selectedProjectId)
  }

  useEffect(() => {
    handleSearch()
  }, [])

  return (
    <div className={styles.container}>
      {/* 页面头部 */}
      <div className={styles.header}>
        <Flex justify="space-between" align="center">
          <Space size={24}>
            <Typography.Title level={5} className="no-margin">
              办理项目组入住
            </Typography.Title>
            <ProjectSelector
              placeholder="请选择项目"
              autoFocus
              className="w300 fw-normal"
              defaultOpen
              popupMatchSelectWidth
              value={selectedProjectId}
              onChange={projectId => setSelectedProjectId(projectId)}
              onSelect={handleProjectSelect}
              allowClear={false}
              otherParams={{ status: [1], productionType: 0 }}
              filterFunc={fitlerProject}
            />
            <CommunitySearch form={searchForm} onSearch={handleSearch} />
            <Alert message="左边选择项目后，将项目组人员拖动到房间完成入住" type="warning" showIcon />
          </Space>

          {selectedProjectId && (
            <Space>
              <Button type="link" ghost icon={<DownloadOutlined />} onClick={handleExportInvitation}>
                导出邀请信
              </Button>
              <Button type="primary" ghost icon={<SwapOutlined />} onClick={handleAutoAllocation}>
                一键分配房间
              </Button>
            </Space>
          )}
        </Flex>
      </div>

      {/* 页面内容 */}
      <div className={styles.content}>
        <Splitter className="full-v">
          <Splitter.Panel defaultSize="60%" min="50%" max="70%">
            <CommunityRoomSection data={groupList} onPersonDrop={handlePersonDrop} />
          </Splitter.Panel>
          <Splitter.Panel className="full-v">
            <ProjectPersonList
              data={persons}
              productionId={selectedProjectId}
              onPersonRoomDeleted={handlePersonRoomDeleted}
            />
          </Splitter.Panel>
        </Splitter>
      </div>

      {/* 房间分配确认弹窗 */}
      {allocationModalOpen ? (
        <RoomAllocationModal
          open={allocationModalOpen}
          room={allocationData?.room || null}
          person={allocationData?.person || null}
          productionId={allocationData?.productionId || 0}
          project={allocationData?.project || null}
          onConfirm={handleAllocationConfirm}
          onCancel={handleAllocationCancel}
          dateRange={formDataRange}
        />
      ) : null}

      {/* 自动分配房间侧边栏 */}
      {autoAllocationOpen && selectedProjectId ? (
        <AutoRoomAllocationSidebar
          open={autoAllocationOpen}
          productionId={selectedProjectId || 0}
          project={selectedProject || undefined}
          onClose={() => setAutoAllocationOpen(false)}
          onSuccess={handleAutoAllocationSuccess}
        />
      ) : null}
    </div>
  )
}

export default ProjectPersonCheckinPage
