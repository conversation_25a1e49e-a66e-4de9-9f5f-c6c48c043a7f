import filterMatch from '@/utils/filterMatch'
import { DatePicker, Form, message, Modal, Select } from 'antd'
import dayjs from 'dayjs'
import React, { useEffect, useState } from 'react'
import useStore, { ISavePersonRoomDto } from '../store'

const { RangePicker } = DatePicker

interface IAssignPersonModalProps {
  open: boolean
  productionId: any
  personId: any
  onCancel: () => void
  onSuccess?: () => void
}

const AssignPersonModal: React.FC<IAssignPersonModalProps> = ({
  open,
  productionId,
  personId,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm()
  const [saving, setSaving] = useState(false)
  const { fetchAllCommunityRoom, allCommunityRoom, savePersonRoom } = useStore()

  useEffect(() => {
    if (open) {
      fetchAllCommunityRoom()
      form.resetFields()
      const today = dayjs()
      const nextMonth = today.add(1, 'day')

      form.setFieldsValue({ dateRange: [today, nextMonth] })
    }
  }, [open, form])

  const handleSubmit = async () => {
    try {
      setSaving(true)
      const values = await form.validateFields()
      const { roomId, dateRange } = values

      if (!dateRange || dateRange.length !== 2) {
        message.error('请选择入住时间范围')

        return
      }

      const submitData: ISavePersonRoomDto = {
        productionId,
        personId,
        roomId,
        startDate: dateRange[0].format('YYYY-MM-DD'),
        endDate: dateRange[1].format('YYYY-MM-DD'),
      }

      const success = await savePersonRoom(submitData)

      if (success) {
        message.success('住宿信息添加成功')
        form.resetFields()
        onCancel() // 关闭弹窗
        onSuccess?.() // 调用成功回调
      }
    } catch (error) {
      console.error('添加住宿信息失败:', error)
      message.error('添加住宿信息失败')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  return (
    <Modal
      title={'分配住宿'}
      open={open}
      onCancel={handleCancel}
      onOk={handleSubmit}
      width={600}
      destroyOnHidden
      confirmLoading={saving}>
      <Form form={form} layout="vertical" className="mt-16">
        <Form.Item name="roomId" label="选择房间" rules={[{ required: true, message: '请选择房间' }]}>
          <Select
            placeholder="请选择房间"
            options={allCommunityRoom}
            fieldNames={{ label: 'labelStr', value: 'id' }}
            showSearch
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.labelStr || '')

              return Boolean(result)
            }}
          />
        </Form.Item>

        <Form.Item name="dateRange" label="入住时间" rules={[{ required: true, message: '请选择入住时间范围' }]}>
          <RangePicker
            showTime={false}
            format="YYYY-MM-DD "
            placeholder={['入住开始时间', '入住结束时间']}
            className="w-full"
          />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default AssignPersonModal
