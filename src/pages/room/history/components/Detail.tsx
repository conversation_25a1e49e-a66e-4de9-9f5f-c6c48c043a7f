import { Descriptions, Drawer, Typography } from 'antd'
import dayjs from 'dayjs'
import React from 'react'
import { ROOM_LEVEL_CONFIG, ROOM_TYPE_CONFIG } from '../../../../consts'
import type { IPersonRoomHistoryItem } from '../../store'

interface IDetailProps {
  open: boolean
  data?: IPersonRoomHistoryItem
  onClose: () => void
}

const Detail: React.FC<IDetailProps> = ({ open, data, onClose }) => {
  if (!data) return null

  const roomTypeConfig = ROOM_TYPE_CONFIG[data.roomType as keyof typeof ROOM_TYPE_CONFIG]
  const roomLevelConfig = ROOM_LEVEL_CONFIG[data.roomLevel as keyof typeof ROOM_LEVEL_CONFIG]

  return (
    <Drawer title="入住详情" placement="right" onClose={onClose} open={open} width={600}>
      <Descriptions column={1} bordered size="small">
        {/* <Descriptions.Item label="ID">{data.id}</Descriptions.Item> */}
        <Descriptions.Item label="拍摄项目">
          {data.productionName}({data.secondProductionCode})
        </Descriptions.Item>
        <Descriptions.Item label="住客">{data.personName || '-'}</Descriptions.Item>
        <Descriptions.Item label="房号">
          <Typography.Text strong>
            {`${data.buildingNumber}#`}
            {data.roomNumber}
            {` ${data.roomPosition}` || ''}
          </Typography.Text>
        </Descriptions.Item>
        <Descriptions.Item label="小区">{data.communityName || '-'}</Descriptions.Item>
        {/* <Descriptions.Item label="完整房间号">{data.fullRoomNumber || '-'}</Descriptions.Item> */}
        <Descriptions.Item label="房型">{roomTypeConfig ? roomTypeConfig.label : data.roomType}</Descriptions.Item>
        <Descriptions.Item label="等级">{roomLevelConfig ? roomLevelConfig.label : data.roomLevel}</Descriptions.Item>
        <Descriptions.Item label="入住日期">
          {data.checkinDate ? dayjs(data.checkinDate).format('YYYY-MM-DD') : '-'}
        </Descriptions.Item>
      </Descriptions>
    </Drawer>
  )
}

export default Detail
