import { useDebounceFn } from 'ahooks'
import { Button, Form, Input, Select, Space } from 'antd'
import React from 'react'
import { ROOM_LEVEL_OPTIONS, ROOM_TYPE_OPTIONS } from '../../../../consts'

interface ISearchProps {
  form: any
  onSearch: (current?: number, pageSize?: number) => void
  onReset: () => void
  loading?: boolean
}

const Search: React.FC<ISearchProps> = ({ form, onSearch, loading }) => {
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(1), { wait: 500 })

  return (
    <Form form={form} layout="vertical" onValuesChange={onSearchDebounce} colon={false} className="search-form">
      <Space wrap size={24}>
        <Form.Item name="personName" label="住客">
          <Input className="w200" placeholder="输入住客姓名" allowClear />
        </Form.Item>
        <Form.Item name="productionName" label="项目">
          <Input className="w200" placeholder="输入拍摄项目" allowClear />
        </Form.Item>
        <Form.Item name="roomNumber" label="房间号">
          <Input className="w200" placeholder="输入房间号" allowClear />
        </Form.Item>
        <Form.Item name="roomType" label="房型">
          <Select className="w200" placeholder="选择房型" allowClear options={ROOM_TYPE_OPTIONS} />
        </Form.Item>
        <Form.Item name="roomLevel" label="等级">
          <Select className="w200" mode="multiple" placeholder="选择等级" allowClear options={ROOM_LEVEL_OPTIONS} />
        </Form.Item>
        <Form.Item label=" ">
          <Button type="primary" loading={loading} onClick={() => onSearchDebounce()}>
            查询
          </Button>
        </Form.Item>
      </Space>
    </Form>
  )
}

export default Search
