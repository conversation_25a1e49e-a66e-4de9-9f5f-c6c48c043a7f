import { Dict } from '@fe/rockrose'
import { List as AntdList, Card, Divider, Flex, Space, Typography } from 'antd'
import dayjs from 'dayjs'
import React from 'react'
import { ROOM_LEVEL_CONFIG, ROOM_TYPE_CONFIG } from '../../../../consts'
import type { IPersonRoomHistoryItem } from '../../store'

const { Text } = Typography

interface IListProps {
  data: IPersonRoomHistoryItem[]
  loading?: boolean
  onOperate: (type: string, data?: IPersonRoomHistoryItem) => void
  onChange: (current?: number, pageSize?: number) => void
  pagination: any
}

const List: React.FC<IListProps> = ({ data, loading, onOperate, onChange, pagination }) => {
  // 渲染人员记录卡片
  const renderPersonCard = (record: IPersonRoomHistoryItem) => {
    const roomTypeConfig = ROOM_TYPE_CONFIG[record.roomType as keyof typeof ROOM_TYPE_CONFIG]
    const roomLevelConfig = ROOM_LEVEL_CONFIG[record.roomLevel as keyof typeof ROOM_LEVEL_CONFIG]

    return (
      <AntdList.Item key={record.id}>
        <Card className="full-h pointer hover-move" size="small" onClick={() => onOperate('view', record)}>
          <Flex justify="space-between" align="center">
            <Flex vertical gap={12} flex={1}>
              <Space size={0} split={<Divider type="vertical" />}>
                <Text strong className="fs-lg">
                  {record.fullRoomNumber}
                </Text>
                {record.communityName && <Text>{record.communityName}</Text>}
                {roomTypeConfig && <Text>{roomTypeConfig.label}</Text>}
                {roomLevelConfig && <Text style={{ color: roomLevelConfig.color }}>{roomLevelConfig.label}</Text>}
              </Space>
              <Space size={0} split={<Divider type="vertical" />}>
                <Text strong>{record.personName}</Text>
                {record.productionName && (
                  <Text>
                    {record.productionName}
                    {record.secondProductionCode && `(${record.secondProductionCode})`}
                  </Text>
                )}
              </Space>
            </Flex>
            <Flex gap={96}>
              {record.checkinDate && <Dict title="入住时间" value={dayjs(record.checkinDate).format('YYYY-MM-DD')} />}
              <Typography.Link
                onClick={e => {
                  e.stopPropagation()
                  onOperate('evaluate', record)
                }}>
                评价
              </Typography.Link>
            </Flex>
          </Flex>
        </Card>
      </AntdList.Item>
    )
  }

  return (
    <Flex vertical>
      <AntdList
        loading={loading}
        dataSource={data}
        split={false}
        renderItem={renderPersonCard}
        rowKey="id"
        className="list-sm"
        pagination={{
          ...pagination,
          onChange,
          onShowSizeChange: onChange,
        }}
      />
    </Flex>
  )
}

export default List
