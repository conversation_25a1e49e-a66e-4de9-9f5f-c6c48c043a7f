import { Flex, Form } from 'antd'
import React, { useEffect, useState } from 'react'
import { PAGINATION } from '../../../consts'
import useSyncParams, { initFormFromUrlState, parsePagination } from '../../../hooks/useSyncParams'
import PersonEvaluationModal from '../group/components/PersonEvaluationModal'
import useRoomStore, { IPersonRoomHistoryItem, IPersonRoomHistorySearchParams } from '../store'
import Detail from './components/Detail'
import List from './components/List'
import Search from './components/Search'

const RoomHistory: React.FC = () => {
    const [urlState, setUrlState] = useSyncParams<IPersonRoomHistorySearchParams>()
    const [form] = Form.useForm()
    const [loading, setLoading] = useState(false)
    const [dataSource, setDataSource] = useState<IPersonRoomHistoryItem[]>([])
    const [pagination, setPagination] = useState(PAGINATION)

    const [show, setShow] = useState(false)
    const [operateData, setOperateData] = useState<IPersonRoomHistoryItem | undefined>()

    // 评价相关状态
    const [showEvaluation, setShowEvaluation] = useState(false)
    const [evaluationData, setEvaluationData] = useState<IPersonRoomHistoryItem | undefined>()

    const { fetchPersonRoomHistory } = useRoomStore()

    // 初始化URL数据
    const initParams = () => {
        // 使用工具函数简化表单初始化
        initFormFromUrlState(urlState, form, {
            numberFields: ['roomNumber', 'roomType'],
            numberArrayFields: ['roomLevel'],
            excludeFields: ['pageIndex', 'pageSize'],
        })

        // 使用工具函数处理分页
        const { current, pageSize } = parsePagination(urlState, {
            current: pagination.current,
            pageSize: pagination.pageSize,
        })

        handleSearch(current, pageSize)
    }

    const handleSearch = async (current = pagination.current, pageSize = pagination.pageSize) => {
        setLoading(true)
        try {
            const values = form.getFieldsValue()
            const searchParams: IPersonRoomHistorySearchParams = {
                pageIndex: current,
                pageSize,
                ...values,
            }

            const result = await fetchPersonRoomHistory(searchParams)

            if (result) {
                setDataSource(result.list)
                setPagination(prev => ({
                    ...prev,
                    total: result.total,
                    current: result.pageIndex,
                    pageSize: result.pageSize,
                }))

                // 同步URL参数
                setUrlState({
                    roomNumber: values.roomNumber || undefined,
                    roomType: values.roomType || undefined,
                    roomLevel: values.roomLevel || [],
                    productionName: values.productionName || '',
                    personName: values.personName || '',
                    pageSize: result.pageSize,
                    pageIndex: result.pageIndex,
                })
            } else {
                setDataSource([])
            }
        } catch (error) {
            setDataSource([])
        } finally {
            setLoading(false)
        }
    }

    // 主分发
    const handleOperate = (type: string, data?: IPersonRoomHistoryItem) => {
        if (type === 'view') {
            setOperateData(data)
            setShow(true)
        } else if (type === 'evaluate') {
            setEvaluationData(data)
            setShowEvaluation(true)
        }
    }

    const handleOperateClose = () => {
        setOperateData(undefined)
        setShow(false)
    }

    // 评价相关处理函数
    const handleEvaluationClose = () => {
        setEvaluationData(undefined)
        setShowEvaluation(false)
    }

    const handleEvaluationSuccess = () => {
        handleEvaluationClose()
        // 可以选择刷新数据
        // handleSearch()
    }

    useEffect(() => {
        initParams()
    }, [])

    return (
        <Flex vertical gap={24}>
            <Search form={form} onSearch={handleSearch} onReset={() => handleSearch(1)} loading={loading} />
            <List
                data={dataSource}
                loading={loading}
                onOperate={handleOperate}
                onChange={handleSearch}
                pagination={pagination}
            />

            {/* 详情抽屉 */}
            <Detail open={show} data={operateData} onClose={handleOperateClose} />

            {/* 评价模态框 */}
            {evaluationData && (
                <PersonEvaluationModal
                    open={showEvaluation}
                    onCancel={handleEvaluationClose}
                    onSuccess={handleEvaluationSuccess}
                    productionId={evaluationData.productionId}
                    personId={evaluationData.personId}
                    personName={evaluationData.personName || ''}
                />
            )}
        </Flex>
    )
}

export default RoomHistory