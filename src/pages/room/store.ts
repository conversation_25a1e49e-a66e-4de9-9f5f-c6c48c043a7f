import { create } from 'zustand'
import { post, get as requestGet } from '../../utils/request'

// 房间上传API
export const prRoomUploadAPI = '/PrActors/UploadFile'

// 查询分组宿舍信息参数（对应API的ParaGroupRoomDto）
export interface IParaGroupRoomDto {
  communityName?: string // 小区名称
  startDate?: string // 起始入住日期
  endDate?: string // 截止入住日期
}

// 房间保洁记录（对应API的PrRoomClean）
export interface IPrRoomClean {
  id: number // ID
  roomId: number // 住宿Id
  beginTime: string // 开始日期
  endTime?: string // 结束日期
  status: number // 保洁状态 0待打扫，1打扫中，2打扫完成
  createTime: string // 创建时间
  updateTime: string // 更新时间
  lastModifier?: string // 最后修改人
}

// 项目人员住宿信息（对应API的PrProductionPersonRoom）
export interface IPrProductionPersonRoom {
  id: number // ID
  productionId: number // 关联项目ID
  personId: number // 人员Id
  roomId: number // 住宿Id
  checkinDate?: string // 入住日期
  createTime: string // 创建时间
  updateTime: string // 更新时间
  productionName: string // 短剧名称
  secondProductionCode?: string // 项目代号
  personName?: string // 人员姓名
  idNumber?: string // 身份证号
  phone?: string // 手机号码
  gender?: number // 性别 (1男，2女，3其他)
}

// 房间信息（对应API的RoomDto）
export interface IRoomDto {
  roomId: number // 房间Id
  buildingNumber: number // 楼栋号
  roomNumber: number // 房间号
  roomPosition?: string // 门号方位（上/下/中）
  roomType: number // 房型（1单床房、2双床房、3三床房）
  roomLevel: number // 等级（1优、2良、3一般）
  fullRoomNumber?: string // 完整房间号（小区+楼栋号+房间号）
  personRoom?: IPrProductionPersonRoom[] // 宿舍人员
  roomClean?: IPrRoomClean[] // 保洁记录
  feRoomCleanStatus?: number // 前端衍生字段：保洁状态（取roomClean第一条的status）
  status?: number
  personCount?: number // 实际入住人数（按personId去重）
  equipments?: string
}

// 宿舍分组（对应API的GroupCommunityDto）
export interface IGroupCommunityDto {
  id: number // ID
  communityName?: string // 小区
  communityArea?: string // 小区地址
  mapCoordinate?: string // 地图坐标（纬度,经度）
  photos?: string // 小区图片(逗号分割)
  photoList?: string[] // 照片列表（序列化处理）
  roomInfos?: IRoomDto[] // 房间信息
}

// 全量统计数据
export interface IRoomStatistics {
  totalRooms: number // 总房间数
  useCount: number
  totalCount: number
}

// 创建人员住宿信息参数（对应API的SavePersonRoomDto）
export interface ISavePersonRoomDto {
  productionId: number // 关联项目ID
  personId: number // 人员Id
  roomId: number // 住宿Id
  startDate: string // 入住起始时间
  endDate: string // 入住截止时间
}

// 更换人员住宿信息参数（对应API的ChangePersonRoomDto）
export interface IChangePersonRoomDto {
  personId: number // 人员Id
  roomId: number // 住宿Id
  changePersonId: number // 更换人员Id
  changeRoomId: number // 更换住宿Id
  startDate: string // 更换起始时间
}

// 批量分配人员住宿信息参数（对应API的BatchPersonRoomDto）
export interface IBatchPersonRoomDto {
  personId?: number[] // 人员Id数组
  productionId: number // 关联项目ID
  communityId?: string // 小区
  buildingNumber: number // 楼栋号
  startDate: string // 入住起始时间
  endDate: string // 入住截止时间
}

// 小区房间信息（对应API的CommunityRoomDto）
export interface ICommunityRoomDto {
  communityName?: string // 小区
  id: number // 房间Id
  buildingNumber: number // 楼栋号
  roomNumber: number // 房间号
  roomPosition?: string // 门号方位（上/下/中）
  fullRoomNumber?: string // 完整房间号（小区+楼栋号+房间号）
  status?: number // 房间状态（0禁用，1启用）
}

// API响应类型
export interface IWebResponseContent {
  status: boolean
  code?: string
  message?: string
  data?: any
}

// 小区信息（对应API的PrCommunity）
export interface IPrCommunity {
  id: number // ID
  communityName?: string // 小区名称
  communityArea?: string // 小区地址
  mapCoordinate?: string // 地图坐标（纬度,经度）
  photos?: string // 小区图片(逗号分割)
  photoList?: string[] // 照片列表（序列化处理）
  createTime: string // 创建时间
  updateTime: string // 更新时间
  creator?: string // 创建人
  lastModifier?: string // 最后修改人
}

// 房间管理分页查询参数（对应API的PagePrDormRoomDto）
export interface IRoomListSearchParams {
  communityName?: string // 小区名称
  communityArea?: string // 小区地址
  buildingNumber?: number // 楼栋号
  roomNumber?: number // 房间号
  roomPosition?: string // 门号方位（上/下/中）
  roomType?: number // 房型（1单床房、2双床房、3三床房）
  roomLevel?: number[] // 等级（1优、2良、3一般）
  status?: number // 房间状态（0禁用，1启用）
}

// 宿舍人员记录分页查询参数（对应API的PagePrProductionPersonRoomDto）
export interface IPersonRoomHistorySearchParams {
  pageIndex?: number // 页码
  pageSize?: number // 每页数量
  roomNumber?: number // 房间号
  roomType?: number // 房型（1单床房、2双床房）
  roomLevel?: number[] // 等级（1优、2良、3一般）
  productionName?: string // 短剧名称
  personName?: string // 人员姓名
}

// 宿舍人员记录详情（对应API的PrProductionPersonRoomDto）
export interface IPersonRoomHistoryItem {
  id: number // ID
  productionId: number // 关联项目ID
  personId: number // 人员Id
  roomId: number // 住宿Id
  checkinDate?: string // 入住日期
  createTime: string // 创建时间
  updateTime: string // 更新时间
  productionName: string // 短剧名称
  secondProductionCode?: string // 项目代号
  personName?: string // 人员姓名
  communityName?: string // 小区
  buildingNumber: number // 楼栋号
  roomNumber: number // 房间号
  roomPosition?: string // 门号方位（上/下/中）
  roomType: number // 房型（1单床房、2双床房）
  roomLevel: number // 等级（1优、2良、3一般）
  fullRoomNumber?: string // 完整房间号（小区+楼栋号+房间号）
}

// 房间信息（对应API的PrDormRoom）
export interface IRoomListItem {
  id: number // ID
  communityId: number // 小区Id
  community?: string // 小区
  buildingNumber: number // 楼栋号
  roomNumber: number // 房间号
  roomPosition?: string // 门号方位（上/下/中）
  status: number // 房间状态（0禁用，1启用）
  isDelete: boolean // 是否删除
  roomType: number // 房型（1单床房、2双床房、3三床房）
  roomLevel: number // 等级（1优、2良、3一般）
  mainDoorPassword?: string // 大门密码
  innerDoorPassword?: string // 内门密码
  broadbandAccount?: string // 宽带账号
  broadbandPassword?: string // 宽带密码
  equipments?: string // 配备（电视机、洗衣机等，用逗号分隔）
  mapCoordinate?: string // 地图坐标（纬度,经度）
  remark?: string // 备注
  photos?: string // 小区图片(逗号分割)
  photoList?: string[] // 照片列表（序列化处理）
  createTime: string // 创建时间
  updateTime: string // 更新时间
  fullRoomNumber?: string // 完整房间号（小区+楼栋号+房间号）
  creator?: string // 创建人
  lastModifier?: string // 最后修改人
  prCommunity?: IPrCommunity // 小区信息
}

export interface IRoomListStore {
  allCommunityRoom: Array<ICommunityRoomDto>
  groupList: IGroupCommunityDto[] // 存储宿舍群组列表
  roomStatistics: IRoomStatistics // 存储房间统计数据

  // 房间管理相关API
  fetchRoomList: (params: IRoomListSearchParams) => Promise<{
    groupList: IGroupCommunityDto[] // 存储宿舍群组列表
    roomStatistics: IRoomStatistics // 存储房间统计数据
  } | null>
  saveRoom: (roomData: IRoomListItem) => Promise<boolean>
  deleteRoom: (id: number) => Promise<boolean>

  // 宿舍分配相关API
  fetchGroupList: (params: IParaGroupRoomDto) => Promise<IGroupCommunityDto[] | null>
  savePersonRoom: (params: ISavePersonRoomDto) => Promise<boolean>
  deletePersonRoom: (params: ISavePersonRoomDto) => Promise<boolean>
  changePersonRoom: (params: IChangePersonRoomDto) => Promise<boolean>
  batchPersonRoom: (params: IBatchPersonRoomDto) => Promise<boolean>
  fetchAllCommunityRoom: () => Promise<ICommunityRoomDto[] | null>

  // 宿舍人员记录历史相关API
  fetchPersonRoomHistory: (params: IPersonRoomHistorySearchParams) => Promise<{
    list: IPersonRoomHistoryItem[]
    total: number
    pageIndex: number
    pageSize: number
  } | null>

  // 保洁相关API
  updateCleanStatus: (id: number, status: number) => Promise<boolean>
}

export default create<IRoomListStore>(set => ({
  allCommunityRoom: [],
  groupList: [],
  roomStatistics: {
    totalRooms: 0,
    useCount: 0,
    totalCount: 0,
  },
  // 获取房间列表
  fetchRoomList: async (params: IRoomListSearchParams) => {
    try {
      const { data, status } = await post<any, any>('/PrDormRoom/GetList', params)

      if (status && data?.dataList) {
        const dataList = Array.isArray(data.dataList) ? data.dataList : [data.dataList]

        // 为每个房间添加 personCount 和 feRoomCleanStatus 衍生字段
        const processedData = dataList.map((community: IGroupCommunityDto) => ({
          ...community,
          roomInfos: community.roomInfos?.map((room: IRoomDto) => {
            // 统计实际入住人数（按personId去重）
            let personCount = 0
            if (room.personRoom && room.personRoom.length > 0) {
              const uniquePersonIds = new Set<number>()
              room.personRoom.forEach(person => {
                if (person.personId) {
                  uniquePersonIds.add(person.personId)
                }
              })
              personCount = uniquePersonIds.size
            }

            // 获取保洁状态（取roomClean第一条的status）
            let feRoomCleanStatus: number | undefined
            if (room.roomClean && room.roomClean.length > 0) {
              feRoomCleanStatus = room.roomClean[0].status
            }

            return {
              ...room,
              personCount,
              feRoomCleanStatus,
            }
          }),
        }))

        // 计算统计数据
        let totalRooms = 0

        processedData.forEach((community: IGroupCommunityDto) => {
          if (community.roomInfos) {
            totalRooms += community.roomInfos.length
          }
        })

        return {
          groupList: processedData,
          roomStatistics: {
            totalRooms,
            useCount: 0,
            totalCount: 0,
          },
        }
      }

      return null
    } catch (error) {
      console.error('获取房间列表失败:', error)

      return null
    }
  },

  // 保存房间信息
  saveRoom: async (roomData: IRoomListItem) => {
    try {
      const { status } = await post<any, any>('/PrDormRoom/Save', roomData)

      return !!status
    } catch (error) {
      console.error('保存房间信息失败:', error)

      return false
    }
  },

  // 删除房间
  deleteRoom: async (id: number) => {
    try {
      const { status } = await requestGet<any, any>(`/PrDormRoom/Delete?id=${id}`)

      return !!status
    } catch (error) {
      console.error('删除房间失败:', error)

      return false
    }
  },

  // 获取宿舍群组列表
  fetchGroupList: async (params: IParaGroupRoomDto) => {
    try {
      const { data, status } = await post<any, any>('/PrDormRoom/GetGroupList', params)

      if (status && data?.dataList) {
        const dataList = Array.isArray(data.dataList) ? data.dataList : [data.dataList]

        // 为每个房间添加 personCount 和 feRoomCleanStatus 衍生字段
        const processedData = dataList.map((community: IGroupCommunityDto) => ({
          ...community,
          roomInfos: community.roomInfos?.map((room: IRoomDto) => {
            // 统计实际入住人数（按personId去重）
            let personCount = 0
            if (room.personRoom && room.personRoom.length > 0) {
              const uniquePersonIds = new Set<number>()
              room.personRoom.forEach(person => {
                if (person.personId) {
                  uniquePersonIds.add(person.personId)
                }
              })
              personCount = uniquePersonIds.size
            }

            // 获取保洁状态（取roomClean第一条的status）
            let feRoomCleanStatus: number | undefined
            if (room.roomClean && room.roomClean.length > 0) {
              feRoomCleanStatus = room.roomClean[0].status
            }

            return {
              ...room,
              personCount,
              feRoomCleanStatus,
            }
          }),
        }))

        // 计算统计数据
        let totalRooms = 0

        processedData.forEach((community: IGroupCommunityDto) => {
          if (community.roomInfos) {
            totalRooms += community.roomInfos.length
          }
        })

        // 更新 store 中的数据
        set({
          groupList: processedData,
          roomStatistics: {
            totalRooms,
            useCount: data.useCount || 0,
            totalCount: data.totalCount || 0,
          },
        })

        return processedData
      }

      // 如果没有数据，重置统计数据
      set({
        groupList: [],
        roomStatistics: {
          totalRooms: 0,
          useCount: 0,
          totalCount: 0,
        },
      })

      return null
    } catch (error) {
      console.error('获取宿舍群组列表失败:', error)

      // 发生错误时，重置统计数据
      set({
        groupList: [],
        roomStatistics: {
          totalRooms: 0,
          useCount: 0,
          totalCount: 0,
        },
      })

      return null
    }
  },

  // 宿舍分配人员
  savePersonRoom: async (params: ISavePersonRoomDto) => {
    try {
      const { status } = await post<any, any>('/PrDormRoom/SavePersonRoom', params)

      return !!status
    } catch (error) {
      console.error('宿舍分配人员失败:', error)

      return false
    }
  },

  // 宿舍删除人员
  deletePersonRoom: async (params: ISavePersonRoomDto) => {
    try {
      const { status } = await post<any, any>('/PrDormRoom/DeletePersonRoom', params)

      return !!status
    } catch (error) {
      console.error('宿舍删除人员失败:', error)

      return false
    }
  },

  // 更换宿舍分配
  changePersonRoom: async (params: IChangePersonRoomDto) => {
    try {
      const { status } = await post<any, any>('/PrDormRoom/ChangePersonRoom', params)

      return !!status
    } catch (error) {
      console.error('更换宿舍分配失败:', error)

      return false
    }
  },

  // 批量排房
  batchPersonRoom: async (params: IBatchPersonRoomDto) => {
    try {
      const { status } = await post<any, any>('/PrDormRoom/BatchPersonRoom', params)

      return !!status
    } catch (error) {
      console.error('批量排房失败:', error)

      return false
    }
  },

  // 获取所有小区房间名称
  fetchAllCommunityRoom: async () => {
    try {
      const { data, status } = await requestGet<any, any>('/PrDormRoom/AllCommunityRoom')

      if (status && data?.dataList) {
        let arr = Array.isArray(data.dataList) ? data.dataList : [data.dataList]

        arr = arr.map((item: any) => {
          item.labelStr = `${item.communityName}_${item.fullRoomNumber}`

          return item
        })
        set({ allCommunityRoom: arr })

        return arr
      }

      return []
    } catch (error) {
      console.error('获取所有小区房间名称失败:', error)

      return []
    }
  },

  // 获取宿舍人员记录历史
  fetchPersonRoomHistory: async (params: IPersonRoomHistorySearchParams) => {
    try {
      const { data, status } = await post<any, any>('/PrDormRoom/GetPersonRoomList', params)

      if (status && data) {
        const list = Array.isArray(data.dataList) ? data.dataList : data.dataList ? [data.dataList] : []

        return {
          list,
          total: data.totalCount || 0,
          pageIndex: params.pageIndex || 1,
          pageSize: params.pageSize || 10,
        }
      }

      return {
        list: [],
        total: 0,
        pageIndex: params.pageIndex || 1,
        pageSize: params.pageSize || 10,
      }
    } catch (error) {
      console.error('获取宿舍人员记录历史失败:', error)

      return {
        list: [],
        total: 0,
        pageIndex: params.pageIndex || 1,
        pageSize: params.pageSize || 10,
      }
    }
  },

  // 保洁房间状态变更
  updateCleanStatus: async (id: number, status: number) => {
    try {
      const { status: responseStatus } = await post<any, any>(`/PrDormRoom/UpdateCleanStatus?id=${id}&status=${status}`, {})

      return !!responseStatus
    } catch (error) {
      console.error('保洁房间状态变更失败:', error)

      return false
    }
  },

  // 计算房间统计数据
  calculateRoomStatistics: (groupList: IGroupCommunityDto[]) => {
    let totalRooms = 0

    groupList.forEach(community => {
      if (community.roomInfos) {
        totalRooms += community.roomInfos.length
      }
    })

    return {
      totalRooms,
      useCount: 0,
      totalCount: 0,
    }
  },
}))
