import { ROOM_LEVEL_OPTIONS, ROOM_STATUS_OPTIONS, ROOM_TYPE_OPTIONS } from '@/consts'
import useCommunityStore from '@/pages/system/community/store'
import filterMatch from '@/utils/filterMatch'
import { useDebounceFn } from 'ahooks'
import { Button, Form, InputNumber, Segmented, Select, Space } from 'antd'
import React, { useEffect } from 'react'

interface IRoomSearchProps {
  form: any
  loading?: boolean
  onSearch: () => void
}

const RoomSearch: React.FC<IRoomSearchProps> = ({ form, loading = false, onSearch }) => {
  const { run: onSearchDebounce } = useDebounceFn(() => onSearch(), { wait: 500 })
  const { allCommunityOptions, getAllCommunityNames } = useCommunityStore()

  useEffect(() => {
    if (allCommunityOptions.length === 0) {
      getAllCommunityNames()
    }
  }, [])

  return (
    <Form
      form={form}
      initialValues={{ status: 1 }}
      layout="vertical"
      onValuesChange={onSearchDebounce}
      colon={false}
      className="search-form">
      <Space size={24} wrap>
        <Form.Item name="communityName" label="小区">
          <Select
            className="w200"
            placeholder="默认全选"
            options={allCommunityOptions}
            showSearch
            allowClear
            fieldNames={{ label: 'communityName', value: 'communityName' }}
            filterOption={(inputValue, option) => {
              const result = filterMatch(inputValue, option?.communityName || '')

              return Boolean(result)
            }}
          />
        </Form.Item>
        <Form.Item name="roomNumber" label="房间号">
          <InputNumber className="w200" placeholder="例如：1101" min={1} precision={0} />
        </Form.Item>
        <Form.Item name="buildingNumber" label="楼栋号">
          <InputNumber className="w200" placeholder="例如：3" min={1} precision={0} />
        </Form.Item>
        <Form.Item name="roomLevel" label="等级">
          <Select
            className="w200"
            mode="multiple"
            placeholder="默认全选"
            allowClear
            options={ROOM_LEVEL_OPTIONS}
            maxTagCount={1}
          />
        </Form.Item>
        <Form.Item name="roomType" label="床位">
          <Segmented className="w300" block options={[{ label: '全部', value: undefined }, ...ROOM_TYPE_OPTIONS]} />
        </Form.Item>
        <Form.Item name="status" label="状态">
          <Segmented className="w200" block options={[{ label: '全部', value: undefined }, ...ROOM_STATUS_OPTIONS]} />
        </Form.Item>

        <Form.Item label=" ">
          <Button type="primary" onClick={onSearchDebounce} loading={loading}>
            查询
          </Button>
        </Form.Item>
      </Space>
    </Form>
  )
}

export default RoomSearch
