import AmapDisplay from '@/components/AmapDisplay'
import EnvImage from '@/components/EnvImage'
import RoomEquipment from '@/components/RoomEquipment'
import { ROOM_LEVEL_CONFIG, ROOM_STATUS_CONFIG, ROOM_TYPE_CONFIG } from '@/consts'
import { envUrl } from '@/utils/request'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { Badge, Button, Card, Descriptions, Drawer, Flex, Image, Popconfirm, Space, Tag, Typography } from 'antd'
import React from 'react'
import { IRoomListItem } from '../../store'

const { Text } = Typography

interface IRoomDetailProps {
  open: boolean
  room?: IRoomListItem | null
  onClose: () => void
  onEdit: (room: IRoomListItem) => void
  onDelete: (room: IRoomListItem) => void
  onRefresh?: () => void
}

const RoomDetail: React.FC<IRoomDetailProps> = ({ open, room, onClose, onEdit, onDelete }) => {
  if (!room) {
    return null
  }

  const handleEdit = () => {
    onEdit(room)
  }

  const handleDelete = () => {
    onDelete(room)
  }

  // 解析配备设施
  const equipmentList = room.equipments ? room.equipments.split(',').filter(Boolean) : []

  // 房间状态配置
  const statusConfig = ROOM_STATUS_CONFIG[room.status]
  const typeConfig = ROOM_TYPE_CONFIG[room.roomType]
  const levelConfig = ROOM_LEVEL_CONFIG[room.roomLevel]

  return (
    <Drawer
      title="详情"
      open={open}
      onClose={onClose}
      width={1000}
      extra={
        <Space.Compact>
          <Popconfirm
            title="警告"
            description={'确定要删除该房间吗？'}
            onConfirm={handleDelete}
            okText="确定删除"
            cancelText="取消">
            <Button type="default" shape="round" icon={<DeleteOutlined />} className="text-primary">
              删除
            </Button>
          </Popconfirm>
          <Button type="default" shape="round" icon={<EditOutlined />} className="text-primary" onClick={handleEdit}>
            编辑
          </Button>
        </Space.Compact>
      }>
      <Flex vertical gap={24}>
        <Descriptions title="1、基本信息" column={2} size="small" bordered>
          <Descriptions.Item label="房间号">
            <Typography.Text strong>{room.roomNumber}</Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="楼栋号">
            <Typography.Text strong>{room.buildingNumber}</Typography.Text>
          </Descriptions.Item>

          <Descriptions.Item label="楼上/楼下">{room.roomPosition || '-'}</Descriptions.Item>
          <Descriptions.Item label="房型">{typeConfig?.label}</Descriptions.Item>
          <Descriptions.Item label="等级">{levelConfig?.label}</Descriptions.Item>
          <Descriptions.Item label="状态">
            <Badge status={room.status ? 'success' : 'error'} text={room.status ? '启用中' : '已禁用'} />
          </Descriptions.Item>
          <Descriptions.Item label="小区" span={1}>
            {room.prCommunity?.communityName || room.community || '暂无'}
          </Descriptions.Item>
          <Descriptions.Item label="详细地址" span={1}>
            {room.prCommunity?.communityArea || '暂无'}
          </Descriptions.Item>
          <Descriptions.Item label="配套" span={2}>
            {equipmentList.length > 0 ? (
              <Space size={0} wrap>
                {equipmentList.sort().map((equipment, index) => (
                  <Tag key={equipment}>
                    <Space>
                      <RoomEquipment name={equipment} />
                      <span>{equipment}</span>
                    </Space>
                  </Tag>
                ))}
              </Space>
            ) : (
              '暂无'
            )}
          </Descriptions.Item>

          {!!room.remark && (
            <Descriptions.Item label="备注" span={2}>
              <Typography.Text mark>{room.remark}</Typography.Text>
            </Descriptions.Item>
          )}
        </Descriptions>
        <Descriptions title="2、账号密码" column={2} size="small" bordered>
          <Descriptions.Item label="大门密码">
            <Text copyable={!!room.mainDoorPassword}>{room.mainDoorPassword || '暂无'}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="内门密码">
            <Text copyable={!!room.innerDoorPassword}>{room.innerDoorPassword || '暂无'}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="宽带账号">
            <Text copyable={!!room.broadbandAccount}>{room.broadbandAccount || '暂无'}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="宽带密码">
            <Text copyable={!!room.broadbandPassword}>{room.broadbandPassword || '暂无'}</Text>
          </Descriptions.Item>
        </Descriptions>

        {/* 房间照片 */}
        {room.photoList && room.photoList.length > 0 && (
          <Flex vertical gap={6}>
            <Text strong>3、房间照片</Text>
            <Card size="small">
              <Image.PreviewGroup items={room.photoList.map(url => `${envUrl}${url}`)}>
                <Space size={8} wrap>
                  {room.photoList.map(photo => (
                    <EnvImage
                      key={photo}
                      width={90}
                      height={120}
                      className="radius img-cover"
                      preview={{ getContainer: document.body }}
                      src={photo}
                    />
                  ))}
                </Space>
              </Image.PreviewGroup>
            </Card>
          </Flex>
        )}

        {/* 地图位置 */}
        {room?.prCommunity?.mapCoordinate ? (
          <AmapDisplay
            showLocationInfo={false}
            dest={room?.prCommunity?.mapCoordinate || ''}
            destName={room.prCommunity?.communityName || ''}
            height={400}
          />
        ) : null}
      </Flex>
    </Drawer>
  )
}

export default RoomDetail
