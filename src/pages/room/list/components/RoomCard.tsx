import RoomEquipment from '@/components/RoomEquipment'
import { LikeOutlined } from '@ant-design/icons'
import { Badge, Card, Flex, Space, Tag, Tooltip, Typography } from 'antd'
import React from 'react'
import { ROOM_LEVEL_CONFIG, ROOM_TYPE_CONFIG, RoomLevel } from '../../../../consts'
import { IGroupCommunityDto, IRoomDto } from '../../store'

const { Text } = Typography

interface IRoomCardProps {
  room: IRoomDto
  community: IGroupCommunityDto
  onOperate: (type: string, data?: IRoomDto) => void
}

const RoomCard: React.FC<IRoomCardProps> = ({ room, community, onOperate }) => {
  const typeConfig = ROOM_TYPE_CONFIG[room.roomType as keyof typeof ROOM_TYPE_CONFIG]
  const levelConfig = ROOM_LEVEL_CONFIG[room.roomLevel as keyof typeof ROOM_LEVEL_CONFIG]
  const equipments = room.equipments?.split(',').sort()

  return (
    <Card
      size="small"
      className={`hover-move ${room.status === 0 ? 'bg-secondary' : ''}`}
      onClick={() => onOperate('view', room)}>
      <Flex vertical gap={12}>
        {/* 房间号和标签 */}
        <Flex justify="space-between" align="center">
          <Text strong className="fs-lg">
            {room.fullRoomNumber || `${room.buildingNumber}-${room.roomNumber}`}
          </Text>
          <Space size={0}>
            <Tag className={RoomLevel.EXCELLENT === room.roomLevel ? 'text-primary' : ''}>
              <Space size={4}>
                {RoomLevel.EXCELLENT === room.roomLevel && <LikeOutlined />}
                {levelConfig.label}
              </Space>
            </Tag>
            <Tag className="no-margin">{typeConfig.label}</Tag>
          </Space>
        </Flex>

        {/* 房间类型和等级标签 */}
        <Flex justify="space-between" wrap>
          {room.status === 0 ? <Badge status="error" text="禁用" /> : <Badge status="success" text="启用" />}
          {equipments && (
            <Tooltip title={equipments.join('、')}>
              <Space wrap size={8}>
                {equipments.map(equipment => (
                  <RoomEquipment name={equipment} />
                ))}
              </Space>
            </Tooltip>
          )}
        </Flex>
      </Flex>
    </Card>
  )
}

export default RoomCard
