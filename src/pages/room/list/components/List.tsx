import { ROOM_LEVEL_CONFIG, ROOM_STATUS_CONFIG, ROOM_TYPE_CONFIG } from '@/consts'
import { DATE_FORMAT_BASE } from '@/consts/date'
import { LikeOutlined, PlusOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Badge, Button, Card, Divider, Flex, List, Space, Tabs, Tag, Typography } from 'antd'
import dayjs from 'dayjs'
import React from 'react'
import { IRoomListItem } from '../../store'

const { Text } = Typography

interface IRoomListProps {
  data: IRoomListItem[]
  loading: boolean
  onOperate: (type: string, data?: IRoomListItem) => void
}

const RoomList: React.FC<IRoomListProps> = ({ data, loading, onOperate }) => {
  const handleCardClick = (room: IRoomListItem, event: React.MouseEvent) => {
    onOperate && onOperate('view', room)
  }

  // 渲染房间卡片
  const renderRoomCard = (room: IRoomListItem) => {
    // 处理配备设施展示
    const equipmentList = room.equipments ? room.equipments.split(',').filter(Boolean) : []

    // 房间状态配置
    const statusConfig = ROOM_STATUS_CONFIG[room.status]
    const typeConfig = ROOM_TYPE_CONFIG[room.roomType]
    const levelConfig = ROOM_LEVEL_CONFIG[room.roomLevel]

    return (
      <List.Item>
        <Card hoverable size="small" className="full-h hover-move" onClick={e => handleCardClick(room, e)}>
          <Flex justify="space-between" align="center">
            <Flex flex={1} vertical gap={16}>
              <Space size={2} split={<Divider type="vertical" />}>
                <Typography.Text strong className="fs-lg">
                  {room.fullRoomNumber ? room.fullRoomNumber : `${room.buildingNumber}-${room.roomNumber}`}{' '}
                </Typography.Text>
                <span>{room?.prCommunity?.communityName || ''}</span>
                <span>{typeConfig?.label}</span>
                {room.roomLevel === 1 ? (
                  <Space className="text-primary">
                    <LikeOutlined />
                    {levelConfig?.label}
                  </Space>
                ) : (
                  levelConfig?.label
                )}
                {equipmentList.length > 0 && (
                  <div>
                    {equipmentList.map((equipment, index) => (
                      <Tag key={index} className="text-success">
                        {equipment.trim()}
                      </Tag>
                    ))}
                  </div>
                )}
              </Space>

              <Typography.Text>{room.prCommunity?.communityArea}</Typography.Text>
              <Space size={4} split={<Divider type="vertical" />}>
                <Badge
                  status={room.status === 1 ? 'success' : 'error'}
                  text={room.status === 1 ? '启用中' : '已禁用'}
                />
                <Dict
                  title={<Typography.Text>{room.creator}</Typography.Text>}
                  value={
                    <Typography.Text type="secondary">
                      最近更新于 {dayjs(room.updateTime).format(DATE_FORMAT_BASE)}
                    </Typography.Text>
                  }></Dict>
              </Space>
            </Flex>
          </Flex>
        </Card>
      </List.Item>
    )
  }

  const groupedData = data.reduce((acc, room) => {
    const communityName = room.prCommunity?.communityName || '其他'
    if (!acc[communityName]) {
      acc[communityName] = []
    }
    acc[communityName].push(room)
    return acc
  }, {} as Record<string, IRoomListItem[]>)

  const tabItems = Object.keys(groupedData).map(communityName => ({
    key: communityName,
    label: communityName,
    children: (
      <List
        loading={loading}
        dataSource={groupedData[communityName]}
        renderItem={renderRoomCard}
        split={false}
        rowKey="id"
        className="list-sm"
      />
    ),
  }))

  return (
    <Flex vertical>
      <ListHeader title="" total={data?.length} unitText="套">
        <Button type="primary" ghost icon={<PlusOutlined />} onClick={() => onOperate && onOperate('create')}>
          添加房间
        </Button>
      </ListHeader>
      <Tabs items={tabItems} />
    </Flex>
  )
}

export default RoomList
