import Upload from '@/components/Upload'
import { ROOM_LEVEL_OPTIONS, ROOM_POSITION_OPTIONS, ROOM_STATUS_OPTIONS, ROOM_TYPE_OPTIONS } from '@/consts/index'
import useCommunityStore from '@/pages/system/community/store'
import filterMatch from '@/utils/filterMatch'
import { Fieldset } from '@fe/rockrose'
import { Button, Col, Drawer, Flex, Form, Input, InputNumber, Row, Select } from 'antd'
import React, { useEffect } from 'react'
import { IRoomListItem } from '../../store'

const { TextArea } = Input

interface IRoomFormProps {
  open: boolean
  data?: IRoomListItem | null
  onCancel: () => void
  onSubmit: (values: IRoomListItem) => void
}

const RoomForm: React.FC<IRoomFormProps> = ({ open, data, onCancel, onSubmit }) => {
  const [form] = Form.useForm()
  const isEdit = !!data?.id
  const { allCommunityOptions, getAllCommunityNames } = useCommunityStore()

  useEffect(() => {
    if (open) {
      getAllCommunityNames()
      if (data) {
        // 编辑模式，填充表单数据
        form.setFieldsValue({
          ...data,
          equipments: data.equipments ? data.equipments.split(',').filter(Boolean) : [],
          photos: data.photoList || [],
        })
      } else {
        // 新增模式，重置表单并设置默认值
        form.resetFields()
        form.setFieldsValue({
          status: 1, // 默认启用
          roomType: 1, // 默认单床房
          roomLevel: 1, // 默认优等级
          equipments: ['洗衣机', '电视机', '冰箱'],
          photoList: [],
        })
      }
    }
  }, [open, data, form])

  const handleCancel = () => {
    form.resetFields()
    onCancel()
  }

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()

      const submitData: IRoomListItem = {
        ...data, // 保留原有数据
        id: data?.id || 0,
        communityId: values?.communityId || 0,
        buildingNumber: values.buildingNumber,
        roomNumber: values.roomNumber,
        roomPosition: values.roomPosition || '',
        status: values.status,
        roomType: values.roomType,
        roomLevel: values.roomLevel,
        mainDoorPassword: values.mainDoorPassword || '',
        innerDoorPassword: values.innerDoorPassword || '',
        broadbandAccount: values.broadbandAccount || '',
        broadbandPassword: values.broadbandPassword || '',
        equipments: Array.isArray(values.equipments) ? values.equipments.join(',') : values.equipments || '',
        mapCoordinate: values.mapCoordinate || '',
        remark: values.remark || '',
        photos: Array.isArray(values.photoList) ? values.photoList.join(',') : '',
      }
      delete submitData.prCommunity
      onSubmit(submitData)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  return (
    <Drawer
      title={isEdit ? '编辑房间' : '添加房间'}
      open={open}
      onClose={handleCancel}
      width={800}
      destroyOnClose={true}
      extra={
        <Button type="primary" onClick={handleSubmit}>
          立即保存
        </Button>
      }>
      <Form form={form} layout="horizontal" colon={false} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <Flex vertical gap={24}>
          <Fieldset title="1、基础信息" direction="vertical" className="no-padding-b">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="communityId" label="小区" rules={[{ required: true, message: '请选择小区' }]}>
                  <Select
                    placeholder="请选择小区"
                    options={allCommunityOptions}
                    showSearch
                    fieldNames={{ label: 'communityName', value: 'id' }}
                    filterOption={(inputValue, option) => {
                      const result = filterMatch(inputValue, option?.communityName || '')

                      return Boolean(result)
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="roomNumber" label="房间号" rules={[{ required: true, message: '请输入房间号' }]}>
                  <InputNumber
                    placeholder="请输入房间号"
                    min={1}
                    precision={0}
                    className="full-h"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="buildingNumber" label="楼栋号" rules={[{ required: true, message: '请输入楼栋号' }]}>
                  <InputNumber
                    placeholder="请输入楼栋号"
                    min={1}
                    precision={0}
                    className="full-h"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="roomType" label="房型" rules={[{ required: true, message: '请选择房型' }]}>
                  <Select placeholder="选择房型" options={ROOM_TYPE_OPTIONS} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="roomPosition" label="楼上楼下">
                  <Select placeholder="选择楼上/楼下" allowClear options={ROOM_POSITION_OPTIONS} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="roomLevel" label="等级" rules={[{ required: true, message: '请选择房间等级' }]}>
                  <Select placeholder="选择房间等级" options={ROOM_LEVEL_OPTIONS} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="status"
                  label="状态"
                  initialValue={1}
                  rules={[{ required: true, message: '请选择房间状态' }]}>
                  <Select placeholder="选择房间状态" options={ROOM_STATUS_OPTIONS} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item name="equipments" label="配备" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                  <Select
                    mode="tags"
                    placeholder="请输入配备设施，如：电视机、洗衣机等"
                    style={{ width: '100%' }}
                    tokenSeparators={[',']}
                    options={[
                      { label: '洗衣机', value: '洗衣机' },
                      { label: '电视机', value: '电视机' },
                      { label: '冰箱', value: '冰箱' },
                      { label: '办公桌', value: '办公桌' },
                      { label: '热水器', value: '热水器' },
                      { label: '热水壶', value: '热水壶' },
                      { label: '沙发', value: '沙发' },
                    ]}
                  />
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item name="remark" label="备注" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
                  <TextArea rows={3} placeholder="请输入备注信息" />
                </Form.Item>
              </Col>
            </Row>
          </Fieldset>

          <Fieldset title="2、账号密码" direction="vertical" className="no-padding-b">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="mainDoorPassword" label="大门密码">
                  <Input placeholder="请输入大门密码" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="innerDoorPassword" label="内门密码">
                  <Input placeholder="请输入内门密码" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item name="broadbandAccount" label="宽带账号">
                  <Input placeholder="请输入宽带账号" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="broadbandPassword" label="宽带密码">
                  <Input placeholder="请输入宽带密码" />
                </Form.Item>
              </Col>
            </Row>
          </Fieldset>
          <Fieldset title="3、照片上传" direction="vertical" className="no-padding-b">
            <Form.Item name="photoList" label="房间照片" labelCol={{ span: 3 }} wrapperCol={{ span: 21 }}>
              <Upload
                action="/PrActors/UploadFile"
                type="image"
                multiple
                maxCount={10}
                accept=".png,.jpg,.jpeg,.gif,.webp"
              />
            </Form.Item>
          </Fieldset>
        </Flex>
      </Form>
    </Drawer>
  )
}

export default RoomForm
