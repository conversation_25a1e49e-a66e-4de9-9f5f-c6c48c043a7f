import useSyncParams from '@/hooks/useSyncParams'
import { PlusOutlined } from '@ant-design/icons'
import { Button, Empty, Flex, Form, message, Spin, Tabs } from 'antd'
import React, { useEffect, useState } from 'react'
import useRoomListStore, { IGroupCommunityDto, IRoomDto, IRoomListItem, IRoomListSearchParams } from '../store'
import RoomForm from './components/Add'
import CommunityRoomList from './components/CommunityRoomList'
import RoomDetail from './components/Detail'
import RoomSearch from './components/Search'

const RoomManagement: React.FC = () => {
  const [urlState, setUrlState] = useSyncParams<Omit<IRoomListSearchParams, 'pageIndex' | 'pageSize'>>()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [groupList, setGroupList] = useState<IGroupCommunityDto[]>([])
  const [roomStatistics, setRoomStatistics] = useState({
    totalRooms: 0,
    occupiedRooms: 0,
    totalPersons: 0,
    occupancyRate: 0,
    useCount: 0,
    totalBeds: 0,
  })

  const [show, setShow] = useState(false)
  const [operateData, setOperateData] = useState<IRoomListItem | null>(null)
  const [operateType, setOperateType] = useState<string>('')

  const { fetchRoomList, saveRoom, deleteRoom } = useRoomListStore()

  // 初始化参数
  const initParams = () => {
    handleSearch()
  }

  // 搜索处理
  const handleSearch = async (formValues = form.getFieldsValue()) => {
    setLoading(true)

    try {
      const params: IRoomListSearchParams = {
        ...formValues,
      }

      const result = await fetchRoomList(params)

      if (result) {
        setGroupList(result.groupList)
        setRoomStatistics(result.roomStatistics)

        // 同步URL参数
        setUrlState({
          ...formValues,
        })
      } else {
        setGroupList([])
        setRoomStatistics({
          totalRooms: 0,
          occupiedRooms: 0,
          totalPersons: 0,
          occupancyRate: 0,
          useCount: 0,
          totalBeds: 0,
        })
      }
    } catch (error) {
      setGroupList([])
      setRoomStatistics({
        totalRooms: 0,
        occupiedRooms: 0,
        totalPersons: 0,
        occupancyRate: 0,
        useCount: 0,
        totalBeds: 0,
      })
      message.error('获取房间列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 删除处理
  const handleDelete = async (data: IRoomListItem) => {
    try {
      const result = await deleteRoom(data.id)

      if (result) {
        message.success('删除成功')
        await handleSearch()
        handleOperateClose(false)
      } else {
        message.error('删除失败')
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 操作处理
  const handleOperate = (type: string, data?: IRoomDto | any) => {
    if (type === 'delete') {
      data && handleDelete(data)
    } else if (['create', 'edit', 'view'].includes(type)) {
      setOperateData(data || null)
      setOperateType(type)
      setShow(true)
    }
  }

  // 关闭操作弹窗
  const handleOperateClose = (fresh = true) => {
    setOperateData(null)
    setOperateType('')
    setShow(false)
    if (fresh) {
      handleSearch()
    }
  }

  // 从详情页编辑
  const handleEditFromDetail = (room: IRoomListItem) => {
    setOperateData(room)
    setOperateType('edit')
    // 保持详情页打开状态，只是切换操作类型
  }

  // 从详情页删除
  const handleDeleteFromDetail = async (room: IRoomListItem) => {
    await handleDelete(room)
  }

  // 从详情页刷新
  const handleRefreshFromDetail = () => {
    handleSearch()
  }

  // 表单提交
  const handleFormSubmit = async (values: IRoomListItem) => {
    try {
      const result = await saveRoom(values)

      if (result) {
        message.success(operateType === 'edit' ? '更新成功' : '新增成功')
        handleOperateClose()
        await handleSearch()
      } else {
        message.error('操作失败')
      }
    } catch (error) {
      message.error('操作失败')
    }
  }

  useEffect(() => {
    initParams()
  }, [])

  return (
    <Flex vertical gap={24}>
      <RoomSearch form={form} onSearch={handleSearch} />
      <Spin spinning={loading}>
        {groupList.length > 0 ? (
          <Tabs
            indicator={{ size: 42 }}
            tabBarExtraContent={
              <Button type="primary" ghost icon={<PlusOutlined />} onClick={() => handleOperate('create')}>
                添加房间
              </Button>
            }
            items={groupList.map(item => ({
              key: `${item.id}`,
              label: item.communityName,
              children: <CommunityRoomList key={item.id} community={item} onOperate={handleOperate} />,
            }))}
          />
        ) : (
          <Empty />
        )}
      </Spin>

      {/* 房间详情抽屉 */}
      <RoomDetail
        open={show && operateType === 'view'}
        room={operateData}
        onClose={handleOperateClose}
        onEdit={handleEditFromDetail}
        onDelete={handleDeleteFromDetail}
        onRefresh={handleRefreshFromDetail}
      />

      {/* 房间表单抽屉 */}
      <RoomForm
        open={show && ['edit', 'create'].includes(operateType)}
        data={operateData}
        onCancel={handleOperateClose}
        onSubmit={handleFormSubmit}
      />
    </Flex>
  )
}

export default RoomManagement
