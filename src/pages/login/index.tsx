import React, { useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Card, Spin } from 'antd'
import useStore from '@/store'
import { getTokenCookie } from '@/utils/cookie'
import styles from './index.scss'

// 作为登录中转
const Login: React.FC = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const { checkAuthCode, checkAuthDingCode, fetchAuthUrl, fetchAuthUrlKeycloak, handleLogin } = useStore()

  const code = searchParams.get('code')
  const sessionState = searchParams.get('session_state')
  const token = searchParams.get('token') || ''
  const authType = searchParams.get('authType') || ''

  const { origin } = window.location

  useEffect(() => {
    initLogin()
  }, [])

  const initLogin = async () => {
    if (authType) {
      localStorage.setItem('AUTH_TYPE', authType)
    }

    const sessionUrl = localStorage.getItem('session-url') || ''
    const accessToken = getTokenCookie()

    if (authType === 'DINGTALK') {
      if (token) {
        localStorage.setItem('ACCESS_TOKEN', decodeURIComponent(token) || accessToken)

        const result = await checkAuthDingCode({
          access_token: token,
          sessionState: '',
        })

        if (result) {
          navigate(sessionUrl.includes('login') ? '/' : sessionUrl, { replace: true })
        }
      } else {
        fetchAuthUrl()
      }
    } else if (code) {
      const result = await checkAuthCode({
        Code: code,
        SessionState: sessionState ?? '',
        redirectUri: `${origin}/login`,
      })

      if (result) {
        navigate(sessionUrl.includes('login') ? '/' : sessionUrl, { replace: true })
      }
    } else {
      handleLogin()
    }
  }

  return (
    <Card bordered={false}>
      <div className={styles.container}>
        <Spin tip="欢迎使用，系统登录中..." size="large" />
      </div>
    </Card>
  )
}

export default Login
