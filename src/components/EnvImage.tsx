import { Image } from 'antd'
import React, { useMemo } from 'react'
import { envUrl } from '../utils/request'

interface EnvImageProps {
  src: string
  scaleSize?: number
  [key: string]: any
}

const EnvImage: React.FC<EnvImageProps> = ({ src, scaleSize, ...rest }) => {
  const lastUrl = useMemo(() => {
    let isPro = location.host.includes('changdu.vip')
    if (scaleSize && isPro) {
      let cacheSrc = src.replace(envUrl, '')
      cacheSrc = cacheSrc.startsWith('http') ? src : 'https://prg.51changdu.com' + src
      return cacheSrc + `?imageMogr2/thumbnail/!${scaleSize}p`
    }
    return src.startsWith('http') ? src : envUrl + src
  }, [src, scaleSize])

  return <Image {...rest} src={lastUrl} />
}

export default EnvImage
