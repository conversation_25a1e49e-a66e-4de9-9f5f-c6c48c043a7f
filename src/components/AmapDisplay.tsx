import { EnvironmentOutlined } from '@ant-design/icons'
import { Button, Card, Space, Typography } from 'antd'
import React, { useMemo } from 'react'
import { AMAP_CONFIG } from '../consts'
import styles from './AmapDisplay.scss'

interface IAmapDisplayProps {
  /** 经纬度坐标，格式：经度,纬度 */
  dest: string

  /** 地点名称 */
  destName: string

  /** 是否隐藏路线图标，默认为 true */
  hideRouteIcon?: boolean

  /** iframe 宽度，默认为 '100%' */
  width?: string | number

  /** iframe 高度，默认为 300 */
  height?: string | number

  /** 是否显示地点信息，默认为 true */
  showLocationInfo?: boolean

  /** 自定义样式 */
  style?: React.CSSProperties

  /** 自定义类名 */
  className?: string
}

const AmapDisplay: React.FC<IAmapDisplayProps> = ({
  dest,
  destName,
  hideRouteIcon = true,
  width = '100%',
  height = 300,
  showLocationInfo = true,
  style,
  className,
}) => {
  // 构建高德地图 iframe URL
  const mapUrl = useMemo(() => {
    const baseUrl = 'https://m.amap.com/navi/'
    const params = new URLSearchParams({
      dest,
      destName,
      key: AMAP_CONFIG.KEY,
      jscode: AMAP_CONFIG.JSCODE,
    })

    if (hideRouteIcon) {
      params.append('hideRouteIcon', '1')
    }

    return `${baseUrl}?${params.toString()}`
  }, [dest, destName, hideRouteIcon])

  // 在新窗口中打开地图
  const handleOpenInNewWindow = () => {
    window.open(mapUrl, '_blank')
  }

  // 解析坐标
  const coordinates = useMemo(() => {
    const [lng, lat] = dest.split(',')

    return { lng: parseFloat(lng), lat: parseFloat(lat) }
  }, [dest])

  return (
    <div className={`${styles.container} ${className || ''}`} style={style}>
      {/* 地点信息 */}
      {showLocationInfo && (
        <Card size="small" className={styles.infoCard} style={{ marginBottom: 8 }}>
          <Space className={styles.locationInfo}>
            <EnvironmentOutlined style={{ color: '#1890ff' }} />
            <Space size={10}>
              <Typography.Text strong className={styles.locationName}>
                {destName}
              </Typography.Text>
              <Typography.Text type="secondary" className={styles.locationCoordinates}>
                经度: {coordinates.lng.toFixed(6)}, 纬度: {coordinates.lat.toFixed(6)}
              </Typography.Text>
            </Space>
          </Space>
          <Space className={styles.locationActions} style={{ float: 'right' }}>
            <Button type="text" size="small" onClick={handleOpenInNewWindow}>
              在新窗口打开
            </Button>
          </Space>
        </Card>
      )}

      {/* 地图 iframe */}
      <iframe
        src={mapUrl}
        width={width}
        height={height}
        className={styles.iframe}
        title={`高德地图 - ${destName}`}
        allowFullScreen
      />
    </div>
  )
}

export default AmapDisplay
