import { ACTOR_ROLE_TYPE_CONFIG, ROLE_TYPE_CONFIG } from '@/consts'
import { DATE_FORMAT_BASE } from '@/consts/date'
import type { IPrPersonEvaluation } from '@/pages/production/project/list/store'
import { HomeOutlined, VideoCameraOutlined } from '@ant-design/icons'
import { Badge, Card, Divider, Empty, Flex, Rate, Space, Statistic, Tabs, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useMemo } from 'react'
import styles from './EvaluationHistory.scss'

interface IEvaluationHistoryProps {
  evaluations: IPrPersonEvaluation[]
  parentType?: number // 1人员，2演员
}

const EvaluationHistory: React.FC<IEvaluationHistoryProps> = ({ evaluations = [], parentType }) => {
  // 获取角色类型标签
  const getRoleTypeLabel = (evaluation: IPrPersonEvaluation) => {
    if (evaluation.parentType === 2 || parentType === 2) {
      // 演员类型
      const config = ACTOR_ROLE_TYPE_CONFIG[String(evaluation.roleType) as keyof typeof ACTOR_ROLE_TYPE_CONFIG]

      return config?.label || '未知角色'
    } else if (evaluation.parentType === 1 || parentType === 1) {
      // 人员类型
      const config = ROLE_TYPE_CONFIG[evaluation.roleType as keyof typeof ROLE_TYPE_CONFIG]

      return config?.label || '未知角色'
    }

    return '未知角色'
  }

  // 按评价类型分组评价数据
  const groupedEvaluations = useMemo(() => {
    const workEvaluations = evaluations.filter(evaluation => evaluation.evaluationType === 0)
    const accommodationEvaluations = evaluations.filter(evaluation => evaluation.evaluationType === 1)

    return {
      work: workEvaluations,
      accommodation: accommodationEvaluations,
    }
  }, [evaluations])

  // 计算平均分
  const calculateAverageScore = (evaluationList: IPrPersonEvaluation[]) => {
    if (evaluationList.length === 0) {
      return '0.0'
    }
    const totalScore = evaluationList.reduce((sum, evaluation) => sum + evaluation.score, 0)

    return (totalScore / evaluationList.length).toFixed(1)
  }

  // 渲染评价统计卡片
  const renderStatsCard = (evaluationList: IPrPersonEvaluation[]) => {
    const averageScore = calculateAverageScore(evaluationList)

    return (
      <Card size="small">
        <Flex justify="center" align="center" gap={16}>
          <Flex flex={1} justify="center">
            <Statistic title="平均分" value={averageScore} className={styles.statistic} />
          </Flex>
          <Divider type="vertical" className={styles.divider} />
          <Flex flex={1} justify="center">
            <Statistic title="评价次数" value={evaluationList.length} className={styles.statistic} />
          </Flex>
        </Flex>
      </Card>
    )
  }

  // 渲染评价列表
  const renderEvaluationList = (evaluationList: IPrPersonEvaluation[], itemType: 'accommodation' | 'work' = 'work') => {
    if (evaluationList.length === 0) {
      return <Empty />
    }

    return (
      <Flex vertical gap={24}>
        {renderStatsCard(evaluationList)}

        <Flex vertical gap={12}>
          {evaluationList.map(evaluation => (
            <Card size="small" style={{ marginBottom: 8 }}>
              <Flex vertical gap={8}>
                <Flex justify="space-between" align="center">
                  <Space size={2} split={<Divider type="vertical" />}>
                    {evaluation?.productionName && (
                      <Typography.Text strong>
                        {evaluation.productionName}
                        {evaluation.secondProductionCode ? `(${evaluation.secondProductionCode})` : ''}
                      </Typography.Text>
                    )}
                    {itemType == 'work' ? (
                      <Typography.Text strong>{getRoleTypeLabel(evaluation)}</Typography.Text>
                    ) : null}
                  </Space>
                  <Rate count={10} value={evaluation.score} />
                </Flex>
                <Flex justify="space-between" gap={36} align="flex-end">
                  <Flex flex={1}>
                    {evaluation.comment && (
                      <Typography.Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                        {evaluation.comment}
                      </Typography.Paragraph>
                    )}
                  </Flex>
                  <Space size={0} split={<Divider type="vertical" />}>
                    {evaluation.creator && <Typography.Text>{evaluation.creator}</Typography.Text>}
                    <Typography.Text type="secondary">
                      {evaluation.updateTime ? dayjs(evaluation.updateTime).format(DATE_FORMAT_BASE) : '未知时间'}
                    </Typography.Text>
                  </Space>
                </Flex>
              </Flex>
            </Card>
          ))}
        </Flex>
      </Flex>
    )
  }

  // 如果没有任何评价数据
  if (evaluations.length === 0) {
    return <Empty />
  }

  // 构建 Tab 项
  const tabItems = [
    {
      key: 'work',
      icon: <VideoCameraOutlined />,
      label: (
        <Space>
          工作评价
          {!!groupedEvaluations.work.length && <Badge color="#ccc" count={groupedEvaluations.work.length} />}
        </Space>
      ),
      children: renderEvaluationList(groupedEvaluations.work),
    },
    {
      key: 'accommodation',
      icon: <HomeOutlined />,
      label: (
        <Space>
          住宿评价
          {!!groupedEvaluations.accommodation.length && (
            <Badge color="#ccc" count={groupedEvaluations.accommodation.length} />
          )}
        </Space>
      ),
      children: renderEvaluationList(groupedEvaluations.accommodation, 'accommodation'),
    },
  ]

  // 如果只有一种类型的评价，直接显示内容，不显示 Tab
  if (tabItems.length === 1) {
    return <div>{tabItems[0].children}</div>
  }

  // 如果有多种类型的评价，显示 Tab
  if (tabItems.length > 1) {
    return <Tabs items={tabItems} tabPosition="left" type="card" tabBarGutter={4} />
  }

  // 兜底情况：显示空状态
  return <Empty />
}

export default EvaluationHistory
