import React from 'react'
import styles from './RoomEquipment.scss'

interface RoomEquipmentProps {
  name: string
}

// 房间设备图标
const RoomEquipment: React.FC<RoomEquipmentProps> = ({ name }) => {
  const EQUIPMENT_ICON_MAP: Record<string, React.ReactNode> = {
    空调: <img className={styles.icon} src={require('@/assets/images/ac.png')} />,
    wifi: <img className={styles.icon} src={require('@/assets/images/wifi.png')} />,
    洗衣机: <img className={styles.icon} src={require('@/assets/images/washer.png')} />,
    冰箱: <img className={styles.icon} src={require('@/assets/images/fridge.png')} />,
    热水器: <img className={styles.icon} src={require('@/assets/images/heater.png')} />,
    热水壶: <img className={styles.icon} src={require('@/assets/images/kettle.png')} />,
    办公桌: <img className={styles.icon} src={require('@/assets/images/sofa.png')} />,
  }

  return EQUIPMENT_ICON_MAP[name] || <span className="fs-sm">{name}</span>
}

export default RoomEquipment
