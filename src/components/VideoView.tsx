import { PlayCircleOutlined } from '@ant-design/icons'
import { Modal, Spin, Typography } from 'antd'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'

import defaultVideoPoster from '@/assets/images/video.png'
import { getVideoThumbnail } from '@/utils/file'
import { envUrl } from '@/utils/request'
import styles from './VideoView.scss'

// 封面缓存Map，避免重复生成
const thumbnailCache = new Map<string, string>()

interface VideoViewProps {
  /** 视频URL，支持相对路径和绝对路径 */
  videoUrl: string

  /** 自定义封面图URL，可选。如果不提供，将自动生成视频封面 */
  posterUrl?: string

  /** 封面图宽度 */
  width?: number | string

  /** 封面图高度 */
  height?: number | string

  /** 组件类名 */
  className?: string

  /** 组件样式 */
  style?: React.CSSProperties

  /** 是否显示播放图标 */
  showPlayIcon?: boolean

  /** 播放图标大小 */
  playIconSize?: number

  /** 视频封面截取时间点（秒），默认为1秒 */
  thumbnailTime?: number
}

const VideoView: React.FC<VideoViewProps> = ({
  videoUrl,
  posterUrl,
  width = 200,
  height = 150,
  className = '',
  style = {},
  showPlayIcon = true,
  playIconSize = 32,
  thumbnailTime = 1,
}) => {
  const [modalOpen, setModalOpen] = useState(false)
  const [posterLoading, setPosterLoading] = useState(false)
  const [posterError, setPosterError] = useState(false)
  const [generatedThumbnail, setGeneratedThumbnail] = useState<string>('')
  const videoRef = useRef<HTMLVideoElement>(null)

  // 获取完整URL
  const getFullUrl = useCallback((url: string): string => {
    if (!url) {
      return ''
    }

    return url.startsWith('http') ? url : envUrl + url
  }, [])

  // 计算URL，使用useMemo避免重复计算
  const fullVideoUrl = useMemo(() => getFullUrl(videoUrl), [getFullUrl, videoUrl])
  const fullPosterUrl = useMemo(() => (posterUrl ? getFullUrl(posterUrl) : null), [getFullUrl, posterUrl])

  // 生成视频封面
  const generateThumbnail = useCallback(async () => {
    if (!fullVideoUrl || fullPosterUrl) {
      return // 如果没有视频URL或已有自定义封面，不生成缩略图
    }

    // 检查缓存
    const cacheKey = `${fullVideoUrl}_${thumbnailTime}`

    if (thumbnailCache.has(cacheKey)) {
      const cachedThumbnail = thumbnailCache.get(cacheKey)

      if (cachedThumbnail) {
        setGeneratedThumbnail(cachedThumbnail)

        return
      }
    }

    setPosterLoading(true)
    setPosterError(false)

    try {
      const thumbnail = await getVideoThumbnail(fullVideoUrl, thumbnailTime)

      setGeneratedThumbnail(thumbnail)
      thumbnailCache.set(cacheKey, thumbnail)
    } catch (error) {
      console.warn('生成视频封面失败:', error)
      setPosterError(true)
    } finally {
      setPosterLoading(false)
    }
  }, [fullVideoUrl, fullPosterUrl, thumbnailTime])

  // 组件挂载时生成封面
  useEffect(() => {
    if (fullVideoUrl && !fullPosterUrl) {
      generateThumbnail()
    }
  }, [fullVideoUrl, fullPosterUrl, generateThumbnail])

  // 计算要显示的封面图URL
  const displayPosterUrl = useMemo(() => {
    if (fullPosterUrl) {
      return fullPosterUrl
    }
    if (generatedThumbnail) {
      return generatedThumbnail
    }

    // 处理默认封面图URL，确保它也是完整URL
    return defaultVideoPoster
  }, [fullPosterUrl, generatedThumbnail, getFullUrl])

  // 处理封面点击
  const handlePosterClick = useCallback(() => {
    if (!fullVideoUrl) {
      return
    }
    setModalOpen(true)
  }, [fullVideoUrl])

  // 处理弹窗关闭
  const handleModalClose = useCallback(() => {
    if (videoRef.current) {
      videoRef.current.pause()
      videoRef.current.currentTime = 0
    }
    setModalOpen(false)
  }, [])

  // 处理封面加载错误
  const handlePosterError = useCallback(() => {
    setPosterError(true)
  }, [])

  // 空状态处理
  if (!videoUrl) {
    return (
      <div className={`${styles.emptyState} ${className}`} style={{ width, height, ...style }}>
        <Typography.Text type="secondary">暂无视频</Typography.Text>
      </div>
    )
  }

  return (
    <>
      {/* 封面容器 */}
      <div
        className={`${styles.container} ${className}`}
        style={{ width, height, ...style }}
        onClick={handlePosterClick}>
        {/* 加载状态 */}
        {posterLoading && (
          <div className={styles.loadingOverlay}>
            <Spin size="small" />
            <Typography.Text type="secondary" className={styles.loadingText}>
              正在生成封面...
            </Typography.Text>
          </div>
        )}

        {/* 封面图 */}
        {displayPosterUrl && !posterLoading && (
          <img className={styles.posterImage} src={displayPosterUrl} alt="视频封面" onError={handlePosterError} />
        )}

        {/* 错误状态 - 当默认封面也加载失败时显示 */}
        {posterError && displayPosterUrl === defaultVideoPoster && (
          <div className={styles.errorState}>
            <PlayCircleOutlined className={styles.errorIcon} />
            <Typography.Text type="secondary" className={styles.errorText}>
              封面加载失败
            </Typography.Text>
          </div>
        )}

        {/* 播放图标 */}
        {showPlayIcon && !posterLoading && (
          <div className={styles.playIcon} style={{ fontSize: playIconSize }}>
            <PlayCircleOutlined />
          </div>
        )}

        {/* 悬停遮罩 */}
        <div className={styles.hoverMask} />
      </div>

      {/* 视频播放弹窗 */}
      <Modal
        title="视频播放"
        open={modalOpen}
        onCancel={handleModalClose}
        footer={null}
        width={800}
        centered
        destroyOnClose>
        <video ref={videoRef} controls autoPlay className={styles.modalVideo} src={fullVideoUrl}>
          您的浏览器不支持视频播放
        </video>
      </Modal>
    </>
  )
}

export default VideoView
