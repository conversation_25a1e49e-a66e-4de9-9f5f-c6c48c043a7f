import { CalendarOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, Cascader, DatePicker, Space } from 'antd'
import { RangePickerProps } from 'antd/es/date-picker'
import type { Dayjs } from 'dayjs'
import dayjs from 'dayjs'
import React from 'react'

const { RangePicker } = DatePicker

const DateRangeCascader: React.FC<RangePickerProps> = ({ onChange, ...props }) => {
  // 快捷选项
  const quickOptions = [
    {
      value: 'today',
      label: '今天',
    },
    {
      value: 'yesterday',
      label: '昨天',
    },
    {
      value: 'thisMonth',
      label: '本月',
    },
    {
      value: 'lastMonth',
      label: '上月',
    },
    {
      value: 'past1Week',
      label: '过去1周',
    },
    {
      value: 'past2Week',
      label: '过去2周',
    },
    {
      value: 'past3Week',
      label: '过去3周',
    },
    {
      value: 'past4Week',
      label: '过去4周',
    },
    {
      value: 'pastWeeks',
      label: '其他',
      children: [
        {
          value: 'past5Week',
          label: '过去5周',
        },
        {
          value: 'past6Week',
          label: '过去6周',
        },
        {
          value: 'past7Week',
          label: '过去7周',
        },
        {
          value: 'past8Week',
          label: '过去8周',
        },
        {
          value: 'past9Week',
          label: '过去9周',
        },
        {
          value: 'past10Week',
          label: '过去10周',
        },
        {
          value: 'past11Week',
          label: '过去11周',
        },
        {
          value: 'past12Week',
          label: '过去12周',
        },
        {
          value: 'past13Week',
          label: '过去13周',
        },
        {
          value: 'past14Week',
          label: '过去14周',
        },
        {
          value: 'past30Days',
          label: '过去30天',
        },
      ],
    },
  ]

  // 处理级联选择器变化
  const handleCascaderChange = (value: string[]) => {
    if (!value || value.length === 0) return

    const now = dayjs()
    let startDate: Dayjs
    let endDate: Dayjs

    const option = value[value.length - 1]

    switch (option) {
      case 'today':
        startDate = now.startOf('day')
        endDate = now.endOf('day')
        break
      case 'yesterday':
        startDate = now.subtract(1, 'day').startOf('day')
        endDate = now.subtract(1, 'day').endOf('day')
        break
      case 'thisMonth':
        startDate = now.startOf('month')
        endDate = now.endOf('month')
        break
      case 'lastMonth':
        startDate = now.subtract(1, 'month').startOf('month')
        endDate = now.subtract(1, 'month').endOf('month')
        break
      case 'past1Week':
        startDate = now.subtract(1, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past2Week':
        startDate = now.subtract(2, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past3Week':
        startDate = now.subtract(3, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past4Week':
        startDate = now.subtract(4, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past5Week':
        startDate = now.subtract(5, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past6Week':
        startDate = now.subtract(6, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past7Week':
        startDate = now.subtract(7, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past8Week':
        startDate = now.subtract(8, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past9Week':
        startDate = now.subtract(9, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past10Week':
        startDate = now.subtract(10, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past11Week':
        startDate = now.subtract(11, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past12Week':
        startDate = now.subtract(12, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past13Week':
        startDate = now.subtract(13, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past14Week':
        startDate = now.subtract(14, 'week').startOf('day')
        endDate = now.endOf('day')
        break
      case 'past30Days':
        startDate = now.subtract(30, 'day').startOf('day')
        endDate = now.endOf('day')
        break
      default:
        return
    }

    if (onChange) {
      onChange([startDate, endDate])
    }
  }

  return (
    <Space size={8}>
      <Cascader options={quickOptions} onChange={handleCascaderChange} allowClear>
        <Button>
          <CalendarOutlined />
        </Button>
      </Cascader>
      <RangePicker onChange={onChange} {...props} />
    </Space>
  )
}

export default DateRangeCascader
