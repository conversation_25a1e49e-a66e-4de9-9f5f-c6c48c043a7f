import { EnvironmentOutlined } from '@ant-design/icons'
import { Button, Card, Modal, Space, Typography } from 'antd'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { AMAP_CONFIG } from '../consts'
import styles from './AmapPicker.scss'

interface ILocationData {
  /** 位置坐标，格式：经度,纬度 */
  location: string

  /** 地址信息 */
  address: string

  /** POI名称 */
  name: string
}

interface IAmapPickerProps {
  /** 搜索关键词，支持3个关键词，用英文逗号分隔，默认：写字楼,小区,学校 */
  keywords?: string

  /** 地图缩放级别，取值范围3-18，默认15 */
  zoom?: number

  /** 中心点坐标，格式：经度,纬度 */
  value?: string

  /** 搜索范围半径，单位米，取值范围1-50000，默认1000 */
  radius?: number

  /** 检索结果条数，取值范围1-50，默认20 */
  total?: number

  /** 自定义样式 */
  style?: React.CSSProperties

  /** 自定义类名 */
  className?: string

  /** 是否显示选择信息，默认为 true */
  showSelectedInfo?: boolean

  /** 选择位置变化时的回调 */
  onChange?: (location: string) => void

  /** 确认选择位置时的回调 */
  onSelect?: (location: ILocationData) => void

  /** 初始选中的位置信息 */
  defaultValue?: ILocationData

  /** 打开地图选择器按钮的文本，默认为"选择位置" */
  buttonText?: string
}

const AmapPicker: React.FC<IAmapPickerProps> = ({
  keywords = '小区',
  zoom = 15,
  value,
  radius = 1000,
  total = 20,
  style,
  className,
  showSelectedInfo = true,
  onChange,
  onSelect,
  defaultValue,
  buttonText = '选择位置',
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const [selectedLocation, setSelectedLocation] = React.useState<ILocationData | undefined>(defaultValue)
  const [isModalVisible, setIsModalVisible] = useState(false)

  // 构建选址组件 iframe URL
  const pickerUrl = useMemo(() => {
    const baseUrl = 'https://m.amap.com/picker/'
    const params = new URLSearchParams({
      key: AMAP_CONFIG.KEY,
      jscode: AMAP_CONFIG.JSCODE,
      keywords,
      zoom: zoom.toString(),
      radius: radius.toString(),
      total: total.toString(),
    })

    if (value) {
      params.append('center', value)
    }

    return `${baseUrl}?${params.toString()}`
  }, [keywords, zoom, value, radius, total])

  // 处理 iframe 消息
  const handleMessage = (event: MessageEvent) => {
    // 验证消息来源
    if (event.origin !== 'https://m.amap.com') {
      return
    }

    // 检查消息数据格式
    console.log('event', event)
    if (event.data && typeof event.data === 'object' && event.data.location) {
      const locationData: ILocationData = {
        location: event.data.location,
        address: event.data.address || '',
        name: event.data.name || '',
      }

      setSelectedLocation(locationData)
    }
  }

  // 监听 iframe 消息
  useEffect(() => {
    window.addEventListener('message', handleMessage, false)

    return () => {
      window.removeEventListener('message', handleMessage, false)
    }
  }, [handleMessage])

  // iframe 加载完成后发送初始化消息
  const handleIframeLoad = useCallback(() => {
    if (iframeRef.current?.contentWindow) {
      // 向 iframe 发送初始化消息
      iframeRef.current.contentWindow.postMessage('hello', 'https://m.amap.com/picker/')
    }
  }, [])

  // 确认选择
  const handleConfirmSelect = () => {
    if (selectedLocation) {
      onChange?.(selectedLocation.location)
      onSelect?.(selectedLocation)
      setIsModalVisible(false)
    }
  }

  // 在新窗口中打开选址页面
  const handleOpenInNewWindow = () => {
    window.open(pickerUrl, '_blank')
  }

  // 打开弹窗
  const handleOpenModal = () => {
    setIsModalVisible(true)
  }

  // 关闭弹窗
  const handleCloseModal = () => {
    setIsModalVisible(false)
  }

  // 解析坐标
  const parseCoordinates = (str?: string) => {
    if (!str) {
      return null
    }
    const [lng, lat] = str.split(',')

    return { lng: parseFloat(lng), lat: parseFloat(lat) }
  }
  const valueCoordinates = useMemo(() => parseCoordinates(value), [value])
  const coordinates = useMemo(() => parseCoordinates(selectedLocation?.location), [selectedLocation])

  return (
    <div className={`${styles.container} ${className || ''}`} style={style}>
      {/* 选中位置信息 */}
      {showSelectedInfo && (
        <Space wrap size={10}>
          {/* <Typography.Text strong>{defaultValue?.name || ''}</Typography.Text> */}
          {valueCoordinates ? <Typography.Text>{defaultValue?.address || ''}</Typography.Text> : null}
          {valueCoordinates && (
            <Typography.Text className={styles.locationCoordinates}>
              经度: {valueCoordinates.lng.toFixed(6)}, 纬度: {valueCoordinates.lat.toFixed(6)}
            </Typography.Text>
          )}
        </Space>
      )}

      {/* 打开地图选择器按钮 */}
      <Button className="text-primary" type="link" onClick={handleOpenModal} icon={<EnvironmentOutlined />}>
        {buttonText}
      </Button>

      {/* 地图选择器弹窗 */}
      <Modal
        title="选择位置"
        open={isModalVisible}
        onCancel={handleCloseModal}
        width={800}
        footer={[
          <Button key="cancel" onClick={handleCloseModal}>
            取消
          </Button>,
          <Button key="confirm" type="primary" onClick={handleConfirmSelect} disabled={!selectedLocation?.location}>
            确认选择
          </Button>,
        ].filter(Boolean)}>
        <Card size="small" className={styles.infoCard} style={{ marginBottom: 8 }}>
          <Space wrap size={10}>
            <Typography.Text strong>{selectedLocation?.name || ''}</Typography.Text>
            <Typography.Text>{selectedLocation?.address || ''}</Typography.Text>
            {coordinates && (
              <Typography.Text className={styles.locationCoordinates}>
                经度: {coordinates.lng.toFixed(6)}, 纬度: {coordinates.lat.toFixed(6)}
              </Typography.Text>
            )}
          </Space>
        </Card>
        {/* 选址 iframe */}
        <iframe
          ref={iframeRef}
          src={pickerUrl}
          width={'100%'}
          height={'50vh'}
          className={styles.iframe}
          title="高德地图选址"
          onLoad={handleIframeLoad}
          allowFullScreen
        />
      </Modal>
    </div>
  )
}

export default AmapPicker
