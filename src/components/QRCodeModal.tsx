import { VerticalAlignBottomOutlined } from '@ant-design/icons'
import { Button, Flex, Modal, QRCode, Space, Typography } from 'antd'
import React from 'react'

interface IQRCodeModalProps {
  open: boolean
  onCancel: () => void
  url: string
  title?: string
  description?: string
  size?: number
  fileName?: string
}

const QRCodeModal: React.FC<IQRCodeModalProps> = ({
  open,
  onCancel,
  url,
  title = '二维码',
  description = '扫描二维码或分享链接',
  size = 200,
  fileName = 'QRCode',
}) => {
  // 下载功能
  const doDownload = (downloadUrl: string, downloadFileName: string) => {
    const a = document.createElement('a')

    a.download = downloadFileName
    a.href = downloadUrl
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }

  const downloadCanvasQRCode = () => {
    const canvas = document.getElementById('qrcode-modal')?.querySelector<HTMLCanvasElement>('canvas')

    if (canvas) {
      const canvasUrl = canvas.toDataURL()

      doDownload(canvasUrl, `${fileName}.png`)
    }
  }

  return (
    <Modal title={title} open={open} onCancel={onCancel} footer={null} width={400} centered>
      <Space id="qrcode-modal" direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
        <Typography.Text type="secondary">{description}</Typography.Text>
        <Flex vertical={true} align="center">
          <QRCode type="canvas" value={url} size={size} bgColor="#fff" style={{ marginBottom: 16 }} />
          <div>
            <Button type="primary" icon={<VerticalAlignBottomOutlined />} onClick={downloadCanvasQRCode} block>
              下载二维码
            </Button>
          </div>
        </Flex>
        <Typography.Text copyable={{ text: url }} type="secondary" style={{ fontSize: 12 }}>
          {url}
        </Typography.Text>
      </Space>
    </Modal>
  )
}

export default QRCodeModal
