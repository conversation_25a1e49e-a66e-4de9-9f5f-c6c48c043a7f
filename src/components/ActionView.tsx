import React from 'react'
import { DownOutlined } from '@ant-design/icons'
import { Dropdown } from 'antd'

const ActionView = (props: any) => {
  const { form, loading, onSearch, onReset } = props
  const onMenuClick = (e: any) => {
    if (e.key === 'reset') {
      form?.resetFields()
      onReset && onReset()
      onSearch(1)
    }
  }

  return (
    <Dropdown.Button
      menu={{ items: [{ key: 'reset', label: '重置' }], onClick: onMenuClick }}
      loading={loading}
      icon={<DownOutlined />}
      type={'primary'}
      onClick={() => onSearch(1)}>
      查询
    </Dropdown.Button>
  )
}

export default ActionView
