import { AutoComplete, Input, message, Space, Tag } from 'antd'
import React, { useMemo, useState } from 'react'

interface IInputTag {
  value?: string | string[]
  onChange?: (data: string | string[]) => void
  mode?: 'string' | 'array'
  options?: Array<any>
  placeholder?: string
}

const InputTag: React.FC<IInputTag> = ({ value, mode, options, placeholder, onChange }) => {
  const [inputValue, setInputValue] = useState('')

  const handleChange = (type: 'add' | 'remove', value?: string) => {
    const arr = [...formatValue]

    if (type == 'add' && value) {
      arr.push(value)
    } else if (type == 'remove') {
      arr.splice(arr.indexOf(value), 1)
    }
    if (mode == 'array') {
      onChange && onChange(arr)
    } else {
      onChange && onChange(arr.join(','))
    }
  }

  const formatValue = useMemo(() => {
    if (value) {
      if (Array.isArray(value)) {
        return value
      }

      return value.toString().split(',')
    }

    return []
  }, [value])

  const handleInputConfirm = () => {
    if (inputValue) {
      if (formatValue.includes(inputValue)) {
        message.error('填写重复')
      } else {
        handleChange('add', inputValue)
      }
    }

    setInputValue('')
  }
  const handleSelect = val => {
    handleChange('add', val)
    setInputValue('')
  }

  const handleRemove = (value: string) => {
    handleChange('remove', value)
  }

  return (
    <Space wrap>
      {formatValue?.map((str: string) => (
        <Tag key={str} closable color="cyan" onClose={() => handleRemove(str)}>
          {str}
        </Tag>
      ))}
      {options && options.length ? (
        <AutoComplete
          options={options}
          value={inputValue}
          onChange={setInputValue}
          onBlur={handleInputConfirm}
          onPressEnter={handleInputConfirm}
          onSelect={handleSelect}
          placeholder={placeholder}
        />
      ) : (
        <Input
          value={inputValue}
          onChange={e => setInputValue(e.target.value)}
          onBlur={handleInputConfirm}
          onPressEnter={handleInputConfirm}
          placeholder={placeholder}
          className="w200"
        />
      )}
    </Space>
  )
}

export default InputTag
