import { ACTOR_ROLE_TYPE_OPTIONS, WORK_TYPE_OPTIONS } from '@/consts'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { Button, Card, Flex, Form, Input, Modal, Select, Switch } from 'antd'
import React, { useEffect } from 'react'

// 作品信息数据结构
export interface IWorkInfo {
  id?: number
  actorId?: number
  workName: string
  isHit: boolean
  roleType: string
  roleDescription: string
  workType: string
  sort: number
}

interface WorkInfoFormProps {
  // 表单模式
  mode?: 'list' | 'modal' // list: 列表模式(用于Add组件), modal: 弹窗模式(用于Detail组件)
  multiple?: boolean // 是否批量模式
  // 列表模式的属性
  fieldName?: string // Form.List 的 name

  // 弹窗模式的属性
  open?: boolean
  title?: string
  loading?: boolean
  onCancel?: () => void
  onSubmit?: (values: IWorkInfo[]) => void // 修改为支持多个作品

  // 通用属性
  form?: any
  disabled?: boolean
  initialValues?: IWorkInfo[]
}

const WorkInfoForm: React.FC<WorkInfoFormProps> = ({
  mode = 'list',
  multiple = true,
  fieldName = 'workInfos',
  open = false,
  title = '作品信息管理',
  loading = false,
  onCancel,
  onSubmit,
  form: externalForm,
  disabled = false,
  initialValues = [],
}) => {
  const [internalForm] = Form.useForm()
  const form = externalForm || internalForm

  // 处理弹窗模式的初始值设置
  useEffect(() => {
    if (mode === 'modal' && !open) {
      // 如果没有初始值，则设置一个空数组，避免表单报错
      form.setFieldsValue({ workInfos: [{}] })
    }
  }, [mode, open, initialValues, form])

  // 处理弹窗模式的提交
  const handleModalSubmit = async () => {
    try {
      const values = await form.validateFields()
      // 获取作品信息数组
      const workInfos = values.workInfos || []

      onSubmit?.(workInfos)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  // 渲染作品信息卡片
  const renderWorkCard = (field: any, index: number, remove?: (index: number) => void) => {
    const { key, name, ...restField } = field || {}
    const formItems = (
      <>
        <Form.Item
          {...restField}
          name={typeof name === 'number' ? [name, 'workName'] : 'workName'}
          label="作品"
          rules={[{ required: true }]}>
          <Input placeholder="输入作品名称" disabled={disabled} />
        </Form.Item>
        <Flex gap={24}>
          <Form.Item {...restField} name={typeof name === 'number' ? [name, 'workType'] : 'workType'} label="类型">
            <Select
              placeholder="选择作品类型"
              options={WORK_TYPE_OPTIONS}
              disabled={disabled}
              allowClear
              className="w200"
            />
          </Form.Item>
          <Form.Item {...restField} name={typeof name === 'number' ? [name, 'roleType'] : 'roleType'} label="角色">
            <Select
              placeholder="选择角色类型"
              options={ACTOR_ROLE_TYPE_OPTIONS}
              disabled={disabled}
              allowClear
              className="w200"
            />
          </Form.Item>

          <Form.Item
            {...restField}
            name={typeof name === 'number' ? [name, 'isHit'] : 'isHit'}
            label="是否爆款"
            valuePropName="checked">
            <Switch checkedChildren="是" unCheckedChildren="否" disabled={disabled} />
          </Form.Item>
        </Flex>
        <Form.Item
          {...restField}
          name={typeof name === 'number' ? [name, 'roleDescription'] : 'roleDescription'}
          label="描述">
          <Input.TextArea placeholder="输入角色描述" rows={2} disabled={disabled} />
        </Form.Item>
      </>
    )

    if (!multiple) {
      return formItems
    }

    return (
      <Card
        key={key}
        size="small"
        style={{ marginBottom: 16 }}
        title={`作品 ${index + 1}`}
        extra={
          remove && !disabled ? (
            <Button type="text" size="small" icon={<DeleteOutlined />} onClick={() => remove(index)} danger />
          ) : null
        }>
        {formItems}
      </Card>
    )
  }

  // 渲染Form.List内容
  const renderFormList = () => (
    <Form.List name="workInfos">
      {(fields, { add, remove }) => (
        <>
          {fields.map((field, index) => renderWorkCard(field, index, remove))}
          {!disabled && multiple && (
            <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
              添加代表作
            </Button>
          )}
        </>
      )}
    </Form.List>
  )

  // 列表模式渲染
  if (mode === 'list') {
    return (
      <Form.List name={fieldName}>
        {(fields, { add, remove }) => (
          <>
            {fields.map((field, index) => renderWorkCard(field, index, remove))}
            {!disabled && (
              <Button type="primary" ghost className="w300 margin-auto" onClick={() => add()} icon={<PlusOutlined />}>
                添加作品
              </Button>
            )}
          </>
        )}
      </Form.List>
    )
  }

  // 弹窗模式渲染（Modal）
  return (
    <Modal
      title={title}
      open={open}
      onCancel={onCancel}
      onOk={handleModalSubmit}
      confirmLoading={loading}
      width={800}
      okText="立即保存"
      bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}>
      <Form form={form} layout="horizontal" colon={false} style={{ marginTop: 20 }}>
        {renderFormList()}
      </Form>
    </Modal>
  )
}

export default WorkInfoForm
