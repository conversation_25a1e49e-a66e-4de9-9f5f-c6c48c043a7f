import { GetProps, Input, Tag, Typography } from 'antd'
import type { TextAreaRef } from 'antd/lib/input/TextArea'
import React, { useRef } from 'react'
import styles from './PromptTextArea.scss'

type TextAreaProps = GetProps<typeof Input.TextArea>

interface PromptTextAreaProps extends TextAreaProps {
  title?: string
  fragments?: string[]
}

// 带提示的 TextArea
const PromptTextArea: React.FC<PromptTextAreaProps> = ({
  title = '快捷选择：',
  fragments,
  onChange,
  value,
  readOnly,
  ...props
}) => {
  const ref = useRef<TextAreaRef>(null)
  const handleTagClick = (label: string) => {
    let additional = ''

    if (value) {
      additional = `${value} `
    }

    additional += `${label} `
    onChange && onChange(additional)

    ref.current?.focus({
      cursor: 'end',
    })
  }

  return (
    <div className={styles.container}>
      {!readOnly && (
        <div className={styles.fragments}>
          <Typography.Text type="secondary" className="fs-sm">
            {title}
          </Typography.Text>
          {fragments?.map(fragment => (
            <Tag onClick={() => handleTagClick(fragment)} className="pointer">
              {fragment}
            </Tag>
          ))}
        </div>
      )}
      <Input.TextArea
        ref={ref}
        value={value}
        readOnly={readOnly}
        onChange={onChange}
        className={!readOnly ? styles.textArea : ''}
        {...props}
      />
    </div>
  )
}

export default PromptTextArea
