import defaultVideoPoster from '@/assets/images/video.png'
import { getUploadProps, getVideoThumbnail } from '@/utils/file'
import { envUrl } from '@/utils/request'
import { PlusOutlined } from '@ant-design/icons'
import { Image, message, Modal, Upload, type GetProp, type UploadFile, type UploadProps } from 'antd'
import { RcFile } from 'antd/es/upload'
import React, { useCallback, useEffect, useRef, useState } from 'react'

import styles from './Upload.css'

type FileType = Parameters<GetProp<UploadProps, 'beforeUpload'>>[0]
type MediaType = 'video' | 'image' | 'pdf' | 'word' | 'excel'

interface IUpload {
  value?: string | string[] // 支持单个文件或文件数组
  type?: MediaType
  disabled?: boolean
  accept?: string
  action: string
  multiple?: boolean
  maxCount?: number
  onChange?: (data: string | string[]) => void
}

const getBase64 = (file: FileType): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader()

    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = error => reject(error)
  })

// 文档文件类型检测工具函数
const isPdfFile = (file: FileType | string): boolean => {
  if (typeof file === 'string') {
    return file.toLowerCase().endsWith('.pdf')
  }

  return file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')
}

const isWordFile = (file: FileType | string): boolean => {
  if (typeof file === 'string') {
    const fileName = file.toLowerCase()

    return fileName.endsWith('.doc') || fileName.endsWith('.docx')
  }
  const wordMimeTypes = [
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ]
  const fileName = file.name.toLowerCase()

  return wordMimeTypes.includes(file.type) || fileName.endsWith('.doc') || fileName.endsWith('.docx')
}

const isExcelFile = (file: FileType | string): boolean => {
  if (typeof file === 'string') {
    const fileName = file.toLowerCase()

    return fileName.endsWith('.xls') || fileName.endsWith('.xlsx')
  }
  const excelMimeTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ]
  const fileName = file.name.toLowerCase()

  return excelMimeTypes.includes(file.type) || fileName.endsWith('.xls') || fileName.endsWith('.xlsx')
}

// 根据文件判断文档类型
const getDocumentType = (file: FileType | string): MediaType | null => {
  if (isPdfFile(file)) {
    return 'pdf'
  }
  if (isWordFile(file)) {
    return 'word'
  }
  if (isExcelFile(file)) {
    return 'excel'
  }

  return null
}

const UploadComponent: React.FC<IUpload> = ({
  value,
  type = 'image',
  accept,
  disabled = false,
  action,
  multiple = false,
  maxCount,
  onChange,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const [previewOpen, setPreviewOpen] = useState(false)
  const [previewImage, setPreviewImage] = useState('')
  const [previewType, setPreviewType] = useState<MediaType>('image')
  const [previewTitle, setPreviewTitle] = useState('')
  const videoRef = useRef<HTMLVideoElement>(null)

  // 用ref记录最后一次通过onChange回调的value，避免循环更新
  const lastCallbackValueRef = React.useRef<string | string[] | undefined>()

  // 根据类型设置默认accept和maxCount
  const getDefaultAccept = (mediaType: MediaType): string => {
    switch (mediaType) {
      case 'image':
        return '.png,.jpg,.jpeg,.gif,.webp'
      case 'video':
        return '.mp4,.avi,.mov,.wmv,.flv,.mkv'
      case 'pdf':
        return '.pdf'
      case 'word':
        return '.doc,.docx'
      case 'excel':
        return '.xls,.xlsx'
      default:
        return '*'
    }
  }

  const defaultAccept = getDefaultAccept(type)
  const finalAccept = accept || defaultAccept
  const finalMaxCount = maxCount || (multiple ? 10 : 1)

  // 异步处理文件列表，为视频文件生成缩略图，为文档文件设置图标
  const processFileList = useCallback(
    async (urls: string[]) => {
      const newFileList: UploadFile[] = await Promise.all(
        urls
          .filter(url => url)
          .map(async (url, index) => {
            const fullUrl = url.startsWith('http') ? url : envUrl + url
            const fileName = url.split('/').pop() || `${type}${index + 1}`
            const isVideo = type === 'video' || fileName.match(/\.(mp4|avi|mov|wmv|flv|mkv)$/i)
            const documentType = getDocumentType(fileName)
            const isDocument = ['pdf', 'word', 'excel'].includes(type) || documentType

            let thumbUrl: string | undefined

            if (isVideo) {
              thumbUrl = defaultVideoPoster
              // 为视频文件生成缩略图
              try {
                thumbUrl = await getVideoThumbnail(fullUrl)
              } catch (error) {
                console.warn('生成视频缩略图失败:', error)
                // 缩略图生成失败时不设置thumbUrl，使用默认显示
              }
            } else {
              thumbUrl = fullUrl
            }

            return {
              uid: `${index}-${Date.now()}`,
              name: fileName,
              status: 'done' as const,
              url: fullUrl,
              thumbUrl,
              response: { data: { data: [{ url: url.startsWith('http') ? url.replace(envUrl, '') : url }] } },
            }
          })
      )

      setFileList(newFileList)
    },
    [type]
  )

  // 将value转换为fileList，避免与handleChange的状态更新冲突
  useEffect(() => {
    // 如果value与最后一次回调的value相同，说明是由当前组件触发的，跳过更新
    if (JSON.stringify(value) === JSON.stringify(lastCallbackValueRef.current)) {
      return
    }

    if (!value) {
      setFileList([])

      return
    }

    const urls = Array.isArray(value) ? value : [value]

    processFileList(urls)
  }, [value, processFileList])

  // 预览处理
  const handlePreview = async (file: UploadFile) => {
    let fileUrl = file.url || file.preview

    if (!fileUrl && file.originFileObj) {
      fileUrl = await getBase64(file.originFileObj as FileType)
    }

    if (!fileUrl) {
      return
    }

    setPreviewImage(fileUrl)
    setPreviewTitle(file.name || '预览')

    // 判断文件类型
    if (type === 'video' || fileUrl.match(/\.(mp4|avi|mov|wmv|flv|mkv)$/i)) {
      setPreviewType('video')
    } else if (['pdf', 'word', 'excel'].includes(type) || getDocumentType(fileUrl)) {
      // 对于文档类型，直接下载而不是预览
      window.open(fileUrl.startsWith('http') ? fileUrl : envUrl + fileUrl, '_blank')

      return
    } else {
      setPreviewType('image')
    }

    setPreviewOpen(true)
  }

  // 处理预览弹窗关闭
  const handlePreviewClose = () => {
    // 如果是视频预览，暂停视频并重置时间
    if (previewType === 'video' && videoRef.current) {
      videoRef.current.pause()
      videoRef.current.currentTime = 0
    }
    setPreviewOpen(false)
    setPreviewImage('')
    setPreviewTitle('')
  }

  // 确保URL是完整的工具函数
  const ensureFullUrl = (url: string) => (url.startsWith('http') ? url : envUrl + url)

  // 确保URL是相对路径的工具函数
  const ensureRelativeUrl = (url: string) => (url.startsWith('http') ? url.replace(envUrl, '') : url)
  const handlePostInit = async (url: string) => {
    try {
      const thumbUrl = await getVideoThumbnail(url)

      setFileList(prevList =>
        prevList.map(file => {
          if (file.url === url || file.response?.data?.data?.[0]?.url === url) {
            return {
              ...file,
              thumbUrl: thumbUrl || defaultVideoPoster, // 使用生成的缩略图或默认缩略图
            }
          }

          return file
        })
      )
    } catch (error) {
      console.warn('生成视频缩略图失败:', error)
      // 缩略图生成失败时不设置thumbUrl，使用默认显示
    }
  }

  // 上传状态变化处理
  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    const processedFileList = newFileList.map(file => {
      if (file.status === 'done' && !file?.url && file.response?.data?.data?.[0]?.url) {
        const responseUrl = file.response.data.data[0].url
        const lastUrl = ensureFullUrl(responseUrl)
        const fileName = file.name || responseUrl.split('/').pop() || 'file'
        const documentType = getDocumentType(fileName)
        const isDocument = ['pdf', 'word', 'excel'].includes(type) || documentType

        let thumbUrl: string | undefined

        if (type === 'video' && !file.thumbUrl) {
          thumbUrl = defaultVideoPoster
          handlePostInit(lastUrl)
        } else {
          thumbUrl = lastUrl
        }

        return {
          ...file,
          url: lastUrl,
          thumbUrl,
        }
      }

      // 对于已有的文件，确保URL是完整的
      if (file.url && !file.url.startsWith('http')) {
        return {
          ...file,
          url: ensureFullUrl(file.url),
        }
      }

      return file
    })

    setFileList(processedFileList)

    // 提取成功上传的文件URL（相对路径，用于保存）
    const successFiles = processedFileList
      .filter(file => file.status === 'done')
      .map(file => {
        // 优先使用response中的URL，再使用file.url
        const responseUrl = file.response?.data?.data?.[0]?.url

        if (responseUrl) {
          return ensureRelativeUrl(responseUrl)
        }

        return file.url ? ensureRelativeUrl(file.url) : ''
      })
      .filter(url => url)

    // 检查上传失败的文件
    const errorFiles = processedFileList.filter(file => file.status === 'error')

    if (errorFiles.length > 0) {
      message.error(`${errorFiles.length} 个文件上传失败`)
    }

    // 回调前记录value，避免循环更新
    const callbackValue = multiple ? successFiles : successFiles[0] || ''

    lastCallbackValueRef.current = callbackValue

    // 回调
    onChange?.(callbackValue)
  }

  // 上传前校验
  const beforeUpload = (file: RcFile) => {
    if (file.size === 0) {
      message.error('空文件不能上传')

      return Upload.LIST_IGNORE
    }

    // 文件类型校验
    if (type === 'image' && !file.type.startsWith('image/')) {
      message.error('只能上传图片文件')

      return false
    }

    if (type === 'video' && !file.type.startsWith('video/')) {
      message.error('只能上传视频文件')

      return false
    }

    // 文档类型校验
    if (type === 'pdf' && !isPdfFile(file)) {
      message.error('只能上传PDF文件')

      return false
    }

    if (type === 'word' && !isWordFile(file)) {
      message.error('只能上传Word文档(.doc/.docx)格式的文件')

      return false
    }

    if (type === 'excel' && !isExcelFile(file)) {
      message.error('只能上传Excel文档(.xls/.xlsx)格式的文件')

      return false
    }

    // 文件大小校验（文档类型限制为50MB）
    const isDocumentType = ['pdf', 'word', 'excel'].includes(type)

    if (isDocumentType) {
      const isLt50M = file.size / 1024 / 1024 < 50

      if (!isLt50M) {
        message.error('文件大小不能超过50MB')

        return false
      }
    }

    return true
  }

  // 获取上传按钮文本
  const getUploadButtonText = (mediaType: MediaType): string => {
    switch (mediaType) {
      case 'image':
        return '上传图片'
      case 'video':
        return '上传视频'
      case 'pdf':
        return '上传PDF'
      case 'word':
        return '上传Word'
      case 'excel':
        return '上传Excel'
      default:
        return '上传文件'
    }
  }

  // 上传按钮
  const uploadButton = (
    <button
      style={{
        border: 0,
        background: 'none',
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: disabled ? 'not-allowed' : 'pointer',
      }}
      type="button"
      disabled={disabled}>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>{getUploadButtonText(type)}</div>
    </button>
  )

  // 计算是否显示上传按钮
  const showUploadButton = !disabled && fileList.length < finalMaxCount

  return (
    <div className={styles.container}>
      <Upload
        {...getUploadProps(action)}
        accept={finalAccept}
        listType="picture-card"
        fileList={fileList}
        multiple={multiple}
        maxCount={finalMaxCount}
        disabled={disabled}
        beforeUpload={beforeUpload}
        onPreview={handlePreview}
        onChange={handleChange}
        name="filename"
        className={styles.upload}>
        {showUploadButton ? uploadButton : null}
      </Upload>

      {/* 图片预览 */}
      {previewType === 'image' && (
        <Image
          wrapperStyle={{ display: 'none' }}
          preview={{
            visible: previewOpen,
            onVisibleChange: visible => {
              if (!visible) {
                handlePreviewClose()
              }
            },
            afterOpenChange: visible => !visible && setPreviewImage(''),
          }}
          src={previewImage}
        />
      )}

      {/* 视频预览Modal */}
      {previewType === 'video' && (
        <Modal
          title={previewTitle}
          open={previewOpen}
          onCancel={handlePreviewClose}
          footer={null}
          width={800}
          centered
          destroyOnHidden>
          <video
            ref={videoRef}
            controls
            autoPlay
            style={{
              width: '100%',
              maxHeight: '60vh',
              objectFit: 'contain',
            }}
            src={previewImage}>
            您的浏览器不支持视频播放
          </video>
        </Modal>
      )}
    </div>
  )
}

export default UploadComponent
