.container {
  position: relative;
  overflow: hidden;
  border-radius: 6px;
  background-color: #f5f5f5;
  cursor: pointer;
}

.emptyState {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #f5f5f5;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: #f5f5f5;
  flex-direction: column;
}

.loadingText {
  margin-top: 8px;
  font-size: 12px;
}

.video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.posterImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.errorState {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #999;
  background-color: #f5f5f5;
  flex-direction: column;
}

.errorIcon {
  margin-bottom: 8px;
  font-size: 32px;
}

.errorText {
  font-size: 12px;
}

.playIcon {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  color: rgb(255 255 255 / 90%);
  text-shadow: 0 2px 8px rgb(0 0 0 / 50%);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);

  &:hover {
    color: rgb(255 255 255 / 100%);
    transform: translate(-50%, -50%) scale(1.1);
  }
}

.hoverMask {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: rgb(0 0 0 / 0%);
  transition: background-color 0.3s ease;

  &:hover {
    background-color: rgb(0 0 0 / 10%);
  }
}

.modalVideo {
  width: 100%;
  max-height: 70vh;
  object-fit: contain;
}
