import { MEDIA_TYPE_OPTIONS } from '@/consts'
import { InputNumber } from 'antd'
import React from 'react'
import MediaUpload from './MediaUpload'

interface MediaFormInputProps {
  mediaType: number
  value?: string | number | string[]
  onChange?: (value: string | number | string[]) => void
  disabled?: boolean
  uploadAction: string
  multiple?: boolean
  maxCount?: number
  maxSize?: number
}

const MediaFormInput: React.FC<MediaFormInputProps> = ({
  mediaType,
  value,
  onChange,
  disabled = false,
  uploadAction,
  multiple,
  maxCount,
  maxSize,
}) => {
  const mediaConfig = MEDIA_TYPE_OPTIONS.find(option => option.value === mediaType)

  if (!mediaConfig) {
    return null
  }

  const {
    component,
    multiple: defaultMultiple = false,
    maxCount: defaultMaxCount = 1,
    maxSize: defaultMaxSize = 50,
    unit = '',
    placeholder = '请输入数值',
  } = mediaConfig

  // 使用传入的参数或配置的默认值
  const finalMultiple = multiple ?? defaultMultiple
  const finalMaxCount = maxCount || defaultMaxCount
  const finalMaxSize = maxSize || defaultMaxSize

  // 根据组件类型渲染不同的输入组件
  switch (component) {
    case 'image':
      return (
        <MediaUpload
          type="image"
          value={value as string | string[]}
          onChange={onChange as (value: string | string[]) => void}
          disabled={disabled}
          action={uploadAction}
          multiple={finalMultiple}
          maxCount={finalMaxCount}
          maxSize={finalMaxSize}
        />
      )

    case 'video':
      return (
        <MediaUpload
          type="video"
          value={value as string | string[]}
          onChange={onChange as (value: string | string[]) => void}
          disabled={disabled}
          action={uploadAction}
          multiple={finalMultiple}
          maxCount={finalMaxCount}
          maxSize={finalMaxSize}
        />
      )

    case 'file':
      return (
        <MediaUpload
          type="file"
          value={value as string | string[]}
          onChange={onChange as (value: string | string[]) => void}
          disabled={disabled}
          action={uploadAction}
          multiple={finalMultiple}
          maxCount={finalMaxCount}
          maxSize={finalMaxSize}
        />
      )

    case 'inputNumber':
      return (
        <InputNumber
          value={value as number}
          onChange={val => onChange?.(val || 0)}
          disabled={disabled}
          placeholder={placeholder}
          style={{ width: '100%' }}
          min={0}
          precision={2}
          addonAfter={unit || null}
        />
      )

    default:
      return (
        <MediaUpload
          type="file"
          value={value as string | string[]}
          onChange={onChange as (value: string | string[]) => void}
          disabled={disabled}
          action={uploadAction}
          multiple={finalMultiple}
          maxCount={finalMaxCount}
          maxSize={finalMaxSize}
        />
      )
  }
}

export default MediaFormInput
