.uploadItemActions {
    position: absolute;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    background-color: rgb(0 0 0 / 50%);
    opacity: 0;
    transition: opacity 0.3s;

    &:hover {
        opacity: 1 !important;
    }
}

.videoPreview {
    width: 100%;
    max-height: 70vh;
    object-fit: contain;
}

// 上传中状态覆盖层
.uploadingOverlay {
    position: absolute;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    background-color: rgb(255 255 255 / 80%);
    flex-direction: column;
}

// 上传失败状态覆盖层
.errorOverlay {
    position: absolute;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgb(255 255 255 / 90%);
    flex-direction: column;
}

// 上传中图标
.uploadingIcon {
    font-size: 24px;
    color: #1890ff;
}

// 错误图标
.errorIcon {
    font-size: 24px;
    color: #ff4d4f;
}

// 上传中文字
.uploadingText {
    margin-top: 8px;
    font-size: 12px;
    color: #666;
}

// 错误文字
.errorText {
    margin-top: 8px;
    font-size: 12px;
    color: #ff4d4f;
}

// 上传进度
.uploadProgress {
    margin-top: 4px;
    font-size: 12px;
    color: #999;
}

// 文档文件容器样式
.documentContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    text-align: center;
}

// 文件项容器样式
.fileItemContainer {
    position: relative;
    width: 100%;
    height: 100%;
}