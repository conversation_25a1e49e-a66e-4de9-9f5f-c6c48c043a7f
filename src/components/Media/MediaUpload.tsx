import { getUploadProps } from '@/utils/file'
import { envUrl } from '@/utils/request'
import {
  DeleteOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  LoadingOutlined,
  PlusOutlined,
} from '@ant-design/icons'
import { Button, Flex, Image, message, Modal, Spin, Typography, Upload } from 'antd'
import { RcFile, UploadFile } from 'antd/es/upload'
import React, { useEffect, useState } from 'react'
import EnvImage from '../EnvImage'
import styles from './MediaUpload.scss'

type MediaType = 'image' | 'video' | 'file'

// 扩展UploadFile类型以包含originalUrl字段
interface ExtendedUploadFile extends UploadFile {
  originalUrl?: string
}

interface MediaUploadProps {
  value?: string | string[]
  type?: MediaType
  disabled?: boolean
  accept?: string
  action: string
  multiple?: boolean
  maxCount?: number
  maxSize?: number // MB
  onChange?: (value: string | string[]) => void
}

const fileExtensions = ['xlsx', 'xls', 'doc', 'docx', 'pdf', 'txt']

const MediaUpload: React.FC<MediaUploadProps> = ({
  value,
  type = 'image',
  accept,
  disabled = false,
  action,
  multiple = false,
  maxCount = 1,
  maxSize = 50,
  onChange,
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>([])
  const [previewOpen, setPreviewOpen] = useState(false)
  const [previewContent, setPreviewContent] = useState('')
  const [previewType, setPreviewType] = useState<MediaType>('image')

  // 根据媒体类型设置默认accept
  const getDefaultAccept = (mediaType: MediaType) => {
    switch (mediaType) {
      case 'image':
        return '.png,.jpg,.jpeg,.gif,.webp'
      case 'video':
        return '.mp4,.avi,.mov,.wmv,.flv,.mkv'
      case 'file':
        return '.pdf,.doc,.docx,.xlsx,.xls,.txt'
      default:
        return '*'
    }
  }

  const acceptType = accept || getDefaultAccept(type)

  // 检查文件是否为文档类型
  const isDocumentFile = (url: string) => {
    try {
      const extension = url.split('.').pop()?.toLowerCase()

      return extension ? fileExtensions.includes(extension) : false
    } catch {
      return false
    }
  }

  // 将value转换为UploadFile格式 - 智能比较，避免重复初始化
  useEffect(() => {
    // 如果fileList中有上传中的文件，不要重置
    const hasUploadingFiles = fileList.some(file => file.status === 'uploading')

    if (hasUploadingFiles) {
      return
    }

    // 比较当前value和fileList中的originalUrl是否一致
    let currentUrls: string[] = []

    if (Array.isArray(value)) {
      currentUrls = value.filter(url => url)
    } else if (value) {
      currentUrls = [value]
    }
    const fileListUrls = fileList.map(file => (file as any).originalUrl).filter(url => url)

    // 如果URL数组相同，不需要重新初始化
    if (JSON.stringify(currentUrls.sort()) === JSON.stringify(fileListUrls.sort())) {
      return
    }

    if (!value) {
      setFileList([])

      return
    }

    const urls = Array.isArray(value) ? value : [value]
    const newFileList: UploadFile[] = urls
      .filter(url => url) // 过滤空值
      .map((url, index) => {
        // 确保originalUrl是相对路径，url是完整URL
        const originalUrl = url.startsWith('http') ? url.replace(envUrl, '') : url
        const fullUrl = url.startsWith('http') ? url : envUrl + url

        return {
          uid: `${index}`,
          name: url.split('/').pop() || `文件${index + 1}`,
          status: 'done',
          url: fullUrl, // 完整URL用于渲染
          originalUrl, // 原始URL（相对路径）用于保存
          response: { data: { data: [{ url: originalUrl }] } },
        }
      })

    setFileList(newFileList)
  }, [value, fileList])

  // 文件上传前检查
  const beforeUpload = (file: RcFile) => {
    // 检查文件大小
    if (file.size === 0) {
      message.error('空文件不能上传')

      return Upload.LIST_IGNORE
    }

    const isLtMaxSize = file.size / 1024 / 1024 < maxSize

    if (!isLtMaxSize) {
      message.error(`文件大小不能超过 ${maxSize}MB`)

      return false
    }

    // 检查文件类型
    if (type === 'image' && !file.type.startsWith('image/')) {
      message.error('只能上传图片文件')

      return false
    }

    if (type === 'video' && !file.type.startsWith('video/')) {
      message.error('只能上传视频文件')

      return false
    }

    return true
  }

  // 处理上传状态变化
  const handleChange = ({ fileList: newFileList }: { fileList: UploadFile[] }) => {
    // 更新文件列表，为新上传的文件设置双URL字段
    const updatedFileList = newFileList.map(file => {
      if (file.status === 'done' && file.response?.data?.data?.[0]?.url) {
        const responseUrl = file.response.data.data[0].url
        const originalUrl = responseUrl.startsWith('http') ? responseUrl.replace(envUrl, '') : responseUrl
        const fullUrl = responseUrl.startsWith('http') ? responseUrl : envUrl + responseUrl

        return {
          ...file,
          url: fullUrl, // 完整URL用于渲染
          originalUrl, // 原始URL用于保存
        }
      }

      return file
    })

    setFileList(updatedFileList)

    // 检查是否有上传失败的文件，显示错误消息
    const failedFiles = updatedFileList.filter(file => file.status === 'error')

    if (failedFiles.length > 0) {
      message.error(`${failedFiles.length} 个文件上传失败，请重新选择文件上传`)
    }

    // 提取已上传成功的文件URL (直接使用originalUrl字段)
    const successFiles = updatedFileList
      .filter(file => file.status === 'done')
      .map(file => (file as any).originalUrl || '')
      .filter(url => url)

    if (multiple) {
      onChange?.(successFiles)
    } else {
      onChange?.(successFiles[0] || '')
    }
  }

  // 预览文件
  const handlePreview = (file: UploadFile) => {
    const url = file.url || ''

    setPreviewContent(url)

    if (url.match(/\.(mp4|avi|mov|wmv|flv|mkv)$/i)) {
      setPreviewType('video')
    } else if (url.match(/\.(png|jpg|jpeg|gif|webp)$/i)) {
      setPreviewType('image')
    } else {
      setPreviewType('file')
    }

    setPreviewOpen(true)
  }

  // 渲染文件项
  const renderFileItem = (file: UploadFile) => {
    console.log('file=>', file)
    const extendedFile = file as ExtendedUploadFile
    const url = file.url || ''
    const isDocument = isDocumentFile(url)

    // 基础内容渲染
    let baseContent = null

    if (type === 'image' && !isDocument) {
      baseContent = (
        <EnvImage
          src={extendedFile.originalUrl || ''}
          height={100}
          width={100}
          alt={file.name}
          className="radius img-cover"
        />
      )
    } else if (type === 'video') {
      baseContent = <video src={url} className="radius img-cover full-v" muted style={{ maxWidth: '100px' }} />
    } else {
      // 文档类型显示文件名
      baseContent = (
        <div className={styles.documentContainer}>
          <Typography.Text ellipsis={{ tooltip: file.name }}>{file.name}</Typography.Text>
        </div>
      )
    }

    // 根据文件状态添加覆盖层
    return (
      <div className={styles.fileItemContainer}>
        {baseContent}

        {/* 上传中状态覆盖层 */}
        {file.status === 'uploading' && (
          <div className={styles.uploadingOverlay}>
            <Spin indicator={<LoadingOutlined className={styles.uploadingIcon} spin />} />
            <div className={styles.uploadingText}>上传中...</div>
            {file.percent && <div className={styles.uploadProgress}>{Math.round(file.percent)}%</div>}
          </div>
        )}

        {/* 上传失败状态覆盖层 */}
        {file.status === 'error' && (
          <div className={styles.errorOverlay}>
            <ExclamationCircleOutlined className={styles.errorIcon} />
            <div className={styles.errorText}>上传失败</div>
          </div>
        )}
      </div>
    )
  }

  // 上传按钮
  const uploadButton = (
    <Flex vertical gap={6} align="center">
      <PlusOutlined className="fs-2xlg" />
      <strong>
        {type === 'image' && '选择图片'}
        {type === 'video' && '选择视频'}
        {type === 'file' && '选择文件'}
      </strong>
      <div className="text-secondary fs-sm">最大 {maxSize}MB</div>
    </Flex>
  )

  console.log('fileList=>', fileList)

  return (
    <>
      <Upload
        {...getUploadProps(action)}
        accept={acceptType}
        multiple={multiple}
        maxCount={maxCount}
        fileList={fileList}
        listType="picture-card"
        beforeUpload={beforeUpload}
        onChange={handleChange}
        onPreview={handlePreview}
        disabled={disabled}
        name="filename"
        itemRender={(originNode, file) => (
          <div style={{ position: 'relative', width: '100%', height: '100px' }}>
            {renderFileItem(file)}
            {!disabled && (
              <div className={styles.uploadItemActions}>
                {/* 只有上传成功时才显示预览按钮 */}
                {file.status === 'done' && (
                  <Button
                    type="text"
                    icon={<EyeOutlined />}
                    onClick={() => handlePreview(file)}
                    style={{ color: 'white', marginRight: 8 }}
                  />
                )}
                {/* 删除按钮始终显示（上传中可以取消，失败可以移除） */}
                <Button
                  type="text"
                  className="text-pure"
                  icon={<DeleteOutlined />}
                  onClick={() => {
                    const newFileList = fileList.filter(item => item.uid !== file.uid)

                    setFileList(newFileList)

                    // 直接使用originalUrl字段
                    const urls = newFileList
                      .filter(f => f.status === 'done')
                      .map(f => (f as any).originalUrl || '')
                      .filter(url => url)

                    onChange?.(multiple ? urls : urls[0] || '')
                  }}
                />
              </div>
            )}
          </div>
        )}>
        {fileList.length >= maxCount ? null : uploadButton}
      </Upload>

      <Modal
        title="文件预览"
        open={previewOpen}
        onCancel={() => setPreviewOpen(false)}
        footer={null}
        width={800}
        centered>
        {previewType === 'image' && <Image src={previewContent} style={{ width: '100%' }} />}
        {previewType === 'video' && (
          <video controls className={styles.videoPreview} src={previewContent}>
            您的浏览器不支持视频播放
          </video>
        )}
        {previewType === 'file' && (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <Typography.Link href={previewContent} target="_blank">
              点击下载文件
            </Typography.Link>
          </div>
        )}
      </Modal>
    </>
  )
}

export default MediaUpload
