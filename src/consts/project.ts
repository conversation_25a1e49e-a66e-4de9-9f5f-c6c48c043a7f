// 项目状态
export const PROJECT_STATUS_MAP: Record<number, { label: string; status: any }> = {
  0: { label: '剧本调研', status: 'default' },
  1: { label: '项目筹备', status: 'default' },
  2: { label: '拍摄中', status: 'processing' },
  3: { label: '后期制作', status: 'processing' },
  4: { label: '上架准备', status: 'processing' },
  5: { label: '已上架', status: 'success' },
  6: { label: '暂停', status: 'warning' },
  7: { label: '取消', status: 'error' },
  8: { label: '补拍', status: 'warning' },
}

export const PROJECT_STATUS = Object.entries(PROJECT_STATUS_MAP).map(([value, item]) => ({
  label: item.label,
  value: Number(value),
}))

// 项目状态枚举
export enum ProjectStatus {
  SCRIPT_RESEARCH = 0, // 剧本调研
  PROJECT_PREPARATION = 1, // 项目筹备
  SHOOTING = 2, // 拍摄中
  POST_PRODUCTION = 3, // 后期制作
  LAUNCH_PREPARATION = 4, // 上架准备
  LAUNCHED = 5, // 已上架
  PAUSED = 6, // 暂停
  CANCELLED = 7, // 取消
  RESHOOTING = 8, // 补拍
}
