import type { Rule } from 'antd/es/form'

export const seriesIdsRule: Rule = {
  pattern: /^[0-9,]+$/, // 只允许数字和英文逗号
  message: '只能输入数字和英文逗号',
}
export const required: Rule = { required: true }
export const phone: Rule = {
  pattern: /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[189]))\d{8}$/,
  message: '请检查手机号码',
}
export const email: Rule = {
  pattern: /^[A-Za-z0-9\u4e00-\u9fa5_]+@[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(?:\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/,
  message: '请检查邮箱格式',
}
export const number: Rule = {
  pattern: /^[0-9]*$/,
  message: '请输入数字',
}
export const penNameZN: Rule = { pattern: /^[\u4E00-\u9FA5a-zA-Z0-9]+$/, message: '笔名要求汉字/英文/数字' }
export const penNameEN: Rule = { pattern: /^[a-zA-Z0-9]+$/, message: '英文笔名要求英文/数字' }
export const qq: Rule = { pattern: /^[1-9][0-9]{4,10}$/, message: '请检查QQ号格式' }
export const count = (min = 0, max = Infinity) => ({ min, max } as Rule)

export const username: Rule = {
  pattern: /^(?=.*[a-zA-Z])(?=.*\d)[a-zA-Z\d]+$/,
  message: '用户名需为英文+数字的组合',
}

export const password: Rule = {
  pattern:
    // eslint-disable-next-line max-len
    /^(?![0-9]+$)(?![A-Za-z]+$)(?![_]+$)(?!.*[\u4E00-\u9FA5\uF900-\uFA2D])(?!^[!@#$%^&*()_+=\\[\\]{}|;:',.<>\/-?"·~\$`!\^"()#]+?$)[\S]{8,16}$/,
  message: '必须为8-16位字母/数字/符号至少2种',
}

// 身份证号验证
export const idCard: Rule = {
  pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  message: '请输入有效的身份证号码',
}

// 统一社会信用代码验证
export const businessLicense: Rule = {
  pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/,
  message: '请输入有效的统一社会信用代码',
}

// 银行卡号验证
export const bankCard: Rule = {
  pattern: /^\d{16,19}$/,
  message: '请输入有效的银行卡号（16-19位数字）',
}

// 校验字符串是否为JSON格式
export const checkIsJSON = (str: string) => {
  try {
    const obj = JSON.parse(str)

    if (typeof obj === 'object' && obj) {
      return true
    }

    return false
  } catch (e) {
    return false
  }
}
