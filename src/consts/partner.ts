// 合作商类型枚举
export enum PartnerType {
  SUPPLIER = 1, // 供应商
  INDIVIDUAL = 2, // 个人
}

// 合作商类型配置（统一管理标签和颜色）
export const PARTNER_TYPE_CONFIG: Record<PartnerType, { label: string; color: string }> = {
  [PartnerType.SUPPLIER]: { label: '供应商', color: 'blue' },
  [PartnerType.INDIVIDUAL]: { label: '个人', color: 'green' },
} as const

// 基于配置生成合作商类型选项数组（用于Select组件）
export const PARTNER_TYPE_OPTIONS = Object.entries(PARTNER_TYPE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as PartnerType,
  color: config.color,
}))

// 合作商状态枚举
export enum PartnerStatus {
  NORMAL = 1, // 正常
  DISABLED = 2, // 禁用
}

// 合作商状态配置（统一管理标签和颜色）
export const PARTNER_STATUS_CONFIG: Record<PartnerStatus, { label: string; color: string }> = {
  [PartnerStatus.NORMAL]: { label: '合作中', color: 'success' },
  [PartnerStatus.DISABLED]: { label: '禁止合作', color: 'error' },
} as const

// 基于配置生成合作商状态选项数组（用于Select组件）
export const PARTNER_STATUS_OPTIONS = Object.entries(PARTNER_STATUS_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as PartnerStatus,
  color: config.color,
}))

// 注意：实名认证状态枚举使用 src/consts/index.ts 中的 FddVerifyStatus 和 FDD_VERIFY_STATUS_CONFIG

// 银行账户类型枚举（合作商）
export enum BankAccountParentType {
  PERSON = 0, // 人员
  PARTNER = 1, // 合作商
}