import type { ModalProps } from 'antd'
import type { ConfigProviderProps } from 'antd/es/config-provider'
import zhCN from 'antd/locale/zh_CN'

export default {
  theme: {
    token: {
      colorPrimary: '#f60',
      colorInfo: '#0080ff',
      colorSuccess: '#2eb492',
      colorError: '#ff4d4f',
      colorBorder: '#f2f3f8',
      colorLink: '#f60',
      colorLinkHover: '#03A9F4',
      colorPrimaryBg: '#F6F7FB',
      green: '#2eb492',
      borderRadius: 4,
      screenXXL: 1700,
      screenXXLMin: 1700,
    },
    components: {
      Layout: {
        bodyBg: 'transparent',
        headerBg: 'transparent',
      },
    },
  },
  form: { validateMessages: { required: '${label}不能为空' } },
  locale: zhCN,
} as ConfigProviderProps

// 只读弹窗配置，用于【查看】
export const dialogReadonlyProps: ModalProps = {
  footer: false, // 没有弹窗底部区域
  keyboard: true, // 可以通过键盘【ESC】关闭
  maskClosable: true, // 可以通过点击遮罩层关闭
}
