import dayjs from 'dayjs'
import type { TimeRangePickerProps } from 'antd'

// format
export const DATE_FORMAT_YEAR = 'YYYY'
export const DATE_FORMAT_DAY = 'YYYY-MM-DD'
export const DATE_FORMAT_DAY_CN = 'YYYY年MM月DD日'
export const DATE_FORMAT_BASE = 'YYYY-MM-DD HH:mm:ss'
export const DATE_FORMAT_BASE_SEC = 'YYYY-MM-DD HH:mm:ss.SSS'
export const DATE_FORMAT_HOURS = 'YYYY-MM-DD HH'
export const DATE_FORMAT_REVERSE = 'HH:mm:ss YYYY-MM-DD'
export const DATE_FORMAT_TIME_REVERSE = 'HH:mm:ss MM-DD'
export const DATE_FORMAT_SHORT = 'YYYYMMDDHHmm'

// presets
export const DATE_PRESETS_BASE: TimeRangePickerProps['presets'] = [
  { label: '今天', value: [dayjs().startOf('day'), dayjs().endOf('day')] },
  { label: '昨天', value: [dayjs().startOf('day').subtract(1, 'd'), dayjs().endOf('day').subtract(1, 'd')] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  {
    label: '上月',
    value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')],
  },
  { label: '最近7天', value: [dayjs().add(-7, 'd'), dayjs()] },
  { label: '最近14天', value: [dayjs().add(-14, 'd'), dayjs()] },
  { label: '最近30天', value: [dayjs().add(-30, 'd'), dayjs()] },
  { label: '最近90天', value: [dayjs().add(-90, 'd'), dayjs()] },
]

// 今天
export const TODAY_RANGE = [dayjs().startOf('day').add(-1, 'd'), dayjs().endOf('day')]

// 最近7天
export const SEVEN_DAYS_RANGE = [dayjs().startOf('day').add(-7, 'd'), dayjs().endOf('day')]

// 最近两个月
export const ONE_MONTH_RANGE = [dayjs().startOf('day').add(-30, 'd'), dayjs().endOf('day')]

// 最近两个月
export const TWO_MONTH_RANGE = [dayjs().startOf('day').add(-60, 'd'), dayjs().endOf('day')]

const generatePresets = (gap = 13) => [
  {
    label: '今日',
    value: [
      dayjs()
        .startOf('day')
        .add(-(gap + 24), 'hour'),
      dayjs().add(-gap, 'hour'),
    ],
  },
  {
    label: '昨天',
    value: [
      dayjs()
        .startOf('day')
        .add(-(gap + 24 + 24), 'hour'),
      dayjs()
        .startOf('day')
        .add(-(gap + 24), 'hour')
        .subtract(1, 'second'),
    ],
  },
  {
    label: '本月',
    value: [dayjs().startOf('month').add(-gap, 'hour'), dayjs().endOf('month').add(-gap, 'hour')],
  },
  {
    label: '上月',
    value: [
      dayjs().subtract(1, 'month').startOf('month').add(-gap, 'hour'),
      dayjs().subtract(1, 'month').endOf('month').add(-gap, 'hour'),
    ],
  },
  {
    label: '最近7天',
    value: [dayjs().add(-(7 * 24 + gap), 'hour'), dayjs().add(-gap, 'hour')],
  },
  {
    label: '最近14天',
    value: [dayjs().add(-(14 * 24 + gap), 'hour'), dayjs().add(-gap, 'hour')],
  },
  {
    label: '最近30天',
    value: [dayjs().add(-(30 * 24 + gap), 'hour'), dayjs().add(-gap, 'hour')],
  },
  {
    label: '最近90天',
    value: [dayjs().add(-(90 * 24 + gap), 'hour'), dayjs().add(-gap, 'hour')],
  },
]

export const DATE_PRESETS_UTC4 = generatePresets(12) // UTC-4

export const DATE_PRESETS_UTC5 = generatePresets() // UTC-5

export const adsPresets = (tz = -13) => [
  {
    label: '今日',
    value: [dayjs().add(tz, 'hour'), dayjs().add(tz, 'hour')],
  },
  { label: '昨天', value: [dayjs().add(tz, 'hour').subtract(1, 'd'), dayjs().add(tz, 'hour').subtract(1, 'd')] },
  { label: '本月', value: [dayjs().startOf('month'), dayjs().endOf('month')] },
  {
    label: '上月',
    value: [dayjs().subtract(1, 'month').startOf('month'), dayjs().subtract(1, 'month').endOf('month')],
  },
  { label: '最近7天', value: [dayjs().add(tz, 'hour').add(-7, 'd'), dayjs().add(tz, 'hour')] },
  { label: '最近14天', value: [dayjs().add(tz, 'hour').add(-14, 'd'), dayjs().add(tz, 'hour')] },
  { label: '最近30天', value: [dayjs().add(tz, 'hour').add(-30, 'd'), dayjs().add(tz, 'hour')] },
  { label: '最近90天', value: [dayjs().add(tz, 'hour').add(-90, 'd'), dayjs().add(tz, 'hour')] },
]

// 时区
export const ZoneOptions = [
  {
    label: '西五区',
    value: 0,
  },
  {
    label: '北京时间',
    value: 1,
  },
]

// C2时区
export const ZoneOptions2 = [
  {
    label: '西四区',
    value: 2,
  },
  {
    label: '北京时间',
    value: 1,
  },
]
