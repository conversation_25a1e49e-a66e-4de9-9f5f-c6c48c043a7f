import _ from 'lodash'
import type { ProTableProps } from '@ant-design/pro-components'

export const TEXT_DEFALUT_PROPS = {
  maxLength: 500,
  showCount: true,
}
export const NO_SEARCH_PROPS: {
  search: ProTableProps<null, null>['search']
  style: ProTableProps<null, null>['style']
} = { search: { optionRender: false }, style: { marginTop: '-16px' } }
export const generateProTableProps = (formRef: any) => {
  const search: ProTableProps<null, null>['search'] = {
    span: 4,
    searchText: '搜索',
    optionRender: (searchConfig: any, formProps: any, dom: any) => [dom[1], dom[0]],
  }
  const form: ProTableProps<null, null>['form'] = {
    layout: 'vertical',
    onValuesChange: _.debounce(() => {
      formRef.current?.submit()
    }, 500),
  }

  return {
    search,
    form,
  }
}

export const STATUS_DICT: Record<number, string> = {
  1: '启用',
  0: '禁用',
}
export const MT_DICT: Record<number, string> = {
  1: 'iOS',
  4: 'Android',
}
export const STATUS_OPTIONS = [
  {
    label: '启用',
    value: 1,
  },
  {
    label: '禁用',
    value: 0,
  },
]
export const MT_OPTIONS = [
  {
    label: 'iOS',
    value: 1,
  },
  {
    label: 'Android',
    value: 4,
  },
]
export const BAR_STATUS_OPTIONS = [
  {
    label: '白色',
    value: 0,
  },
  {
    label: '黑色',
    value: 1,
  },
]
