// 预算分类枚举和配置

// 一级分类枚举
export enum BudgetCategory {
  PRODUCTION = 1, // 制片
  DIRECTOR = 2, // 导演
  PHOTOGRAPHY = 3, // 摄影
  PHOTOGRAPHY_EQUIPMENT = 4, // 摄影器材
  SOUND = 5, // 收音
  POST_PRODUCTION = 6, // 后期
  LIGHTING = 7, // 灯光
  ART = 8, // 美术组
  COSTUME_MAKEUP = 9, // 服化组
  ACTOR = 10, // 演员
  MISCELLANEOUS = 11, // 小计
  VENUE_RENTAL = 12, // 场租
}

// 二级分类枚举
export enum BudgetSubcategory {
  // 制片 (1xx)
  ON_SITE_PRODUCER = 101, // 现场制片
  LOCATION_PRODUCER = 102, // 外联制片
  LIFE_PRODUCER = 103, // 生活制片
  COORDINATOR = 104, // 统筹
  FIELD_ASSISTANT = 105, // 场务

  // 导演 (2xx)
  DIRECTOR = 201, // 导演
  EXECUTIVE_DIRECTOR = 202, // 执行导演
  SCRIPT_SUPERVISOR = 203, // 场记
  MARTIAL_ARTS_DIRECTOR = 204, // 武术指导
  ACTOR_ASSISTANT_DIRECTOR = 205, // 演员副导

  // 摄影 (3xx)
  CINEMATOGRAPHER_A = 301, // 摄影师A
  CINEMATOGRAPHER_B = 302, // 摄影师B
  CINEMATOGRAPHER_C = 303, // 摄影师C
  CAMERA_ASSISTANT = 304, // 摄影助理
  STILL_PHOTOGRAPHER = 305, // 剧照

  // 摄影器材 (4xx)
  PHOTOGRAPHY_EQUIPMENT = 401, // 摄影器材

  // 收音 (5xx)
  SOUND_ENGINEER = 501, // 收音师
  SOUND_ASSISTANT = 502, // 收音助理

  // 后期 (6xx)
  DIT = 601, // DIT
  DUBBING = 602, // 配音
  VFX = 603, // 特效
  EDITING = 604, // 剪辑

  // 灯光 (7xx)
  LIGHTING_DIRECTOR = 701, // 灯光师
  LIGHTING_ASSISTANT = 702, // 灯光助理
  LIGHTING_CHIEF_ASSISTANT = 703, // 灯光大助
  LIGHTING_EQUIPMENT = 704, // 灯光器材

  // 美术组 (8xx)
  ART_DIRECTOR = 801, // 美术师
  PROPS = 802, // 道具
  SET_DECORATION = 803, // 陈设

  // 服化组 (9xx)
  COSTUME_DESIGNER = 901, // 服装师
  MAKEUP_ARTIST = 902, // 化妆师
  COSTUME_PURCHASE_RENTAL = 903, // 服装采买/租赁

  // 演员 (10xx)
  FEMALE_LEAD = 1001, // 女主
  FEMALE_SECOND_LEAD = 1002, // 女二
  MALE_LEAD = 1003, // 男主
  MALE_SECOND_LEAD = 1004, // 男二
  FEMALE_ANTAGONIST = 1005, // 女反
  MALE_ANTAGONIST = 1006, // 男反
  FEMALE_SECOND_ANTAGONIST = 1007, // 女反二
  MALE_SECOND_ANTAGONIST = 1008, // 男反二
  ACCOMPANYING_ACTOR = 1009, // 跟组演员
  SPECIAL_GUEST_ACTOR = 1010, // 特约演员
  SUPPORTING_ACTOR = 1011, // 配角
  SPECIAL_ACTOR = 1012, // 特殊演员
  EXTRA = 1013, // 群演
  STUNT_PERFORMER = 1014, // 武行

  // 小计 (11xx)
  PREPARATION_EXPENSES = 1101, // 筹备期费用
  VEHICLE_RENTAL = 1102, // 车辆租赁
  FUEL_COSTS = 1103, // 燃油费
  DRAMA_VEHICLE = 1104, // 戏用车辆
  OPENING_CEREMONY = 1105, // 开机
  FIELD_CONSUMPTION = 1106, // 场务消耗
  TRAVEL_EXPENSES = 1107, // 差旅费
  MISCELLANEOUS_DRAMA_EXPENSES = 1108, // 剧杂费
  INSURANCE = 1109, // 保险费
  MEAL_EXPENSES = 1110, // 餐费
  RISK_CONTROL_BUDGET = 1111, // 片场风险控制预算
  ACCOMMODATION = 1112, // 住宿费

  // 场租 (12xx)
  VENUE_RENTAL = 1201, // 场租
}

// 一级分类配置
export const BUDGET_CATEGORY_CONFIG: Record<BudgetCategory, { label: string; color: string }> = {
  [BudgetCategory.PRODUCTION]: { label: '制片', color: 'blue' },
  [BudgetCategory.DIRECTOR]: { label: '导演', color: 'green' },
  [BudgetCategory.PHOTOGRAPHY]: { label: '摄影', color: 'orange' },
  [BudgetCategory.PHOTOGRAPHY_EQUIPMENT]: { label: '摄影器材', color: 'purple' },
  [BudgetCategory.SOUND]: { label: '收音', color: 'cyan' },
  [BudgetCategory.POST_PRODUCTION]: { label: '后期', color: 'magenta' },
  [BudgetCategory.LIGHTING]: { label: '灯光', color: 'yellow' },
  [BudgetCategory.ART]: { label: '美术组', color: 'red' },
  [BudgetCategory.COSTUME_MAKEUP]: { label: '服化组', color: 'pink' },
  [BudgetCategory.ACTOR]: { label: '演员', color: 'gold' },
  [BudgetCategory.MISCELLANEOUS]: { label: '小计', color: 'gray' },
  [BudgetCategory.VENUE_RENTAL]: { label: '场租', color: 'lime' },
} as const

// 二级分类配置
export const BUDGET_SUBCATEGORY_CONFIG: Record<BudgetSubcategory, { label: string; category: BudgetCategory }> = {
  // 制片
  [BudgetSubcategory.ON_SITE_PRODUCER]: { label: '现场制片', category: BudgetCategory.PRODUCTION },
  [BudgetSubcategory.LOCATION_PRODUCER]: { label: '外联制片', category: BudgetCategory.PRODUCTION },
  [BudgetSubcategory.LIFE_PRODUCER]: { label: '生活制片', category: BudgetCategory.PRODUCTION },
  [BudgetSubcategory.COORDINATOR]: { label: '统筹', category: BudgetCategory.PRODUCTION },
  [BudgetSubcategory.FIELD_ASSISTANT]: { label: '场务', category: BudgetCategory.PRODUCTION },

  // 导演
  [BudgetSubcategory.DIRECTOR]: { label: '导演', category: BudgetCategory.DIRECTOR },
  [BudgetSubcategory.EXECUTIVE_DIRECTOR]: { label: '执行导演', category: BudgetCategory.DIRECTOR },
  [BudgetSubcategory.SCRIPT_SUPERVISOR]: { label: '场记', category: BudgetCategory.DIRECTOR },
  [BudgetSubcategory.MARTIAL_ARTS_DIRECTOR]: { label: '武术指导', category: BudgetCategory.DIRECTOR },
  [BudgetSubcategory.ACTOR_ASSISTANT_DIRECTOR]: { label: '演员副导', category: BudgetCategory.DIRECTOR },

  // 摄影
  [BudgetSubcategory.CINEMATOGRAPHER_A]: { label: '摄影师A', category: BudgetCategory.PHOTOGRAPHY },
  [BudgetSubcategory.CINEMATOGRAPHER_B]: { label: '摄影师B', category: BudgetCategory.PHOTOGRAPHY },
  [BudgetSubcategory.CINEMATOGRAPHER_C]: { label: '摄影师C', category: BudgetCategory.PHOTOGRAPHY },
  [BudgetSubcategory.CAMERA_ASSISTANT]: { label: '摄影助理', category: BudgetCategory.PHOTOGRAPHY },
  [BudgetSubcategory.STILL_PHOTOGRAPHER]: { label: '剧照', category: BudgetCategory.PHOTOGRAPHY },

  // 摄影器材
  [BudgetSubcategory.PHOTOGRAPHY_EQUIPMENT]: { label: '摄影器材', category: BudgetCategory.PHOTOGRAPHY_EQUIPMENT },

  // 收音
  [BudgetSubcategory.SOUND_ENGINEER]: { label: '收音师', category: BudgetCategory.SOUND },
  [BudgetSubcategory.SOUND_ASSISTANT]: { label: '收音助理', category: BudgetCategory.SOUND },

  // 后期
  [BudgetSubcategory.DIT]: { label: 'DIT', category: BudgetCategory.POST_PRODUCTION },
  [BudgetSubcategory.DUBBING]: { label: '配音', category: BudgetCategory.POST_PRODUCTION },
  [BudgetSubcategory.VFX]: { label: '特效', category: BudgetCategory.POST_PRODUCTION },
  [BudgetSubcategory.EDITING]: { label: '剪辑', category: BudgetCategory.POST_PRODUCTION },

  // 灯光
  [BudgetSubcategory.LIGHTING_DIRECTOR]: { label: '灯光师', category: BudgetCategory.LIGHTING },
  [BudgetSubcategory.LIGHTING_ASSISTANT]: { label: '灯光助理', category: BudgetCategory.LIGHTING },
  [BudgetSubcategory.LIGHTING_CHIEF_ASSISTANT]: { label: '灯光大助', category: BudgetCategory.LIGHTING },
  [BudgetSubcategory.LIGHTING_EQUIPMENT]: { label: '灯光器材', category: BudgetCategory.LIGHTING },

  // 美术组
  [BudgetSubcategory.ART_DIRECTOR]: { label: '美术师', category: BudgetCategory.ART },
  [BudgetSubcategory.PROPS]: { label: '道具', category: BudgetCategory.ART },
  [BudgetSubcategory.SET_DECORATION]: { label: '陈设', category: BudgetCategory.ART },

  // 服化组
  [BudgetSubcategory.COSTUME_DESIGNER]: { label: '服装师', category: BudgetCategory.COSTUME_MAKEUP },
  [BudgetSubcategory.MAKEUP_ARTIST]: { label: '化妆师', category: BudgetCategory.COSTUME_MAKEUP },
  [BudgetSubcategory.COSTUME_PURCHASE_RENTAL]: { label: '服装采买/租赁', category: BudgetCategory.COSTUME_MAKEUP },

  // 演员
  [BudgetSubcategory.FEMALE_LEAD]: { label: '女主', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.FEMALE_SECOND_LEAD]: { label: '女二', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.MALE_LEAD]: { label: '男主', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.MALE_SECOND_LEAD]: { label: '男二', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.FEMALE_ANTAGONIST]: { label: '女反', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.MALE_ANTAGONIST]: { label: '男反', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.FEMALE_SECOND_ANTAGONIST]: { label: '女反二', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.MALE_SECOND_ANTAGONIST]: { label: '男反二', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.ACCOMPANYING_ACTOR]: { label: '跟组演员', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.SPECIAL_GUEST_ACTOR]: { label: '特约演员', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.SUPPORTING_ACTOR]: { label: '配角', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.SPECIAL_ACTOR]: { label: '特殊演员', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.EXTRA]: { label: '群演', category: BudgetCategory.ACTOR },
  [BudgetSubcategory.STUNT_PERFORMER]: { label: '武行', category: BudgetCategory.ACTOR },

  // 小计
  [BudgetSubcategory.PREPARATION_EXPENSES]: { label: '筹备期费用', category: BudgetCategory.MISCELLANEOUS },
  [BudgetSubcategory.VEHICLE_RENTAL]: { label: '车辆租赁', category: BudgetCategory.MISCELLANEOUS },
  [BudgetSubcategory.FUEL_COSTS]: { label: '燃油费', category: BudgetCategory.MISCELLANEOUS },
  [BudgetSubcategory.DRAMA_VEHICLE]: { label: '戏用车辆', category: BudgetCategory.MISCELLANEOUS },
  [BudgetSubcategory.OPENING_CEREMONY]: { label: '开机', category: BudgetCategory.MISCELLANEOUS },
  [BudgetSubcategory.FIELD_CONSUMPTION]: { label: '场务消耗', category: BudgetCategory.MISCELLANEOUS },
  [BudgetSubcategory.TRAVEL_EXPENSES]: { label: '差旅费', category: BudgetCategory.MISCELLANEOUS },
  [BudgetSubcategory.MISCELLANEOUS_DRAMA_EXPENSES]: { label: '剧杂费', category: BudgetCategory.MISCELLANEOUS },
  [BudgetSubcategory.INSURANCE]: { label: '保险费', category: BudgetCategory.MISCELLANEOUS },
  [BudgetSubcategory.MEAL_EXPENSES]: { label: '餐费', category: BudgetCategory.MISCELLANEOUS },
  [BudgetSubcategory.RISK_CONTROL_BUDGET]: { label: '片场风险控制预算', category: BudgetCategory.MISCELLANEOUS },
  [BudgetSubcategory.ACCOMMODATION]: { label: '住宿费', category: BudgetCategory.MISCELLANEOUS },

  // 场租
  [BudgetSubcategory.VENUE_RENTAL]: { label: '场租', category: BudgetCategory.VENUE_RENTAL },
} as const

// ==================== 静态数据结构设计 ====================

// 选项数据类型定义
export interface CategoryOption {
  label: string
  value: BudgetCategory
  color: string
}

export interface SubcategoryOption {
  label: string
  value: BudgetSubcategory
  category: BudgetCategory
}

// 一级分类选项数组（用于Select组件）
export const CATEGORY_OPTIONS: CategoryOption[] = Object.entries(BUDGET_CATEGORY_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as BudgetCategory,
  color: config.color,
}))

// 二级分类选项数组（用于Select组件）
export const SUBCATEGORY_OPTIONS: SubcategoryOption[] = Object.entries(BUDGET_SUBCATEGORY_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value) as BudgetSubcategory,
  category: config.category,
}))

// 按一级分类分组的二级分类选项Map（静态数据，可复用）
export const SUBCATEGORY_OPTIONS_BY_CATEGORY: Record<BudgetCategory, SubcategoryOption[]> = (() => {
  const optionsMap = {} as Record<BudgetCategory, SubcategoryOption[]>

  // 初始化所有分类为空数组
  Object.values(BudgetCategory).forEach(category => {
    if (typeof category === 'number') {
      optionsMap[category] = []
    }
  })

  // 按分类分组二级选项
  SUBCATEGORY_OPTIONS.forEach(option => {
    optionsMap[option.category].push(option)
  })

  return optionsMap
})()

// 二级分类值到一级分类的映射（用于快速查找）
export const SUBCATEGORY_TO_CATEGORY_MAP: Record<BudgetSubcategory, BudgetCategory> = (() => {
  const categoryMap = {} as Record<BudgetSubcategory, BudgetCategory>

  Object.entries(BUDGET_SUBCATEGORY_CONFIG).forEach(([subcategory, config]) => {
    categoryMap[Number(subcategory) as BudgetSubcategory] = config.category
  })

  return categoryMap
})()

// ==================== 工具函数 ====================

// 获取一级分类标签
export const getCategoryLabel = (category: BudgetCategory): string => {
  return BUDGET_CATEGORY_CONFIG[category]?.label || '未知分类'
}

// 获取二级分类标签
export const getSubcategoryLabel = (subcategory: BudgetSubcategory): string => {
  return BUDGET_SUBCATEGORY_CONFIG[subcategory]?.label || '未知子分类'
}

// 根据二级分类获取对应的一级分类
export const getCategoryBySubcategory = (subcategory: BudgetSubcategory): BudgetCategory => {
  return SUBCATEGORY_TO_CATEGORY_MAP[subcategory]
}

// 根据一级分类获取对应的二级分类选项
export const getSubcategoryOptions = (category: BudgetCategory): SubcategoryOption[] => {
  return SUBCATEGORY_OPTIONS_BY_CATEGORY[category] || []
}

// 根据已添加的subcategory集合生成过滤后的选项Map
export const getFilteredSubcategoryOptions = (addedSubcategories: Set<BudgetSubcategory>): Record<BudgetCategory, SubcategoryOption[]> => {
  const filteredMap = {} as Record<BudgetCategory, SubcategoryOption[]>

  Object.entries(SUBCATEGORY_OPTIONS_BY_CATEGORY).forEach(([category, options]) => {
    const categoryKey = Number(category) as BudgetCategory
    const filteredOptions = options.filter(option => !addedSubcategories.has(option.value))
    filteredMap[categoryKey] = filteredOptions
  })

  return filteredMap
}

// 获取分类标签的辅助函数
export const getBudgetCategoryLabel = (category: BudgetCategory): string => {
  return BUDGET_CATEGORY_CONFIG[category]?.label || '未知分类'
}

export const getBudgetSubcategoryLabel = (subcategory: BudgetSubcategory): string => {
  return BUDGET_SUBCATEGORY_CONFIG[subcategory]?.label || '未知子分类'
}

// 是否有发票枚举
export enum HasInvoice {
  NO = 0, // 否
  YES = 1, // 是
}

// 是否有发票配置
export const HAS_INVOICE_CONFIG: Record<HasInvoice, { label: string; color: string }> = {
  [HasInvoice.NO]: { label: '否', color: 'red' },
  [HasInvoice.YES]: { label: '是', color: 'green' },
} as const

// 是否有发票选项数组
export const HAS_INVOICE_OPTIONS = Object.entries(HAS_INVOICE_CONFIG).map(([value, config]) => ({
  label: config.label,
  value: Number(value),
  color: config.color,
}))

// 获取发票状态标签
export const getHasInvoiceLabel = (hasInvoice: number): string => {
  return HAS_INVOICE_CONFIG[hasInvoice as HasInvoice]?.label || '未知'
}

// 是否打包枚举值
export const IS_PACKAGE_OPTIONS = [
  { label: '否', value: false },
  { label: '是', value: true },
]

// 预算分组配置（用于批量添加和管理）
export interface IBudgetGroup {
  key: string
  label: string
  category: BudgetCategory
  subcategories: BudgetSubcategory[]
  description: string
}

export const BUDGET_GROUPS: IBudgetGroup[] = [
  {
    key: 'production',
    label: '制片组',
    category: BudgetCategory.PRODUCTION,
    subcategories: [
      BudgetSubcategory.ON_SITE_PRODUCER,
      BudgetSubcategory.LOCATION_PRODUCER,
      BudgetSubcategory.LIFE_PRODUCER,
      BudgetSubcategory.COORDINATOR,
      BudgetSubcategory.FIELD_ASSISTANT
    ],
    description: '制片人员相关费用'
  },
  {
    key: 'director',
    label: '导演组',
    category: BudgetCategory.DIRECTOR,
    subcategories: [
      BudgetSubcategory.DIRECTOR,
      BudgetSubcategory.EXECUTIVE_DIRECTOR,
      BudgetSubcategory.SCRIPT_SUPERVISOR,
      BudgetSubcategory.MARTIAL_ARTS_DIRECTOR,
      BudgetSubcategory.ACTOR_ASSISTANT_DIRECTOR
    ],
    description: '导演组人员费用'
  },
  {
    key: 'photography',
    label: '摄影组',
    category: BudgetCategory.PHOTOGRAPHY,
    subcategories: [
      BudgetSubcategory.CINEMATOGRAPHER_A,
      BudgetSubcategory.CINEMATOGRAPHER_B,
      BudgetSubcategory.CINEMATOGRAPHER_C,
      BudgetSubcategory.CAMERA_ASSISTANT,
      BudgetSubcategory.STILL_PHOTOGRAPHER
    ],
    description: '摄影人员费用'
  },
  {
    key: 'photography_equipment',
    label: '摄影器材',
    category: BudgetCategory.PHOTOGRAPHY_EQUIPMENT,
    subcategories: [BudgetSubcategory.PHOTOGRAPHY_EQUIPMENT],
    description: '摄影设备租赁费用'
  },
  {
    key: 'sound',
    label: '收音组',
    category: BudgetCategory.SOUND,
    subcategories: [
      BudgetSubcategory.SOUND_ENGINEER,
      BudgetSubcategory.SOUND_ASSISTANT
    ],
    description: '收音人员费用'
  },
  {
    key: 'post_production',
    label: '后期组',
    category: BudgetCategory.POST_PRODUCTION,
    subcategories: [
      BudgetSubcategory.DIT,
      BudgetSubcategory.DUBBING,
      BudgetSubcategory.VFX,
      BudgetSubcategory.EDITING
    ],
    description: '后期制作费用'
  },
  {
    key: 'lighting',
    label: '灯光组',
    category: BudgetCategory.LIGHTING,
    subcategories: [
      BudgetSubcategory.LIGHTING_DIRECTOR,
      BudgetSubcategory.LIGHTING_ASSISTANT,
      BudgetSubcategory.LIGHTING_CHIEF_ASSISTANT,
      BudgetSubcategory.LIGHTING_EQUIPMENT
    ],
    description: '灯光人员及设备费用'
  },
  {
    key: 'art',
    label: '美术组',
    category: BudgetCategory.ART,
    subcategories: [
      BudgetSubcategory.ART_DIRECTOR,
      BudgetSubcategory.PROPS,
      BudgetSubcategory.SET_DECORATION
    ],
    description: '美术组人员费用'
  },
  {
    key: 'costume_makeup',
    label: '服化组',
    category: BudgetCategory.COSTUME_MAKEUP,
    subcategories: [
      BudgetSubcategory.COSTUME_DESIGNER,
      BudgetSubcategory.MAKEUP_ARTIST,
      BudgetSubcategory.COSTUME_PURCHASE_RENTAL
    ],
    description: '服装化妆费用'
  },
  {
    key: 'actor',
    label: '演员组',
    category: BudgetCategory.ACTOR,
    subcategories: [
      BudgetSubcategory.FEMALE_LEAD,
      BudgetSubcategory.FEMALE_SECOND_LEAD,
      BudgetSubcategory.MALE_LEAD,
      BudgetSubcategory.MALE_SECOND_LEAD,
      BudgetSubcategory.FEMALE_ANTAGONIST,
      BudgetSubcategory.MALE_ANTAGONIST,
      BudgetSubcategory.FEMALE_SECOND_ANTAGONIST,
      BudgetSubcategory.MALE_SECOND_ANTAGONIST,
      BudgetSubcategory.ACCOMPANYING_ACTOR,
      BudgetSubcategory.SPECIAL_GUEST_ACTOR,
      BudgetSubcategory.SUPPORTING_ACTOR,
      BudgetSubcategory.SPECIAL_ACTOR,
      BudgetSubcategory.EXTRA,
      BudgetSubcategory.STUNT_PERFORMER
    ],
    description: '演员费用'
  },
  {
    key: 'miscellaneous',
    label: '制片费',
    category: BudgetCategory.MISCELLANEOUS,
    subcategories: [
      BudgetSubcategory.PREPARATION_EXPENSES,
      BudgetSubcategory.VEHICLE_RENTAL,
      BudgetSubcategory.FUEL_COSTS,
      BudgetSubcategory.DRAMA_VEHICLE,
      BudgetSubcategory.OPENING_CEREMONY,
      BudgetSubcategory.FIELD_CONSUMPTION,
      BudgetSubcategory.TRAVEL_EXPENSES,
      BudgetSubcategory.MISCELLANEOUS_DRAMA_EXPENSES,
      BudgetSubcategory.INSURANCE,
      BudgetSubcategory.MEAL_EXPENSES,
      BudgetSubcategory.RISK_CONTROL_BUDGET,
      BudgetSubcategory.ACCOMMODATION
    ],
    description: '其他制片相关费用'
  },
  {
    key: 'venue',
    label: '场地费用',
    category: BudgetCategory.VENUE_RENTAL,
    subcategories: [BudgetSubcategory.VENUE_RENTAL],
    description: '场地租赁相关费用'
  },
]

// 根据分组生成预算子项列表初始值的辅助函数
export const generateGroupedBudgetList = () => {
  const groupedList: Record<string, any[]> = {}

  BUDGET_GROUPS.forEach(group => {
    groupedList[group.key] = []
    group.subcategories.forEach(subcategory => {
      groupedList[group.key].push({
        category: group.category,
        subcategory: subcategory,
        personCount: 1,
      })
    })
  })

  return groupedList
}

