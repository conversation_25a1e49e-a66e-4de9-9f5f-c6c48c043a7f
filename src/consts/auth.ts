import { ENV } from '@/utils'

const { origin } = window.location

// 应用标识
export const CLIENT_ID = 'pr'

// 登录中转页路由
export const LOGGING_IN_URL = `${origin}/login`

// 登录系统页面地址
export const LOGIN_URL_MAP: Record<string, string> = {
  local: 'https://login-dev.changdu.ltd',
  dev: 'https://login-dev.changdu.ltd',
  test: 'https://login-test.changdu.ltd',
  stage: 'https://login-stage.changdu.vip',
  pro: 'https://login.changdu.vip',
}

// 统一登录退出地址
export const LOGOUT_URL = `${LOGIN_URL_MAP[ENV]}/net?callback=${encodeURIComponent(
  LOGGING_IN_URL
)}&project=${CLIENT_ID}`
