import { FormInstance, PaginationProps } from 'antd'

declare global {
  // 环境
  type IEnv = 'local' | 'dev' | 'test' | 'stage' | 'pro'
  interface ISearchParams {
    pageIndex?: number
    pageSize?: number
    [key: string]: any
  }

  enum Role {
    NULL = 0,
    STAFF = 1,
    ORGANIZATION = 2,
    NORMAL = 3,
    ADMIN = 9,
    GROUPER = 4,
  }

  type IOperateType =
    | 'view'
    | 'create'
    | 'copy'
    | 'edit'
    | 'publish'
    | 'delete'
    | 'disable'
    | 'download'
    | 'reset'
    | 'update'
    | 'invite'
    | 'reParse' // 重置
    | 'ignore' // 忽略
    | 'supplement' // 补充
    | 'nextStep'
    | 'evaluate'
    | 'end'
    | 'submitRecording'
    | 'abort'
    | 'historyEvaluate'
    | 'play'
    | 'refresh'
    | 'urgeEvaluate'
    | 'close'
    | 'submitApproval'
    | 'upload'
    | 'deleteFile'
    | 'remark'
    | 'deleteRemark'

  interface ISearchProps {
    form: FormInstance | any
    loading?: boolean
    initialValues?: any
    onReset?: () => void
    onSearch: (pageIndex: number) => void
    onOperate?: (type: IOperateType) => void
  }

  interface ISearchRoiProps {
    form: FormInstance | any
    loading?: boolean
    onSearch: (pageIndex: number) => void
    onExport?: () => void
    getParams: () => any
    normal?: string
    reportType: string
    reportKey: string
    core?: string
    assetReportType?: number
    tz: string
  }
  interface IListProps {
    data: any[]
    loading: boolean
    pagination: PaginationProps
    onChange: (page: number, pageSize: number) => void
    onOperate?: (event: IOperateType | string | any, data?: any) => void
  }

  interface IOptions {
    text?: string
    label: string
    value: number | string | boolean
    disabled?: boolean
  }

  interface ILibraryOptions extends IOptions {
    holder: string
  }
  interface IPagination {
    total: number
    current: number
    seriesTotal?: number
    pageIndex?: number
    pageSize?: number
  }

  interface ILoadingParams {
    Account: string
    Pwd: string
  }
  interface IUpdatePassword {
    password: string
    sourcePassword: string
  }

  interface IAuthority {
    href?: string
    id: string
    level: number
    menuType: string
    permission: string
    spread: boolean
    title: string
    children?: IAuthority[]
  }

  interface IUserInfo {
    userId: any
    holder?: string
    account: string
    nickName?: string
    company?: string
    name?: string // 姓名
    userName: string // 工号
  }

  // ================ 枚举定义 ================

  // 性别枚举
  enum Gender {
    MALE = 1, // 男
    FEMALE = 2, // 女
    OTHER = 3, // 其他
    UNDISCLOSED = 4, // 不透入性别
  }

  // 演员媒体类型枚举
  enum ActorMediaType {
    RECENT_PHOTO = 1, // 近照
    SELF_TAPE = 2, // self-tape
    DRAMA_SERIES = 3, // 剧集表演
    LATEST_QUOTE = 4, // 最新报价
  }

  // 角色类型枚举
  enum RoleType {
    LEAD = 1, // 主演
    SUPPORTING = 2, // 配角
    GUEST = 3, // 客串
    EXTRA = 4, // 群演
  }

  // 项目媒体类型枚举
  enum ProductionMediaType {
    PRODUCTION_PHOTO = 1, // 项目剧照
    COSTUME_PHOTO = 2, // 定妆照
    TRAILER = 3, // 片花
    WORD = 4, // Word文档
    EXCEL = 5, // Excel文档
    PDF = 6, // PDF文档
    GROUP_PHOTO = 7, // 合照
  }

  // 货币类型枚举
  enum CurrencyType {
    CNY = 0, // 人民币
    USD = 1, // 美元
  }

  // ================ 新的类型定义 ================

  // 演员基础信息（对应 pr_actors 表）
  interface IPrActor {
    id?: number
    fullName: string // 姓名
    stageName: string // 艺名
    dateOfBirth: number // 出生年份
    isChildActor: boolean // 是否儿童演员
    gender: Gender // 性别
    height?: number // 身高 (cm)
    weight?: number // 体重 (kg)
    bust?: number // 胸围 (cm)
    waist?: number // 腰围 (cm)
    hips?: number // 臀围 (cm)
    idNumber?: string // 身份证号
    school?: string // 毕业院校
    specialty?: string // 特长
    actingStyle?: string // 戏路风格
    isDelete: boolean // 是否删除
    createTime?: string // 创建时间
    updateTime?: string // 更新时间
  }

  // 演员媒体文件（对应 pr_actor_media 表）
  interface IPrActorMedia {
    id?: number
    actorId: number // 关联演员ID
    mediaType: ActorMediaType // 媒体类型
    mediaUrl?: string // 媒体文件URL或路径
    description?: string // 媒体描述
    createTime?: string // 上传时间
    updateTime?: string // 更新时间
  }

  // 项目信息（对应 pr_productions 表）
  interface IPrProduction {
    id?: number
    productionName: string // 短剧名称
    description?: string // 项目描述
    isDelete: boolean // 是否删除
    createTime?: string // 创建时间
    updateTime?: string // 更新时间
  }

  // 项目选角信息（对应 pr_production_actor 表）
  interface IPrProductionActor {
    id?: number
    productionId: number // 关联项目ID
    actorId: number // 关联演员ID
    roleType: RoleType // 角色类型
    cooperationEvaluation?: string // 合作度评价
    quotedPrice?: number // 合作价格/报价
    priceCurrency: CurrencyType // 价格货币类型
    sort: number // 排序
    description?: string // 备注
    createTime?: string // 创建时间
    updateTime?: string // 更新时间
  }

  // 项目媒体文件（对应 pr_production_media 表）
  interface IPrProductionMedia {
    id?: number
    productionId: number // 关联项目ID
    actorId?: number // 关联演员Id
    mediaType: ProductionMediaType // 媒体类型
    mediaUrl: string // 媒体文件URL或路径
    description?: string // 媒体描述
    sort: number // 排序
    createTime?: string // 创建时间
    updateTime?: string // 更新时间
  }

  // 操作日志（对应 pr_operation_log 表）
  interface IPrOperationLog {
    id?: number
    account?: string // 账号
    nickName?: string // 名称
    oldData?: string // 旧数据
    newData?: string // 新数据
    remark: string // 操作备注
    createTime?: string // 创建时间
  }

  // 项目搜索参数
  export interface IPrProductionSearchParams extends ISearchParams {
    productionName?: string // 项目名称
    description?: string // 项目描述
  }
}
