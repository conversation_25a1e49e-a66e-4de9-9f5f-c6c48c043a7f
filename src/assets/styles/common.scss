@use './variables' as *;

html,
body,
#root {
  margin: 0;
  padding: 0;
  height: 100%;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
}

:root {
  --color-primary: $color-primary;
  --color-primary-rgb: 175, 89, 227;
}

::-webkit-scrollbar {
  position: absolute;
  width: $scrollbar-width;
  height: $scrollbar-height;
  border-radius: $scrollbar-border-radius;
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  position: absolute;
  border-radius: $scrollbar-border-radius;
  background-color: $scrollbar-thumb-bg-color;
}

:global {
  // Layout
  .full-h {
    width: 100%;
  }

  .full-v {
    overflow: hidden;
    height: 100%;
  }

  .inline-block {
    display: inline-block !important;
  }

  .flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .flex-v-center {
    display: flex;
    align-items: center;
  }

  .flex-end {
    display: flex;
    justify-content: end;
  }

  .space-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  // == Color
  .text-primary {
    color: $color-primary !important;
  }

  .text-secondary {
    color: $color-secondary !important;
  }

  .text-assistant {
    color: #ccc !important;
  }

  .text-pure {
    color: #fff !important;
  }

  .text-processing {
    color: $color-processing !important;
  }

  .text-warning {
    color: $color-warning !important;
  }

  .text-success {
    color: $color-success !important;
  }

  .text-danger {
    color: $color-danger !important;
  }

  .text-transparent {
    color: transparent !important;
  }

  // Background
  .bg-primary {
    background-color: $color-primary !important;
  }

  .bg-danger {
    background-color: $color-danger !important;
  }

  .bg-warning {
    background-color: $color-warning !important;
  }

  .bg-success {
    background-color: $color-success !important;
  }

  .bg-pure {
    background-color: #fff !important;
  }

  .bg-secondary {
    background-color: $sider-bg-color !important;
  }

  // Align
  .text-left {
    text-align: left !important;
  }

  .text-center {
    text-align: center !important;
  }

  .text-right {
    text-align: right !important;
  }

  .text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // Font
  .fs-sm {
    font-size: $font-size-sm !important;
  }

  .fs-xs {
    font-size: $font-size-xs !important;
  }

  .fs-normal {
    font-size: $font-size-default !important;
  }

  .fs-lg {
    font-size: $font-size-lg !important;
  }

  .fs-xlg {
    font-size: $font-size-xlg !important;
  }

  .fs-2xlg {
    font-size: $font-size-2xlg !important;
  }

  .fw-normal {
    font-weight: normal !important;
  }

  .bold,
  .fw-bold {
    font-weight: bold !important;
  }

  .nowrap {
    white-space: nowrap;
  }

  .ellipsis {
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  // Space
  .no-margin {
    margin: 0 !important;
  }

  .no-margin-h {
    margin-inline: 0 !important;
  }

  .no-margin-v {
    margin-block: 0 !important;
  }

  .no-margin-l {
    margin-left: 0 !important;
  }

  .no-margin-r {
    margin-right: 0 !important;
  }

  .no-margin-t {
    margin-top: 0 !important;
  }

  .no-margin-b {
    margin-bottom: 0 !important;
  }

  .margin-auto {
    margin: auto !important;
  }

  .mt {
    margin-top: $space-block !important;
  }

  .mt-lg {
    margin-top: $space-block-lg !important;
  }

  .mr {
    margin-right: $space-inline !important;
  }

  .mb {
    margin-bottom: $space-block !important;
  }

  .mb-lg {
    margin-bottom: $space-block-lg !important;
  }

  .ml {
    margin-left: $space-inline !important;
  }

  .no-padding {
    padding: 0 !important;
  }

  .no-padding-h {
    padding-inline: 0 !important;
  }

  .no-padding-v {
    padding-block: 0 !important;
  }

  .no-padding-b {
    padding-bottom: 0 !important;

    .ant-card-body {
      padding-bottom: 0 !important;
    }
  }

  .pd-top-6 {
    padding-top: 6px !important;
  }

  .pd-top-10 {
    padding-top: 10px !important;
  }

  .pd-top-20 {
    padding-top: 20px !important;
  }

  .pd-top-24 {
    padding-top: 24px !important;
  }

  // Cursor
  .pointer {
    cursor: pointer;
  }

  .cursor-pointer {
    cursor: pointer;
  }

  .cursor-move {
    cursor: move;
  }

  // Border
  .border-primary {
    border-color: $color-primary !important;

    :global {
      .ant-card {
        border-color: $color-primary !important;
      }
    }
  }

  .border-success {
    border-color: $color-success !important;
  }

  // Border Radius
  .radius {
    border-radius: $border-radius;
  }

  .radius-lg {
    border-radius: $border-radius-lg;
  }

  // 宽度设置
  .w60 {
    width: 60px !important;
  }

  .w100 {
    width: 100px !important;
  }

  .w150 {
    width: 150px !important;
  }

  .w200 {
    width: 200px !important;
  }

  .w250 {
    width: 250px !important;
  }

  .w300 {
    width: 300px !important;
  }

  .w-full {
    width: 100% !important;
  }

  // 新增样式类 ws-pre-line
  .ws-pre-line {
    white-space: pre-line !important;
  }

  // 新增样式类 ws-pre-wrap 支持回车换行
  .ws-pre-wrap {
    white-space: pre-wrap;
  }

  // 新增样式类 hover-shadow
  .hover-shadow {
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 0 8px rgb(0 0 0 / 10%);
    }
  }

  // 图片
  .img-cover {
    object-fit: cover;
  }

  .img-contain {
    object-fit: contain;
    object-position: center;
  }

  .img-top {
    object-position: top;

    &,
    img {
      object-position: top;
    }
  }

  // 相对定位
  .affix {
    position: relative;
  }

  .tack {
    position: absolute;
  }

  .tack-lt {
    top: 0;
    left: 0;
  }

  .tack-rt {
    top: 0;
    right: 0;
  }

  .tack-center {
    inset: 0;
    margin: auto;
  }

  // hover显示操作栏
  .hover-show-actions {
    .actions {
      visibility: hidden;
    }

    &:hover {
      .actions {
        visibility: visible;
      }
    }
  }

  // hover显示删除图标
  .hover-show-remove {
    position: relative;
    border-radius: 4px;

    .remove {
      position: absolute;
      top: -16px;
      right: -15px;
      z-index: 10;
      visibility: hidden;
    }

    &:hover {
      transform: translateY(-2px);

      .remove {
        visibility: visible;
      }
    }
  }

  // Hover 效果
  .hover-move {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }
  }

  // 表单
  .search-form {
    .ant-row .ant-form-item {
      margin-bottom: 12px;
    }

    .ant-space-item .ant-form-item {
      margin-bottom: 0;
    }

    .ant-space {
      row-gap: 12px !important;
    }
  }

  .form-readOnly {
    .ant-radio-wrapper,
    .ant-select-selector {
      color: inherit !important;
      background: #fff !important;
    }

    .ant-select-selection-item {
      color: inherit !important;
    }
  }

  // List
  .list-sm {
    :global {
      .ant-list-item {
        padding: 0 0 16px !important;
      }
    }
  }
}
