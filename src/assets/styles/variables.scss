// 基础变量
$color-primary: #f60;
$color-default: #333;
$color-success: #2eb492;
$color-warning: #fc0;
$color-secondary: rgb(0 0 0 / 45%);
$color-processing: #0080ff;
$color-danger: #ff4d4f;
$color-link: #0080ff;
$border-color: rgb(5 5 5 / 6%);

// Layout
$nav-gap: 8px;
$nav-height: 56px;
$nav-padding-inline: 24px;
$nav-bg-color: #fff;
$nav-dark-bg-color: #2d3541;
$sider-width: 220px;
$sider-bg-color: #f9f9f9;
$sider-menu-bg: #e6edfb;
$sider-border-color: #f0f0f0;
$sider-icon-font-size: 16px;
$sider-menu-selected-bg: #e6edfb;
$scrollbar-width: 12px;
$content-padding: 24px;
$content-padding-inline: 24px;
$content-padding-block: 24px;
$logo-bg-color: $sider-bg-color;

// Font
$font-size-default: 14px;
$font-size-sm: 13px;
$font-size-xs: 12px;
$font-size-lg: 16px;
$font-size-xlg: 18px;
$font-size-2xlg: 24px;

// Space
$space-inline: 12px;
$space-block: 12px;
$space-block-lg: 24px;

// Scrollbar
$scrollbar-width: 8px;
$scrollbar-height: 8px;
$scrollbar-border-radius: 10px;
$scrollbar-thumb-bg-color: #ccc;

// Border Radius
$border-radius: 4px;
$border-radius-lg: 8px;

// Box Shadow
$box-shadow: 0 2px 6px #0000001a;

// Drawer
$drawer-padding-inline: 36px;
$drawer-padding-block: 36px;
$ant-drawer-btn-min-width: 120px;
$ant-drawer-btn-font-weight: 500;
