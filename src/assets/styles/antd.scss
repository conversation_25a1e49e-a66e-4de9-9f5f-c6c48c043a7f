@use './variables' as *;

:global {
  // 展开缩起
  .ant-typography-expand,
  .ant-typography-collapse {
    font-size: 12px;
  }

  // segmented
  .ant-segmented-grace {
    .ant-segmented-item {
      min-width: 80px;
    }
  }

  .ant-segmented-item-selected {
    color: $color-primary !important;
  }

  // Modal、Drawer
  .ant-modal-footer,
  .ant-drawer-footer {
    .ant-btn:not(.ant-btn-link) {
      font-weight: $ant-drawer-btn-font-weight;
    }

    .ant-btn-default {
      color: $color-primary;
    }

    .ant-btn-link {
      padding-inline: 2px;
    }
  }

  // Drawer
  .ant-drawer-header {
    height: $nav-height;

    .ant-btn:not(.ant-btn-link) {
      min-width: 100px;
    }
  }

  // Badge
  .ant-badge-count-sm {
    min-width: 16px !important;
    height: 16px !important;
    line-height: 16px !important;
    border-radius: 8px !important;
  }

  .ant-alert {
    padding: 2px 6px;
    font-size: 12px;
    color: #777 !important;
  }

  // Button
  .ant-btn-sm {
    height: 28px;
    font-size: 13px;
    border-radius: 2px;
  }

  // Descriptions
  .ant-descriptions-small {
    .ant-descriptions-header {
      margin-bottom: 4px;
    }

    .ant-descriptions-title {
      font-size: 14px;
    }
  }

  // List
  .ant-list-item {
    > .ant-ribbon-wrapper {
      width: 100%;
    }
  }
}
