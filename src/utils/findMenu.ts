import { IAuthority } from '../store'

function isNormal(path1?: string, path2?: string) {
  if (!path1 || !path2) {
    return false
  }
  if (path1 === path2) {
    return true
  }

  const paths1 = path1.split('/')
  const paths2 = path2.split('/')

  if (paths1.length !== paths2.length) {
    return false
  }

  return paths1.every((value, index) => value === paths2[index] || value?.includes(':') || paths2[index]?.includes(':'))
}

export function findMenu(path: string, routes: IAuthority[]) {
  let menu: IAuthority | undefined = void 0

  routes?.forEach(item => {
    if (!menu && item) {
      if (isNormal(path, item.href || item.url)) {
        menu = item
      } else if (item.children || item.menuList) {
        menu = findMenu(path, item.children || item.menuList)
      }
    }

    return false
  })

  return menu
}
