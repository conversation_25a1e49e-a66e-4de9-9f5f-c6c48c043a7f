import dayjs from 'dayjs'
import { envUrl } from './request'

export function isPdf(src?: string) {
  const ext = src?.toLowerCase().split('.').pop()

  return ext === 'pdf'
}
export function isWord(url?: string) {
  const wordExtensions = ['doc', 'docx']
  const ext = url?.split('.').pop()

  return wordExtensions.includes(ext?.toLowerCase() || '')
}
export const downloadXlsx = (res: Blob, name: string) => {
  const url = window.URL.createObjectURL(new Blob([res]))
  const link = document.createElement('a')

  link.href = url
  link.setAttribute('download', `${name}_${dayjs().format('YYYYMMDDHHmmss')}.xlsx`)
  document.body.appendChild(link)
  link.click()
}

export const getUploadProps = (action: string, headers = {}) => ({
  action: `${envUrl}${action}`,
  headers: {
    Authorization: `Bearer ${localStorage.getItem('token') || ''}`,
    Area: 'Changdu',
    ...headers,
  },
})

/**
 * 生成视频缩略图
 * @param videoUrl 视频URL
 * @param currentTime 截取时间点（秒），默认为1秒
 * @returns Promise<string> 返回base64格式的缩略图
 */
export const getVideoThumbnail = (videoUrl: string, currentTime = 1): Promise<string> =>
  new Promise((resolve, reject) => {
    const video = document.createElement('video')
    const canvas = document.createElement('canvas')

    video.crossOrigin = 'anonymous'
    video.currentTime = currentTime

    video.onloadeddata = () => {
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        reject(new Error('无法获取Canvas上下文'))

        return
      }

      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

      try {
        const thumbnail = canvas.toDataURL('image/jpeg', 0.8)

        resolve(thumbnail)
      } catch (error) {
        reject(error)
      }
    }

    video.onerror = () => reject(new Error('视频加载失败'))
    // video.src = videoUrl

    video.src = videoUrl.replace(
      'https://cdproductionserverwebapi.51changdu.com',
      'https://cdproductionserverwebapi-2.51changdu.com'
    )
  })
