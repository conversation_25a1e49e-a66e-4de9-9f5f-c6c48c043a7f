import { Modal, Typography, message } from 'antd'
import React from 'react'
import { FddVerifyStatus } from '../consts'

interface IPersonInfo {
  fddVerifyStatus?: number
  fddCustomerVerifyUrl?: string
  personId?: number
  [key: string]: any
}

type Itype = React.FC

/**
 * 验证法大大实名认证状态
 * @param value - 人员ID
 * @param personInfo - 人员信息，包含实名认证状态和认证链接
 * @param onIncomplete - 信息不全时的回调函数
 * @param getVerifyUrl - 获取验证链接的函数
 * @returns Promise<''>
 */
export const validateFddRealName = async (
  value: number,
  personInfo: IPersonInfo,
  onIncomplete?: (personId: number) => void,
  getVerifyUrl?: (id: number) => Promise<{ validResult: number; url?: string } | null>
): Promise<''> => {
  if (!value || !personInfo) {
    return Promise.reject('未找到选中的人员信息')
  }

  const { fddVerifyStatus, fddCustomerVerifyUrl, personId } = personInfo

  // 已认证状态
  if (fddVerifyStatus === FddVerifyStatus.Verified) {
    return Promise.resolve('')
  }
  // 信息不全状态特殊处理
  if (fddVerifyStatus === FddVerifyStatus.Incomplete && personId && onIncomplete) {
    onIncomplete(personId)

    return Promise.reject('该人员信息不全，请先完善信息')
  }

  // 未激活或未认证状态，需要获取验证链接
  if (
    (fddVerifyStatus === FddVerifyStatus.Inactive || fddVerifyStatus === FddVerifyStatus.Uncertified) &&
    getVerifyUrl &&
    !fddCustomerVerifyUrl &&
    personId
  ) {
    const hide = message.loading('正在获取用户实名验证链接...', 0)

    try {
      const response = await getVerifyUrl(personId)

      hide()

      if (response?.url) {
        Modal.info({
          title: fddVerifyStatus === FddVerifyStatus.Inactive ? '请将激活链接复制给该人员' : '请将认证链接复制给该人员',
          closable: true,
          icon: null,
          footer: false,
          maskClosable: true,
          width: 600,
          content: (
            <div>
              <p style={{ marginBottom: 16 }}>
                {fddVerifyStatus === FddVerifyStatus.Inactive
                  ? '该人员未激活，请先激活账号'
                  : '该人员未认证，请先完成实名认证'}
              </p>
              <Typography.Text
                copyable={{
                  text: response.url,
                  onCopy: () => message.success('链接已复制到剪贴板'),
                }}
                style={{
                  backgroundColor: '#f5f5f5',
                  padding: '12px',
                  borderRadius: '4px',
                  display: 'block',
                  wordBreak: 'break-all',
                  lineHeight: '1.5',
                }}>
                {response.url}
              </Typography.Text>
              <p style={{ marginTop: 12, color: '#666' }}>点击右侧复制按钮即可复制链接</p>
            </div>
          ),
        })

        return Promise.reject(
          fddVerifyStatus === FddVerifyStatus.Inactive ? '该人员未激活，请先激活账号' : '该人员未认证，请先完成实名认证'
        )
      }
      message.error('获取验证链接失败')

      return Promise.reject('获取验证链接失败')
    } catch (error) {
      hide()
      console.error('获取验证链接失败:', error)
      message.error('获取验证链接失败')

      return Promise.reject('获取验证链接失败')
    }
  }

  // 构建错误信息
  let errorMsg = ''

  switch (fddVerifyStatus) {
    case FddVerifyStatus.Inactive:
      errorMsg = '该人员未激活，请先激活账号'
      break
    case FddVerifyStatus.Uncertified:
      errorMsg = '该人员未认证，请先完成实名认证'
      break
    case FddVerifyStatus.Pending:
      errorMsg = '该人员实名认证正在审核中，请等待审核结果'
      break
    case FddVerifyStatus.Rejected:
      errorMsg = '该人员实名认证未通过，请重新提交认证'
      break
    case FddVerifyStatus.Incomplete:
      errorMsg = '该人员信息不全，请先完善信息'
      break
    default:
      errorMsg = '该人员未完成实名认证'
  }

  // 如果有认证链接，显示弹窗
  if (fddCustomerVerifyUrl) {
    Modal.info({
      title: '请将实名认证地址复制给该人员',
      closable: true,
      icon: null,
      footer: false,
      maskClosable: true,
      width: 600,
      content: (
        <div>
          <p style={{ marginBottom: 16 }}>{errorMsg}</p>
          <Typography.Text
            copyable={{
              text: fddCustomerVerifyUrl,
              onCopy: () => message.success('认证链接已复制到剪贴板'),
            }}
            style={{
              backgroundColor: '#f5f5f5',
              padding: '12px',
              borderRadius: '4px',
              display: 'block',
              wordBreak: 'break-all',
              lineHeight: '1.5',
            }}>
            {fddCustomerVerifyUrl}
          </Typography.Text>
          <p style={{ marginTop: 12, color: '#666' }}>点击右侧复制按钮即可复制链接</p>
        </div>
      ),
    })
  }

  return Promise.reject(errorMsg)
}
