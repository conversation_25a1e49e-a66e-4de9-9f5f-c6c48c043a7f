import { routes } from '@/router'

/**
 * 根据路由ID获取路径名
 * @param id 路由ID
 */
export function getBreadcrumbItemsById(id: string) {
  if (!id) {
    return []
  }

  const ids = id.split('-')
  const names = []
  let iterativeRoutes = routes

  while (ids.length) {
    const index = ids.shift()
    const currentRouter = typeof index === 'string' ? (iterativeRoutes[parseInt(index)] as any) : null

    if (currentRouter) {
      currentRouter.name && names.push(currentRouter.name)
      iterativeRoutes = currentRouter.children
    }
  }

  return names.map(item => ({
    key: item,
    title: item,
  }))
}
