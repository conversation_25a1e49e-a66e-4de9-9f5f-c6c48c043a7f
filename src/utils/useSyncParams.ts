import { useEffect } from 'react'
import { useLocation } from 'react-router-dom'
import useUrlState from '@ahooksjs/use-url-state'
import { useLocalStorageState } from 'ahooks'

/**
 * 页面使用的 search 参数(object) + url 参数(string) + storage 缓存参数(string) 同步
 * 依赖 ahooks/use-local-storage-state, 文档地址: https://ahooks.gitee.io/zh-CN/hooks/use-local-storage-state
 * 依赖 ahooks/use-url-state, 文档地址: https://ahooks.gitee.io/zh-CN/hooks/use-url-state
 */
export default <T>(): [T, any] => {
  const [storageSearch, setStorageSearch] = useLocalStorageState<any>('url-search', { defaultValue: {} })
  const { pathname, search } = useLocation()

  /**
   * 首次加载
   * 若 url - search 有值，则使用 url - search；
   * 若 url - search 没有值，则使用 storage 中的 search
   */
  const [urlState, setUrlState] = useUrlState<Partial<{ [key in keyof T]: any }>>(
    search ? null : storageSearch[pathname],
    {
      navigateMode: 'replace',
      parseOptions: {
        arrayFormat: 'comma', // 识别逗号转为 array 类型, parse('foo=1,2,3') => {foo: ['1', '2', '3']}
        parseNumbers: false, // 识别数字转为 number 类型, parse('foo=1') => { foo: 1 }
        parseBooleans: true, // 识别布尔值转为 boolean 类型, parse('foo=true') => { foo: true }
      },
      stringifyOptions: {
        arrayFormat: 'comma', // 识别 array 类型转为逗号, stringify({ foo: [1, 2, 3] }) => 'foo=1,2,3'
        skipNull: true, // 识别 null/undefined 删除, stringify({ a: 1, b: undefined, c: null, d: 4 }) => 'a=1&d=4'
        skipEmptyString: true, // 识别 '' 删除, stringify({ a: 1, b: '', c: '', d: 4 }) => 'a=1&d=4'
      },
    }
  )

  /**
   * urlState 改变时，更新 storage 中的 search
   */
  useEffect(() => {
    const result: any = {}
    const params = new URLSearchParams(search)

    for (const [key, value] of params.entries()) {
      result[key] = value
    }
    setStorageSearch({ ...storageSearch, [pathname]: result })
  }, [search])

  return [urlState as any, setUrlState]
}
