// 随机获取数组中的选项
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import Decimal from 'decimal.js'

dayjs.extend(utc)
dayjs.extend(timezone)
type IEnv = 'local' | 'dev' | 'test' | 'stage' | 'pro'

/**
 *
 * @param array 获取随机项
 * @returns
 */
export function getRandomItem(array: any[]): any {
  if (!array || !array.length) {
    return void 0
  }

  if (array.length === 1) {
    return array[0]
  }

  const index = Math.floor(Math.random() * array.length)

  return array[index]
}

// 对象转数组
export const object2Arr = (obj: any, isNumber = false) =>
  Object.keys(obj).map(key => ({
    label: obj[key],
    value: isNumber ? Number(key) : key,
  }))

// 设置域名cookie
export const Cookie = {
  set(name: string, value: any, days: number) {
    let domain, domainParts, date, expires

    if (days) {
      const EXPIRES_TIME = days * 24 * 60 * 60 * 1000

      date = new Date()
      date.setTime(date.getTime() + EXPIRES_TIME)
      expires = `; expires=${date.toGMTString()}`
    } else {
      expires = ''
    }

    const host = location.host

    if (host.split('.').length === 1) {
      document.cookie = `${name}=${value}${expires}; path=/`
    } else {
      domainParts = host.split('.')
      domainParts.shift()
      domain = `.${domainParts.join('.')}`

      document.cookie = `${name}=${value}${expires}; path=/; domain=${domain}`
    }
  },
  get(name: string) {
    const cookieString = document.cookie
    const cookies = cookieString.split('; ')

    for (let i = 0; i < cookies.length; i++) {
      const cookie = cookies[i].split('=')

      if (cookie[0] === name) {
        return decodeURIComponent(cookie[1])
      }
    }

    return ''
  },
}

// 生成随机字符串
export const getUuid = () => {
  const buf = new Uint32Array(4)

  window.crypto.getRandomValues(buf)

  let idx = -1

  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    idx++
    let r = (buf[idx >> 3] >> ((idx % 8) * 4)) & 15

    r = r ^ (c === 'x' ? 0 : c === 'y' ? 8 : 3)

    return r.toString(16)
  })
}

// 生成随机字符串
export const generate32RandomId = () => {
  const length = 32
  const array = new Uint32Array(length / 8)

  window.crypto.getRandomValues(array)

  return Array.from(array)
    .map(dec => dec.toString(16))
    .join('')
}

export const ENV: IEnv = (() => {
  if (
    window.location.origin.includes('localhost') ||
    window.location.origin.includes(':38') ||
    window.location.origin.includes('192')
  ) {
    return 'local'
  }
  if (window.location.origin.includes('-dev')) {
    return 'dev'
  }
  if (window.location.origin.includes('-test')) {
    return 'test'
  }
  if (window.location.origin.includes('-stage')) {
    return 'stage'
  }

  return 'pro'
})()

// 解析query
export const getQueryParams = (url = window.location.search) => {
  const theRequest: any = {}

  if (url.indexOf('?') !== -1) {
    const str = url.substr(1)
    const params: Array<string> = str.split('&')

    for (let i = 0; i < params.length; i++) {
      theRequest[params[i].split('=')[0]] = unescape(params[i].split('=')[1])
    }
  }

  return theRequest
}

export const copy = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
  } catch (err) {
    const input = document.createElement('input')

    input.value = text
    document.body.appendChild(input)
    input.select()
    document.execCommand('copy')
    document.body.removeChild(input)
  }
}

export const addTime = (time?: string, gap = 13) => (time ? dayjs(time).add(gap, 'hours').valueOf() : void 0)

export const deleteTime = (time?: string, gap = 13) => (time ? dayjs(time).add(-gap, 'hours').valueOf() : void 0)

/**
 * 获取调整后的当前时间（5:00以后才算当天）
 * 如果当前时间在5:00之前，算作前一天
 * @param time 可选的时间参数，默认为当前时间
 * @returns 调整后的dayjs对象
 */
export const getAdjustedCurrentTime = (time?: dayjs.Dayjs) => {
  const targetTime = time || dayjs()
  // 如果当前时间在5:00之前，算作前一天
  if (targetTime.hour() < 5) {
    return targetTime.subtract(1, 'day')
  }
  return targetTime
}

export function secondsToMinutes(seconds: number) {
  if (!seconds) {
    return ''
  }
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60

  if (minutes === 0) {
    return `${remainingSeconds}秒`
  }

  return `${minutes}分钟 ${remainingSeconds}秒`
}

export function toLocaleString(amount = 0) {
  return Number(Number(Math.trunc(amount * 100) / 100).toFixed(2))
    .toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      style: 'currency',
      currency: 'USD',
      currencyDisplay: 'symbol', // 你可以尝试不同的值如 'code', 'name', 'narrowSymbol'
    })
    .replace(/\$/g, '')
}

// 除以100
export const formatDivide100 = (value: any) => {
  if (!value) {
    return 0
  }

  return new Decimal(value).div(100).toNumber()
}

// 乘以100
export const formatMultiply100 = (value: any) => {
  if (!value) {
    return 0
  }

  return new Decimal(value).mul(100).toNumber()
}

// 获取url参数
export const urlSearchKey = (name: string) => {
  const reg = `(^|&)${name}=([^&]*)(&|$)`
  const r = window.location.search.substr(1).match(reg)

  if (r !== null) {
    return unescape(r[2])
  }

  return null
}

/**
 * 解析中国身份证号码
 * @param idCard 身份证号码字符串
 * @returns {check: boolean, gender: number, dateOfBirth: number, isChild: boolean}
 */
export function parseIdCard(idCard: string): {
  check: boolean
  gender: number
  dateOfBirth: number
  isChild: boolean
} {
  // 默认返回值
  const result = {
    check: false,
    gender: 0,
    dateOfBirth: 0,
    isChild: false,
  }

  // 基本格式验证
  if (!idCard || typeof idCard !== 'string') {
    return result
  }

  // 转为大写并去除空格
  const cleanIdCard = idCard.toUpperCase().trim()

  // 长度验证
  if (cleanIdCard.length !== 18) {
    return result
  }

  // 字符验证（前17位必须是数字，最后一位可以是数字或X）
  const pattern = /^[0-9]{17}[0-9X]$/

  if (!pattern.test(cleanIdCard)) {
    return result
  }

  // 校验码验证
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

  let sum = 0

  for (let i = 0; i < 17; i++) {
    sum += parseInt(cleanIdCard[i], 10) * weights[i]
  }

  const checkCodeIndex = sum % 11
  const expectedCheckCode = checkCodes[checkCodeIndex]

  if (cleanIdCard[17] !== expectedCheckCode) {
    return result
  }

  // 提取出生日期
  const birthYear = parseInt(cleanIdCard.substring(6, 10), 10)
  const birthMonth = parseInt(cleanIdCard.substring(10, 12), 10)
  const birthDay = parseInt(cleanIdCard.substring(12, 14), 10)

  // 验证出生日期的合理性
  if (birthYear < 1900 || birthYear > new Date().getFullYear()) {
    return result
  }

  if (birthMonth < 1 || birthMonth > 12) {
    return result
  }

  if (birthDay < 1 || birthDay > 31) {
    return result
  }

  // 更详细的日期验证
  const birthDate = new Date(birthYear, birthMonth - 1, birthDay)

  if (
    birthDate.getFullYear() !== birthYear ||
    birthDate.getMonth() !== birthMonth - 1 ||
    birthDate.getDate() !== birthDay
  ) {
    return result
  }

  // 提取性别（第17位，奇数为男，偶数为女）
  const genderCode = parseInt(cleanIdCard[16], 10)
  const gender = genderCode % 2 === 1 ? 1 : 2

  // 判断是否儿童（18岁以下）
  const currentYear = new Date().getFullYear()
  const age = currentYear - birthYear
  const isChild = age < 18

  return {
    check: true,
    gender,
    dateOfBirth: birthYear,
    isChild,
  }
}


export function uuid(): string {
  return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, c => {
    const r = (Math.random() * 16) | 0,
      v = c === 'x' ? r : (r & 0x3) | 0x8

    return v.toString(16)
  })
}