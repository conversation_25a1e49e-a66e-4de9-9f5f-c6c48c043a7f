import { useEffect, useState } from 'react'
import { useLocation } from 'react-router-dom'
import useStore from '../store'
import { findMenu } from './findMenu'

const getChildren = (children: any, name?: string): string => {
  if (name) {
    return name
  }
  if (children?.props?.children) {
    return getChildren(children.props.children)
  }

  return children
}

export default function useAuthority() {
  const { authority, userAuthority } = useStore()
  const [menu, setMenu] = useState<any>()
  const [buttons, setButtons] = useState<string[]>([])
  const [allAuthority, setAllAuthority] = useState(false)
  const location = useLocation()

  const allowToUse = (children: any, name?: string) =>
    buttons.includes(getChildren(children, name)) || allAuthority ? children : null

  useEffect(() => {
    const menu: any = findMenu(location.pathname, authority)

    setAllAuthority(userAuthority?.type === 1)
    setMenu(menu)
    if (menu?.children) {
      setButtons(menu.children?.map((item: { title: string }) => item.title))
    } else {
      setButtons([])
    }
  }, [location.pathname, authority, userAuthority])

  return { menu, buttons, allAuthority, allowToUse }
}
