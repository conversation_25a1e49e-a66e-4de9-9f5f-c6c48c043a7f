import Cookies from 'js-cookie'

/**
 * 从当前页面 URL 提取域名后缀
 * @returns {string} 提取到的域名后缀，若提取失败则返回默认值 'vip'
 */
const getDomainSuffix = (): string => {
  // 本地返回测试域名
  if (location.href.includes('localhost') || location.href.includes('192.168')) {
    return 'ltd'
  }

  const originArr = location.origin.split('.')

  return originArr[originArr.length - 1] || 'vip'
}

const domainSuffix = getDomainSuffix()

/**
 * 构建 cookie 操作所需的域名
 * @returns {string} 构建好的域名，格式为 `.changdu.${suffix}`
 */
const buildCookieDomain = (): string => `.changdu.${domainSuffix}`

/**
 * 移除名为 'accessToken' 的 cookie
 * @description 从指定域名和路径下移除 'accessToken' cookie，要求使用安全连接（HTTPS）
 */
export const removeTokenCookie = (): void => {
  const domain = buildCookieDomain()

  Cookies.remove('accessToken', { path: '', domain, secure: true })
}

/**
 * 获取名为 'accessToken' 的 cookie
 * @description 从指定域名和路径下获取 'accessToken' cookie
 * @returns {string | undefined} 获取到的 'accessToken' cookie 值，若不存在则返回 undefined
 */
export const getTokenCookie = (): string | undefined => {
  const domain = buildCookieDomain()

  return Cookies.get('accessToken', { path: '', domain })
}
