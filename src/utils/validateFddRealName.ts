import { message } from 'antd'
import { FddVerifyStatus, ValidRealNameResult } from '../consts'

/**
 * 验证法大大实名认证状态
 * @param personId 人员ID
 * @param selectedPerson 选中的人员信息
 * @param handleIncomplete 处理信息不全的回调
 * @param validRealName 验证实名的API方法
 * @returns Promise<void>
 */
export const validateFddRealName = async (
  personId: number,
  selectedPerson: any,
  handleIncomplete: (personId: number) => void,
  validRealName: (personId: number) => Promise<any>
) => {
  const { fddVerifyStatus } = selectedPerson

  // 如果状态是信息不全，打开编辑表单
  if (fddVerifyStatus === FddVerifyStatus.Incomplete) {
    handleIncomplete(personId)

    return Promise.reject(new Error('请完善人员信息'))
  }

  // 如果状态是未激活或未认证，需要获取验证链接
  if (fddVerifyStatus === FddVerifyStatus.Inactive || fddVerifyStatus === FddVerifyStatus.Uncertified) {
    const hide = message.loading('正在获取验证链接...', 0)

    try {
      const response = await validRealName(personId)

      hide()

      if (!response) {
        message.error('获取验证链接失败')

        return Promise.reject(new Error('获取验证链接失败'))
      }

      const { validResult, url } = response

      // 如果验证结果是需要实名认证
      if (validResult === ValidRealNameResult.Unverified && url) {
        message.info('请完成实名认证')
        window.open(url, '_blank')

        return Promise.reject(new Error('请完成实名认证'))
      }

      // 如果验证结果是需要激活
      if (validResult === ValidRealNameResult.Incomplete && url) {
        message.info('请先激活账号')
        window.open(url, '_blank')

        return Promise.reject(new Error('请先激活账号'))
      }

      return Promise.reject(new Error('验证状态异常'))
    } catch (error) {
      hide()
      console.error('验证实名失败:', error)
      message.error('验证实名失败')

      return Promise.reject(new Error('验证实名失败'))
    }
  }

  // 如果状态是审核中，提示等待
  if (fddVerifyStatus === FddVerifyStatus.Pending) {
    message.info('实名认证正在审核中，请耐心等待')

    return Promise.reject(new Error('实名认证正在审核中'))
  }

  // 如果状态是审核不通过，提示重新认证
  if (fddVerifyStatus === FddVerifyStatus.Rejected) {
    message.error('实名认证未通过，请重新认证')

    return Promise.reject(new Error('实名认证未通过'))
  }

  // 如果状态是已通过，则验证通过
  if (fddVerifyStatus === FddVerifyStatus.Verified) {
    return Promise.resolve()
  }

  // 其他情况
  message.error('实名认证状态异常')

  return Promise.reject(new Error('实名认证状态异常'))
}
