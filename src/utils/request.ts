import useStore from '@/store'
import { axiosProgress } from '@/utils/progress'
import { message as Message } from 'antd'
import axios, { AxiosResponse } from 'axios'
import COS from 'cos-js-sdk-v5'

/*
 * 登录/权限相关：https://cdcreatorsimwebapi-cn-test1.changdu.ltd
 * 业务相关：https://cdcreatorwebapi-cn-dev1.changdu.ltd
 */

const request = axios.create({ timeout: 120000, withCredentials: true })
const progress = axiosProgress()

let baseURL = 'https://cdproductionserverwebapi.51changdu.com'
let authURL = 'https://cdproductionpermissionwebapi.changdu.vip'

if (location.href.includes('-dev.')) {
  baseURL = 'https://cdproductionserverwebapi-none-new-test.changdu.ltd'
  authURL = 'https://cdproductionpermission-none-new-test.changdu.ltd'
}
if (location.href.includes('localhost') || location.href.includes('192')) {
  baseURL = 'https://cdproductionserverwebapi-none-new-test.changdu.ltd'
  authURL = 'https://cdproductionpermission-none-new-test.changdu.ltd'
}

if (location.href.includes('-test.')) {
  baseURL = 'https://cdproductionserverwebapi-none-new-test.changdu.ltd'
  authURL = 'https://cdproductionpermission-none-new-test.changdu.ltd'
}

if (location.href.includes('-stage')) {
  baseURL = 'https://cdproductionserverwebapi-stage.changdu.vip'
  authURL = 'https://cdproductionpermissionwebapi-stage.changdu.vip'
}
request.defaults.baseURL = baseURL

// 清除请求参数中的空格
const trimParams = obj => {
  if (window?._.isObject(obj)) {
    Object.keys(obj).forEach(key => {
      const value = obj[key]

      if (window?._.isString(value)) {
        obj[key] = value.trim()
      } else if (window?._.isObject(value)) {
        trimParams(value)
      }
    })
  }
}

request.interceptors.request.use(
  (config: any) => {
    progress.start()

    const token = localStorage.getItem('token')

    config.headers.Authorization = `Bearer ${token}`
    config.headers.Area = 'Changdu'

    if (config.params) {
      trimParams(config.params)
    }

    if (config.data) {
      trimParams(config.data)
    }

    return config
  },
  error => {
    progress.done()

    return Promise.reject(error)
  }
)

request.interceptors.response.use(
  response => {
    progress.done()
    const { data, statusMsg, statusCode, code, message } = response?.data || {}

    if (response?.config?.ignoreFormat) {
      return response
    }
    if (code == '312' || statusCode == -1 || code == '-1') {
      Message.error(statusMsg || message || '请求异常')

      return { data: null, status: false }
    }
    if (code == '401') {
      useStore.getState().handleLogin()
    }

    return { data: data || response?.data, status: true }
  },
  error => {
    progress.done()
    if (error?.response?.data?.code === 9998 || error?.response?.status === 401) {
      Message.error(error.response.data.message)
      useStore.getState().handleLogin()
    }
    Message.error('请求异常')

    return { ...error, status: false }
  }
)

// cos文件上传
export const fileUpload = (file: any, filename: string, auth: any, fn: (value: string) => void) => {
  const cos = new COS({
    Protocol: 'https:',
    getAuthorization(options, callback) {
      callback({
        TmpSecretId: auth.tmpSecretId, // 临时密钥的 tmpSecretId
        TmpSecretKey: auth.tmpSecretKey, // 临时密钥的 tmpSecretKey
        SecurityToken: auth.token, // 临时密钥的 sessionToken
        // 建议返回服务器时间作为签名的开始时间，避免用户浏览器本地时间偏差过大导致签名错误
        StartTime: +auth.startTime, // 时间戳，单位秒，如：1580000000
        ExpiredTime: +auth.expiredTime, // 临时密钥失效时间戳，是申请临时密钥时，时间戳加 durationSeconds
      })
    },
  })

  const cosParams = {
    Bucket: 'koc-hongkong-1319644609',
    Region: 'ap-hongkong',
  }

  try {
    const userLanguage = navigator.language || navigator.userLanguage
    const isChinese = userLanguage.startsWith('zh')

    if (isChinese) {
      cosParams.Bucket = 'koc-gz-1319644609'
      cosParams.Region = 'ap-guangzhou'
    }
  } catch (error) {
    console.error('获取浏览器语言失败：', error)
  }

  console.log('cosParams', cosParams)

  cos.putObject(
    {
      ...cosParams,
      Key: `${filename}`,
      Body: file, // 上传文件对象
      ContentDisposition: 'inline',
      ContentType: 'text/html', // 设置 Content-Type
    },
    (err, data) => {
      if (data?.statusCode === 200) {
        // message.success('文件上传成功')
        fn(true)
      } else {
        // 重试一次
        cos.putObject(
          {
            ...cosParams,
            Key: `${filename}`,
            Body: file, // 上传文件对象
            ContentDisposition: 'inline',
            ContentType: 'text/html', // 设置 Content-Type
          },
          (err, data) => {
            if (data?.statusCode === 200) {
              // message.success('文件上传成功')
              fn(true)
            } else {
              fn(false)
            }
            // message.warning('文件上传失败')
          }
        )
      }
      // message.warning('文件上传失败')
    }
  )
}

interface IAdsRequestOptions {
  url: string
  params?: any
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  [key: string]: any
}
export const adsRequest = ({
  url,
  params = {},
  method = 'POST',
  isMaterial = false,
  isUpload = false,
  ...config
}: IAdsRequestOptions): Promise<any> =>
  new Promise((resolve, reject) => {
    const routeKey = url.split('/').pop() as string

    request({
      url: `/api/AdsReport/GetAdsSign?RouteKey=${routeKey}`,
      method: 'GET',
    })
      .then((signResponse: AxiosResponse<any>) => {
        const signData = signResponse?.data?.data || {}
        let isAds = true

        if (isMaterial) {
          isAds = false
        }

        if (isUpload) {
          const formData = new FormData()
          const data = {
            ...signData,
            ...params,
          }

          Object.keys(data)?.forEach(key => {
            formData.append(key, data[key])
          })

          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          return request({
            url,
            method,
            headers: { Signature: signData.Sign },
            data: formData,
            ...config,
            isAds,
            isMaterial,
            isUpload,
          })
        }

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        return request({
          url,
          method,
          headers: { Signature: signData.Sign },
          data: {
            ...signData,
            ...params,
            Sign: void 0,
          },
          ...config,
          isAds,
          isMaterial,
        })
      })
      .then((dataResponse: AxiosResponse<any>) => {
        resolve(dataResponse || {})
      })
      .catch(error => {
        reject(error)
      })
  })

export const get = request.get
export const post = request.post
export const envUrl = baseURL
export const envAuthUrl = authURL
export default request
