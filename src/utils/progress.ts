import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

export const axiosProgress = () => {
  let progressTime: null | ReturnType<typeof setTimeout> = null

  return {
    start() {
      if (progressTime) {
        clearTimeout(progressTime)
        progressTime = null
      }
      progressTime = setTimeout(NProgress.start, 200)
    },
    done() {
      if (progressTime) {
        clearTimeout(progressTime)
        progressTime = null
      }
      NProgress.done()
    },
  }
}
