export const copy = (text: string): void => {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            console.log('Text copied to clipboard');
        }).catch(err => {
            console.error('Failed to copy text: ', err);
            // Fallback to execCommand if clipboard API fails
            fallbackCopyTextToClipboard(text);
        });
    } else {
        // Fallback to execCommand if clipboard API is not available
        fallbackCopyTextToClipboard(text);
    }
};

function fallbackCopyTextToClipboard(text: string) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        const msg = successful ? 'successful' : 'unsuccessful';
        console.log('Fallback: Text copied ' + msg);
    } catch (err) {
        console.error('Fallback: Failed to copy text: ', err);
    }

    document.body.removeChild(textArea);
}
