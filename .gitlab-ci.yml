stages:
  - notify

notify_tag_build:
  stage: notify
  script:
    - |
      if [ -n "$CI_COMMIT_TAG" ]; then
        AUTHOR_NAME=$(git show -s --format='%an' $CI_COMMIT_SHA)
        EMPLOYEE_ID=$(python3 -c "import urllib.parse; print(urllib.parse.quote('''$AUTHOR_NAME'''))")
        TAG_NAME="$CI_COMMIT_TAG"
        REPO_URL="$CI_PROJECT_URL.git"
        HOOK_URL="https://kunlun-api.changdu.vip/base/outter/hooks/build?tag=${TAG_NAME}&gitAddress=${REPO_URL}&employeeId=${EMPLOYEE_ID}"
        echo "Notify URL: $HOOK_URL"
        RETRY=0
        MAX_RETRY=2
        echo "xunhuan"
        while [ $RETRY -le $MAX_RETRY ]; do
          HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$HOOK_URL")
          echo "HTTP_CODE: $HTTP_CODE"
          if [ "$HTTP_CODE" = "200" ]; then
            echo "Notify succeeded."
            break
          else
            RETRY=$((RETRY+1))
            if [ $RETRY -le $MAX_RETRY ]; then
              echo "Notify failed, retry $RETRY after 60 seconds..."
              sleep 60
            else
              echo "Notify failed after $((MAX_RETRY+1)) attempts."
              exit 1
            fi
          fi
        done
      else
        echo "Not a tag pipeline, skip notify."
      fi
  only:
    - tags
  tags:
    - gitlab-test
